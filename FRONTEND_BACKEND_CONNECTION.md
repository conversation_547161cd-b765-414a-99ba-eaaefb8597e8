# Frontend-Backend Connection Guide

## Single Source of Truth Configuration

### 1. Environment Variables

**Frontend (.env)**
```env
REACT_APP_API_URL=http://localhost:4000/api/v1
REACT_APP_TENANT_ID=acme-corp
REACT_APP_MOCK_API=false
```

**Backend (.env)**
```env
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sistema_cobranca?schema=public"
JWT_SECRET="your-secure-jwt-secret-change-this"
JWT_EXPIRATION="7d"
FRONTEND_URL="http://localhost:3000"
NODE_ENV="development"
PORT=4000
```

### 2. API Configuration

All API calls go through a single centralized instance:

**File:** `frontend/src/services/api.ts`

```typescript
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});
```

Key features:
- Automatic token injection from localStorage
- Tenant ID header added to all requests
- Request/response interceptors for error handling
- Token expiry detection and redirect

### 3. Authentication Flow

1. **Login Process:**
   - User enters email/password
   - Frontend calls `POST /auth/login` via centralized API
   - Backend validates credentials against PostgreSQL
   - Backend returns JWT token and user info
   - Frontend stores token in localStorage

2. **Token Storage:**
   - Key: `unified_auth_tokens`
   - Contains: access_token, refresh_token, token_type
   - Expiry tracked separately in `unified_token_expiry`

3. **Protected Requests:**
   - API interceptor adds `Authorization: Bearer <token>` header
   - API interceptor adds `x-tenant-id` header
   - Backend validates JWT and tenant on each request

### 4. Data Flow

```
Frontend                    Backend                     PostgreSQL
   |                           |                             |
   |-- API Request ----------->|                             |
   |   (with token & tenant)   |                             |
   |                           |-- Query with tenant ------->|
   |                           |                             |
   |                           |<-- Filtered data -----------|
   |<-- JSON Response ---------|                             |
```

### 5. Services Using Centralized API

✅ **Using Backend API (Correct):**
- UnifiedAuthService (refactored)
- OrderService
- NutraService
- CorreiosService
- UnifiedUserContext

❌ **Using LocalStorage Only (Need Migration):**
- ZapConfigService
- InventoryService
- ProductService

### 6. Multi-Tenancy

**Current Implementation:**
- Tenant ID comes from environment variable
- All requests include `x-tenant-id` header
- Backend filters all queries by tenant

**Production Implementation (TODO):**
- Tenant determined by subdomain or login
- Tenant ID stored in JWT payload
- Row-level security in database

### 7. Common Issues and Solutions

**Issue:** "Erro no servidor. Por favor, tente novamente."
**Solution:** Check that backend is running on port 4000 and DATABASE_URL is correct

**Issue:** Orders not showing in frontend
**Solution:** Ensure REACT_APP_MOCK_API=false in .env

**Issue:** 401 Unauthorized errors
**Solution:** Check token expiry and ensure JWT_SECRET matches between restarts

### 8. Testing the Connection

1. Start PostgreSQL:
```bash
docker-compose up -d
```

2. Start Backend:
```bash
cd backend
npm run start:dev
```

3. Start Frontend:
```bash
cd frontend
npm start
```

4. Test Login:
- Email: <EMAIL>
- Password: admin123

### 9. API Endpoints

**Authentication:**
- POST /auth/login - Login with email/password
- GET /auth/me - Get current user info

**Orders:**
- GET /orders - List all orders (filtered by tenant)
- POST /orders - Create new order
- PUT /orders/:id - Update order
- DELETE /orders/:id - Delete order

**Users:**
- GET /users - List all users (admin only)
- POST /users - Create new user
- PUT /users/:id - Update user
- DELETE /users/:id - Delete user

### 10. Security Considerations

⚠️ **Current Issues:**
- JWT secret has fallback value (remove in production)
- Tokens stored in localStorage (vulnerable to XSS)
- No refresh token implementation
- Tenant ID in frontend (should come from auth)

✅ **Implemented:**
- Password hashing with bcrypt
- JWT token validation
- Tenant isolation in queries
- CORS configuration

### 11. Next Steps

1. Remove JWT secret fallback in auth.module.ts
2. Implement httpOnly cookies for tokens
3. Add refresh token endpoint
4. Move tenant determination to backend
5. Add rate limiting
6. Implement audit logging