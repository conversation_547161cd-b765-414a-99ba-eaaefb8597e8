<!DOCTYPE html>
<html>
<head>
    <title>Test Product API</title>
</head>
<body>
    <h1>Test Product API</h1>
    <button onclick="testLogin()">1. Login</button>
    <button onclick="testGetProducts()">2. Get Products</button>
    <button onclick="testCreateProduct()">3. Create Product</button>
    <div id="result"></div>

    <script>
        let token = null;

        async function testLogin() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-tenant-id': 'cmc4a2wg50000uvkzj76t0mpt'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'senha123'
                    })
                });
                const data = await response.json();
                if (data.access_token) {
                    token = data.access_token;
                    document.getElementById('result').innerHTML = '<pre>Login successful! Token: ' + token.substring(0, 50) + '...</pre>';
                } else {
                    document.getElementById('result').innerHTML = '<pre>Login failed: ' + JSON.stringify(data, null, 2) + '</pre>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }

        async function testGetProducts() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/products', {
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'x-tenant-id': 'cmc4a2wg50000uvkzj76t0mpt'
                    }
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }

        async function testCreateProduct() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/products', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + token,
                        'x-tenant-id': 'cmc4a2wg50000uvkzj76t0mpt'
                    },
                    body: JSON.stringify({
                        name: 'Test Product ' + new Date().getTime(),
                        description: 'Test description',
                        variations: [
                            {
                                type: 'CAPSULAS',
                                costPrice: 10
                            }
                        ]
                    })
                });
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>