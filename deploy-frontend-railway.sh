#!/bin/bash

echo "🚀 Deploying Frontend via Railway (which has more memory)..."
echo ""
echo "Steps:"
echo "1. Go to https://railway.app"
echo "2. Create a new project"
echo "3. Deploy from GitHub repo"
echo "4. Set these environment variables:"
echo "   - REACT_APP_API_URL=https://zencash-production.up.railway.app/api/v1"
echo "   - REACT_APP_TENANT_ID=28a833c0-c2a1-4498-85ca-b028f982ffb2"
echo "   - GENERATE_SOURCEMAP=false"
echo "   - NODE_OPTIONS=--max-old-space-size=8192"
echo ""
echo "Railway provides up to 32GB RAM which should handle the build."
echo ""
echo "Alternative: Use Netlify instead of Vercel"
echo "1. Go to https://app.netlify.com"
echo "2. Import from Git"
echo "3. Set build command: cd frontend && npm run build"
echo "4. Set publish directory: frontend/build"