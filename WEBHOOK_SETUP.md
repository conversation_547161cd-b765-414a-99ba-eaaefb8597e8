# Webhook Setup Documentation

## Overview
Your ZenCash system can receive webhook notifications from external platforms to automatically update order and tracking information.

## Webhook URLs

### Production Environment
- **Base URL**: `https://zencash-production-1ccd.up.railway.app`

### Available Endpoints

1. **Test Endpoint** (No authentication required)
   - URL: `https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test`
   - Method: `POST`
   - Use this to test connectivity before setting up production webhooks

2. **Production Endpoint**
   - URL: `https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/shipments`
   - Method: `POST`
   - Authentication: Optional in development, required in production

3. **Info Endpoint**
   - URL: `https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/info`
   - Method: `GET`
   - Returns webhook configuration details

## Quick Start

### Step 1: Test Connectivity
Send a test request to verify the webhook is accessible:

```bash
curl -X POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{"test": "Hello"}'
```

### Step 2: Configure Your Platform
Provide the following URL to the external platform:
```
https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/shipments
```

### Step 3: Set Required Headers
```
Content-Type: application/json
```

## Supported Fields

The webhook accepts the following fields (in Portuguese or English):

- `codigoRastreio` or `trackingCode` - Tracking number
- `situacao` or `status` - Current status
- `cliente` or `customer` - Customer name
- `telefone` or `phone` - Phone number
- `email` - Email address
- `valorTotal` or `total` - Order total value
- `cpf` or `documento` - Customer document
- `eventos` or `events` - Array of tracking events

## Example Payload

```json
{
  "codigoRastreio": "BR123456789BR",
  "situacao": "Em trânsito",
  "cliente": "João Silva",
  "telefone": "11999999999",
  "email": "<EMAIL>",
  "valorTotal": 150.00,
  "cpf": "12345678901",
  "eventos": [
    {
      "data": "2025-01-22",
      "hora": "10:30",
      "descricao": "Objeto postado",
      "local": "São Paulo - SP"
    }
  ]
}
```

## Authentication (Production Only)

For production environments with sensitive data, we support HMAC-SHA256 signature validation.

### Webhook Secret
Contact your administrator to obtain the webhook secret key.

### Generating Signature
```javascript
const crypto = require('crypto');
const payload = JSON.stringify(yourWebhookData);
const signature = crypto
  .createHmac('sha256', webhookSecret)
  .update(payload)
  .digest('hex');

// Add to headers
headers['X-Webhook-Signature'] = signature;
```

## Field Mapping

After receiving your first webhook:
1. Go to the dashboard: `/dashboard/webhook-mappings`
2. Map the received fields to your database columns
3. Future webhooks will automatically use these mappings

## Testing Your Integration

1. Use the test script provided:
```bash
./test-webhook.sh
```

2. Or manually test with curl:
```bash
# Test endpoint
curl -X POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Production endpoint
curl -X POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/shipments \
  -H "Content-Type: application/json" \
  -d '{"codigoRastreio": "TEST123", "cliente": "Test Customer"}'
```

## Troubleshooting

### "Invalid or inaccessible webhook URL"
- Ensure you're using HTTPS (not HTTP)
- Include the full path: `/api/v1/webhooks/shipments`
- Set Content-Type header to `application/json`

### "Invalid webhook signature"
- This only applies in production mode
- Contact admin for the correct webhook secret
- Ensure signature is calculated correctly

### Data not appearing
- Check the webhook mappings dashboard
- Ensure fields are properly mapped
- Check application logs for errors

## Support

For assistance with webhook setup, check:
1. Webhook info endpoint: `GET /api/v1/webhooks/info`
2. Application logs for detailed error messages
3. Webhook mappings dashboard for field configuration