# Status Update Error Investigation

## Problem
User was getting "Erro ao atualizar status. Por favor, tente novamente." when trying to change order status.

## Root Cause
The backend enforces strict status transition rules. Not all status changes are allowed. For example:
- From "<PERSON>ara<PERSON>" → can only go to "<PERSON><PERSON><PERSON><PERSON><PERSON>" or "Can<PERSON><PERSON>"
- From "An<PERSON><PERSON><PERSON>" → can only go to "Separa<PERSON>" or "Cancelado"
- Some statuses are final (Completo, Frustrado, Cancelado) and cannot transition to any other status

The UI buttons were trying to set statuses that weren't valid transitions from the current order status.

## Changes Made

### 1. Enhanced Error Logging (Frontend)
- Added detailed error logging in OrdersTable.tsx
- Logs current status, new status, and full error details
- Shows more specific error messages to users

### 2. Created Dedicated Status Update Method
- Added `updateOrderStatus` method in OrderService.ts
- Uses the correct endpoint: `PATCH /orders/:id/status`
- Properly maps Portuguese status names to Prisma enum values

### 3. Improved Backend Error Messages
- Updated orders.service.ts to provide helpful error messages
- Now shows which transitions are valid from the current status
- Example: "Transição inválida de Separacao para Frustrado. Status válidos a partir de Separacao: Transito, Cancelado"

## Valid Status Transitions

```
Analise → Separacao, Cancelado
Separacao → Transito, Cancelado
Transito → ConfirmarEntrega, EntregaFalha, RetirarCorreios, DevolvidoCorreios
ConfirmarEntrega → Completo, PagamentoPendente, Negociacao, Parcial
PagamentoPendente → Completo, Parcial, Negociacao, Frustrado
Negociacao → Recuperacao, Cancelado, Completo, Parcial, Frustrado
Parcial → Completo, Negociacao
Recuperacao → Transito, Negociacao, Cancelado, Completo, Parcial
EntregaFalha → Recuperacao, Negociacao, Frustrado
RetirarCorreios → Frustrado, Cancelado
DevolvidoCorreios → Frustrado, Cancelado

Final states (no transitions allowed):
- Completo
- Frustrado
- Cancelado
```

## Next Steps
Consider implementing one of these solutions:
1. Make the status buttons context-aware (only show valid transitions based on current status)
2. Add an admin override option to bypass transition rules
3. Review and potentially relax some transition rules based on business requirements

## Testing
When testing status updates, check the browser console for detailed logs showing:
- Current order status
- Attempted new status
- Valid transitions from current status