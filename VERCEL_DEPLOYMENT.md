# Vercel Deployment Guide

## Current Issue
The frontend build is failing on Vercel due to memory constraints (heap out of memory error).

## Solution Options

### Option 1: GitHub Actions Deployment (Recommended)
1. Add these secrets to your GitHub repository:
   - `VERCEL_TOKEN`: Get from https://vercel.com/account/tokens
   - `VERCEL_ORG_ID`: `team_eTDuqsWBm8fW995PjxDyQCqg`
   - `VERCEL_PROJECT_ID`: `prj_KjaxDgILQIa90hEMgr5Km1M0Myi9`

2. The workflow will automatically deploy when you push to main

### Option 2: Local Build & Deploy
```bash
cd frontend

# Build locally with more memory
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP=false
npm run build

# Deploy the built files
vercel --prod --prebuilt
```

### Option 3: Temporary Fix - Reduce Bundle Size
1. Remove unused dependencies
2. Enable code splitting
3. Lazy load heavy components

## Environment Variables
Make sure these are set in Vercel:
- `REACT_APP_API_URL`: Your Railway backend URL
- `REACT_APP_TENANT_ID`: Your tenant ID
- Any other required env vars

## Vercel Project Details
- Org ID: `team_eTDuqsWBm8fW995PjxDyQCqg`
- Project ID: `prj_KjaxDgILQIa90hEMgr5Km1M0Myi9`
- Framework: Create React App
- Build Output: `build`