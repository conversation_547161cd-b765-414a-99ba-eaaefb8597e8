# Post-Deployment Monitoring & Operations Guide

## 🎯 Monitoring Overview

Post-deployment monitoring ensures the anti-fraud system operates efficiently, detects issues early, and provides insights for continuous improvement.

## 📊 Key Performance Indicators (KPIs)

### Business Metrics

#### 1. Fraud Prevention Effectiveness
```sql
-- Daily fraud prevention value
SELECT 
  DATE(created_at) as date,
  COUNT(*) as duplicates_caught,
  SUM(CASE WHEN duplicate_status = 'DENIED' THEN total ELSE 0 END) as fraud_prevented_value,
  AVG(duplicate_match_score) as avg_confidence
FROM orders
WHERE is_duplicate = true
  AND created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

**Targets**:
- Detection Rate: >90% of known duplicates
- False Positive Rate: <10%
- ROI: >R$50,000/month in prevented fraud

#### 2. Operational Efficiency
```sql
-- Review queue performance
SELECT 
  DATE(reviewed_at) as date,
  COUNT(*) as reviews_completed,
  AVG(EXTRACT(EPOCH FROM (reviewed_at - created_at))/60) as avg_review_time_minutes,
  COUNT(DISTINCT reviewed_by) as active_reviewers,
  COUNT(*) / COUNT(DISTINCT reviewed_by) as reviews_per_user
FROM orders
WHERE is_duplicate = true 
  AND reviewed_at IS NOT NULL
  AND reviewed_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(reviewed_at);
```

**Targets**:
- Average Review Time: <5 minutes
- Reviews per Hour per User: >20
- Queue Depth: <100 pending reviews

### Technical Metrics

#### 1. System Performance
```typescript
// Prometheus queries for Grafana
const performanceQueries = {
  // API Latency
  apiLatency: `
    histogram_quantile(0.95, 
      rate(http_request_duration_seconds_bucket{
        controller="AntifraudController"
      }[5m])
    )
  `,
  
  // Detection Performance
  detectionLatency: `
    histogram_quantile(0.95,
      rate(antifraud_detection_duration_seconds_bucket[5m])
    )
  `,
  
  // Database Query Time
  dbQueryTime: `
    avg(rate(prisma_query_duration_seconds_sum[5m]) / 
        rate(prisma_query_duration_seconds_count[5m]))
  `,
  
  // Error Rate
  errorRate: `
    rate(antifraud_errors_total[5m]) / 
    rate(antifraud_operations_total[5m])
  `
};
```

**Targets**:
- API p95 Latency: <200ms
- Detection p95 Latency: <2s
- Error Rate: <0.1%
- Availability: 99.9%

## 🔍 Monitoring Stack Configuration

### 1. Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'zencash-antifraud'
    static_configs:
      - targets: ['zencash-api.railway.app:9090']
    metrics_path: '/metrics'
    params:
      format: ['prometheus']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

rule_files:
  - 'antifraud-alerts.yml'
```

### 2. Alert Rules
```yaml
# antifraud-alerts.yml
groups:
  - name: antifraud_critical
    interval: 30s
    rules:
      - alert: HighErrorRate
        expr: |
          rate(antifraud_errors_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "High error rate in anti-fraud system"
          description: "Error rate is {{ $value | humanizePercentage }}"

      - alert: DetectionTimeout
        expr: |
          histogram_quantile(0.99, rate(antifraud_detection_duration_seconds_bucket[5m])) > 5
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "Anti-fraud detection taking too long"

      - alert: QueueOverflow
        expr: |
          antifraud_review_queue_size > 1000
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Review queue has {{ $value }} pending items"

  - name: antifraud_warnings
    interval: 60s
    rules:
      - alert: HighFalsePositiveRate
        expr: |
          rate(antifraud_reviews_total{decision="approved"}[1h]) / 
          rate(antifraud_reviews_total[1h]) > 0.8
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "High false positive rate: {{ $value | humanizePercentage }}"

      - alert: SlowReviews
        expr: |
          avg(antifraud_review_duration_seconds) > 300
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Reviews taking avg {{ $value }}s"
```

### 3. Grafana Dashboards

#### Anti-Fraud Overview Dashboard
```json
{
  "dashboard": {
    "title": "Anti-Fraud System Overview",
    "panels": [
      {
        "title": "Detection Rate",
        "targets": [{
          "expr": "rate(antifraud_detections_total[5m])"
        }],
        "type": "graph"
      },
      {
        "title": "Review Queue Size",
        "targets": [{
          "expr": "antifraud_review_queue_size"
        }],
        "type": "gauge"
      },
      {
        "title": "Decision Distribution",
        "targets": [{
          "expr": "rate(antifraud_reviews_total[1h]) by (decision)"
        }],
        "type": "piechart"
      },
      {
        "title": "Match Score Distribution",
        "targets": [{
          "expr": "histogram_quantile(0.5, antifraud_match_score_bucket)"
        }],
        "type": "heatmap"
      }
    ]
  }
}
```

## 📈 Daily Operations Runbook

### Morning Checks (9:00 AM)
```bash
#!/bin/bash
# morning-checks.sh

echo "🌅 Running morning checks..."

# 1. Check system health
curl -s https://api.zencash.com/health/detailed | jq .

# 2. Review overnight metrics
psql $DATABASE_URL -c "
  SELECT 
    COUNT(*) as overnight_orders,
    SUM(CASE WHEN is_duplicate THEN 1 ELSE 0 END) as duplicates_found,
    AVG(CASE WHEN is_duplicate THEN duplicate_match_score ELSE NULL END) as avg_score
  FROM orders
  WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '12 hours';
"

# 3. Check queue status
curl -s https://api.zencash.com/antifraud/statistics | jq .queue

# 4. Review any overnight alerts
curl -s http://prometheus:9090/api/v1/alerts | jq '.data.alerts[] | select(.state=="firing")'

echo "✅ Morning checks complete"
```

### Hourly Monitoring
```typescript
// Automated hourly health check
setInterval(async () => {
  const metrics = await collectMetrics();
  
  if (metrics.queueDepth > 500) {
    await notifySlack('#antifraud-monitoring', 
      `⚠️ Queue depth high: ${metrics.queueDepth} pending reviews`);
  }
  
  if (metrics.errorRate > 0.02) {
    await notifyPagerDuty('high-error-rate', metrics);
  }
  
  await logMetrics(metrics);
}, 60 * 60 * 1000); // Every hour
```

### Weekly Performance Review
```sql
-- Weekly performance report
WITH weekly_stats AS (
  SELECT 
    DATE_TRUNC('week', created_at) as week,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN is_duplicate THEN 1 END) as duplicates_found,
    COUNT(CASE WHEN duplicate_status = 'DENIED' THEN 1 END) as fraud_prevented,
    SUM(CASE WHEN duplicate_status = 'DENIED' THEN total ELSE 0 END) as value_saved,
    AVG(CASE WHEN reviewed_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (reviewed_at - created_at))/60 
        END) as avg_review_minutes
  FROM orders
  WHERE created_at >= CURRENT_DATE - INTERVAL '4 weeks'
  GROUP BY week
)
SELECT 
  week,
  total_orders,
  duplicates_found,
  ROUND(100.0 * duplicates_found / total_orders, 2) as detection_rate,
  fraud_prevented,
  value_saved,
  ROUND(avg_review_minutes, 1) as avg_review_minutes
FROM weekly_stats
ORDER BY week DESC;
```

## 🚨 Incident Response Procedures

### Severity Levels

#### SEV1: Critical (System Down)
**Examples**: API not responding, database connection lost, detection failing for all orders

**Response**:
1. Page on-call engineer immediately
2. Start incident bridge call
3. Implement immediate mitigation (e.g., disable anti-fraud)
4. Root cause analysis within 24 hours

#### SEV2: Major (Degraded Performance)
**Examples**: High latency, queue overflow, error rate >5%

**Response**:
1. Alert on-call engineer (Slack)
2. Investigate within 30 minutes
3. Scale resources if needed
4. Monitor closely

#### SEV3: Minor (Non-Critical Issues)
**Examples**: Individual tenant issues, UI bugs, false positive spike

**Response**:
1. Create ticket for next sprint
2. Notify product team
3. Workaround if available

### Common Issues & Solutions

#### 1. High False Positive Rate
```bash
# Diagnosis
psql $DATABASE_URL -c "
  SELECT 
    tenant_id,
    COUNT(*) as flagged,
    SUM(CASE WHEN duplicate_status = 'APPROVED' THEN 1 ELSE 0 END) as approved,
    ROUND(100.0 * SUM(CASE WHEN duplicate_status = 'APPROVED' THEN 1 ELSE 0 END) / COUNT(*), 2) as false_positive_rate
  FROM orders
  WHERE is_duplicate = true
    AND reviewed_at >= CURRENT_DATE - INTERVAL '24 hours'
  GROUP BY tenant_id
  HAVING COUNT(*) > 10
  ORDER BY false_positive_rate DESC;
"

# Solution: Adjust thresholds
railway run --environment production \
  npx ts-node scripts/update-config.ts \
  --fuzzyMatchThreshold=0.8 \
  --minAddressComponents=3
```

#### 2. Queue Overflow
```bash
# Add temporary reviewers
UPDATE users 
SET role = 'SUPERVISOR' 
WHERE email IN ('<EMAIL>', '<EMAIL>');

# Bulk approve low-score matches
UPDATE orders 
SET duplicate_status = 'APPROVED',
    reviewed_at = NOW(),
    reviewed_by = 'system-bulk'
WHERE is_duplicate = true
  AND duplicate_status = 'PENDING_REVIEW'
  AND duplicate_match_score < 60
  AND created_at < CURRENT_TIMESTAMP - INTERVAL '24 hours';
```

#### 3. Performance Degradation
```bash
# Check slow queries
SELECT 
  query,
  mean_time,
  calls,
  total_time
FROM pg_stat_statements
WHERE query LIKE '%orders%'
ORDER BY mean_time DESC
LIMIT 10;

# Add missing index if needed
CREATE INDEX CONCURRENTLY idx_orders_tenant_duplicate_status 
ON orders(tenant_id, is_duplicate, duplicate_status) 
WHERE is_duplicate = true;

# Vacuum and analyze
VACUUM ANALYZE orders;
```

## 📊 Monthly Reporting

### Executive Dashboard
```typescript
interface MonthlyReport {
  fraudPrevented: {
    ordersBlocked: number;
    valueSaved: number;
    percentageOfTotal: number;
  };
  operationalMetrics: {
    avgReviewTime: number;
    reviewsPerDay: number;
    systemUptime: number;
  };
  accuracy: {
    truePositives: number;
    falsePositives: number;
    precision: number;
    recall: number;
  };
  costs: {
    infrastructureCost: number;
    laborCost: number;
    costPerOrderChecked: number;
    roi: number;
  };
}
```

### Stakeholder Report Template
```markdown
# Anti-Fraud System Monthly Report - [Month Year]

## Executive Summary
- Total fraud prevented: R$ [X]
- Orders checked: [Y]
- Detection accuracy: [Z]%
- ROI: [W]x

## Key Achievements
- [Achievement 1]
- [Achievement 2]
- [Achievement 3]

## Challenges & Solutions
- [Challenge 1] → [Solution/Status]
- [Challenge 2] → [Solution/Status]

## Next Month Focus
- [Priority 1]
- [Priority 2]
- [Priority 3]

## Recommendations
- [Recommendation 1]
- [Recommendation 2]
```

## 🔄 Continuous Improvement

### A/B Testing Framework
```typescript
// Test different matching algorithms
const abTest = {
  name: "improved-address-matching",
  variants: {
    control: currentAlgorithm,
    treatment: improvedAlgorithm
  },
  allocation: {
    control: 0.5,
    treatment: 0.5
  },
  metrics: [
    "false_positive_rate",
    "detection_accuracy",
    "processing_time"
  ],
  duration: "2 weeks"
};
```

### Feedback Loop
1. **Weekly Review Meeting**
   - Review metrics
   - Discuss edge cases
   - Prioritize improvements

2. **Monthly Retrospective**
   - What worked well?
   - What needs improvement?
   - Action items for next month

3. **Quarterly Planning**
   - Feature roadmap
   - Performance targets
   - Resource allocation

## 🎯 Success Metrics Tracking

### Real-time Success Dashboard
```
┌─────────────────────────────────────────────┐
│          ANTI-FRAUD SUCCESS METRICS         │
├─────────────────────────────────────────────┤
│ 📊 Today's Performance                      │
│   • Orders Checked: 12,456                  │
│   • Duplicates Found: 1,823 (14.6%)        │
│   • Fraud Prevented: R$ 45,230             │
│   • Avg Review Time: 3.2 min               │
├─────────────────────────────────────────────┤
│ 📈 This Month                               │
│   • Total Saved: R$ 1,234,567              │
│   • Accuracy: 94.3%                        │
│   • False Positives: 5.7%                  │
│   • ROI: 12.4x                             │
├─────────────────────────────────────────────┤
│ 🎯 Targets                                  │
│   ✅ Detection Rate: 92% (Target: 90%)     │
│   ✅ Review Time: 3.2m (Target: <5m)       │
│   ⚠️  False Positives: 5.7% (Target: <5%)  │
│   ✅ Uptime: 99.95% (Target: 99.9%)        │
└─────────────────────────────────────────────┘
```

This comprehensive monitoring setup ensures the anti-fraud system operates efficiently and delivers continuous value to the business.