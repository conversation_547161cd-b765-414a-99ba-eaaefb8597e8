{"permissions": {"allow": ["Bash(find:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx husky init:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(export:*)", "Bash(ls:*)", "Bash(/usr/local/bin/docker compose up -d)", "mcp__ide__getDiagnostics", "Bash(npm run lint:*)", "Bash(cp:*)", "Bash(npm run:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(mysql:*)", "Bash(PORT=3001 npm start)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(npm start)", "Bash(kill:*)", "Bash(npx serve:*)", "Bash(PORT=3002 npm start)", "Bash(rg:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(true)", "Bash(open http://localhost:3002/users)", "Bash(pgrep:*)", "<PERSON><PERSON>(open:*)", "Bash(npm test)", "Bash(npm test:*)", "Bash(REACT_APP_API_URL=http://localhost:3333/api/v1 PORT=3001 npm start)", "Bash(PORT=4001 npm run start:dev)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx nest g module:*)", "Bash(npx nest g service:*)", "Bash(npx nest g controller:*)", "Bash(psql:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(perl:*)", "Bash(for i in 7 8 9 10 11 12)", "Bash(do sed -i '' \"$is|ZenCash - Sistema de Log[^/]*_files|landing-assets|g\" landing.html)", "Bash(done)", "Bash(for i in 468 469 470)", "Bash(do sed -i '' \"$is|ZenCash - Sistema de Log[^/]*_files|landing-assets|g\" /Users/<USER>/sistema-cobranca/frontend/public/landing.html)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" PORT=3001 npm start)", "Bash(node:*)", "Bash(git remote add:*)", "Bash(git remote set-url:*)", "Bash(git push:*)", "Bash(PORT=3000 npm run start:dev)", "Bash(PORT=3003 npm run start:dev)", "<PERSON><PERSON>(cat:*)", "Bash(ps:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" npm start)", "Bash(ssh-keygen:*)", "Bash(brew install:*)", "<PERSON><PERSON>(scp:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(claude --debug)", "Bash(npx:*)", "Bash(claude --debug \"test\" 2 >& 1)", "Bash(npm ls:*)", "Bash(timeout 5 npx @modelcontextprotocol/server-puppeteer)", "Bash(git checkout:*)", "<PERSON>sh(./test-commission.sh:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(railway link:*)", "Bash(./railway-deploy-automated.sh)", "Bash(railway status:*)", "<PERSON><PERSON>(vercel:*)", "Bash(./fix-build-errors.sh)", "Bash(./fix-remaining-errors.sh:*)", "Bash(./minimal-fix.sh:*)", "Bash(./prepare-for-railway.sh:*)", "Bash(./final-build-fix.sh:*)", "Bash(./railway-final-fix.sh:*)", "Bash(./railway-simple-build.sh)", "Bash(./generate-keys.sh:*)", "Bash(git update-index:*)", "Bash(./build.sh)", "Bash(GENERATE_SOURCEMAP=false CI=false npm run build)", "<PERSON><PERSON>(railway login:*)", "Bash(./railway-frontend-quick-setup.sh:*)", "Bash(railway up:*)", "Bash(npm view:*)", "Bash(railway logs:*)", "Bash(REACT_APP_API_URL=https://zencash-production.up.railway.app/api/v1 REACT_APP_TENANT_ID=************************************ npm run build:railway)", "Bash(./check-deployment.sh)", "Bash(DATABASE_URL=\"postgresql://postgres:<EMAIL>:48459/railway\" npx tsx scripts/fix-admin-user.ts)", "Bash(git reset:*)", "Bash(git restore:*)", "Bash(awk:*)", "Bash(npm cache clean:*)", "Bash(npm ci:*)", "Bash(brew services start:*)", "Bash(NODE_OPTIONS='--max-old-space-size=8192' npm run build)", "Bash(SKIP_PREFLIGHT_CHECK=true TSC_COMPILE_ON_ERROR=true DISABLE_ESLINT_PLUGIN=true npm run build)", "Bash(ts-node:*)", "Bash(git pull:*)", "Bash(/Users/<USER>/zencash/backend/test-webhook.sh)", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"SELECT COUNT(*) as total FROM \"\"Order\"\";\")", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"SELECT COUNT(*) as total FROM \"\"Order\"\";\")", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"DELETE FROM \"\"OrderItem\"\"; DELETE FROM \"\"OrderStatusHistory\"\"; DELETE FROM \"\"Tracking\"\"; DELETE FROM \"\"OrderAuditLog\"\"; DELETE FROM \"\"Order\"\";\")", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"SELECT COUNT(*) as total FROM \"\"Order\"\";\")", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"\\d \"\"Product\"\"\")", "Bash(psql \"postgresql://postgres:<EMAIL>:48459/railway\" -c \"\\d \"\"Kit\"\"\")", "Bash(/Users/<USER>/zencash/backend/test-webhook-debug.sh:*)", "Bash(git commit -m \"$(cat <<''EOF''\nfix: allow SUPERVISOR role to access antifraud statistics endpoint\n\n- Updated @Roles decorator on GET /antifraud/statistics endpoint to include SUPERVISOR role\n- This ensures consistency with other antifraud endpoints that already allow both ADMIN and SUPERVISOR access\n\n🤖 Generated with [<PERSON> Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")", "<PERSON><PERSON>(source .env)", "Bash(pm2 list:*)", "<PERSON><PERSON>(source:*)", "Bash(echo \"# To disable CSP in Chrome (Mac):\nopen -a ''Google Chrome'' --args --disable-web-security --user-data-dir=/tmp/chrome_dev\n\n# Or for Chrome Canary:\nopen -a ''Google Chrome Canary'' --args --disable-web-security --user-data-dir=/tmp/chrome_dev\")", "Bash(DATABASE_URL=\"mysql://root:@localhost:3306/zencash\" npx tsx src/scripts/fix-antifraud-direct.ts)", "Bash(DATABASE_URL=\"mysql://root:@localhost:3306/zencash\" npx tsx src/scripts/fix-antifraud-scores.ts)", "Bash(DATABASE_URL=\"postgresql://postgres:<EMAIL>:48459/railway\" npx tsx src/scripts/fix-antifraud-scores.ts)"]}, "enableAllProjectMcpServers": false}