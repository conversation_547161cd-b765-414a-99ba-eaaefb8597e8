// Test script to create an order with nextPaymentDate set to today

import axios from 'axios';

const API_URL = 'http://localhost:3001/api';

async function createTestOrder() {
  try {
    // First, login to get auth token
    console.log('Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>', // Change to your admin email
      password: 'admin123' // Change to your admin password
    });

    const token = loginResponse.data.access_token;
    console.log('Login successful');

    // Create axios instance with auth header
    const api = axios.create({
      baseURL: API_URL,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Get today's date in ISO format
    const today = new Date();
    today.setHours(12, 0, 0, 0); // Set to noon to avoid timezone issues
    const todayISO = today.toISOString();

    // Find an existing Negociação order to update
    console.log('Finding Negociação orders...');
    const ordersResponse = await api.get('/orders');
    const negociacaoOrder = ordersResponse.data.find(
      order => order.status === 'Negociacao' || order.status === 'Negociação'
    );

    if (negociacaoOrder) {
      console.log(`Found Negociação order: ${negociacaoOrder.orderNumber}`);
      
      // Update the order with today's nextPaymentDate
      console.log(`Updating order with nextPaymentDate: ${todayISO}`);
      const updateResponse = await api.patch(`/orders/${negociacaoOrder.id}`, {
        nextPaymentDate: todayISO
      });

      console.log('Order updated successfully:', {
        orderNumber: updateResponse.data.orderNumber,
        status: updateResponse.data.status,
        nextPaymentDate: updateResponse.data.nextPaymentDate
      });
    } else {
      console.log('No Negociação order found. Creating a new order...');
      
      // Create a new order
      const createResponse = await api.post('/orders', {
        customerName: 'Test Receber Hoje',
        customerPhone: '11999999999',
        customerCPF: '12345678901',
        items: [{
          productId: 'test-product',
          productName: 'Test Product',
          quantity: 1,
          unitPrice: 100
        }]
      });

      const newOrderId = createResponse.data.id;
      console.log('Order created:', createResponse.data.orderNumber);

      // Update status to Negociação and set nextPaymentDate
      console.log('Updating to Negociação with nextPaymentDate...');
      const statusResponse = await api.patch(`/orders/${newOrderId}/status`, {
        status: 'Negociacao'
      });

      const dateResponse = await api.patch(`/orders/${newOrderId}`, {
        nextPaymentDate: todayISO
      });

      console.log('Order updated successfully:', {
        orderNumber: dateResponse.data.orderNumber,
        status: dateResponse.data.status,
        nextPaymentDate: dateResponse.data.nextPaymentDate
      });
    }

    console.log('\nTest completed! Check the Receber Hoje filter in the frontend.');
    console.log('Note: Check the browser console for debug logs.');

  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
createTestOrder();