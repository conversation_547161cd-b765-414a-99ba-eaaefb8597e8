# Railway Frontend Deployment Guide

## Prerequisites
✅ Railway CLI installed (you now have it)
✅ Frontend configuration files ready (railway.json, nixpacks.toml)
✅ Code pushed to GitHub

## Step-by-Step Deployment

### 1. Login to Railway CLI
```bash
railway login
```
- This will open your browser to authenticate

### 2. Link to Your Existing Project
```bash
cd /Users/<USER>/zencash
railway link
```
- Select your existing ZenCash project (where backend is deployed)

### 3. Create a New Service for Frontend
```bash
railway service create frontend
```

### 4. Deploy the Frontend
```bash
cd frontend
railway up --service frontend
```

### 5. Set Environment Variables
```bash
# Set all required environment variables
railway vars set REACT_APP_API_URL="https://zencash-backend.railway.app/api/v1" --service frontend
railway vars set REACT_APP_TENANT_ID="************************************" --service frontend
railway vars set NODE_OPTIONS="--max-old-space-size=16384" --service frontend
railway vars set GENERATE_SOURCEMAP="false" --service frontend
railway vars set CI="false" --service frontend
railway vars set REACT_APP_ENV="production" --service frontend
railway vars set REACT_APP_ENABLE_DEBUG="false" --service frontend
```

### 6. Configure Domain (Optional)
```bash
railway domain --service frontend
```
- This will generate a Railway domain like `frontend-production.up.railway.app`

## Alternative: Deploy via Railway Dashboard

1. Go to https://railway.app
2. Open your existing project
3. Click "New Service"
4. Select "GitHub Repo"
5. Choose your repo and set:
   - **Root Directory**: `/frontend`
   - **Watch Patterns**: `/frontend/**`
6. Add environment variables:
   ```
   REACT_APP_API_URL=https://zencash-backend.railway.app/api/v1
   REACT_APP_TENANT_ID=************************************
   NODE_OPTIONS=--max-old-space-size=16384
   GENERATE_SOURCEMAP=false
   CI=false
   REACT_APP_ENV=production
   REACT_APP_ENABLE_DEBUG=false
   ```

## Deployment Settings Already Configured

Your `railway.json` and `nixpacks.toml` will:
- Use Node.js 20
- Allocate 16GB memory for build
- Build with `npm run build`
- Serve static files with `serve`
- Listen on Railway's PORT

## Expected Build Time
- First build: 5-10 minutes (due to large app)
- Subsequent builds: 2-5 minutes (with cache)

## Troubleshooting

If build still fails with memory:
1. Increase NODE_OPTIONS to 24GB: `--max-old-space-size=24576`
2. Contact Railway support for higher limits

## After Deployment
Your frontend will be available at:
- Railway domain: `https://[service-name]-production.up.railway.app`
- Custom domain: Can be configured in Railway dashboard