services:
  db:
    image: postgres:13
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=billing_system
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: always
    environment:
      - ENVIRONMENT=production
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=billing_system
      - SECRET_KEY=${SECRET_KEY:-change-this-in-production}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET:-change-this-in-production}
      - CORS_ORIGINS=${CORS_ORIGINS:-["https://yourdomain.com"]}
      - CORREIOS_API_URL=${CORREIOS_API_URL:-https://api.correios.com.br}
      - CORREIOS_API_KEY=${CORREIOS_API_KEY:-change-this-in-production}
    command: >
      sh -c "python -m app.db.create_tables &&
             uvicorn app.main:app --host 0.0.0.0 --port 8000"
    depends_on:
      db:
        condition: service_healthy

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: always
    environment:
      - REACT_APP_API_URL=${API_URL:-https://yourdomain.com/api}
      - REACT_APP_MOCK_API=false
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - backend

volumes:
  postgres_data:
