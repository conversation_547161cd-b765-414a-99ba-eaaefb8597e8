{"project": {"name": "ZenCash Backend (Uso Interno - NestJS + Railway)", "description": "Backend do sistema ZenCash simplificado para uso interno da equipe, sem arquitetura multi-tenant. O foco é colocar o sistema em funcionamento de forma estável, com funcionalidades essenciais como autenticação, pedidos, produtos, notificações, relatórios e rastreamento."}, "instructions": "Você é um engenheiro full stack especialista em NestJS, Prisma e Railway. Sua função é construir o backend completo do ZenCash para uso interno da empresa. Não existe multi-tenant neste projeto. Tudo deve estar focado em uso interno por uma única empresa, com múltiplos usuários com diferentes funções: admin, supervisor, cobrador e vendedor. A stack é: NestJS (TypeScript), Prisma ORM, PostgreSQL (Railway), Redis + BullMQ, JWT para autenticação. O sistema já possui frontend funcionando. Cada módulo do backend deve respeitar as rotas e permissões previstas no frontend. A conexão com o banco será fornecida via variável DATABASE_URL. Cada sprint deve ser pequena, validada e funcional isoladamente.", "tasks": [{"title": "Sprint 0 – Setup Inicial (Backend Local)", "description": "Configurar projeto NestJS com Prisma conectado ao Railway.", "subtasks": ["Criar projeto NestJS com estrutura modular", "Instalar Prisma e client", "Criar .env com DATABASE_URL e JWT_SECRET", "Criar schema.prisma inicial com User, Role, Order, OrderStatus", "Rodar migration inicial", "Organizar estrutura de pastas por domínio (auth, users, orders, common)"]}, {"title": "Sprint 1 – Autenticação e Usuário", "description": "Sistema de login com JWT, guards por role, e criação de usuário administrador.", "subtasks": ["<PERSON><PERSON>r AuthService com login usando e-mail/senha", "Utilizar bcrypt para hashing de senha", "Gerar JWT com userId, email, role", "C<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> para proteger endpoints por perfil", "Criar endpoint GET /me para retornar o usuário logado", "Criar seed inicial com um usuário admin"]}, {"title": "Sprint 2 – CRUD de Usuários", "description": "Gerenciar usuários com base nos papéis definidos.", "subtasks": ["CRUD completo: criar, listar, editar, desativar usuários", "Validação de e-mail único", "Filtro por role e status", "Permissões: apenas admin pode criar/editar/deletar"]}, {"title": "Sprint 3 – Pedidos", "description": "CRUD de pedidos com fluxo de status, vendedores e cobradores.", "subtasks": ["Modelar entidade Order com sellerId, status, valor, cliente", "Implementar fluxos de status", "Permissões: ve<PERSON><PERSON> c<PERSON>, cobrador <PERSON><PERSON>, admin edita tudo", "<PERSON><PERSON> histórico de status"]}, {"title": "Sprint 4 – Produtos e Kits", "description": "Cadastrar produtos, variações e kits com controle de estoque.", "subtasks": ["Entidades: Product, Variation, Offer, Kit", "Controle de estoque: entrada, sa<PERSON>da, quantidade disponível", "CRUD de produtos com status ativo/inativo", "Composição de kits com múltiplos produtos"]}, {"title": "Sprint 5 – Clientes", "description": "Cadastro de clientes com CPF, CEP e histórico de pedidos.", "subtasks": ["Entidades: Customer, Address", "Validação de CPF", "Integração com ViaCEP para preencher endereço", "Histórico de interações e pedidos do cliente"]}, {"title": "Sprint 6 – Notifica<PERSON><PERSON><PERSON>", "description": "Integração com WhatsApp e envio de mensagens automáticas.", "subtasks": ["Configurar Redis + BullMQ", "Criar NotificationService com fila", "Templates de mensagens por status de pedido", "<PERSON><PERSON> via worker"]}, {"title": "Sprint 7 – Ra<PERSON>ento <PERSON>", "description": "Buscar status via Correios e alertar mudanças.", "subtasks": ["Entidade Tracking associada ao pedido", "Integração com Correios a cada 1 hora", "Painel de status críticos", "Histórico de status por pedido"]}, {"title": "Sprint 8 – Relat<PERSON><PERSON>s", "description": "Gerar relató<PERSON> de vendas, cobrança e performance.", "subtasks": ["Relatório de vendas por período, vendedor, produto", "Financeiro: recebido, pendente, cancelado", "Ranking de cobradores", "Exportação CSV"]}, {"title": "Sprint 9 – Configurações e Integrações", "description": "Salvar e recuperar configurações de integração.", "subtasks": ["<PERSON><PERSON> configurações no banco: <PERSON>s<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stripe", "Salvar templates de mensagens", "Testar envio de mensagens", "Chaves e segurança de APIs"]}, {"title": "Sprint 10 – Finalização e Deploy", "description": "Testes finais, documentação Swagger, deploy no Railway e backup.", "subtasks": ["Testes unitários e e2e", "Documentação com Swagger", "Deploy para Railway com variáveis configuradas", "Backup automático e verificação de logs"]}]}