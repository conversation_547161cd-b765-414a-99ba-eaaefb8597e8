# Sistema de Cobrança

Sistema completo de gerenciamento de cobrança com arquitetura multi-tenant usando NestJS e React.

## 🚀 Características

- **Multi-tenant**: Suporte a múltiplos tenants/empresas
- **Autenticação JWT**: Sistema de autenticação seguro
- **Gestão de Pedidos**: CRUD completo para pedidos
- **Gestão de Usuários**: Diferentes níveis de acesso (Admin, Supervisor, Collector, Seller)
- **API RESTful**: Documentação automática com Swagger
- **Testes**: Cobertura de testes unitários e e2e
- **Docker**: Containerização completa
- **Rate Limiting**: Proteção contra spam
- **Validação**: Validação robusta de dados
- **Health Checks**: Monitoramento da aplicação
- **Security Headers**: Proteção com Helmet

## 🛠️ Tecnologias

- **Backend**: NestJS + TypeScript + Prisma + MySQL
- **Frontend**: React + TypeScript + Material-UI
- **Autenticação**: JWT + Passport
- **Banco de Dados**: MySQL 8.0
- **ORM**: Prisma
- **Documentação**: Swagger/OpenAPI
- **Testes**: Jest + Supertest
- **Deploy**: Docker + Docker Compose

## Configuração do Ambiente

### Usando Docker (Recomendado)

```bash
# Clone o repositório
git clone <url-do-repositorio>
cd sistema-cobranca

# Configure as variáveis de ambiente
cp backend/.env.example backend/.env
# Edite o arquivo .env com suas configurações

# Inicie todos os serviços
docker-compose up -d

# A aplicação estará disponível em:
# Frontend: http://localhost:3001
# Backend API: http://localhost:3000/api/v1
# Swagger Docs: http://localhost:3000/docs
```

### Instalação Manual

1. Clone o repositório
2. Instale as dependências do backend:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```
3. Configure as variáveis de ambiente no arquivo `.env`
4. Inicie o servidor de desenvolvimento:
   ```bash
   uvicorn app.main:app --reload
   ```
5. Instale as dependências do frontend:
   ```bash
   cd frontend
   npm install
   ```
6. Inicie o servidor de desenvolvimento do frontend:
   ```bash
   npm start
   ```

## Estrutura do Projeto

```
├── backend/
│   ├── app/
│   │   ├── api/
│   │   ├── core/
│   │   ├── db/
│   │   ├── models/
│   │   ├── schemas/
│   │   └── services/
│   ├── tests/
│   └── alembic/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── contexts/
│   │   ├── hooks/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   └── public/
├── docs/
│   ├── quick_start.md
│   ├── user_guide.md
│   ├── role_guides.md
│   └── technical_docs.md
├── docker-compose.yml
├── docker-compose.prod.yml
└── deploy.sh
```

## Usuários Padrão

Para desenvolvimento e testes, os seguintes usuários estão disponíveis:

- **Admin**:
  - Email: <EMAIL>
  - Senha: admin123

- **Supervisor**:
  - Email: <EMAIL>
  - Senha: supervisor123

- **Operador**:
  - Email: <EMAIL>
  - Senha: operador123

- **Vendedor**:
  - Email: <EMAIL>
  - Senha: vendedor123

## Documentação

Consulte a pasta `docs/` para documentação detalhada:

- [Guia Rápido](docs/quick_start.md)
- [Guia do Usuário](docs/user_guide.md)
- [Guias por Perfil](docs/role_guides.md)
- [Documentação Técnica](docs/technical_docs.md)

## Testes

### Frontend

```bash
cd frontend
npm test
```

### Backend

```bash
cd backend
pytest
```

## Contribuição

### Fluxo de Trabalho Git

1. Clone o repositório:
   ```bash
   git clone https://github.com/SEU_USUARIO/sistema-cobranca.git
   cd sistema-cobranca
   ```

2. Crie uma branch para sua feature:
   ```bash
   git checkout -b feature/nome-da-feature
   ```

3. Faça suas alterações e commit:
   ```bash
   git add .
   git commit -m "Descrição das alterações"
   ```

4. Envie para o GitHub:
   ```bash
   git push origin feature/nome-da-feature
   ```

5. Crie um Pull Request no GitHub para revisão.

## Licença

Proprietário - Todos os direitos reservados