# Deployment Guide - Sistema de Cobrança

## Step 1: Prepare for GitHub

### 1.1 Check Git Status
First, let's see what files have been modified:

```bash
git status
```

### 1.2 Create/Update .gitignore Files
Make sure sensitive files are not committed:

**Backend .gitignore** (`/backend/.gitignore`):
```
node_modules/
dist/
.env
.env.local
.env.production
*.log
.DS_Store
prisma/*.db
prisma/*.db-journal
test-*.js
```

**Frontend .gitignore** (`/frontend/.gitignore`):
```
node_modules/
build/
.env
.env.local
.env.production
*.log
.DS_Store
test-*.js
get-token.js
src/utils/setupTestData.ts
src/components/SetupTestData.tsx
src/components/AuthDebug.tsx
src/components/TestProductCreation.tsx
```

### 1.3 Create Environment File Templates

**Backend** (`/backend/.env.example`):
```
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/database_name"

# JWT
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="24h"

# API
PORT=3000
API_PREFIX="api/v1"

# Environment
NODE_ENV="production"

# CORS
CORS_ORIGINS="https://yourdomain.com"
```

**Frontend** (`/frontend/.env.example`):
```
# API Configuration
REACT_APP_API_URL=https://api.yourdomain.com/api/v1

# Environment
REACT_APP_ENV=production

# Tenant Configuration (if needed)
REACT_APP_TENANT_ID=your-tenant-id
```

## Step 2: Clean Up Development Code

### 2.1 Remove Test Components
Remove development-only components from production build:

1. Remove test buttons and debug components from pages
2. Remove console.log statements from production code
3. Remove hardcoded test data

### 2.2 Update API Configuration
Ensure all API calls use environment variables:

```javascript
// src/services/api.ts
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
```

## Step 3: Database Migration for Production

### 3.1 Create Migration Script
Create a production migration script (`/backend/prisma/migrations/deploy.sh`):

```bash
#!/bin/bash
echo "Running database migrations..."
npx prisma migrate deploy
echo "Migrations complete!"
```

### 3.2 Backup Current Production Database
**CRITICAL**: Always backup before migrating!

```bash
pg_dump -h your-host -U your-user -d your-database > backup-$(date +%Y%m%d-%H%M%S).sql
```

## Step 4: Git Commands

### 4.1 Stage Files
```bash
# Add all modified files
git add .

# Or add specific files
git add backend/src/products/
git add backend/prisma/schema.prisma
git add frontend/src/pages/ProdutosV2Page.tsx
# ... etc
```

### 4.2 Review Changes
```bash
# See what will be committed
git status

# Review specific file changes
git diff --staged
```

### 4.3 Commit Changes
```bash
git commit -m "feat: Add product management system with variations and kits

- Add new product creation with variations (Capsulas, Gotas, Gel, etc)
- Implement kit creation from product variations
- Add automatic SKU generation for kits
- Update database schema with new models
- Create new product management UI
- Add V2 API endpoints for products

Breaking changes:
- Products module now uses V2 controller only
- Database requires migration for new product structure"
```

### 4.4 Push to GitHub
```bash
# Push to main branch
git push origin main

# Or create a feature branch
git checkout -b feature/product-variations
git push origin feature/product-variations
```

## Step 5: Production Deployment Steps

### 5.1 Backend Deployment

1. **Update Environment Variables**
   - Set all production environment variables
   - Update DATABASE_URL for production database
   - Set secure JWT_SECRET

2. **Install Dependencies**
   ```bash
   cd backend
   npm ci --production
   ```

3. **Run Database Migrations**
   ```bash
   npx prisma migrate deploy
   ```

4. **Build Application**
   ```bash
   npm run build
   ```

5. **Start Application**
   ```bash
   npm run start:prod
   ```

### 5.2 Frontend Deployment

1. **Update Environment Variables**
   ```bash
   REACT_APP_API_URL=https://api.yourdomain.com/api/v1
   ```

2. **Install Dependencies**
   ```bash
   cd frontend
   npm ci
   ```

3. **Build for Production**
   ```bash
   npm run build
   ```

4. **Deploy Build Folder**
   - Upload contents of `build/` folder to your web server
   - Or use a service like Vercel, Netlify, or AWS S3

## Step 6: Post-Deployment Checklist

- [ ] Verify database migrations completed successfully
- [ ] Test authentication flow
- [ ] Create first admin user if needed
- [ ] Test product creation with variations
- [ ] Test kit creation
- [ ] Verify all API endpoints are working
- [ ] Check browser console for errors
- [ ] Monitor server logs for issues

## Step 7: Rollback Plan

If something goes wrong:

1. **Database Rollback**
   ```bash
   # Restore from backup
   psql -h your-host -U your-user -d your-database < backup-file.sql
   ```

2. **Code Rollback**
   ```bash
   git revert HEAD
   git push origin main
   ```

## Important Notes

1. **Test in Staging First**: Always deploy to a staging environment first
2. **Database Backup**: Never skip the database backup step
3. **Monitor Logs**: Watch application logs during and after deployment
4. **Health Checks**: Ensure health check endpoints are working
5. **SSL/HTTPS**: Ensure production uses HTTPS
6. **Rate Limiting**: Verify rate limiting is properly configured
7. **CORS**: Update CORS settings for production domain

## Environment-Specific Configurations

### Production Backend (`backend/src/main.ts`)
- Ensure Helmet security headers are enabled
- Set appropriate CORS origins
- Enable rate limiting
- Use production logging levels

### Production Frontend
- Remove React DevTools
- Enable production builds
- Configure proper error boundaries
- Set up monitoring (e.g., Sentry)

## Quick Commands Reference

```bash
# Full deployment from local
git add .
git commit -m "feat: product management system"
git push origin main

# On production server
git pull origin main
cd backend
npm ci
npx prisma migrate deploy
npm run build
pm2 restart sistema-cobranca-backend

cd ../frontend
npm ci
npm run build
# Deploy build folder to web server
```