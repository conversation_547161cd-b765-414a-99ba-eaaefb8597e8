# Deployment Configuration

## Frontend (Vercel)

Set the following environment variable in Vercel dashboard:

```
NEXT_PUBLIC_API_URL = https://zencash-production.up.railway.app/api/v1
```

**Important**: No trailing slash!

## Backend (Railway)

Set the following environment variables in Railway dashboard:

```
CORS_ORIGIN = https://zencash-sand.vercel.app
API_PREFIX = api/v1
DATABASE_URL = [your PostgreSQL connection string]
NODE_ENV = production
```

**Important**: 
- `CORS_ORIGIN` must exactly match your Vercel frontend URL
- Do NOT set `PORT` - Railway provides this automatically

## Testing Connectivity

1. After deploying both services, open browser DevTools
2. Navigate to the Produtos tab
3. Check Network tab - requests should go to:
   ```
   https://zencash-production.up.railway.app/api/v1/products
   ```
4. Response should be 200 OK with JSON data

## Troubleshooting

If you see "Network Error":
1. Check that `NEXT_PUBLIC_API_URL` is set correctly in Vercel
2. Check that `CORS_ORIGIN` matches your Vercel URL exactly
3. Look for CORS errors in browser console
4. Check Railway logs for "Blocked by CORS" messages