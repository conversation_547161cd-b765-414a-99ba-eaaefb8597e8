# Field Mapping Documentation

## Current State: Portuguese Frontend → English Backend

### Core Order Fields
| Portuguese (Frontend)        | English (Backend)           | UI Label (Portuguese)       |
|-----------------------------|-----------------------------|----------------------------|
| idVenda                     | orderNumber                 | ID Venda                   |
| dataVenda                   | createdAt                   | Data Venda                 |
| cliente                     | customerName                | Cliente                    |
| telefone                    | customerPhone               | Telefone                   |
| valorVenda                  | total                       | Valor Venda                |
| valorRecebido               | paymentReceivedAmount       | Valor Recebido             |
| situacaoVenda               | status                      | Situação                   |
| codigoRastreio              | trackingCode                | Código Rastreio            |
| ultimaAtualizacao           | updatedAt                   | Última Atualização         |
| vendedor                    | seller.name                 | Vendedor                   |
| operador                    | collector.name              | Operador                   |
| historico                   | billingHistory (formatted)  | Histórico                  |
| statusCorreios              | trackingStatus              | Status Correios            |
| atualizacaoCorreios         | trackingLastUpdate          | Atualização Correios       |

### Address Fields
| Portuguese (Frontend)        | English (Backend)           | UI Label (Portuguese)       |
|-----------------------------|-----------------------------|----------------------------|
| estadoDestinatario          | state                       | Estado                     |
| cidadeDestinatario          | city                        | Cidade                     |
| ruaDestinatario             | street                      | Rua                        |
| cepDestinatario             | zipCode                     | CEP                        |
| complementoDestinatario     | complement                  | Complemento                |
| bairroDestinatario          | neighborhood                | Bairro                     |
| numeroEnderecoDestinatario  | streetNumber                | Número                     |

### Additional Fields
| Portuguese (Frontend)        | English (Backend)           | UI Label (Portuguese)       |
|-----------------------------|-----------------------------|----------------------------|
| dataRecebimento             | paymentReceivedDate         | Data Recebimento           |
| dataNegociacao              | lastContactDate             | Data Negociação            |
| documentoCliente            | customerCPF                 | CPF                        |
| formaPagamento              | (not in backend)            | Forma Pagamento            |
| oferta                      | (items relation)            | Oferta                     |

## Refactoring Plan

### Phase 1: Update Frontend Types
1. Update Order interface to use English field names
2. Remove duplicate/legacy fields
3. Align exactly with backend model

### Phase 2: Remove Transformations
1. Remove `transformOrderToFrontendFormat` from backend
2. Remove `convertToFrontendOrder` from frontend
3. Update API responses to return raw data

### Phase 3: Update Components
1. Update OrdersTable to use English fields
2. Update column headers to Portuguese labels
3. Update all order detail/edit components
4. Update filters and search logic

### Phase 4: Update Import/Export
1. Update CSV import mapping
2. Update any export functionality

### Phase 5: Testing
1. Test order creation
2. Test order listing/filtering
3. Test order editing
4. Test CSV import
5. Test all user roles