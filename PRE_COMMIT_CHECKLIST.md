# Pre-Commit Checklist

## 1. Remove Development/Test Files

### Files to Remove:
```bash
# Remove test files
rm frontend/test-login.js
rm frontend/get-token.js
rm backend/test-products.js

# Remove development components (optional - they can stay if wrapped in NODE_ENV checks)
# rm frontend/src/utils/setupTestData.ts
# rm frontend/src/components/SetupTestData.tsx
# rm frontend/src/components/AuthDebug.tsx
# rm frontend/src/components/TestProductCreation.tsx
```

## 2. Clean Up Console Logs

### Backend Files to Check:
- `backend/src/products/products-v2.controller.ts` - Remove console.logs
- `backend/src/products/products-v2.service.ts` - Remove debug logs

### Frontend Files to Check:
- `frontend/src/pages/ProdutosV2Page.tsx` - Remove console.logs (lines 197-199)
- `frontend/src/services/ProductServiceV2.ts` - Remove debug logs
- `frontend/src/components/ProductCreationV2Dialog.tsx` - Remove console.logs

## 3. Update Configuration Files

### Backend:
```bash
# Create .env.example if it doesn't exist
cp backend/.env backend/.env.example
# Then edit .env.example to remove actual values
```

### Frontend:
```bash
# Create .env.example
echo "REACT_APP_API_URL=http://localhost:3000/api/v1" > frontend/.env.example
```

## 4. Database Documentation

Create a migration README:
```bash
echo "# Database Migration Guide

## New Models Added:
- ProductVariation
- Kit
- KitItem
- VariationType enum

## Migration Command:
\`\`\`bash
npx prisma migrate deploy
\`\`\`

## Rollback:
Keep a backup before migrating!
" > backend/prisma/migrations/README.md
```

## 5. Quick Cleanup Commands

Run these commands to clean up:

```bash
# Remove console.logs from specific files
sed -i '' '/console\.log/d' backend/src/products/products-v2.controller.ts
sed -i '' '/console\.log/d' frontend/src/pages/ProdutosV2Page.tsx

# Or manually review and remove them
```

## 6. Final Git Commands

```bash
# Check what will be committed
git status

# Stage all changes
git add .

# Unstage test files if accidentally added
git reset HEAD frontend/test-login.js
git reset HEAD frontend/get-token.js
git reset HEAD backend/test-products.js

# Final review
git diff --staged

# Commit
git commit -m "feat: Add product management with variations and kits system"

# Push
git push origin main
```