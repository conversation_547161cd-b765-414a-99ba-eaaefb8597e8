# Sistema de Cobrança - Setup Instructions

## What Has Been Completed

### Backend Improvements
1. ✅ **Logging Infrastructure**: Implemented <PERSON> with daily rotating log files
2. ✅ **Test Suite**: Fixed all failing tests
3. ✅ **Authentication**: JWT-based authentication with proper token handling
4. ✅ **Database**: PostgreSQL with Prisma ORM, migrations, and seed data
5. ✅ **Security**: Rate limiting, helmet.js, CORS, input validation
6. ✅ **Error Handling**: Global exception filter with detailed logging
7. ✅ **Multi-tenancy**: Tenant-based data isolation
8. ✅ **API Structure**: RESTful API with proper routing

### Frontend Updates
1. ✅ **Authentication Service**: Updated to work with backend
2. ✅ **API Configuration**: Proper environment variables
3. ✅ **Type Definitions**: Updated to match backend models
4. ✅ **Order Service**: Adapted to backend API structure
5. ✅ **Sidebar Navigation**: Added Pedidos and Produtos buttons

## Quick Start Guide

### 1. Backend Setup

```bash
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
npx prisma migrate deploy

# Seed the database
npx prisma db seed

# Start the backend
npm run start:dev
```

### 2. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Update .env with the correct tenant ID
echo "REACT_APP_API_URL=http://localhost:3000/api/v1" > .env
echo "REACT_APP_TENANT_ID=cmc4a2wg50000uvkzj76t0mpt" >> .env
echo "REACT_APP_MOCK_API=false" >> .env

# Start the frontend
npm start
```

### 3. Login Credentials

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | admin123 | Admin |
| <EMAIL> | supervisor123 | Supervisor |
| <EMAIL> | vendedor123 | Seller |
| <EMAIL> | cobrador123 | Collector |

### 4. Important Notes

- **Tenant ID**: The system uses multi-tenancy. The default tenant ID is `cmc4a2wg50000uvkzj76t0mpt`
- **API Headers**: All API requests require:
  - `Authorization: Bearer <jwt-token>`
  - `x-tenant-id: <tenant-id>`

## Testing the System

### 1. Test Authentication
```bash
# Login
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: cmc4a2wg50000uvkzj76t0mpt" \
  -d '{"email": "<EMAIL>", "password": "admin123"}'

# Test authenticated endpoint
curl -X GET http://localhost:3000/api/v1/auth/me \
  -H "Authorization: Bearer <token-from-login>" \
  -H "x-tenant-id: cmc4a2wg50000uvkzj76t0mpt"
```

### 2. Access the Application
- Open http://localhost:3001 in your browser
- Login with the credentials above
- Navigate through Dashboard, Pedidos, and Produtos

## Production Deployment

### Backend
1. Build: `npm run build`
2. Start: `npm run start:prod`
3. Use PM2 or Docker for process management
4. Set up PostgreSQL with proper credentials
5. Configure environment variables for production

### Frontend
1. Build: `npm run build`
2. Serve the `build` folder with nginx or similar
3. Update API_URL to point to production backend
4. Configure proper CORS origins in backend

## Troubleshooting

### "Invalid tenant" error
- Ensure the tenant ID in frontend .env matches the database
- Check that x-tenant-id header is being sent

### "Credenciais inválidas" error
- Verify the password is correct (default: admin123)
- Ensure the tenant ID is correct
- Check that the user exists in the database

### Frontend not connecting to backend
- Verify backend is running on port 3000
- Check CORS configuration
- Ensure environment variables are loaded

## Next Steps for Production

1. **Security**
   - Change JWT_SECRET to a strong random value
   - Implement HTTPS
   - Review SECURITY_CHECKLIST.md

2. **Performance**
   - Add Redis for caching
   - Implement database indexes
   - Set up CDN for frontend

3. **Monitoring**
   - Set up APM (Application Performance Monitoring)
   - Configure log aggregation
   - Implement health check monitoring

4. **Testing**
   - Add integration tests
   - Implement E2E tests
   - Set up CI/CD pipeline

## Documentation

- Backend API: http://localhost:3000/docs (Swagger)
- Production Guide: backend/PRODUCTION_README.md
- Security Checklist: backend/SECURITY_CHECKLIST.md

## Support

For issues:
1. Check the logs in `backend/logs/`
2. Verify database connection
3. Ensure all environment variables are set
4. Check the browser console for frontend errors