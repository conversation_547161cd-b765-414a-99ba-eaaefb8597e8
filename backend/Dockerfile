FROM node:20-alpine

WORKDIR /app

# Install dependencies including Python and build tools for native dependencies
RUN apk add --no-cache openssl python3 make g++

# Copy package files
COPY package*.json ./

# Copy prisma directory
COPY prisma ./prisma/

# Install dependencies (including dev dependencies for building)
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma Client
RUN npx prisma generate

# Build the application
RUN npm run build

# The app will use PORT env var from Railway
EXPOSE 3000

# Start the application
CMD ["node", "dist/main.js"]