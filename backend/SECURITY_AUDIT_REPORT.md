# Security Audit Report - ZenCash Backend

## Executive Summary

Comprehensive security audit completed on 2025-08-05 to identify potentially dangerous commands that could cause data loss in production.

### Critical Findings

1. **ALREADY FIXED**: `fix-production-db.sh` previously contained `--force-reset` flag (now removed)
2. **HIGH RISK**: `delete-all-orders.ts` - Script that deletes ALL orders and related data
3. **MEDIUM RISK**: Multiple scripts with DELETE operations and clear migrations
4. **LOW RISK**: Documentation files containing dangerous commands (not executable)

## Detailed Findings

### 1. Database Reset Commands

#### Fixed Issues
- **fix-production-db.sh** (line 21):
  - Previously: `npx prisma db push --force-reset --skip-generate`
  - Fixed to: `npx prisma db push --skip-generate`
  - Status: ✅ FIXED

#### Documentation References (Safe - Not Executable)
- `PRODUCTION_DATABASE_RECOVERY.md`: Contains `prisma db push --force-reset` examples
- `DEPLOYMENT.md`: References `prisma migrate reset --force`
- `README.md`: References `prisma migrate reset`

### 2. DROP Commands

#### Commented/Safe
- **restore-database.sh** (line 79):
  ```bash
  # PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
  ```
  - Status: ✅ SAFE (commented out)

#### Documentation Only
- `PHASE8-DEPLOYMENT-STRATEGY.md`: Contains DROP TABLE examples (documentation only)

### 3. DELETE Operations

#### HIGH RISK - Requires Immediate Attention
- **delete-all-orders.ts**:
  - Deletes ALL orders, order items, audit logs, status history, tracking, commissions, and addresses
  - No safeguards or confirmation prompts
  - Uses `deleteMany({})` which deletes all records
  - **Recommendation**: Add confirmation prompt, environment check, or remove entirely

#### Medium Risk
- **clear_migrations.ts**: Deletes all Prisma migration history
- **fix-migration.ts**: Deletes specific migrations
- **fix-order-id-final.ts**: Deletes specific orders by ID
- **fix-order-ids-advanced.ts**: Deletes specific orders by ID

### 4. File System Operations

#### Safe Operations
- `prepare-for-railway.sh`: `rm -rf src/monitoring` (removes only monitoring module)
- Various build scripts: Remove only build directories
- Docker cleanup: Standard apt cache cleanup

### 5. Database State Scripts (Safe)

These scripts only read data:
- `check-db-state.js`: Shows table counts
- `restore-railway-backup.sh`: Guide for Railway backup restoration
- `seed-*.ts/js`: Add initial data

## Recommendations

### Immediate Actions

1. **Remove or Secure delete-all-orders.ts**:
   ```bash
   # Option 1: Remove the script
   rm /Users/<USER>/zencash/backend/scripts/delete-all-orders.ts
   
   # Option 2: Rename to prevent accidental execution
   mv /Users/<USER>/zencash/backend/scripts/delete-all-orders.ts \
      /Users/<USER>/zencash/backend/scripts/DANGEROUS-delete-all-orders.ts.disabled
   ```

2. **Add Production Safeguards** to any deletion scripts:
   ```typescript
   if (process.env.NODE_ENV === 'production') {
     console.error('❌ This script cannot run in production!');
     process.exit(1);
   }
   ```

3. **Create Safe Alternatives** for common operations

### Best Practices for Production

1. **Never use** in production scripts:
   - `--force-reset` flag with Prisma
   - `deleteMany({})` without WHERE clause
   - `DROP SCHEMA/TABLE/DATABASE` commands
   - `TRUNCATE` commands

2. **Always include** in dangerous scripts:
   - Environment checks
   - Confirmation prompts
   - Backup creation before operations
   - Transaction wrapping for rollback capability

3. **Script Naming Convention**:
   - Prefix dangerous scripts with `DANGEROUS-`
   - Add `.disabled` extension to prevent execution
   - Keep in separate `dangerous-scripts/` directory

### Monitoring Recommendations

1. Set up alerts for:
   - Mass deletion operations
   - Schema changes
   - Migration resets

2. Enable database audit logging

3. Implement regular automated backups

## Conclusion

The system is largely secure with only one critical script (`delete-all-orders.ts`) requiring immediate attention. The previously dangerous `--force-reset` flag has already been removed from production scripts.

The restore functionality using Railway backups has been tested and verified to work correctly, providing a safety net for data recovery.

## Verification Commands

To verify no dangerous commands exist:

```bash
# Check for force-reset
grep -r "force-reset" --include="*.sh" --include="*.ts" --include="*.js" .

# Check for DROP commands
grep -r "DROP\s\+\(TABLE\|DATABASE\|SCHEMA\)" --include="*.sh" --include="*.ts" --include="*.js" .

# Check for deleteMany without conditions
grep -r "deleteMany\s*(\s*{\s*}\s*)" --include="*.ts" --include="*.js" .

# Check for TRUNCATE
grep -r "TRUNCATE" --include="*.sh" --include="*.ts" --include="*.js" .
```