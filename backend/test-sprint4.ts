import axios from 'axios';

const API_URL = 'http://localhost:3000';
let authToken = '';

async function login() {
  try {
    const response = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin@123',
    });
    authToken = response.data.access_token;
    console.log('✅ Login realizado com sucesso');
  } catch (error: any) {
    console.error('❌ Erro no login:', error.response?.data || error.message);
  }
}

async function testProducts() {
  console.log('\n🧪 Testando rotas de Produtos...');
  
  try {
    // Listar produtos
    const products = await axios.get(`${API_URL}/products`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log(`✅ Produtos encontrados: ${products.data.length}`);
    
    // Buscar produtos com baixo estoque
    const lowStock = await axios.get(`${API_URL}/products/low-stock`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log(`✅ Variações com baixo estoque: ${lowStock.data.length}`);
    
    // Verificar estoque de uma variação específica
    if (products.data[0]?.variations?.[0]) {
      const variationId = products.data[0].variations[0].id;
      const stock = await axios.get(`${API_URL}/products/variations/${variationId}/stock`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });
      console.log(`✅ Estoque verificado:`, stock.data);
    }
  } catch (error: any) {
    console.error('❌ Erro nos testes de produtos:', error.response?.data || error.message);
  }
}

async function testKits() {
  console.log('\n🧪 Testando rotas de Kits...');
  
  try {
    // Listar kits
    const kits = await axios.get(`${API_URL}/kits`, {
      headers: { Authorization: `Bearer ${authToken}` },
    });
    console.log(`✅ Kits encontrados: ${kits.data.length}`);
    
    // Verificar disponibilidade de um kit
    if (kits.data[0]) {
      const kitId = kits.data[0].id;
      const availability = await axios.get(`${API_URL}/kits/${kitId}/availability?quantity=2`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });
      console.log(`✅ Disponibilidade do kit "${kits.data[0].name}":`, availability.data);
    }
  } catch (error: any) {
    console.error('❌ Erro nos testes de kits:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 Iniciando testes da Sprint 4...\n');
  
  await login();
  await testProducts();
  await testKits();
  
  console.log('\n✅ Testes concluídos!');
}

runTests().catch(console.error);