#!/bin/bash

echo "🔧 Building Docker image..."
docker build -t zencash-backend . || { echo "❌ Build failed"; exit 1; }

echo "🚀 Starting container with Railway-style environment..."
docker run -d --name zencash-test \
  -e PORT=3000 \
  -e API_PREFIX=api/v1 \
  -e DATABASE_URL="postgresql://test:test@localhost:5432/test" \
  -e CORS_ORIGIN="https://zencash-sand.vercel.app" \
  -e NODE_ENV=production \
  -p 3000:3000 \
  zencash-backend

echo "⏳ Waiting for container to start (10 seconds)..."
sleep 10

echo "📋 Container logs:"
echo "========================================="
docker logs zencash-test
echo "========================================="

echo -e "\n🏥 Testing health endpoints:"

# Function to test endpoint
test_endpoint() {
  local url=$1
  local name=$2
  echo -n "Testing $name... "
  
  response=$(curl -s -o /dev/null -w "%{http_code}" $url)
  if [ "$response" = "200" ]; then
    echo "✅ 200 OK"
    curl -s $url | head -1
  else
    echo "❌ FAILED (HTTP $response)"
    return 1
  fi
}

# Test all health endpoints
test_endpoint "http://localhost:3000/health" "/health"
test_endpoint "http://localhost:3000/ping" "/ping"
test_endpoint "http://localhost:3000/api/v1/health" "/api/v1/health"
test_endpoint "http://localhost:3000/api/v1/health/ready" "/api/v1/health/ready"
test_endpoint "http://localhost:3000/api/v1/health/live" "/api/v1/health/live"

# Summary
echo -e "\n📊 Health Check Summary:"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/v1/health | grep -q "^200$"; then
  echo "✅ Railway health endpoint (/api/v1/health) is working!"
else
  echo "❌ Railway health endpoint (/api/v1/health) FAILED!"
  echo "Container is still running. Check logs with: docker logs zencash-test"
  exit 1
fi

echo -e "\n🧹 Cleaning up..."
docker stop zencash-test > /dev/null 2>&1
docker rm zencash-test > /dev/null 2>&1

echo "✅ Test complete!"