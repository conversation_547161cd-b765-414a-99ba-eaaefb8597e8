# Railway Environment Variables Setup

## DO NOT SET PORT!
Railway automatically provides the PORT variable. Do not set it manually.

## Variables to Set in Railway Dashboard:

Copy and paste these exactly (one per line in Railway):

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=zencash-super-secret-jwt-key-2024
API_PREFIX=api/v1
NODE_ENV=production
CORS_ORIGIN=https://zencash-sand.vercel.app
```

## How to Add Variables:
1. Go to your Railway service
2. Click "Variables" tab
3. Click "Raw Editor"
4. Paste all variables at once
5. Click "Save"

## Variables NOT to Set:
- PORT (Railway provides this automatically)
- Any variable starting with RAILWAY_

## After Setting Variables:
1. Railway will automatically redeploy
2. Check logs for any errors
3. Once deployed, test the health endpoint

## Testing After Deploy:
```bash
# Get your Railway URL from the dashboard
curl https://[your-app].railway.app/api/v1/health
```

This should return JSO<PERSON> with status information.