const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createTestUsers() {
  try {
    const password = await bcrypt.hash('senha123', 10);
    
    // Create or update test users
    const vendedor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { password },
      create: {
        email: '<EMAIL>',
        name: 'Vended<PERSON> Teste',
        password,
        role: 'VENDEDOR',
        active: true
      }
    });
    
    const cobrador = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { password },
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON> Teste',
        password,
        role: 'COBRADOR',
        active: true
      }
    });
    
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { password },
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON>e',
        password,
        role: 'ADMIN',
        active: true
      }
    });
    
    console.log('Usuários criados:');
    console.log('- Vendedor:', vendedor.id, vendedor.email);
    console.log('- Cobrador:', cobrador.id, cobrador.email);
    console.log('- Admin:', admin.id, admin.email);
    console.log('\nSenha para todos: senha123');
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();