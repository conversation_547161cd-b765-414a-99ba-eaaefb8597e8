#!/bin/bash

# Base URL
BASE_URL="http://localhost:3003/api/v1"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color
BLUE='\033[0;34m'

echo -e "${BLUE}=== Testing ZenCash Commission System ===${NC}\n"

# 1. <PERSON><PERSON> as supervisor
echo -e "${GREEN}1. <PERSON><PERSON> as supervisor${NC}"
RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "Supervisor@123"}')
  
echo "Login response: $RESPONSE"
SUPERVISOR_TOKEN=$(echo $RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$SUPERVISOR_TOKEN" ]; then
  echo -e "${RED}Failed to login as supervisor${NC}"
  exit 1
fi
echo "Supervisor token obtained"

# 2. Get all commission settings
echo -e "\n${GREEN}2. Get all commission settings${NC}"
curl -s -X GET "$BASE_URL/commission/settings" \
  -H "Authorization: Bearer $SUPERVISOR_TOKEN" | jq

# 3. Get pending approvals
echo -e "\n${GREEN}3. Get orders pending approval${NC}"
curl -s -X GET "$BASE_URL/commission/pending-approvals" \
  -H "Authorization: Bearer $SUPERVISOR_TOKEN" | jq

# 4. Approve payment for the first order
echo -e "\n${GREEN}4. Approve payment for COMPLETO order${NC}"
ORDER_ID="372d6d35-194b-4cc4-b89d-26afdba7f31a"
curl -s -X POST "$BASE_URL/commission/approve/$ORDER_ID" \
  -H "Authorization: Bearer $SUPERVISOR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approved": true,
    "paymentAmount": 1500.00,
    "rejectionReason": null
  }' | jq

# 5. Get commission report
echo -e "\n${GREEN}5. Get commission report${NC}"
curl -s -X GET "$BASE_URL/commission/report" \
  -H "Authorization: Bearer $SUPERVISOR_TOKEN" | jq

# 6. Login as seller to check their commission
echo -e "\n${GREEN}6. Login as seller${NC}"
SELLER_TOKEN=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "Vendedor1@123"}' | \
  jq -r '.access_token')

echo -e "\n${GREEN}7. Get seller's commission report${NC}"
curl -s -X GET "$BASE_URL/commission/report" \
  -H "Authorization: Bearer $SELLER_TOKEN" | jq

echo -e "\n${BLUE}=== Test completed ===${NC}"