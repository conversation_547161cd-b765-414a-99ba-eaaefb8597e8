import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function testLogin() {
  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('User found:', {
      email: user.email,
      name: user.name,
      role: user.role,
      active: user.active
    });

    // Test password
    const plainPassword = 'senha123';
    const isValid = await bcrypt.compare(plainPassword, user.password);
    console.log('Password valid:', isValid);

    // Test with new hash
    const newHash = await bcrypt.hash(plainPassword, 10);
    const isValidNewHash = await bcrypt.compare(plainPassword, newHash);
    console.log('New hash test valid:', isValidNewHash);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin();