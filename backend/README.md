# ZenCash Backend

Sistema completo de gestão de vendas e cobranças com rastreamento de entregas.

## 🚀 Visão Geral

ZenCash é uma plataforma B2B desenvolvida em NestJS + Prisma + PostgreSQL que oferece:

- **Gestão de Vendas**: Controle completo de pedidos, produtos e kits
- **Sistema de Cobrança**: Fluxo de cobrança com diferentes status e atribuições
- **Rastreamento**: Integração com Correios para rastreamento de entregas
- **Notificações**: Sistema de notificações via WhatsApp e Email
- **Relatórios**: Dashboards e relatórios operacionais
- **Multi-role**: Sistema de permissões baseado em roles (Admin, Supervisor, Vendedor, Cobrador)

## 📋 Pré-requisitos

- Node.js 18+ 
- PostgreSQL 14+
- Redis (opcional, para filas)
- npm ou yarn

## 🛠️ Instalação

### 1. Clone o repositório
```bash
git clone https://github.com/your-org/zencash-backend.git
cd zencash-backend
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/zencash"

# JWT
JWT_SECRET="sua-chave-secreta-aqui"
JWT_EXPIRES_IN="7d"

# Encryption (32 caracteres)
ENCRYPTION_KEY="sua-chave-de-32-caracteres-aqui!"

# Application
PORT=3000
NODE_ENV=development

# Rate Limiting
THROTTLE_TTL=60000
THROTTLE_LIMIT=100
```

### 4. Configure o banco de dados
```bash
# Criar as tabelas
npx prisma migrate deploy

# Gerar o cliente Prisma
npx prisma generate

# Popular com dados iniciais
npm run seed
```

## 🎮 Executando o Projeto

### Desenvolvimento
```bash
npm run start:dev
```

### Produção
```bash
npm run build
npm run start:prod
```

### Testes
```bash
# Testes unitários
npm test

# Testes E2E
npm run test:e2e

# Coverage
npm run test:cov
```

## 📚 Documentação da API

Com o servidor rodando, acesse:
- Swagger: http://localhost:3000/docs
- Health Check: http://localhost:3000/health

## 🏗️ Estrutura do Projeto

```
src/
├── auth/               # Autenticação JWT e guards
├── users/              # Gerenciamento de usuários
├── orders/             # Gestão de pedidos
├── products/           # Produtos e variações
├── kits/               # Kits de produtos
├── customers/          # Gestão de clientes
├── tracking/           # Rastreamento Correios
├── notifications/      # Sistema de notificações
├── reports/            # Relatórios e dashboards
├── configuration/      # Configurações do sistema
├── common/             # Utilitários compartilhados
│   ├── decorators/     # Decorators customizados
│   ├── filters/        # Exception filters
│   ├── guards/         # Guards de autenticação
│   └── interceptors/   # Interceptors globais
├── prisma/             # Schema e migrations
└── test/               # Testes E2E
```

## 🔐 Autenticação e Autorização

### Roles disponíveis:
- **ADMIN**: Acesso total ao sistema
- **SUPERVISOR**: Gerencia equipes e visualiza relatórios
- **VENDEDOR**: Cria pedidos e gerencia clientes
- **COBRADOR**: Atualiza status de pedidos e faz cobranças

### Login
```bash
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "Admin@123"
}
```

### Usando o token
```bash
Authorization: Bearer {token}
```

## 📦 Principais Módulos

### 1. Orders (Pedidos)
- Criação e gestão de pedidos
- Fluxo de status configurável
- Histórico de alterações
- Atribuição automática vendedor/cobrador

### 2. Tracking (Rastreamento)
- Integração com API dos Correios
- Alertas automáticos
- Sincronização periódica
- Webhook para atualizações

### 3. Notifications (Notificações)
- Templates configuráveis
- Fila de processamento
- Multi-canal (WhatsApp, Email, SMS)
- Retry automático

### 4. Reports (Relatórios)
- Dashboard em tempo real
- Filtros dinâmicos
- Exportação de dados
- Métricas de performance

### 5. Configuration (Configurações)
- Gerenciamento centralizado
- Criptografia de dados sensíveis
- Cache em memória
- Auditoria de alterações

## 🔧 Scripts Úteis

### Banco de Dados
```bash
# Criar migration
npm run db:migrate

# Aplicar migrations
npx prisma migrate deploy

# Abrir Prisma Studio
npm run db:studio

# Backup do banco
./scripts/backup-database.sh

# Restore do banco
./scripts/restore-database.sh backup.sql.gz
```

### Seeds
```bash
# Seed completo (produção)
npm run seed

# Seed simples (desenvolvimento)
npm run db:seed-simple
```

## 🚀 Deploy

### Railway
O projeto está configurado para deploy no Railway:

1. Conecte seu repositório
2. Configure as variáveis de ambiente
3. O deploy será automático via `railway.json`

### Docker
```bash
docker build -t zencash-backend .
docker run -p 3000:3000 --env-file .env zencash-backend
```

## 📊 Monitoramento

### Logs
- Winston para logging estruturado
- Rotação diária de logs
- Níveis: error, warn, info, debug

### Métricas
- Health check: `/health`
- Detailed health: `/health/detailed`
- Ready probe: `/health/ready`
- Live probe: `/health/live`

### APM (Application Performance Monitoring)
Configure no `.env`:
```env
SENTRY_DSN=seu-dsn-aqui
NEW_RELIC_LICENSE_KEY=sua-chave-aqui
```

## 🔒 Segurança

### Implementações:
- Helmet.js para headers de segurança
- CORS configurável
- Rate limiting global
- Validação de entrada com class-validator
- Criptografia de dados sensíveis
- Sanitização de logs

### Boas práticas:
- Nunca commitar `.env`
- Usar senhas fortes
- Rotacionar JWT secrets
- Manter dependências atualizadas
- Fazer backup regular do banco

## 🤝 Contribuindo

1. Crie uma branch para sua feature
2. Commit suas mudanças
3. Push para a branch
4. Abra um Pull Request

### Padrões de código:
- ESLint + Prettier configurados
- Husky para pre-commit hooks
- Conventional commits

## 📝 Credenciais Padrão (Desenvolvimento)

Após executar `npm run seed`:

- **Admin**: <EMAIL> / Admin@123
- **Supervisor**: <EMAIL> / Supervisor@123
- **Vendedor 1**: <EMAIL> / Vendedor1@123
- **Cobrador 1**: <EMAIL> / Cobrador1@123

## 🆘 Troubleshooting

### Erro de conexão com banco
- Verifique se o PostgreSQL está rodando
- Confirme as credenciais no `.env`
- Teste a conexão: `npx prisma db pull`

### Erro de migrations
- Execute: `npx prisma migrate reset`
- Reaplique: `npx prisma migrate deploy`

### Erro de autenticação
- Verifique o JWT_SECRET
- Confirme que o token não expirou
- Valide o formato: `Bearer {token}`

## 📞 Suporte

- Email: <EMAIL>
- Docs: https://docs.zencash.com
- Issues: https://github.com/your-org/zencash-backend/issues

## 📄 Licença

Este projeto é proprietário e confidencial.

---

Desenvolvido com ❤️ pela equipe ZenCash
