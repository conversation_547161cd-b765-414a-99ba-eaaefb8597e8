#!/bin/bash

echo "🔐 Generating security keys for ZenCash..."
echo ""

# Generate ENCRYPTION_KEY (32 characters)
echo "ENCRYPTION_KEY (32 characters for AES-256):"
ENCRYPTION_KEY=$(openssl rand -base64 24 | tr -d '\n' | cut -c1-32)
echo "ENCRYPTION_KEY=$ENCRYPTION_KEY"
echo ""

# Generate SIGNING_KEY (64 characters)
echo "SIGNING_KEY (for audit log signatures):"
SIGNING_KEY=$(openssl rand -base64 48 | tr -d '\n')
echo "SIGNING_KEY=$SIGNING_KEY"
echo ""

# Generate JWT_SECRET (32 characters)
echo "JWT_SECRET (for authentication tokens):"
JWT_SECRET=$(openssl rand -base64 24 | tr -d '\n' | cut -c1-32)
echo "JWT_SECRET=$JWT_SECRET"
echo ""

echo "📋 Copy these to your Railway environment variables!"
echo ""
echo "⚠️  IMPORTANT:"
echo "1. Save these keys securely - you'll need them for backups"
echo "2. Use the SAME keys across all deployments"
echo "3. NEVER commit these to Git"
echo "4. If you change ENCRYPTION_KEY, you won't be able to decrypt old data"