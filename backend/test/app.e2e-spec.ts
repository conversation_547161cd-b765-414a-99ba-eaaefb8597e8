import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

describe('ZenCash E2E Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let adminToken: string;
  let vendedorToken: string;
  let cobradorToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));
    
    await app.init();
    
    prisma = app.get<PrismaService>(PrismaService);
    
    // Limpar dados de teste
    await prisma.notificationJob.deleteMany();
    await prisma.orderItem.deleteMany();
    await prisma.orderStatusHistory.deleteMany();
    await prisma.tracking.deleteMany();
    await prisma.order.deleteMany();
    await prisma.address.deleteMany();
    await prisma.customer.deleteMany();
    await prisma.inventory.deleteMany();
    await prisma.kitItem.deleteMany();
    await prisma.productVariation.deleteMany();
    await prisma.product.deleteMany();
    await prisma.kit.deleteMany();
    await prisma.user.deleteMany();
    
    // Criar usuários de teste
    const hashedPassword = await bcrypt.hash('Test@123', 10);
    
    await prisma.user.createMany({
      data: [
        {
          name: 'Admin Test',
          email: '<EMAIL>',
          password: hashedPassword,
          role: Role.ADMIN,
          active: true,
        },
        {
          name: 'Vendedor Test',
          email: '<EMAIL>',
          password: hashedPassword,
          role: Role.VENDEDOR,
          active: true,
        },
        {
          name: 'Cobrador Test',
          email: '<EMAIL>',
          password: hashedPassword,
          role: Role.COBRADOR,
          active: true,
        },
      ],
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication', () => {
    it('/auth/login (POST) - should login admin user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test@123',
        })
        .expect(201);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.role).toBe('ADMIN');
      
      adminToken = response.body.access_token;
    });

    it('/auth/login (POST) - should login vendedor user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test@123',
        })
        .expect(201);

      expect(response.body.user.role).toBe('VENDEDOR');
      vendedorToken = response.body.access_token;
    });

    it('/auth/login (POST) - should login cobrador user', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'Test@123',
        })
        .expect(201);

      expect(response.body.user.role).toBe('COBRADOR');
      cobradorToken = response.body.access_token;
    });

    it('/auth/login (POST) - should fail with invalid credentials', async () => {
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword',
        })
        .expect(401);
    });

    it('/auth/me (GET) - should return current user', async () => {
      const response = await request(app.getHttpServer())
        .get('/auth/me')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.role).toBe('ADMIN');
    });

    it('/auth/me (GET) - should fail without token', async () => {
      await request(app.getHttpServer())
        .get('/auth/me')
        .expect(401);
    });
  });

  describe('Products Management', () => {
    let productId: string;
    let variationId: string;

    it('/products (POST) - should create product as admin', async () => {
      const response = await request(app.getHttpServer())
        .post('/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test Product',
          description: 'Test Description',
          variations: [
            {
              variation: 'Size M',
              price: 99.90,
              sku: 'TEST-M-001',
            },
          ],
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('Test Product');
      expect(response.body.variations).toHaveLength(1);
      
      productId = response.body.id;
      variationId = response.body.variations[0].id;
    });

    it('/products (POST) - should fail as vendedor', async () => {
      await request(app.getHttpServer())
        .post('/products')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .send({
          name: 'Another Product',
          description: 'Test',
        })
        .expect(403);
    });

    it('/products (GET) - should list products', async () => {
      const response = await request(app.getHttpServer())
        .get('/products')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
      expect(response.body).toHaveProperty('total');
    });
  });

  describe('Customer Management', () => {
    let customerId: string;

    it('/customers (POST) - should create customer', async () => {
      const response = await request(app.getHttpServer())
        .post('/customers')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test Customer',
          cpf: '12345678901',
          phone: '11999999999',
          email: '<EMAIL>',
          addresses: [
            {
              cep: '01310-100',
              street: 'Av. Paulista',
              number: '1000',
              neighborhood: 'Bela Vista',
              city: 'São Paulo',
              state: 'SP',
              main: true,
            },
          ],
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      customerId = response.body.id;
    });

    it('/customers/:id (GET) - should get customer by id', async () => {
      const response = await request(app.getHttpServer())
        .get(`/customers/${customerId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.id).toBe(customerId);
      expect(response.body.addresses).toHaveLength(1);
    });
  });

  describe('Orders Management', () => {
    let orderId: string;
    let customerId: string;
    let sellerId: string;
    let collectorId: string;
    let productVariationId: string;

    beforeAll(async () => {
      // Criar dados necessários
      const customer = await prisma.customer.create({
        data: {
          name: 'Order Test Customer',
          cpf: '98765432100',
          phone: '11888888888',
        },
      });
      customerId = customer.id;

      const seller = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      sellerId = seller.id;

      const collector = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      collectorId = collector.id;

      const product = await prisma.product.create({
        data: {
          name: 'Order Test Product',
          variations: {
            create: {
              variation: 'Default',
              price: 50.00,
              sku: 'ORDER-TEST-001',
            },
          },
        },
        include: { variations: true },
      });
      productVariationId = product.variations[0].id;
    });

    it('/orders (POST) - should create order as vendedor', async () => {
      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .send({
          customerId,
          items: [
            {
              productId: productVariationId,
              productName: 'Order Test Product',
              quantity: 2,
              unitPrice: 50.00,
            },
          ],
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.total).toBe('100');
      expect(response.body.status).toBe('PENDENTE');
      expect(response.body.sellerId).toBe(sellerId);
      
      orderId = response.body.id;
    });

    it('/orders/:id/status (PATCH) - should update order status as cobrador', async () => {
      const response = await request(app.getHttpServer())
        .patch(`/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${cobradorToken}`)
        .send({
          status: 'SEPARACAO',
        })
        .expect(200);

      expect(response.body.status).toBe('SEPARACAO');
      expect(response.body.collectorId).toBe(collectorId);
    });

    it('/orders/:id/status (PATCH) - vendedor should not update to ENVIADO', async () => {
      await request(app.getHttpServer())
        .patch(`/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${vendedorToken}`)
        .send({
          status: 'ENVIADO',
        })
        .expect(403);
    });

    it('/orders (GET) - vendedor should see only their orders', async () => {
      const response = await request(app.getHttpServer())
        .get('/orders')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      response.body.data.forEach(order => {
        expect(order.sellerId).toBe(sellerId);
      });
    });
  });

  describe('Tracking Management', () => {
    let orderId: string;
    let trackingCode: string;

    beforeAll(async () => {
      // Criar pedido com status ENVIADO
      const seller = await prisma.user.findUnique({
        where: { email: '<EMAIL>' },
      });
      
      const order = await prisma.order.create({
        data: {
          customerName: 'Tracking Test',
          customerPhone: '11777777777',
          total: 100,
          status: 'ENVIADO',
          sellerId: seller.id,
        },
      });
      orderId = order.id;
      trackingCode = `BR${Date.now()}TEST`;
    });

    it('/tracking (POST) - should create tracking as admin', async () => {
      const response = await request(app.getHttpServer())
        .post('/tracking')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          orderId,
          code: trackingCode,
        })
        .expect(201);

      expect(response.body.code).toBe(trackingCode);
      expect(response.body.orderId).toBe(orderId);
    });

    it('/tracking/:code (GET) - should get tracking by code', async () => {
      const response = await request(app.getHttpServer())
        .get(`/tracking/${trackingCode}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(trackingCode);
    });
  });

  describe('Reports', () => {
    it('/reports/summary (GET) - admin should access all data', async () => {
      const response = await request(app.getHttpServer())
        .get('/reports/summary')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('totalOrders');
      expect(response.body).toHaveProperty('totalRevenue');
      expect(response.body).toHaveProperty('totalDelivered');
    });

    it('/reports/summary (GET) - vendedor should see only their data', async () => {
      const response = await request(app.getHttpServer())
        .get('/reports/summary')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('totalOrders');
    });

    it('/reports/alerts (GET) - should be restricted to admin/supervisor', async () => {
      await request(app.getHttpServer())
        .get('/reports/alerts')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .expect(403);
    });
  });

  describe('Configuration', () => {
    it('/config (POST) - should create config as admin', async () => {
      const response = await request(app.getHttpServer())
        .post('/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          key: 'test_config',
          value: { enabled: true },
        })
        .expect(201);

      expect(response.body.key).toBe('test_config');
    });

    it('/config/:key (GET) - should get config', async () => {
      const response = await request(app.getHttpServer())
        .get('/config/test_config')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.value).toEqual({ enabled: true });
    });

    it('/config (POST) - vendedor should not create config', async () => {
      await request(app.getHttpServer())
        .post('/config')
        .set('Authorization', `Bearer ${vendedorToken}`)
        .send({
          key: 'another_config',
          value: { data: 'test' },
        })
        .expect(403);
    });
  });
});
