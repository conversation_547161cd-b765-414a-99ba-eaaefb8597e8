# Railway Deployment - Step by Step

## The Correct Way to Link Your Project

### Option 1: Interactive Mode (Recommended)
```bash
# Just run railway link without any arguments
railway link
```
Then:
1. Select your project from the list
2. Choose the environment (usually "production")

### Option 2: Using Project/Environment IDs
If you know your project ID and environment:
```bash
# First, set the project and environment
railway link
# Then select from the interactive menu
```

### Option 3: Direct Environment Variable Setup
If linking isn't working, you can set variables directly in Railway Dashboard:

1. Go to https://railway.app/dashboard
2. Click on your project
3. Click on the "Variables" tab
4. Add these variables:

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=your-super-secret-jwt-key-here
API_PREFIX=api/v1
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://zencash-sand.vercel.app
```

## Alternative: Deploy via GitHub

### 1. Push your code to GitHub
```bash
git add .
git commit -m "Ready for Railway deployment"
git push origin main
```

### 2. In Railway Dashboard:
1. Click "New Project"
2. Choose "Deploy from GitHub repo"
3. Select your repository
4. Choose the `/backend` directory as the root directory
5. Railway will automatically detect it's a Node.js app

### 3. Configure Service Settings:
- Root Directory: `/backend`
- Build Command: `npm install && npx prisma generate && npm run build`
- Start Command: `npx prisma migrate deploy && npm run start:prod`

### 4. Add Environment Variables in Railway Dashboard

### 5. Connect to Existing Database:
1. In your Railway project, click "New"
2. Choose "Database"
3. Select "Add Existing Database"
4. Choose your PostgreSQL database

## Quick Deploy Commands (After Linking)

```bash
# Check if you're linked correctly
railway status

# Deploy
railway up

# View logs
railway logs

# Open your app
railway open
```

## Getting Your Deployed URL

After deployment:
```bash
railway open
```

Or check in Railway Dashboard - it will show something like:
`https://zencash-backend-production.up.railway.app`

## Verify Deployment

Test your health endpoint:
```bash
curl https://your-app.railway.app/api/v1/health
```

Expected response:
```json
{
  "status": "ok",
  "database": "connected",
  "timestamp": "2024-...",
  "environment": "production"
}
```