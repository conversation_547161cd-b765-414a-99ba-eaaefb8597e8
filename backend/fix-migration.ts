import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixMigration() {
  try {
    console.log('🔧 Checking migration status...\n');

    // Check if the failed migration exists
    const migrations = await prisma.$queryRaw`
      SELECT * FROM "_prisma_migrations" 
      WHERE migration_name LIKE '%update_order_status%'
    `;
    
    console.log('Migrations found:', migrations);

    // Remove failed migration if exists
    const result = await prisma.$executeRaw`
      DELETE FROM "_prisma_migrations" 
      WHERE migration_name LIKE '%update_order_status%' 
      AND finished_at IS NULL
    `;

    console.log(`Removed ${result} failed migration(s)`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixMigration();