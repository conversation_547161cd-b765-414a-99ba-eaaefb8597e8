#!/bin/bash

echo "Preparing backend for Railway deployment..."

# 1. Remove monitoring module entirely
echo "Removing monitoring module..."
rm -rf src/monitoring
sed -i '' '/MonitoringModule/d' src/app.module.ts
sed -i '' '/import.*monitoring/d' src/app.module.ts

# 2. Remove telemetry interceptor
echo "Removing telemetry interceptor..."
rm -f src/common/interceptors/telemetry.interceptor.ts
sed -i '' '/TelemetryInterceptor/d' src/app.module.ts
sed -i '' '/APP_INTERCEPTOR/d' src/app.module.ts
sed -i '' '/telemetry.interceptor/d' src/app.module.ts

# 3. Simplify logger service
echo "Simplifying logger service..."
cat > src/common/services/logger.service.ts << 'EOF'
import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';

@Injectable()
export class LoggerService implements NestLoggerService {
  log(message: string, context?: string) {
    console.log(`[${context || 'Application'}] ${message}`);
  }

  error(message: string, trace?: string, context?: string) {
    console.error(`[${context || 'Application'}] ${message}`, trace);
  }

  warn(message: string, context?: string) {
    console.warn(`[${context || 'Application'}] ${message}`);
  }

  debug(message: string, context?: string) {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[${context || 'Application'}] ${message}`);
    }
  }

  verbose(message: string, context?: string) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[${context || 'Application'}] ${message}`);
    }
  }

  // Anti-fraud specific logging methods
  logDuplicateDetection(data: any) {
    this.log(`Duplicate detection: ${JSON.stringify(data)}`, 'AntifraudService');
  }

  logAddressParsing(data: any) {
    this.debug(`Address parsing: ${JSON.stringify(data)}`, 'AddressParser');
  }

  logPhoneticEncoding(data: any) {
    this.debug(`Phonetic encoding: ${JSON.stringify(data)}`, 'PhoneticEncoder');
  }

  logSecurityEvent(event: any) {
    this.warn(`Security event: ${JSON.stringify(event)}`, 'Security');
  }
}
EOF

# 4. Fix duplicate detection service
echo "Fixing duplicate detection service..."
# Fix the array initialization and type issues
sed -i '' 's/const matches: DuplicateMatch\[\] = \[\]/const matches: any[] = []/' src/antifraud/services/duplicate-detection.service.ts
sed -i '' 's/customerName: order.customerName || "Unknown", totalAmount: Number(order.total || 0)} as DuplicateMatch);/customerName: order.customerName || "Unknown", totalAmount: Number(order.total || 0)});/' src/antifraud/services/duplicate-detection.service.ts

# 5. Fix kits service
echo "Fixing kits service..."
# Fix the create method to include tenantId
sed -i '' '/data: {$/a\
        tenantId,' src/kits/kits.service.ts

# 6. Fix controllers to match updated service methods
echo "Fixing controllers..."
# Fix addresses controller
sed -i '' 's/return this.addressesService.findAll(customerId, tenantId);/return this.addressesService.findAll(customerId);/' src/addresses/addresses.controller.ts

# Fix kits controller
sed -i '' 's/return this.kitsService.create(createKitDto, tenantId);/return this.kitsService.create(createKitDto);/' src/kits/kits.controller.ts
sed -i '' 's/return this.kitsService.findAll(tenantId, active);/return this.kitsService.findAll(active);/' src/kits/kits.controller.ts
sed -i '' 's/return this.kitsService.update(id, updateKitDto, tenantId);/return this.kitsService.update(id, tenantId, updateKitDto);/' src/kits/kits.controller.ts
sed -i '' 's/return this.kitsService.remove(id, tenantId);/return this.kitsService.remove(id, tenantId);/' src/kits/kits.controller.ts
sed -i '' 's/return this.kitsService.addItems(kitId, items, tenantId);/return this.kitsService.addItems(kitId, tenantId, items);/' src/kits/kits.controller.ts

# 7. Create a minimal package.json for production
echo "Creating production configuration..."
cat > railway.toml << 'EOF'
[build]
builder = "nixpacks"
buildCommand = "npm ci && npm run build && npx prisma generate"

[deploy]
startCommand = "npm run start:prod"
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "always"
restartPolicyMaxRetries = 3
EOF

echo "Build preparation complete. Running build..."
npm run build