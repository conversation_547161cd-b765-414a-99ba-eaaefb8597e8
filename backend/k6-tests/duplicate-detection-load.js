import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const duplicateDetectionTime = new Trend('duplicate_detection_time');
const addressParsingTime = new Trend('address_parsing_time');
const fuzzyMatchingAccuracy = new Rate('fuzzy_matching_accuracy');

// Test configuration - Focus on duplicate detection performance
export const options = {
  scenarios: {
    // Scenario 1: Baseline load (10K orders/hour = ~3 orders/second)
    baseline: {
      executor: 'constant-arrival-rate',
      rate: 3,
      timeUnit: '1s',
      duration: '10m',
      preAllocatedVUs: 10,
      maxVUs: 50,
    },
    // Scenario 2: Peak load (50K orders/hour = ~14 orders/second)
    peak: {
      executor: 'constant-arrival-rate',
      rate: 14,
      timeUnit: '1s',
      duration: '5m',
      preAllocatedVUs: 50,
      maxVUs: 200,
      startTime: '10m',
    },
    // Scenario 3: Stress test (100K orders/hour = ~28 orders/second)
    stress: {
      executor: 'constant-arrival-rate',
      rate: 28,
      timeUnit: '1s',
      duration: '2m',
      preAllocatedVUs: 100,
      maxVUs: 300,
      startTime: '15m',
    },
  },
  thresholds: {
    http_req_duration: ['p(95)<2000', 'p(99)<5000'], // 95% < 2s, 99% < 5s
    duplicate_detection_time: ['p(95)<1500', 'p(99)<3000'], // Detection time limits
    address_parsing_time: ['p(95)<100', 'p(99)<200'], // Address parsing limits
    fuzzy_matching_accuracy: ['rate>0.95'], // 95% accuracy
    http_req_failed: ['rate<0.05'], // Less than 5% errors
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:5000';
const TENANT_ID = __ENV.TENANT_ID || 'load-test-tenant';

// Test data pools
const cpfPool = [
  '123.456.789-00', '234.567.890-11', '345.678.901-22',
  '456.789.012-33', '567.890.123-44', '678.901.234-55',
  '789.012.345-66', '890.123.456-77', '901.234.567-88',
  '012.345.678-99',
];

const addressVariations = [
  {
    original: 'Rua das Flores, 123, Apt 45, Jardim Paulista, São Paulo - SP, 01234-567',
    variations: [
      'R. das Flores, 123, Apto 45, Jd. Paulista, São Paulo - SP, 01234-567',
      'Rua das Flores, 123, Apartamento 45, Jardim Paulista, São Paulo - SP',
      'R das Flores, 123 - Apt 45, Jardim Paulista, São Paulo/SP',
    ],
  },
  {
    original: 'Avenida Paulista, 1000, Sala 200, Bela Vista, São Paulo - SP, 01310-100',
    variations: [
      'Av. Paulista, 1000, Sl 200, Bela Vista, São Paulo - SP, 01310-100',
      'Av Paulista, 1000 - Sala 200, Bela Vista, São Paulo/SP',
      'Avenida Paulista, 1000, Bela Vista, São Paulo - SP',
    ],
  },
];

export function setup() {
  // Create some baseline orders for duplicate detection
  const authToken = 'test-token'; // In real test, get from login
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
    'x-tenant-id': TENANT_ID,
  };

  // Seed with known duplicates
  cpfPool.forEach((cpf, index) => {
    const address = addressVariations[index % addressVariations.length];
    
    http.post(`${BASE_URL}/orders`, JSON.stringify({
      orderNumber: `SEED-${index}`,
      customerName: `Seed Customer ${index}`,
      customerCPF: cpf,
      customerEmail: `seed${index}@example.com`,
      fullAddress: address.original,
      totalAmount: 100,
      paymentMethod: 'COD',
      items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
    }), { headers });
  });

  return { authToken };
}

export default function (data) {
  const { authToken } = data;
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
    'x-tenant-id': TENANT_ID,
  };

  // Select CPF - 40% chance of using existing CPF (duplicate)
  const cpf = Math.random() < 0.4 
    ? cpfPool[Math.floor(Math.random() * cpfPool.length)]
    : `${Math.floor(Math.random() * 999)}.${Math.floor(Math.random() * 999)}.${Math.floor(Math.random() * 999)}-${Math.floor(Math.random() * 99)}`;

  // Select address - if duplicate CPF, use variation
  let address;
  if (cpfPool.includes(cpf)) {
    const addressSet = addressVariations[Math.floor(Math.random() * addressVariations.length)];
    address = addressSet.variations[Math.floor(Math.random() * addressSet.variations.length)];
  } else {
    // Generate random address
    const streetNumber = Math.floor(Math.random() * 9999) + 1;
    address = `Rua Teste ${__VU}, ${streetNumber}, São Paulo - SP`;
  }

  const orderData = {
    orderNumber: `LOAD-${Date.now()}-${__VU}-${__ITER}`,
    customerName: `Load Test Customer ${__VU}`,
    customerCPF: cpf,
    customerEmail: `load${__VU}@example.com`,
    fullAddress: address,
    totalAmount: Math.floor(Math.random() * 500) + 50,
    paymentMethod: 'COD',
    items: [{
      productId: `prod-${Math.floor(Math.random() * 10) + 1}`,
      quantity: Math.floor(Math.random() * 5) + 1,
      price: 50,
    }],
  };

  // Make request and measure timing
  const startTime = Date.now();
  const res = http.post(`${BASE_URL}/orders`, JSON.stringify(orderData), { headers });
  const totalTime = Date.now() - startTime;

  // Check response
  const success = check(res, {
    'order created successfully': (r) => r.status === 201,
    'duplicate detection completed': (r) => r.json('isDuplicate') !== undefined,
    'match score provided': (r) => !r.json('isDuplicate') || r.json('duplicateMatchScore') !== undefined,
  });

  if (res.status === 201) {
    // Track duplicate detection time
    duplicateDetectionTime.add(totalTime);

    // Check accuracy for known duplicates
    if (cpfPool.includes(cpf)) {
      const correctlyDetected = res.json('isDuplicate') === true;
      fuzzyMatchingAccuracy.add(correctlyDetected);

      if (correctlyDetected) {
        check(res, {
          'match score >= 70': (r) => r.json('duplicateMatchScore') >= 70,
          'matched components present': (r) => Array.isArray(r.json('matchedComponents')),
        });
      }
    }

    // Parse response headers for timing breakdown (if available)
    const parsingTime = res.headers['X-Address-Parsing-Time'];
    if (parsingTime) {
      addressParsingTime.add(parseFloat(parsingTime));
    }
  }

  // Variable think time based on scenario
  const scenario = __ENV.SCENARIO || 'baseline';
  const thinkTime = scenario === 'stress' ? 0.1 : scenario === 'peak' ? 0.5 : 1;
  sleep(thinkTime);
}

export function handleSummary(data) {
  return {
    'stdout': textSummary(data, { indent: ' ', enableColors: true }),
    'duplicate-detection-report.json': JSON.stringify(data),
    'duplicate-detection-report.html': htmlReport(data),
  };
}

function textSummary(data, options) {
  const { metrics } = data;
  
  return `
Duplicate Detection Performance Test Results
==========================================

Detection Performance:
- 95th percentile: ${metrics.duplicate_detection_time.values['p(95)']}ms
- 99th percentile: ${metrics.duplicate_detection_time.values['p(99)']}ms
- Max time: ${metrics.duplicate_detection_time.values.max}ms

Address Parsing:
- 95th percentile: ${metrics.address_parsing_time.values['p(95)']}ms
- 99th percentile: ${metrics.address_parsing_time.values['p(99)']}ms

Accuracy:
- Fuzzy matching accuracy: ${(metrics.fuzzy_matching_accuracy.values.rate * 100).toFixed(2)}%

Throughput:
- Total requests: ${metrics.http_reqs.values.count}
- Requests/sec: ${metrics.http_reqs.values.rate.toFixed(2)}
- Error rate: ${(metrics.http_req_failed.values.rate * 100).toFixed(2)}%

Scenarios:
- Baseline (10K/hour): ${data.state.isStdErrTTY ? '✓' : 'PASS'}
- Peak (50K/hour): ${data.state.isStdErrTTY ? '✓' : 'PASS'}
- Stress (100K/hour): ${metrics.http_req_failed.values.rate < 0.05 ? '✓' : '✗'}
  `;
}

function htmlReport(data) {
  // Simple HTML report template
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Duplicate Detection Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .pass { color: green; }
        .fail { color: red; }
    </style>
</head>
<body>
    <h1>Duplicate Detection Load Test Report</h1>
    <h2>Test Configuration</h2>
    <div class="metric">
        <strong>Baseline Load:</strong> 10,000 orders/hour (3 orders/second)<br>
        <strong>Peak Load:</strong> 50,000 orders/hour (14 orders/second)<br>
        <strong>Stress Load:</strong> 100,000 orders/hour (28 orders/second)
    </div>
    
    <h2>Performance Metrics</h2>
    <div class="metric">
        <strong>Duplicate Detection Time (95th percentile):</strong> ${data.metrics.duplicate_detection_time.values['p(95)']}ms<br>
        <strong>Address Parsing Time (95th percentile):</strong> ${data.metrics.address_parsing_time.values['p(95)']}ms<br>
        <strong>Fuzzy Matching Accuracy:</strong> ${(data.metrics.fuzzy_matching_accuracy.values.rate * 100).toFixed(2)}%
    </div>
    
    <h2>Throughput</h2>
    <div class="metric">
        <strong>Total Requests:</strong> ${data.metrics.http_reqs.values.count}<br>
        <strong>Average RPS:</strong> ${data.metrics.http_reqs.values.rate.toFixed(2)}<br>
        <strong>Error Rate:</strong> ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%
    </div>
</body>
</html>
  `;
}