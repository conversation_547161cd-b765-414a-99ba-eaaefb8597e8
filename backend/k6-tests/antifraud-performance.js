import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const duplicateDetectionRate = new Rate('duplicate_detection_rate');
const reviewProcessingRate = new Rate('review_processing_rate');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 50 },   // Ramp up to 50 users
    { duration: '5m', target: 100 },  // Stay at 100 users
    { duration: '3m', target: 200 },  // Peak load at 200 users
    { duration: '2m', target: 0 },    // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.1'],     // Error rate must be below 10%
    duplicate_detection_rate: ['rate>0.8'], // 80% success rate for duplicate detection
    review_processing_rate: ['rate>0.9'],   // 90% success rate for reviews
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:5000';
const TENANT_ID = __ENV.TENANT_ID || 'perf-test-tenant';

// Helper function to generate Brazilian CPF
function generateCPF() {
  const random = () => Math.floor(Math.random() * 9);
  const digits = Array.from({ length: 9 }, random);
  
  // Simple CPF generation (not valid, but consistent format)
  return `${digits.slice(0, 3).join('')}.${digits.slice(3, 6).join('')}.${digits.slice(6, 9).join('')}-00`;
}

// Helper function to generate Brazilian address
function generateAddress() {
  const streets = ['Rua das Flores', 'Av. Paulista', 'Rua Augusta', 'Av. Brasil'];
  const numbers = Math.floor(Math.random() * 9999) + 1;
  const cities = ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Porto Alegre'];
  const states = ['SP', 'RJ', 'MG', 'RS'];
  
  const cityIndex = Math.floor(Math.random() * cities.length);
  
  return `${streets[Math.floor(Math.random() * streets.length)]}, ${numbers}, ${cities[cityIndex]} - ${states[cityIndex]}`;
}

// Setup function - runs once per VU
export function setup() {
  // Login and get auth token
  const loginRes = http.post(`${BASE_URL}/auth/login`, JSON.stringify({
    email: '<EMAIL>',
    password: 'test123',
  }), {
    headers: { 
      'Content-Type': 'application/json',
      'x-tenant-id': TENANT_ID,
    },
  });

  const authToken = loginRes.json('access_token');
  
  return { authToken };
}

export default function (data) {
  const { authToken } = data;
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
    'x-tenant-id': TENANT_ID,
  };

  // Scenario 1: Create orders with potential duplicates (60% of load)
  if (Math.random() < 0.6) {
    const cpf = Math.random() < 0.3 ? '123.456.789-00' : generateCPF(); // 30% chance of duplicate CPF
    const address = generateAddress();
    
    const orderData = {
      orderNumber: `PERF-${Date.now()}-${__VU}`,
      customerName: `Customer ${__VU}`,
      customerCPF: cpf,
      customerEmail: `customer${__VU}@example.com`,
      fullAddress: address,
      totalAmount: Math.floor(Math.random() * 500) + 50,
      paymentMethod: 'COD',
      items: [{
        productId: 'prod-1',
        quantity: Math.floor(Math.random() * 5) + 1,
        price: 50,
      }],
    };

    const createRes = http.post(`${BASE_URL}/orders`, JSON.stringify(orderData), { headers });
    
    const success = check(createRes, {
      'order created': (r) => r.status === 201,
      'duplicate detection completed': (r) => r.json('isDuplicate') !== undefined,
    });

    duplicateDetectionRate.add(success);

    // If duplicate detected, add to review queue
    if (createRes.json('isDuplicate') === true) {
      sleep(0.5); // Brief pause before next action
    }
  }
  
  // Scenario 2: Review duplicate orders (30% of load)
  else if (Math.random() < 0.9) {
    // Get review queue
    const queueRes = http.get(`${BASE_URL}/antifraud/duplicates/review-queue?limit=10`, { headers });
    
    if (queueRes.status === 200 && queueRes.json('items').length > 0) {
      const orders = queueRes.json('items');
      const orderToReview = orders[Math.floor(Math.random() * orders.length)];
      
      const reviewData = {
        decision: Math.random() < 0.7 ? 'APPROVE_ORDER' : 'DENY_ORDER',
        reason: 'Performance test review',
      };

      const reviewRes = http.post(
        `${BASE_URL}/antifraud/duplicates/${orderToReview.id}/review`,
        JSON.stringify(reviewData),
        { headers }
      );

      const success = check(reviewRes, {
        'review processed': (r) => r.status === 200,
      });

      reviewProcessingRate.add(success);
    }
  }
  
  // Scenario 3: Bulk operations (10% of load)
  else {
    const queueRes = http.get(`${BASE_URL}/antifraud/duplicates/review-queue?limit=20`, { headers });
    
    if (queueRes.status === 200 && queueRes.json('items').length >= 5) {
      const orders = queueRes.json('items').slice(0, 5);
      const orderIds = orders.map(o => o.id);
      
      const bulkReviewData = {
        orderIds,
        review: {
          decision: 'APPROVE_ORDER',
          reason: 'Bulk performance test',
        },
      };

      const bulkRes = http.post(
        `${BASE_URL}/antifraud/duplicates/bulk-review`,
        JSON.stringify(bulkReviewData),
        { headers }
      );

      check(bulkRes, {
        'bulk review processed': (r) => r.status === 200,
        'all orders processed': (r) => r.json('processed') === 5,
      });
    }
  }

  sleep(1); // Think time between requests
}

// Teardown function - runs once after all VUs finish
export function teardown(data) {
  // Clean up test data if needed
  console.log('Performance test completed');
}