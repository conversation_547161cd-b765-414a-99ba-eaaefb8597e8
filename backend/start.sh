#!/bin/sh
# Startup script for Railway deployment

echo "🚀 Starting ZenCash Backend..."
echo "Environment: ${NODE_ENV}"
echo "Port: ${PORT}"
echo "Database URL: ${DATABASE_URL:0:30}..."

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "❌ Error: dist directory not found!"
    echo "📁 Current directory contents:"
    ls -la
    exit 1
fi

# Check if main.js exists
if [ ! -f "dist/main.js" ]; then
    echo "❌ Error: dist/main.js not found!"
    echo "📁 dist directory contents:"
    ls -la dist/
    exit 1
fi

# Set default environment variables if not provided
export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-3000}
export API_PREFIX=${API_PREFIX:-api/v1}

# Ensure required environment variables are set
if [ -z "$DATABASE_URL" ]; then
    echo "⚠️ Warning: DATABASE_URL not set!"
fi

if [ -z "$ENCRYPTION_KEY" ]; then
    echo "⚠️ Warning: ENCRYPTION_KEY not set, using default!"
    export ENCRYPTION_KEY="12345678901234567890123456789012"
fi

if [ -z "$JWT_SECRET" ]; then
    echo "⚠️ Warning: JWT_SECRET not set, using default!"
    export JWT_SECRET="default-jwt-secret"
fi

# Start the application
echo "✅ Starting Node.js application..."
exec node dist/main.js