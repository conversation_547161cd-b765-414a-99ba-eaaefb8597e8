import { TemplateParser } from './src/notifications/templates/template-parser';
import { notificationTemplates, statusMessages } from './src/notifications/templates/notification-templates';
import { NotificationType } from '@prisma/client';

console.log('🚀 Demonstração do Sistema de Notificações WhatsApp\n');

// Exemplo 1: Notificação de Pedido Criado
console.log('📱 1. NOTIFICAÇÃO DE PEDIDO CRIADO');
console.log('================================');

const orderCreatedTemplate = notificationTemplates[NotificationType.ORDER_CREATED];
const orderCreatedContext = {
  customer: { name: '<PERSON>' },
  order: {
    id: '2024-001',
    total: '189.70',
    itemsList: '• 2x Camiseta Básica (R$ 29,90)\n• 1x Calça Jeans (R$ 89,90)',
  },
  seller: { name: 'Maria Vendedora' },
};

const orderCreatedMessage = TemplateParser.parse(orderCreatedTemplate.body, orderCreatedContext);
console.log('Mensagem final:');
console.log(orderCreatedMessage);
console.log('\n---\n');

// Exemplo 2: Notificação de Mudança de Status
console.log('📱 2. NOTIFICAÇÃO DE MUDANÇA DE STATUS');
console.log('=====================================');

const statusChangedTemplate = notificationTemplates[NotificationType.STATUS_CHANGED];
const statusChangedContext = {
  customer: { name: 'Maria Oliveira' },
  order: { id: '2024-002' },
  previousStatus: 'Em Separação',
  newStatus: 'Enviado',
  statusMessage: statusMessages['ENVIADO'],
};

const statusChangedMessage = TemplateParser.parse(statusChangedTemplate.body, statusChangedContext);
console.log('Mensagem final:');
console.log(statusChangedMessage);
console.log('\n---\n');

// Exemplo 3: Notificação de Estoque Baixo
console.log('📱 3. NOTIFICAÇÃO DE ESTOQUE BAIXO');
console.log('==================================');

const lowStockTemplate = notificationTemplates[NotificationType.LOW_STOCK];
const lowStockContext = {
  product: { name: 'Tênis Esportivo' },
  variation: { name: '42 - Preto/Branco' },
  inventory: {
    quantity: 2,
    minAlert: 5,
  },
};

const lowStockMessage = TemplateParser.parse(lowStockTemplate.body, lowStockContext);
console.log('Mensagem final:');
console.log(lowStockMessage);
console.log('\n---\n');

// Demonstração do fluxo de envio
console.log('🔄 FLUXO DE ENVIO VIA WHATSAPP');
console.log('==============================');
console.log('1. Evento ocorre no sistema (ex: pedido criado)');
console.log('2. NotificationService cria job e adiciona à fila BullMQ');
console.log('3. NotificationWorker processa o job:');
console.log('   - Busca template apropriado');
console.log('   - Aplica contexto com TemplateParser');
console.log('   - Envia via WhatsAppProvider');
console.log('4. WhatsAppProvider (mockado):');
console.log('   - Valida número de telefone');
console.log('   - Simula envio com delay');
console.log('   - Retorna ID da mensagem');
console.log('5. Status é atualizado no banco (SENT ou FAILED)');
console.log('\n');

// Exemplo de payload completo
console.log('📦 EXEMPLO DE PAYLOAD NA FILA');
console.log('=============================');
const examplePayload = {
  id: 'notif_123',
  type: 'ORDER_CREATED',
  channel: 'WHATSAPP',
  recipient: {
    phone: '11987654321',
    name: 'João Silva',
  },
  payload: orderCreatedContext,
};
console.log(JSON.stringify(examplePayload, null, 2));

console.log('\n✅ Sistema pronto para integração com providers reais!');
console.log('   Substitua WhatsAppProvider por: Twilio, Zapvoice, Leadzy, etc.');