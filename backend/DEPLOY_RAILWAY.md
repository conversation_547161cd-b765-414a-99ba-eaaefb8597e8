# Deploy do Backend NestJS no Railway

## Pré-requisitos
- Conta no Railway
- PostgreSQL já criado no Railway
- Frontend deployado no Vercel: https://zencash-sand.vercel.app

## Variáveis de Ambiente Necessárias no Railway

```bash
# Banco de Dados (já fornecida pelo Railway)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway

# JWT
JWT_SECRET=sua-chave-secreta-super-segura-aqui

# API
API_PREFIX=api/v1
PORT=3000
NODE_ENV=production

# CORS - Frontend no Vercel
CORS_ORIGIN=https://zencash-sand.vercel.app

# Opcional: Logs
LOG_LEVEL=info
```

## Passos para Deploy

1. **No terminal, na pasta do backend:**
```bash
# Inicializar projeto Railway
railway login
railway link

# Deploy
railway up
```

2. **Após o deploy, obter a URL pública:**
```bash
railway open
```

A URL será algo como: `https://seu-projeto.railway.app`

## Variáveis no Frontend (Vercel)

Adicione no painel do Vercel:

```bash
VITE_API_URL=https://seu-projeto.railway.app/api/v1
VITE_TENANT_ID=************************************
```

## Testando

1. **Health Check:**
```bash
curl https://seu-projeto.railway.app/api/v1/health
```

2. **Login:**
```bash
curl -X POST https://seu-projeto.railway.app/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: ************************************" \
  -d '{"email":"<EMAIL>","password":"Gilson123$"}'
```

## Troubleshooting

### Erro de CORS
- Verifique se CORS_ORIGIN está configurado corretamente
- Confirme que o frontend está usando a URL correta

### Erro de Banco de Dados
- Verifique se DATABASE_URL está configurado
- Execute `npx prisma migrate deploy` manualmente se necessário

### Erro 500
- Verifique os logs no Railway: `railway logs`