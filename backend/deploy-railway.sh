#!/bin/bash
# Railway Deployment Script for ZenCash Anti-Fraud System

set -e

echo "🚀 Starting Railway deployment for ZenCash Anti-Fraud System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo -e "${RED}Railway CLI not found. Please install it first:${NC}"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check environment
ENVIRONMENT=${1:-staging}
echo -e "${YELLOW}Deploying to environment: $ENVIRONMENT${NC}"

# Function to generate secure keys
generate_key() {
    openssl rand -base64 $1 | tr -d '\n'
}

# Set environment variables based on environment
if [ "$ENVIRONMENT" == "production" ]; then
    echo -e "${GREEN}Setting production environment variables...${NC}"
    
    # Database
    railway variables set DATABASE_URL="$PRODUCTION_DATABASE_URL" --environment production
    railway variables set DATABASE_REPLICA_URL="$PRODUCTION_DATABASE_REPLICA_URL" --environment production
    
    # Redis
    railway variables set REDIS_URL="$PRODUCTION_REDIS_URL" --environment production
    railway variables set REDIS_CLUSTER_NODES="$PRODUCTION_REDIS_CLUSTER_NODES" --environment production
    
    # Security Keys (generate if not provided)
    if [ -z "$ENCRYPTION_KEY" ]; then
        ENCRYPTION_KEY=$(generate_key 32)
        echo -e "${YELLOW}Generated new encryption key${NC}"
    fi
    railway variables set ENCRYPTION_KEY="$ENCRYPTION_KEY" --environment production
    
    if [ -z "$SIGNING_KEY" ]; then
        SIGNING_KEY=$(generate_key 64)
        echo -e "${YELLOW}Generated new signing key${NC}"
    fi
    railway variables set SIGNING_KEY="$SIGNING_KEY" --environment production
    
    # JWT Secret
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(generate_key 32)
        echo -e "${YELLOW}Generated new JWT secret${NC}"
    fi
    railway variables set JWT_SECRET="$JWT_SECRET" --environment production
    
    # Anti-fraud Configuration
    railway variables set DUPLICATE_CHECK_TIMEOUT_MS="2000" --environment production
    railway variables set FUZZY_MATCH_CONFIG='{"threshold":0.7,"algorithms":["levenshtein","soundex","metaphone"],"weights":{"street":0.3,"number":0.2,"neighborhood":0.2,"city":0.2,"zipCode":0.1}}' --environment production
    
    # Feature Flags
    railway variables set FEATURE_FLAGS='{"antifraud":{"enabled":false,"shadowMode":true,"pilotTenants":[],"enableBulkOperations":true,"maxBulkSize":100}}' --environment production
    
    # External Services
    railway variables set SENTRY_DSN="$PRODUCTION_SENTRY_DSN" --environment production
    railway variables set SENTRY_ENVIRONMENT="production" --environment production
    
    # Monitoring
    railway variables set METRICS_PORT="9090" --environment production
    railway variables set PROMETHEUS_PUSHGATEWAY="$PRODUCTION_PROMETHEUS_GATEWAY" --environment production
    railway variables set OTLP_ENABLED="true" --environment production
    railway variables set JAEGER_ENDPOINT="$PRODUCTION_JAEGER_ENDPOINT" --environment production
    
    # Logging
    railway variables set LOG_LEVEL="info" --environment production
    railway variables set LOG_FORMAT="json" --environment production
    
    # Rate Limiting
    railway variables set RATE_LIMIT_CONFIG='{"windowMs":60000,"max":100,"skipSuccessfulRequests":false,"keyGenerator":"tenant"}' --environment production
    
    # Node.js
    railway variables set NODE_ENV="production" --environment production
    railway variables set NODE_OPTIONS="--max-old-space-size=2048" --environment production
    
elif [ "$ENVIRONMENT" == "staging" ]; then
    echo -e "${GREEN}Setting staging environment variables...${NC}"
    
    # Similar to production but with staging URLs and relaxed limits
    railway variables set DATABASE_URL="$STAGING_DATABASE_URL" --environment staging
    railway variables set REDIS_URL="$STAGING_REDIS_URL" --environment staging
    railway variables set ENCRYPTION_KEY=$(generate_key 32) --environment staging
    railway variables set SIGNING_KEY=$(generate_key 64) --environment staging
    railway variables set JWT_SECRET=$(generate_key 32) --environment staging
    railway variables set DUPLICATE_CHECK_TIMEOUT_MS="3000" --environment staging
    railway variables set FUZZY_MATCH_CONFIG='{"threshold":0.6,"algorithms":["levenshtein","soundex"],"weights":{"street":0.4,"number":0.3,"neighborhood":0.2,"city":0.1}}' --environment staging
    railway variables set FEATURE_FLAGS='{"antifraud":{"enabled":true,"shadowMode":false,"pilotTenants":["test-tenant-1","test-tenant-2"],"enableBulkOperations":true,"maxBulkSize":50}}' --environment staging
    railway variables set LOG_LEVEL="debug" --environment staging
    railway variables set NODE_ENV="staging" --environment staging
else
    echo -e "${RED}Unknown environment: $ENVIRONMENT${NC}"
    exit 1
fi

# Deploy
echo -e "${GREEN}Starting deployment...${NC}"
railway up --environment $ENVIRONMENT

# Wait for deployment to complete
echo -e "${YELLOW}Waiting for deployment to complete...${NC}"
sleep 30

# Health check
echo -e "${GREEN}Running health check...${NC}"
HEALTH_URL=$(railway status --environment $ENVIRONMENT --json | jq -r '.url')/health

if curl -f -s "$HEALTH_URL" > /dev/null; then
    echo -e "${GREEN}✅ Health check passed!${NC}"
else
    echo -e "${RED}❌ Health check failed!${NC}"
    exit 1
fi

# Run database migrations
echo -e "${GREEN}Running database migrations...${NC}"
railway run --environment $ENVIRONMENT npx prisma migrate deploy

# Verify deployment
echo -e "${GREEN}Verifying deployment...${NC}"
railway logs --environment $ENVIRONMENT --lines 50

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${YELLOW}Remember to:${NC}"
echo "1. Update frontend environment variables to point to new backend URL"
echo "2. Configure monitoring dashboards"
echo "3. Set up alerting rules"
echo "4. Test the deployment thoroughly"
echo "5. Update DNS records if needed"

# Save deployment info
DEPLOYMENT_INFO="deployment-info-$ENVIRONMENT-$(date +%Y%m%d-%H%M%S).json"
railway status --environment $ENVIRONMENT --json > $DEPLOYMENT_INFO
echo -e "${GREEN}Deployment info saved to: $DEPLOYMENT_INFO${NC}"