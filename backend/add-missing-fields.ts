import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addMissingFields() {
  try {
    console.log('🔧 Adding missing fields to Order table...\n');

    // Add nextPaymentDate field
    await prisma.$executeRaw`
      ALTER TABLE "Order" 
      ADD COLUMN IF NOT EXISTS "nextPaymentDate" TIMESTAMP(3)
    `;
    console.log('✓ Added nextPaymentDate field');

    // Add lastContactDate field
    await prisma.$executeRaw`
      ALTER TABLE "Order" 
      ADD COLUMN IF NOT EXISTS "lastContactDate" TIMESTAMP(3)
    `;
    console.log('✓ Added lastContactDate field');

    console.log('\n✅ Missing fields added successfully!');

  } catch (error) {
    console.error('❌ Error adding fields:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addMissingFields();