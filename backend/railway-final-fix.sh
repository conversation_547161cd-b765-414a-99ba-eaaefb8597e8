#!/bin/bash

echo "Final fixes for Railway deployment..."

# 1. Fix antifraud service - update EncryptionUtil usage to use instance methods
echo "Fixing antifraud service..."
# Replace static method calls with instance injection
sed -i '' 's/EncryptionUtil\.encryptCPF/this.encryptionUtil.encrypt/g' src/antifraud/antifraud.service.ts
sed -i '' 's/EncryptionUtil\.hashWithSalt/this.encryptionUtil.hash/g' src/antifraud/antifraud.service.ts
sed -i '' 's/EncryptionUtil\.generateSignature/this.generateSignature/g' src/antifraud/antifraud.service.ts
sed -i '' 's/EncryptionUtil\.decrypt/this.encryptionUtil.decrypt/g' src/antifraud/services/duplicate-detection.service.ts

# Add encryptionUtil injection to antifraud service constructor
sed -i '' '/constructor(/,/)/s/)/, private encryptionUtil: EncryptionUtil)/' src/antifraud/antifraud.service.ts

# Add simple signature generation method to antifraud service
cat >> src/antifraud/antifraud.service.ts << 'EOF'

  private generateSignature(data: string, key: string): string {
    return crypto.createHmac('sha256', key).update(data).digest('hex');
  }
EOF

# 2. Fix duplicate detection service 
echo "Fixing duplicate detection service..."
# Add encryptionUtil injection
sed -i '' '/constructor(/,/)/s/)/, private encryptionUtil: EncryptionUtil)/' src/antifraud/services/duplicate-detection.service.ts

# 3. Fix kits service - add tenantId properly
echo "Fixing kits service tenantId..."
sed -i '' '/data: {/{n;s/^/        tenantId,\n/;}' src/kits/kits.service.ts

# 4. Fix addresses service tenantId parameter
echo "Fixing addresses service..."
sed -i '' 's/createAddressDto: CreateAddressDto,/createAddressDto: CreateAddressDto, tenantId: string/' src/customers/addresses.service.ts

# 5. Fix Sentry service - use new API
echo "Fixing Sentry service..."
cat > src/common/services/sentry-simple.ts << 'EOF'
import { Injectable, OnModuleInit } from '@nestjs/common';
import * as Sentry from '@sentry/node';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SentryService implements OnModuleInit {
  constructor(private configService: ConfigService) {}

  onModuleInit() {
    const dsn = this.configService.get<string>('SENTRY_DSN');
    if (!dsn) {
      console.warn('Sentry DSN not configured. Error tracking disabled.');
      return;
    }

    Sentry.init({
      dsn,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      tracesSampleRate: 0.1,
    });
  }

  setTenantContext(tenantId: string): void {
    Sentry.setTag('tenant_id', tenantId);
  }

  setUserContext(userId: string, email?: string, role?: string): void {
    Sentry.setUser({ id: userId, email, role });
  }

  captureException(error: Error, context?: any): void {
    Sentry.withScope((scope) => {
      if (context) {
        scope.setContext('additional', context);
      }
      Sentry.captureException(error);
    });
  }

  captureMessage(message: string, level: Sentry.SeverityLevel = 'info'): void {
    Sentry.captureMessage(message, level);
  }

  captureAntifraudError(error: Error, context: any): void {
    Sentry.withScope((scope) => {
      scope.setTag('antifraud', true);
      scope.setContext('antifraud', context);
      Sentry.captureException(error);
    });
  }

  startTransaction(name: string, op: string): any {
    // Return a dummy transaction for now
    return { finish: () => {} };
  }

  async flush(timeout?: number): Promise<boolean> {
    return Sentry.flush(timeout);
  }
}
EOF

mv src/common/services/sentry-simple.ts src/common/services/sentry.service.ts

# 6. Add missing import to antifraud service
echo "Adding crypto import..."
sed -i '' '1i\
import * as crypto from "crypto";\
' src/antifraud/antifraud.service.ts

# 7. Add EncryptionUtil import to duplicate detection service
sed -i '' '/import.*PrismaService/a\
import { EncryptionUtil } from "../../common/utils/encryption.util";' src/antifraud/services/duplicate-detection.service.ts

# 8. Fix configuration service to filter by tenantId
echo "Fixing configuration service..."
sed -i '' 's/await this.prisma.configuration.findMany()/await this.prisma.configuration.findMany({ where: { tenantId } })/' src/configuration/configuration.service.ts

# 9. Add tenantId to cache keys in configuration service
sed -i '' 's/this.configCache.set(key, value)/this.configCache.set(`${tenantId}:${key}`, value)/' src/configuration/configuration.service.ts

echo "Running build..."
npm run build