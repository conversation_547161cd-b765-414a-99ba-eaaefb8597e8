const { PrismaClient } = require('@prisma/client');
const { Decimal } = require('@prisma/client/runtime/library');

const prisma = new PrismaClient();

async function createDashboardTestData() {
  try {
    console.log('Criando dados de teste para o dashboard...\n');

    // 1. Buscar usuários de teste
    const vendedor = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    const cobrador = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!vendedor || !cobrador) {
      console.error('❌ Usuários de teste não encontrados. Execute create-test-users.js primeiro.');
      return;
    }

    // 2. Criar ou buscar cliente de teste
    let customer = await prisma.customer.findUnique({
      where: { cpf: '12345678901' }
    });
    
    if (!customer) {
      customer = await prisma.customer.create({
        data: {
          name: 'Cliente Dashboard Teste',
          email: '<EMAIL>',
          phone: '11999999999',
          cpf: '12345678901',
          active: true
        }
      });
    }

    // 3. Criar pedidos com diferentes status e datas
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const weekAgo = new Date(today);
    weekAgo.setDate(weekAgo.getDate() - 7);
    const monthAgo = new Date(today);
    monthAgo.setMonth(monthAgo.getMonth() - 1);

    // Pedido 1: Completo (vendedor)
    const order1 = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        status: 'COMPLETO',
        total: new Decimal(1000),
        paymentReceivedDate: today,
        items: {
          create: [{
            productId: 'prod-test-1',
            productName: 'Produto Teste 1',
            quantity: 2,
            unitPrice: new Decimal(500)
          }]
        }
      }
    });

    // Pedido 2: Pagamento Parcial (vendedor)
    const order2 = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        status: 'PAGAMENTO_PARCIAL',
        total: new Decimal(2000),
        paymentReceivedAmount: new Decimal(1000), // Valor parcial recebido
        paymentReceivedDate: yesterday,
        items: {
          create: [{
            productId: 'prod-test-2',
            productName: 'Produto Teste 2',
            quantity: 1,
            unitPrice: new Decimal(2000)
          }]
        }
      }
    });

    // Pedido 3: Falha na entrega (cobrador)
    const order3 = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        status: 'FALHA',
        total: new Decimal(1500),
        createdAt: weekAgo,
        items: {
          create: [{
            productId: 'prod-test-3',
            productName: 'Produto Teste 3',
            quantity: 3,
            unitPrice: new Decimal(500)
          }]
        }
      }
    });

    // Pedido 4: Em negociação (cobrador)
    const order4 = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        status: 'NEGOCIACAO',
        total: new Decimal(3000),
        createdAt: monthAgo,
        items: {
          create: [{
            productId: 'prod-test-4',
            productName: 'Produto Teste 4',
            quantity: 2,
            unitPrice: new Decimal(1500)
          }]
        }
      }
    });

    // Pedido 5: Pendente
    const order5 = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        sellerId: vendedor.id,
        status: 'PENDENTE',
        total: new Decimal(500),
        createdAt: today,
        items: {
          create: [{
            productId: 'prod-test-5',
            productName: 'Produto Teste 5',
            quantity: 1,
            unitPrice: new Decimal(500)
          }]
        }
      }
    });

    // 4. Criar pagamento de comissão
    await prisma.commissionPayment.create({
      data: {
        orderId: order1.id,
        userId: vendedor.id,
        userRole: 'VENDEDOR',
        baseAmount: new Decimal(1000),
        percentage: new Decimal(10),
        commissionAmount: new Decimal(100), // 10% de 1000
        paymentDate: today
      }
    });

    await prisma.commissionPayment.create({
      data: {
        orderId: order1.id,
        userId: cobrador.id,
        userRole: 'COBRADOR',
        baseAmount: new Decimal(1000),
        percentage: new Decimal(5),
        commissionAmount: new Decimal(50), // 5% de 1000
        paymentDate: today
      }
    });

    // 5. Criar alertas de rastreamento
    await prisma.tracking.create({
      data: {
        orderId: order3.id,
        code: `BR${Date.now()}TEST`,
        status: 'Destinatário ausente',
        isDelivered: false,
        hasAlert: true,
        alertReason: 'DESTINATÁRIO AUSENTE - 3 tentativas',
        lastUpdate: today
      }
    });

    console.log('✓ Dados de teste criados com sucesso:');
    console.log('  - 1 cliente');
    console.log('  - 5 pedidos com diferentes status');
    console.log('  - 2 pagamentos de comissão');
    console.log('  - 1 alerta de rastreamento');
    console.log('\nResumo dos pedidos:');
    console.log('  - Completo: 1 (R$ 1.000)');
    console.log('  - Pagamento Parcial: 1 (R$ 2.000, pago R$ 1.000)');
    console.log('  - Falha: 1 (R$ 1.500)');
    console.log('  - Negociação: 1 (R$ 3.000)');
    console.log('  - Pendente: 1 (R$ 500)');
    console.log('\nTotal: R$ 8.000');

  } catch (error) {
    console.error('Erro ao criar dados de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createDashboardTestData();