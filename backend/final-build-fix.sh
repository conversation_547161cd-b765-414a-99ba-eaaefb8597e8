#!/bin/bash

echo "Final build fixes for Railway deployment..."

# 1. Fix kits service syntax errors
echo "Fixing kits service syntax..."
sed -i '' 's/tenantId,        /tenantId, /g' src/kits/kits.service.ts
sed -i '' 's/tenantId, tenantId, quantity/quantity/' src/kits/kits.service.ts

# 2. Fix duplicate detection service type
echo "Fixing duplicate detection array type..."
cat > src/antifraud/services/duplicate-detection-fix.ts << 'EOF'
export interface DuplicateMatch {
  orderId: string;
  orderNumber: string;
  customerName?: string;
  totalAmount?: number;
  matchScore: number;
  matchedComponents?: string[];
  createdAt: Date;
}

// Replace this in duplicate-detection.service.ts:
// const matches: any[] = []
// with:
// const matches: DuplicateMatch[] = []
EOF

# Update the service to properly initialize the array
sed -i '' 's/const matches: any\[\] = \[\]/const matches = [] as any[]/' src/antifraud/services/duplicate-detection.service.ts

# 3. Fix customers service missing tenantId
echo "Fixing addresses service..."
sed -i '' 's/await this.verifyCustomerExists(customerId);/await this.verifyCustomerExists(customerId, tenantId);/' src/customers/addresses.service.ts
sed -i '' 's/async create(customerId: string, createAddressDto: CreateAddressDto)/async create(customerId: string, createAddressDto: CreateAddressDto, tenantId: string)/' src/customers/addresses.service.ts
sed -i '' 's/async findAll(customerId: string)/async findAll(customerId: string, tenantId: string)/' src/customers/addresses.service.ts

# Update addresses service to include tenantId in create
sed -i '' '/data: createAddressDto,/a\
        tenantId,' src/customers/addresses.service.ts

# 4. Fix encryption util (use createCipheriv with proper auth tag handling)
echo "Fixing encryption util..."
cat > src/common/utils/encryption-fix.ts << 'EOF'
import * as crypto from 'crypto';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EncryptionUtil {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;

  constructor(private configService: ConfigService) {
    const keyString = this.configService.get<string>('ENCRYPTION_KEY');
    if (!keyString || keyString.length !== 32) {
      throw new Error('ENCRYPTION_KEY must be 32 characters long');
    }
    this.key = Buffer.from(keyString);
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = (cipher as any).getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }
    
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
    (decipher as any).setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  hash(text: string): string {
    return crypto.createHash('sha256').update(text).digest('hex');
  }
}
EOF

mv src/common/utils/encryption-fix.ts src/common/utils/encryption.util.ts

# 5. Fix Sentry service (remove deprecated methods)
echo "Fixing Sentry service..."
sed -i '' 's/Sentry.startTransaction/Sentry.startSpan/g' src/common/services/sentry.service.ts
sed -i '' 's/Sentry.getCurrentHub().configureScope/Sentry.configureScope/g' src/common/services/sentry.service.ts

# 6. Fix configuration service
echo "Fixing configuration service..."
sed -i '' 's/async getAll()/async getAll(tenantId: string)/' src/configuration/configuration.service.ts

# 7. Remove the 'tenantId,' from inventory updates (not a field in inventory)
echo "Fixing inventory updates..."
sed -i '' '/data: {/{n;s/^[[:space:]]*tenantId,[[:space:]]*//;}' src/kits/kits.service.ts

# 8. Fix kit service findOne call
echo "Fixing kit service calls..."
sed -i '' 's/const kit = await this.findOne(kitId);/const kit = await this.findOne(kitId, tenantId);/' src/kits/kits.service.ts

# 9. Clean up and run build
echo "Running final build..."
npm run build