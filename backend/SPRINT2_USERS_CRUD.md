# Sprint 2 - CRUD de Usuários

## ✅ Implementações Concluídas

### 📁 Arquivos Criados/Modificados

1. **DTOs**
   - `/src/users/dto/create-user.dto.ts` - DTO para criação de usuário
   - `/src/users/dto/update-user.dto.ts` - DTO para atualização de usuário

2. **Service**
   - `/src/users/users.service.ts` - Implementação completa do CRUD com validações

3. **Controller**
   - `/src/users/users.controller.ts` - Todas as rotas do CRUD com guards

4. **Module**
   - `/src/users/users.module.ts` - Atualizado com controller

5. **Testes**
   - `/src/users/users.service.spec.ts` - Testes unitários básicos

6. **Seeds**
   - `/prisma/seed-users.ts` - Script para criar usuários de teste

## 🔐 Rotas e Permissões

### 1. **POST /users** - Criar usu<PERSON>rio
- **Permissão**: Somente `ADMIN`
- **Body**:
```json
{
  "name": "Nome do Usuário",
  "email": "<EMAIL>",
  "password": "senha123",
  "role": "VENDEDOR",
  "active": true
}
```

### 2. **GET /users** - Listar usuários
- **Permissão**: `ADMIN` e `SUPERVISOR`
- **Query Params**:
  - `role`: Filtrar por role (ADMIN, SUPERVISOR, COBRADOR, VENDEDOR)
  - `active`: Filtrar por status (true/false)
  - `email`: Buscar por email (busca parcial)
- **Exemplo**: `/users?role=VENDEDOR&active=true`

### 3. **GET /users/:id** - Buscar um usuário
- **Permissão**: `ADMIN` e `SUPERVISOR`
- **Retorna**: Dados do usuário sem a senha

### 4. **PATCH /users/:id** - Editar usuário
- **Permissão**: `ADMIN` e `SUPERVISOR`
- **Regras**:
  - `ADMIN` pode editar qualquer usuário
  - `SUPERVISOR` não pode editar usuários `ADMIN`
- **Body** (todos campos opcionais):
```json
{
  "name": "Novo Nome",
  "email": "<EMAIL>",
  "role": "COBRADOR",
  "active": false
}
```

### 5. **DELETE /users/:id** - Desativar usuário
- **Permissão**: Somente `ADMIN`
- **Comportamento**: Soft delete (define `active: false`)

## 🛡️ Validações Implementadas

1. **Email único**: Não permite emails duplicados
2. **Formato de email**: Valida formato correto
3. **Senha mínima**: 6 caracteres
4. **Role válido**: Apenas valores do enum
5. **Senha criptografada**: Usando bcrypt
6. **Senha nunca retornada**: Removida de todas as respostas

## 🧪 Usuários de Teste

Execute `npx ts-node prisma/seed-users.ts` para criar:

- `<EMAIL>` / `senha123` (ADMIN)
- `<EMAIL>` / `senha123` (SUPERVISOR)
- `<EMAIL>` / `senha123` (VENDEDOR)
- `<EMAIL>` / `senha123` (COBRADOR)
- `<EMAIL>` / `senha123` (VENDEDOR - Inativo)

## 📝 Exemplos de Uso

### 1. Login como Admin
```bash
POST http://localhost:3003/auth/login
{
  "email": "<EMAIL>",
  "password": "senha123"
}
```

### 2. Criar Novo Usuário (como Admin)
```bash
POST http://localhost:3003/users
Authorization: Bearer {token}
{
  "name": "Novo Vendedor",
  "email": "<EMAIL>",
  "password": "senha123",
  "role": "VENDEDOR"
}
```

### 3. Listar Vendedores Ativos (como Supervisor)
```bash
GET http://localhost:3003/users?role=VENDEDOR&active=true
Authorization: Bearer {token}
```

### 4. Editar Usuário (como Admin)
```bash
PATCH http://localhost:3003/users/{userId}
Authorization: Bearer {token}
{
  "name": "Nome Atualizado",
  "active": false
}
```

### 5. Desativar Usuário (como Admin)
```bash
DELETE http://localhost:3003/users/{userId}
Authorization: Bearer {token}
```

## ⚠️ Observações Importantes

1. **Senha não pode ser alterada** nesta sprint
2. **Usuários não podem editar a si mesmos** (será implementado futuramente)
3. **Soft delete** mantém o registro no banco, apenas marca como inativo
4. Todas as rotas exigem autenticação JWT
5. Guards verificam tanto autenticação quanto roles