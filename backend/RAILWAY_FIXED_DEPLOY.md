# Railway Deployment - Fixed Version

## What I Fixed:
1. Removed the Dockerfile (renamed to Dockerfile.backup) to use Nixpacks instead
2. Created nixpacks.toml for proper Node.js deployment
3. Fixed postinstall script to only run `prisma generate`
4. Migrations will run on startup

## Deploy Now on Railway Dashboard:

### 1. Go to your Railway project
https://railway.app/dashboard

### 2. If you already have a failed service:
- Click on the failed service
- Go to Settings → Delete Service
- Then create a new one

### 3. Create New Service:
- Click "+ New" → "GitHub Repo"
- Select your repository: `gilsonljr/zencash`
- Set root directory to: `/backend`

### 4. Environment Variables (IMPORTANT!)
Add these in the Variables tab:

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=zencash-jwt-secret-super-secure-2024-production
API_PREFIX=api/v1
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://zencash-sand.vercel.app
```

### 5. Deploy Settings (should auto-detect):
- Build Command: `npm ci && npx prisma generate && npm run build`
- Start Command: `npx prisma migrate deploy && npm run start:prod`

### 6. Deploy
Click "Deploy" or it will auto-deploy after adding variables

## After Successful Deployment:

### 1. Get Your Backend URL
It will be shown in the service dashboard, something like:
`https://backend-production-xxxx.up.railway.app`

### 2. Test Your Backend
```bash
# Health check
curl https://[your-url]/api/v1/health

# Should return:
{
  "status": "ok",
  "database": "connected",
  ...
}
```

### 3. Update Vercel Frontend
Go to Vercel → Settings → Environment Variables and add:
```
REACT_APP_API_URL=https://[your-railway-url]/api/v1
REACT_APP_TENANT_ID=************************************
```

### 4. Test Login from Frontend
Visit https://zencash-sand.vercel.app and try logging in with:
- Email: <EMAIL>
- Password: Gilson123$

## Troubleshooting:

### If build fails:
- Check logs in Railway dashboard
- Make sure all environment variables are set
- Try redeploying

### If "Module not found" errors:
- Make sure root directory is set to `/backend`
- Check that nixpacks.toml is being used

### If database connection fails:
- Ensure DATABASE_URL uses `postgres.railway.internal`
- Check that your backend service is in the same project as the database

## Monitor Your App:
```bash
# Using Railway CLI
railway logs

# Or view in dashboard
Click on your service → Logs tab
```