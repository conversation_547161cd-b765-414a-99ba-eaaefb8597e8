# Manual Railway Deployment Steps

Since the CLI requires interactive input, please follow these steps manually:

## Option 1: Deploy via Railway Dashboard (RECOMMENDED)

### 1. Go to Railway Dashboard
Open: https://railway.app/dashboard

### 2. Open Your PostgreSQL Project
- Find the project with ID: `afe56c34-d890-4fde-9fe6-b18f21095611`
- Click on it to open

### 3. Add a New Service
- Click the "+ New" button
- Select "GitHub Repo" (if your code is on GitHub)
- OR select "Empty Service" (to deploy from CLI later)

### 4. If using GitHub:
- Connect your GitHub account
- Select your repository
- Set the root directory to `/backend`

### 5. Configure Service Settings
In the service settings, set:
- **Root Directory**: `/backend` (if using monorepo)
- **Build Command**: `npm install && npx prisma generate && npm run build`
- **Start Command**: `npx prisma migrate deploy && npm run start:prod`

### 6. Add Environment Variables
Click on "Variables" tab and add:

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=zencash-jwt-secret-**********-94faa7c08c0a6b99f2a7d28f881324a9627bb36d4824ebdd8e818f562c121dbd
API_PREFIX=api/v1
NODE_ENV=production
PORT=3000
CORS_ORIGIN=https://zencash-sand.vercel.app
```

### 7. Deploy
Click "Deploy" or it will auto-deploy if connected to GitHub

### 8. Get Your URL
Once deployed, you'll see the URL in the service settings.
It will be something like: `https://backend-production-xxxx.up.railway.app`

## Option 2: Deploy via CLI (Manual Commands)

Run these commands one by one in your terminal:

```bash
# 1. Login
railway login

# 2. Link project (interactive - select your project)
railway link

# 3. Check status
railway status

# 4. Set variables one by one
railway vars set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"
railway vars set JWT_SECRET="zencash-jwt-secret-**********-94faa7c08c0a6b99f2a7d28f881324a9627bb36d4824ebdd8e818f562c121dbd"
railway vars set API_PREFIX="api/v1"
railway vars set NODE_ENV="production"
railway vars set PORT="3000"
railway vars set CORS_ORIGIN="https://zencash-sand.vercel.app"

# 5. Deploy
railway up

# 6. Get URL
railway domain
```

## After Deployment - Update Vercel

1. Go to your Vercel project: https://vercel.com/dashboard
2. Go to Settings → Environment Variables
3. Add these variables:

```
REACT_APP_API_URL=https://[your-railway-url]/api/v1
REACT_APP_TENANT_ID=************************************
```

4. Redeploy your Vercel frontend

## Test Your Deployment

```bash
# Health check
curl https://[your-railway-url]/api/v1/health

# Test login
curl -X POST https://[your-railway-url]/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: ************************************" \
  -d '{"email":"<EMAIL>","password":"Gilson123$"}'
```