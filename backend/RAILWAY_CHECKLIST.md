# Railway Deployment Checklist

## Environment Variables Required

✅ **DO SET** these in Railway dashboard:
- `DATABASE_URL` - Your PostgreSQL connection string
- `API_PREFIX` - Set to `api/v1`
- `CORS_ORIGIN` - Your Vercel frontend URL (e.g., `https://your-app.vercel.app`)
- `NODE_ENV` - Set to `production`

❌ **DO NOT SET** these (Railway handles automatically):
- `PORT` - Railway injects this dynamically
- Any "Start Command" - Leave blank, use Dockerfile CMD

## Railway Service Settings

1. **Service Type**: Docker
2. **Health Check Path**: `/api/v1/health`
3. **Port**: Leave blank (uses dynamic PORT)
4. **Start Command**: Leave blank (uses Dockerfile CMD)

## Verify Deployment

After pushing, check Railway logs for:

```
[CONTAINER] Starting up...
[CONTAINER] DATABASE_URL: postgresql://...
[CONTAINER] Running Prisma migrations...
[CONTAINER] Starting NestJS application...
========================================
🚀 NESTJS STARTUP SEQUENCE INITIATED
========================================
📍 Port: [number]
🔗 API Prefix: api/v1
🌍 Environment: production
🌐 CORS Origin: https://your-app.vercel.app
========================================
🏥 Registering health endpoints...
✅ Health endpoints registered: /health, /ping, /api/v1/health, /api/v1/health/ready, /api/v1/health/live
========================================
✅ NESTJS APPLICATION STARTED SUCCESSFULLY!
========================================
🌐 Server: http://0.0.0.0:[port]
🏥 Health: http://0.0.0.0:[port]/api/v1/health
📚 Swagger: http://0.0.0.0:[port]/docs
========================================
```

If you see these logs, Railway health checks will pass!