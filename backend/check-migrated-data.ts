import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkMigratedData() {
  try {
    console.log('🔍 Verificando dados migrados...\n');

    // Check current enum values in database
    const enumValues = await prisma.$queryRaw`
      SELECT unnest(enum_range(NULL::"OrderStatus"))::text as value
    `;
    
    console.log('✅ Novos valores do enum OrderStatus no banco:');
    enumValues.forEach((v: any) => console.log(`  - ${v.value}`));

    // Check orders
    const orderCount = await prisma.order.count();
    console.log(`\n📦 Total de pedidos: ${orderCount}`);

    if (orderCount > 0) {
      const statusCounts = await prisma.order.groupBy({
        by: ['status'],
        _count: true,
      });
      
      console.log('\n📊 Distribuição de status dos pedidos:');
      statusCounts.forEach(s => {
        console.log(`  - ${s.status}: ${s._count} pedidos`);
      });
    }

    // Check if new fields are working
    const ordersWithDates = await prisma.order.findMany({
      where: {
        OR: [
          { nextPaymentDate: { not: null } },
          { lastContactDate: { not: null } }
        ]
      }
    });

    console.log(`\n📅 Pedidos com campos de data: ${ordersWithDates.length}`);

  } catch (error) {
    console.error('❌ Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMigratedData();