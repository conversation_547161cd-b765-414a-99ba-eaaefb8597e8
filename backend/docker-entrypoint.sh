#!/bin/sh

echo "Starting ZenCash Anti-fraud Backend..."

# Generate Prisma client first (this should always succeed)
echo "Generating Prisma client..."
npx prisma generate

# Try to run database migrations, but don't fail if database is not ready
echo "Running database migrations..."
if npx prisma migrate deploy; then
  echo "✅ Database migrations completed successfully"
else
  echo "⚠️  Database migrations failed - this might be OK if database is still initializing"
  echo "The application will start anyway and retry database connection"
fi

# Seed database if in development
if [ "$NODE_ENV" = "development" ]; then
  echo "Seeding database..."
  npx prisma db seed || true
fi

# Start the application
echo "Starting NestJS application on port ${PORT:-5000}..."
exec node dist/main