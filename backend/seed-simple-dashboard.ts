import { PrismaClient, OrderStatus } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function seedSimpleDashboard() {
  console.log('🌱 Creating simple test data for Dashboard...\n');

  try {
    // Get test users
    const supervisor = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    const vendedor = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    const cobrador = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!supervisor || !vendedor || !cobrador) {
      console.error('Test users not found!');
      return;
    }

    // Get or create a test customer
    let customer = await prisma.customer.findUnique({
      where: { cpf: '12345678901' }
    });

    if (!customer) {
      customer = await prisma.customer.create({
        data: {
          name: 'Cliente Dashboard Test',
          cpf: '12345678901',
          phone: '11999999999',
          email: '<EMAIL>',
          addresses: {
            create: {
              cep: '01310-100',
              street: 'Av. Paulista',
              number: '1000',
              neighborhood: 'Bela Vista',
              city: 'São Paulo',
              state: 'SP',
              main: true,
            },
          },
        },
      });
    }

    // Create a test product
    const product = await prisma.product.create({
      data: {
        name: 'Produto Dashboard Test',
        description: 'Produto para testes',
        variations: {
          create: {
            variation: 'Padrão',
            price: new Decimal(100),
            sku: 'DASH-TEST-001',
          },
        },
      },
      include: {
        variations: true,
      },
    });

    const variation = product.variations[0];

    // Create an order with nextPaymentDate = today (for Receber Hoje)
    const today = new Date();
    today.setHours(12, 0, 0, 0);

    const order1 = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(500),
        status: OrderStatus.PAGAMENTO_PENDENTE,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        nextPaymentDate: today,
        lastContactDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 5,
            unitPrice: variation.price,
          },
        },
      },
    });

    console.log('✓ Created order for "Receber Hoje":', order1.id);

    // Create an order with status FALHA (for Riscos)
    const order2 = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(300),
        status: OrderStatus.ENTREGA_FALHA,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        lastContactDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 3,
            unitPrice: variation.price,
          },
        },
      },
    });

    console.log('✓ Created order for "Riscos":', order2.id);

    console.log('\n✅ Simple dashboard test data created successfully!');

  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedSimpleDashboard();