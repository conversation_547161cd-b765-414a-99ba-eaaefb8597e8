#!/bin/bash

# Test production webhook with debug info
echo "Testing production webhook with full debug..."

# First test the test endpoint to see field discovery
echo "1. Testing /test endpoint for field discovery:"
curl -X POST https://zencash-production.up.railway.app/api/v1/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{
    "sale": {
      "id": "test-sale-001",
      "code": "SAL001",
      "value": 15000,
      "created_at": "2025-01-23T12:00:00Z",
      "payment_method": "PIX",
      "status": "PAID"
    },
    "customer": {
      "name": "Test Customer",
      "email": "<EMAIL>",
      "phone": "11999998888",
      "document": "12345678901",
      "address": {
        "street": "Rua Teste",
        "number": "123",
        "complement": "Apto 101",
        "district": "Centro",
        "city": "São Paulo",
        "state": "SP",
        "zip": "01234567"
      }
    },
    "vendor": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "code": "VND001"
    }
  }' -s | python3 -m json.tool

echo -e "\n\n2. Testing /shipments endpoint (production):"
curl -X POST https://zencash-production.up.railway.app/api/v1/webhooks/shipments \
  -H "Content-Type: application/json" \
  -d '{
    "sale": {
      "id": "test-sale-002", 
      "code": "SAL002",
      "value": 25000,
      "created_at": "2025-01-23T14:00:00Z",
      "payment_method": "CREDIT_CARD",
      "status": "PROCESSING"
    },
    "customer": {
      "name": "Maria Silva",
      "email": "<EMAIL>", 
      "phone": "11888887777",
      "document": "98765432100"
    }
  }' -s | python3 -m json.tool