import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('webhook_mappings')
@Index(['payloadKey', 'tenantId'], { unique: true })
export class WebhookMapping {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  payloadKey: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  entityColumn: string | null;

  @Column({ type: 'varchar', length: 50, default: 'Sale' })
  entityType: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  dataType: string | null; // 'string', 'number', 'date', 'boolean', 'json'

  @Column({ type: 'text', nullable: true })
  sampleValue: string | null;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true })
  tenantId: string | null;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}