import { IsString, IsOptional, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateMappingDto {
  @ApiProperty({
    description: 'The entity column to map the payload key to',
    example: 'trackingCode',
  })
  @IsString()
  @IsNotEmpty()
  entityColumn: string;

  @ApiPropertyOptional({
    description: 'Optional description for the mapping',
    example: 'Maps the tracking code from webhook payload',
  })
  @IsString()
  @IsOptional()
  description?: string;
}