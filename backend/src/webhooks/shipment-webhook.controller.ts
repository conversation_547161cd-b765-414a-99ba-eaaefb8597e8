import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Head,
  Options,
  Body,
  Headers,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  HttpCode,
  Res,
  All,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { WebhookMappingService } from './webhook-mapping.service';
import { WebhookSalesService } from './webhook-sales.service';
import { EventStatusMappingService } from './event-status-mapping.service';
import { OrderStatus } from '@prisma/client';
import * as crypto from 'crypto';

@ApiTags('Webhooks')
@Controller('webhooks')
export class ShipmentWebhookController {
  private readonly logger = new Logger(ShipmentWebhookController.name);
  private readonly webhookSecret: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly webhookMappingService: WebhookMappingService,
    private readonly webhookSalesService: WebhookSalesService,
    private readonly eventStatusMappingService: EventStatusMappingService,
  ) {
    this.webhookSecret = this.configService.get<string>('WEBHOOK_SECRET') || 'default-secret';
  }

  // Root webhook endpoint - handles any method
  @All()
  @HttpCode(200)
  async rootWebhook(@Res() res: Response) {
    res.set('Content-Type', 'application/json');
    res.json({
      status: 'ok',
      service: 'webhook',
      message: 'Webhook service ready',
      endpoints: [
        '/api/v1/webhooks/test',
        '/api/v1/webhooks/shipments',
        '/api/v1/webhooks/validate',
        '/api/v1/webhooks/ping'
      ]
    });
  }

  // Simple validation endpoint that just returns "ok"
  @All('validate')
  @HttpCode(200)
  async validateWebhook(@Res() res: Response) {
    res.set('Content-Type', 'text/plain');
    res.send('ok');
  }

  // Another simple validation endpoint
  @Post('ping')
  @HttpCode(200)
  async pingWebhook() {
    return { status: 'ok' };
  }

  // Support for HEAD requests (validation checks)
  @Head('test')
  @HttpCode(200)
  async testWebhookHead(@Res() res: Response) {
    res.set({
      'X-Webhook-Ready': 'true',
      'X-Webhook-Version': '1.0',
    });
    res.end();
  }

  @Head('shipments')
  @HttpCode(200)
  async shipmentsWebhookHead(@Res() res: Response) {
    res.set({
      'X-Webhook-Ready': 'true',
      'X-Webhook-Version': '1.0',
    });
    res.end();
  }

  // Support for OPTIONS requests (CORS preflight)
  @Options('test')
  @HttpCode(200)
  async testWebhookOptions(@Res() res: Response) {
    res.set({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Webhook-Signature, Authorization',
      'Access-Control-Max-Age': '86400',
    });
    res.end();
  }

  @Options('shipments')
  @HttpCode(200)
  async shipmentsWebhookOptions(@Res() res: Response) {
    res.set({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Webhook-Signature, Authorization',
      'Access-Control-Max-Age': '86400',
    });
    res.end();
  }

  // Universal endpoint that accepts any method
  @All('echo')
  @HttpCode(200)
  async echoWebhook(@Res() res: Response, @Body() body: any, @Headers() headers: any) {
    this.logger.log(`Echo webhook received: ${JSON.stringify({ method: res.req.method, body, headers })}`);
    
    res.set({
      'Content-Type': 'application/json',
      'X-Webhook-Echo': 'true',
    });
    
    res.json({
      success: true,
      message: 'Echo endpoint working',
      method: res.req.method,
      timestamp: new Date().toISOString(),
      received: body,
    });
  }

  @Get('health')
  @ApiOperation({ summary: 'Webhook health check' })
  @ApiResponse({ status: 200, description: 'Webhook service is healthy' })
  async healthCheck() {
    return {
      status: 'ok',
      service: 'webhook',
      timestamp: new Date().toISOString(),
      endpoints: {
        test: '/api/v1/webhooks/test',
        shipments: '/api/v1/webhooks/shipments',
        info: '/api/v1/webhooks/info',
        validate: '/api/v1/webhooks/validate',
        ping: '/api/v1/webhooks/ping',
        echo: '/api/v1/webhooks/echo'
      }
    };
  }

  @Get('mappings')
  @ApiOperation({ summary: 'List all webhook mappings' })
  @ApiResponse({ status: 200, description: 'List of webhook mappings' })
  async getMappings() {
    try {
      return await this.webhookMappingService.listMappings();
    } catch (error) {
      this.logger.error('Error fetching mappings:', error);
      return [];
    }
  }

  @Get('mappings/count')
  @ApiOperation({ summary: 'Get webhook mappings count' })
  @ApiResponse({ status: 200, description: 'Mappings count' })
  async getMappingsCount() {
    try {
      const mappings = await this.webhookMappingService.listMappings();
      return {
        count: mappings.length,
        mappings: mappings,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        count: 0,
        error: error.message,
        hint: 'Table might not exist. Check database logs.',
      };
    }
  }

  @Put('mappings/:id')
  @ApiOperation({ summary: 'Update a webhook mapping' })
  async updateMapping(
    @Param('id') id: string,
    @Body() updateData: { entityColumn: string; description?: string },
  ) {
    try {
      this.logger.log(`Updating mapping ${id} with:`, updateData);
      const result = await this.webhookMappingService.updateMapping(
        id,
        updateData.entityColumn,
        updateData.description,
      );
      this.logger.log(`Successfully updated mapping ${id}`);
      return result;
    } catch (error) {
      this.logger.error('Error updating mapping:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete('mappings/:id')
  @ApiOperation({ summary: 'Delete a webhook mapping' })
  async deleteMapping(@Param('id') id: string) {
    try {
      await this.webhookMappingService.deleteMapping(id);
      return { message: 'Mapping deleted successfully' };
    } catch (error) {
      this.logger.error('Error deleting mapping:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('mappings/bulk-delete')
  @ApiOperation({ summary: 'Bulk delete webhook mappings' })
  async bulkDeleteMappings(@Body('ids') ids: string[]) {
    try {
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        throw new HttpException('Invalid request: ids array is required', HttpStatus.BAD_REQUEST);
      }

      await this.webhookMappingService.bulkDeleteMappings(ids);
      return { 
        message: 'Mappings deleted successfully',
        deletedCount: ids.length 
      };
    } catch (error) {
      this.logger.error('Error bulk deleting mappings:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('mappings/cleanup-json-values')
  @ApiOperation({ summary: 'Clean up webhook mappings with JSON in sample values' })
  async cleanupJsonValues() {
    try {
      return await this.webhookMappingService.cleanupJsonSampleValues();
    } catch (error) {
      this.logger.error('Error cleaning up mappings:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('logs')
  @ApiOperation({ summary: 'Get webhook logs' })
  async getWebhookLogs(@Query('limit') limit = '20', @Query('status') status?: string) {
    const where: any = {};
    if (status) {
      where.status = status;
    }

    const logs = await this.prisma.webhookLog.findMany({
      where,
      orderBy: { receivedAt: 'desc' },
      take: parseInt(limit),
    });

    return logs.map(log => ({
      ...log,
      payload: typeof log.payload === 'string' ? JSON.parse(log.payload) : log.payload,
      headers: typeof log.headers === 'string' ? JSON.parse(log.headers) : log.headers,
      mappedData: log.mappedData ? (typeof log.mappedData === 'string' ? JSON.parse(log.mappedData) : log.mappedData) : null,
      responseData: log.responseData ? (typeof log.responseData === 'string' ? JSON.parse(log.responseData) : log.responseData) : null,
    }));
  }

  @Post('test-status-update')
  @ApiOperation({ summary: 'Test webhook status update (development only)' })
  @HttpCode(200)
  async testStatusUpdate(@Body() body: { orderId: string; event: string; description?: string }) {
    const { orderId, event, description } = body;
    
    // Find the order
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
    });
    
    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }
    
    // Get the internal status from event mapping
    const internalStatus = await this.eventStatusMappingService.getInternalStatus(event, description);
    
    if (!internalStatus) {
      throw new HttpException(`No mapping found for event: ${event}`, HttpStatus.BAD_REQUEST);
    }
    
    // Update the order status
    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: internalStatus,
        updatedAt: new Date(),
      },
    });
    
    this.logger.log(`Test status update: Order ${orderId} status changed to ${internalStatus} via event ${event}`);
    
    return {
      success: true,
      orderId,
      previousStatus: order.status,
      newStatus: internalStatus,
      event,
      description,
    };
  }

  @Get('logs/:id')
  @ApiOperation({ summary: 'Get specific webhook log' })
  async getWebhookLog(@Param('id') id: string) {
    const log = await this.prisma.webhookLog.findUnique({
      where: { id },
    });

    if (!log) {
      throw new HttpException('Webhook log not found', HttpStatus.NOT_FOUND);
    }

    return {
      ...log,
      payload: typeof log.payload === 'string' ? JSON.parse(log.payload) : log.payload,
      headers: typeof log.headers === 'string' ? JSON.parse(log.headers) : log.headers,
      mappedData: log.mappedData ? (typeof log.mappedData === 'string' ? JSON.parse(log.mappedData) : log.mappedData) : null,
      responseData: log.responseData ? (typeof log.responseData === 'string' ? JSON.parse(log.responseData) : log.responseData) : null,
    };
  }

  @Get('mappings/available-columns')
  @ApiOperation({ summary: 'Get available Order table columns for mapping' })
  async getAvailableColumns() {
    // Return available columns from Order model
    return {
      orderFields: [
        { value: 'orderNumber', label: 'Código da Venda / ID Venda' },
        { value: 'customerName', label: 'Nome do Cliente' },
        { value: 'customerPhone', label: 'Telefone do Cliente' },
        { value: 'customerEmail', label: 'Email do Cliente' },
        { value: 'customerCPF', label: 'Documento Cliente' },
        { value: 'status', label: 'Status' },
        { value: 'statusCorreios', label: 'Status Correios' },
        { value: 'total', label: 'Valor Total' },
        { value: 'fullAddress', label: 'Endereço Completo' },
        { value: 'observation', label: 'Observações' },
        { value: 'paymentMethod', label: 'Forma de Pagamento' },
        { value: 'paymentReceivedAmount', label: 'Valor Recebido' },
        { value: 'paymentReceivedDate', label: 'Data de Recebimento' },
        { value: 'createdAt', label: 'Data Venda' },
        { value: 'updatedAt', label: 'Data Atualização' },
        { value: 'nextPaymentDate', label: 'Próximo Pagamento' },
        { value: 'lastContactDate', label: 'Último Contato' },
        { value: 'zapSourceId', label: '💬 WhatsApp/Zap (extraído do email)' },
      ],
      sellerFields: [
        { value: 'sellerName', label: '👤 Nome do Vendedor' },
        { value: 'sellerEmail', label: '📧 Email do Vendedor (busca vendedor por email exato)' },
        { value: 'sellerId', label: '🆔 ID do Vendedor' },
        { value: 'sellerCode', label: '🔢 Código do Vendedor' },
      ],
      addressFields: [
        { value: 'address.street', label: 'Rua' },
        { value: 'address.streetNumber', label: 'Número' },
        { value: 'address.complement', label: 'Complemento' },
        { value: 'address.neighborhood', label: 'Bairro' },
        { value: 'address.city', label: 'Cidade' },
        { value: 'address.state', label: 'Estado' },
        { value: 'address.zipCode', label: 'CEP' },
      ],
      trackingFields: [
        { value: 'tracking.code', label: 'Código de Rastreamento' },
        { value: 'tracking.status', label: 'Status do Rastreamento' },
      ],
      productFields: [
        { value: '_product_mapping', label: '🛍️ Mapear para Produto Existente' },
        { value: '_product_name', label: '📦 Nome do Produto (texto)' },
        { value: '_product_code', label: '🔢 Código do Produto' },
        { value: '_product_price', label: '💰 Preço do Produto' },
        { value: '_product_quantity', label: '📊 Quantidade' },
      ],
      kitFields: [
        { value: '_kit_mapping', label: '🎁 Mapear para Kit/Oferta Existente' },
        { value: '_kit_name', label: '📦 Nome do Kit/Oferta (texto)' },
        { value: '_kit_code', label: '🔢 Código do Kit/Oferta' },
        { value: '_kit_price', label: '💰 Preço do Kit/Oferta' },
        { value: '_kit_type', label: '🏷️ Tipo da Oferta (MAIN, UPSELL, etc)' },
      ],
      customFields: [
        { value: '_ignore', label: '-- Ignorar este campo --' },
        { value: '_custom', label: '-- Campo customizado --' },
      ]
    };
  }

  @Get('info')
  @ApiOperation({ summary: 'Get webhook configuration information' })
  @ApiResponse({ status: 200, description: 'Webhook info' })
  async getWebhookInfo() {
    const baseUrl = 'https://zencash-production.up.railway.app';
    
    return {
      endpoints: {
        production: {
          url: `${baseUrl}/api/v1/webhooks/shipments`,
          method: 'POST',
          description: 'Main webhook endpoint for receiving shipment updates',
          authentication: {
            required: process.env.NODE_ENV === 'production',
            type: 'HMAC-SHA256',
            header: 'X-Webhook-Signature',
            secret: 'Contact administrator for webhook secret'
          }
        },
        test: {
          url: `${baseUrl}/api/v1/webhooks/test`,
          method: 'POST',
          description: 'Test endpoint to verify webhook connectivity',
          authentication: {
            required: false
          }
        },
        info: {
          url: `${baseUrl}/api/v1/webhooks/info`,
          method: 'GET',
          description: 'This endpoint - provides webhook configuration details'
        }
      },
      samplePayload: {
        codigoRastreio: 'BR123456789BR',
        situacao: 'Em trânsito',
        cliente: 'João Silva',
        telefone: '11999999999',
        email: '<EMAIL>',
        valorTotal: 150.00,
        cpf: '12345678901',
        eventos: [
          {
            data: '2025-01-22',
            hora: '10:30',
            descricao: 'Objeto postado',
            local: 'São Paulo - SP'
          }
        ]
      },
      instructions: {
        step1: 'Use the test endpoint first to verify connectivity',
        step2: 'Configure field mappings in the dashboard at /dashboard/webhook-mappings',
        step3: 'Send webhooks to the production endpoint',
        step4: 'Monitor received data in the orders dashboard'
      },
      supportedFields: [
        'codigoRastreio (tracking code)',
        'situacao (status)',
        'cliente (customer name)',
        'telefone (phone)',
        'email',
        'valorTotal (total value)',
        'cpf (document)',
        'eventos (tracking events array)'
      ]
    };
  }

  @Post('test')
  @HttpCode(200)
  @ApiOperation({ summary: 'Test webhook endpoint - no authentication required' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testWebhook(@Body() payload: any, @Res() res: Response) {
    this.logger.log('Test webhook received:', JSON.stringify(payload));
    
    try {
      // Discover keys from test payload
      const discoveredMappings = await this.webhookMappingService.discoverKeys(payload);
      
      this.logger.log(`Test webhook discovered ${discoveredMappings.length} new keys`);
      
      // Set headers explicitly
      res.set({
        'Content-Type': 'application/json',
        'X-Webhook-Received': 'true',
        'Cache-Control': 'no-cache',
      });
      
      // Send response with discovered keys info
      res.json({
        success: true,
        message: 'Webhook test successful',
        timestamp: new Date().toISOString(),
        receivedData: payload,
        discoveredKeys: discoveredMappings.length,
        hint: discoveredMappings.length > 0 
          ? 'New fields discovered! Check the webhook mappings page to configure them.'
          : 'No new fields discovered. If you have configured mappings, use /webhooks/shipments to save data.',
      });
    } catch (error) {
      this.logger.error('Error in test webhook:', error);
      res.json({
        success: true,
        message: 'Test webhook received',
        error: error.message,
      });
    }
  }

  @Post('shipments')
  @HttpCode(200)
  @ApiOperation({ summary: 'Handle shipment tracking webhook' })
  @ApiHeader({
    name: 'X-Webhook-Signature',
    description: 'HMAC signature for webhook validation',
    required: false,
  })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid signature' })
  @ApiResponse({ status: 400, description: 'Invalid payload' })
  async handleShipmentWebhook(
    @Headers('x-webhook-signature') signature: string,
    @Headers() headers: any,
    @Body() payload: any,
    @Res() res: Response,
  ) {
    const startTime = Date.now();
    
    // Create webhook log entry
    const webhookLog = await this.prisma.webhookLog.create({
      data: {
        endpoint: '/webhooks/shipments',
        method: 'POST',
        headers: headers,
        payload: payload,
        status: 'received',
      }
    });

    this.logger.log(`[WebhookLog ${webhookLog.id}] Received shipment webhook`);

    try {
      // Step 1: Validate webhook signature (optional in development)
      const requireSignature = this.configService.get<string>('NODE_ENV') === 'production' 
        && this.configService.get<string>('WEBHOOK_REQUIRE_SIGNATURE') !== 'false';
      if (requireSignature && !this.validateWebhookSignature(payload, signature)) {
        this.logger.warn(`[WebhookLog ${webhookLog.id}] Webhook signature validation failed`);
        
        await this.prisma.webhookLog.update({
          where: { id: webhookLog.id },
          data: {
            status: 'error',
            error: 'Invalid webhook signature',
            processedAt: new Date(),
            processingTimeMs: Date.now() - startTime,
            responseStatus: 401,
          }
        });
        
        res.status(401).json({ 
          error: 'Invalid webhook signature',
          hint: 'Add X-Webhook-Signature header with HMAC-SHA256 signature or set WEBHOOK_REQUIRE_SIGNATURE=false' 
        });
        return;
      }

      this.logger.log(`[WebhookLog ${webhookLog.id}] Processing webhook:`, JSON.stringify(payload));

      // If payload is empty or invalid, return success anyway
      if (!payload || Object.keys(payload).length === 0) {
        await this.prisma.webhookLog.update({
          where: { id: webhookLog.id },
          data: {
            status: 'success',
            error: 'Empty payload received',
            processedAt: new Date(),
            processingTimeMs: Date.now() - startTime,
            responseStatus: 200,
          }
        });
        
        res.json({
          success: true,
          message: 'Webhook received (empty payload)',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // Update log to processing status
      await this.prisma.webhookLog.update({
        where: { id: webhookLog.id },
        data: { status: 'processing' }
      });

      // Step 2: Discover new keys from payload (for future mappings)
      const tenantId = payload.tenantId || null;
      const discoveredMappings = await this.webhookMappingService.discoverKeys(payload, tenantId);
      
      if (discoveredMappings.length > 0) {
        this.logger.log(`[WebhookLog ${webhookLog.id}] Discovered ${discoveredMappings.length} new webhook keys`);
      }

      // Step 3: Process the sale using the new service with detailed logging
      const result = await this.webhookSalesService.processWebhookSale(payload, tenantId, webhookLog.id);

      // Update webhook log with results
      await this.prisma.webhookLog.update({
        where: { id: webhookLog.id },
        data: {
          status: result.success ? 'success' : 'error',
          error: result.error || null,
          identifierField: result.identifierField || null,
          identifierValue: result.identifierValue || null,
          orderId: result.orderId || null,
          isNewOrder: result.isNew || null,
          mappingsUsed: result.mappingsUsed || null,
          mappedData: result.mappedData || null,
          processedAt: new Date(),
          processingTimeMs: Date.now() - startTime,
          responseStatus: 200,
        }
      });

      // Check if processing failed due to no mappings
      if (!result.success && result.message === 'No webhook mappings configured') {
        const response = {
          success: true,
          message: 'Webhook received but no field mappings configured',
          hint: 'Please configure field mappings at /dashboard/webhook-mappings',
          discoveredKeys: discoveredMappings.length,
          timestamp: new Date().toISOString(),
          webhookLogId: webhookLog.id,
        };
        
        await this.prisma.webhookLog.update({
          where: { id: webhookLog.id },
          data: { responseData: response }
        });
        
        res.json(response);
        return;
      }

      // Send success response
      const response = {
        success: true,
        message: result.isNew ? 'New sale created' : 'Existing sale updated',
        saleId: result.orderId,
        isNew: result.isNew,
        antifraudRun: result.antifraudRun,
        discoveredKeys: discoveredMappings.length,
        timestamp: new Date().toISOString(),
        webhookLogId: webhookLog.id,
      };
      
      await this.prisma.webhookLog.update({
        where: { id: webhookLog.id },
        data: { responseData: response }
      });
      
      res.json(response);

    } catch (error) {
      this.logger.error(`[WebhookLog ${webhookLog.id}] Error processing webhook:`, error);
      
      const response = {
        success: false,
        message: 'Webhook received but processing failed',
        error: error.message,
        timestamp: new Date().toISOString(),
        webhookLogId: webhookLog.id,
      };
      
      await this.prisma.webhookLog.update({
        where: { id: webhookLog.id },
        data: {
          status: 'error',
          error: error.message,
          processedAt: new Date(),
          processingTimeMs: Date.now() - startTime,
          responseStatus: 200,
          responseData: response,
        }
      });
      
      // Send error response but with 200 status to avoid webhook retry
      res.status(200).json(response);
    }
  }

  /**
   * Validate webhook signature using HMAC
   */
  private validateWebhookSignature(payload: any, signature: string): boolean {
    if (!signature) {
      return false;
    }

    const payloadString = JSON.stringify(payload);
    const expectedSignature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(payloadString)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature),
    );
  }

  // Event Status Mapping Endpoints
  
  @Get('event-mappings')
  @ApiOperation({ summary: 'Get all event status mappings' })
  async getEventMappings() {
    try {
      const mappings = await this.eventStatusMappingService.getActiveMappings();
      const unmappedEvents = await this.eventStatusMappingService.getUnmappedEvents();
      return {
        mappings,
        unmappedEvents,
        statusOptions: Object.values(OrderStatus),
      };
    } catch (error) {
      this.logger.error('Error fetching event mappings:', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('event-mappings')
  @ApiOperation({ summary: 'Create or update an event status mapping' })
  async upsertEventMapping(
    @Body() mapping: {
      event: string;
      correiosDescription?: string;
      internalStatus: OrderStatus;
      priority?: number;
    },
  ) {
    try {
      return await this.eventStatusMappingService.upsertMapping(mapping);
    } catch (error) {
      this.logger.error('Error creating event mapping:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete('event-mappings/:id')
  @ApiOperation({ summary: 'Delete an event status mapping' })
  async deleteEventMapping(@Param('id') id: string) {
    try {
      await this.eventStatusMappingService.deleteMapping(id);
      return { message: 'Event mapping deleted successfully' };
    } catch (error) {
      this.logger.error('Error deleting event mapping:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Put('event-mappings/:id/toggle')
  @ApiOperation({ summary: 'Toggle event mapping active status' })
  async toggleEventMapping(@Param('id') id: string) {
    try {
      return await this.eventStatusMappingService.toggleMapping(id);
    } catch (error) {
      this.logger.error('Error toggling event mapping:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('event-mappings/seed')
  @ApiOperation({ summary: 'Seed default event mappings' })
  async seedEventMappings() {
    try {
      await this.eventStatusMappingService.seedDefaultMappings();
      return { message: 'Default event mappings seeded successfully' };
    } catch (error) {
      this.logger.error('Error seeding event mappings:', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('event-mappings/from-unmapped/:unmappedId')
  @ApiOperation({ summary: 'Create a mapping from an unmapped event' })
  async createMappingFromUnmapped(
    @Param('unmappedId') unmappedId: string,
    @Body() body: { internalStatus: OrderStatus; priority?: number },
  ) {
    try {
      return await this.eventStatusMappingService.createMappingFromUnmapped(
        unmappedId,
        body.internalStatus,
        body.priority,
      );
    } catch (error) {
      this.logger.error('Error creating mapping from unmapped:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete('unmapped-events/:id')
  @ApiOperation({ summary: 'Delete an unmapped event record' })
  async deleteUnmappedEvent(@Param('id') id: string) {
    try {
      await this.eventStatusMappingService.deleteUnmappedEvent(id);
      return { message: 'Unmapped event deleted successfully' };
    } catch (error) {
      this.logger.error('Error deleting unmapped event:', error);
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('logs/:id/reprocess')
  @HttpCode(200)
  @ApiOperation({ summary: 'Reprocess a specific webhook log' })
  @ApiResponse({ status: 200, description: 'Webhook reprocessed successfully' })
  @ApiResponse({ status: 404, description: 'Webhook log not found' })
  async reprocessWebhookLog(@Param('id') id: string) {
    this.logger.log(`Reprocessing webhook log: ${id}`);
    
    try {
      // Find the webhook log
      const webhookLog = await this.prisma.webhookLog.findUnique({
        where: { id },
      });

      if (!webhookLog) {
        throw new HttpException('Webhook log not found', HttpStatus.NOT_FOUND);
      }

      // Extract the original payload
      const originalPayload = typeof webhookLog.payload === 'string' 
        ? JSON.parse(webhookLog.payload) 
        : webhookLog.payload;

      // Create a new webhook log entry for the reprocess
      const newLogId = crypto.randomUUID();
      const startTime = Date.now();

      await this.prisma.webhookLog.create({
        data: {
          id: newLogId,
          endpoint: webhookLog.endpoint,
          method: webhookLog.method,
          headers: { 
            'X-Reprocessed': 'true', 
            'X-Original-Log-Id': webhookLog.id,
            'X-Reprocessed-At': new Date().toISOString()
          },
          payload: originalPayload as any,
          status: 'processing',
          receivedAt: new Date(),
        },
      });

      // Process the webhook again
      const tenantId = originalPayload.tenantId || null;
      const result = await this.webhookSalesService.processWebhookSale(
        originalPayload, 
        tenantId, 
        newLogId
      );

      const processingTime = Date.now() - startTime;

      // Update the new log with results
      await this.prisma.webhookLog.update({
        where: { id: newLogId },
        data: {
          status: result.success ? 'success' : 'error',
          error: result.error || null,
          identifierField: result.identifierField || null,
          identifierValue: result.identifierValue || null,
          orderId: result.orderId || null,
          isNewOrder: result.isNew || false,
          mappingsUsed: result.mappingsUsed || null,
          mappedData: result.mappedData as any || null,
          processedAt: new Date(),
          processingTimeMs: processingTime,
          responseStatus: 200,
          responseData: result as any,
        },
      });

      return {
        success: result.success,
        message: result.success 
          ? (result.isNew ? 'Webhook reprocessed - new order created' : 'Webhook reprocessed - order updated')
          : 'Webhook reprocessing failed',
        originalLogId: webhookLog.id,
        newLogId,
        orderId: result.orderId,
        processingTimeMs: processingTime,
        result,
      };
    } catch (error) {
      this.logger.error(`Error reprocessing webhook log ${id}:`, error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `Failed to reprocess webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('event-mappings/reactivate-all')
  @ApiOperation({ summary: 'Reactivate all inactive mappings' })
  async reactivateAllMappings() {
    try {
      const result = await this.eventStatusMappingService.reactivateAllMappings();
      return { 
        message: 'Mappings reactivated successfully',
        count: result.count 
      };
    } catch (error) {
      this.logger.error('Error reactivating mappings:', error);
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('rerun/:orderNumber')
  @HttpCode(200)
  @ApiOperation({ summary: 'Rerun the latest webhook for a specific order' })
  @ApiResponse({ status: 200, description: 'Webhook rerun successful' })
  @ApiResponse({ status: 404, description: 'No webhook logs found for this order' })
  async rerunLatestWebhook(@Param('orderNumber') orderNumber: string, @Res() res: Response) {
    this.logger.log(`Rerun webhook requested for order: ${orderNumber}`);
    
    try {
      // Find the latest webhook log for this order
      const latestLog = await this.prisma.webhookLog.findFirst({
        where: {
          OR: [
            { identifierValue: orderNumber },
            { orderId: orderNumber },
            { payload: { path: ['sale', 'code'], equals: orderNumber } },
            { payload: { path: ['orderNumber'], equals: orderNumber } },
          ],
        },
        orderBy: { receivedAt: 'desc' },
      });

      if (!latestLog) {
        throw new HttpException(`No webhook logs found for order ${orderNumber}`, HttpStatus.NOT_FOUND);
      }

      this.logger.log(`Found webhook log ${latestLog.id} for order ${orderNumber}`);
      
      // Extract the original payload
      const originalPayload = typeof latestLog.payload === 'string' 
        ? JSON.parse(latestLog.payload) 
        : latestLog.payload;

      // Create a new webhook log entry for the rerun
      const webhookLogId = crypto.randomUUID();
      const startTime = Date.now();

      await this.prisma.webhookLog.create({
        data: {
          id: webhookLogId,
          endpoint: latestLog.endpoint || '/webhooks/shipments',
          method: 'POST',
          headers: { 'X-Rerun': 'true', 'X-Original-Log-Id': latestLog.id },
          payload: originalPayload as any,
          status: 'processing',
          receivedAt: new Date(),
        },
      });

      // Process the webhook again
      const result = await this.webhookSalesService.processWebhookSale(
        originalPayload, 
        undefined, 
        webhookLogId
      );

      const processingTime = Date.now() - startTime;

      // Update the log with results
      await this.prisma.webhookLog.update({
        where: { id: webhookLogId },
        data: {
          status: result.success ? 'success' : 'failed',
          error: result.error,
          identifierField: result.identifierField,
          identifierValue: result.identifierValue,
          orderId: result.orderId,
          isNewOrder: false, // Rerun should never create new orders
          mappingsUsed: result.mappingsUsed,
          mappedData: result.mappedData as any,
          processedAt: new Date(),
          processingTimeMs: processingTime,
          responseStatus: 200,
          responseData: result as any,
        },
      });

      // Send response
      res.set('Content-Type', 'application/json');
      res.json({
        success: true,
        message: `Webhook rerun successful for order ${orderNumber}`,
        webhookLogId,
        originalLogId: latestLog.id,
        orderId: result.orderId,
        processingTimeMs: processingTime,
        result,
      });
    } catch (error) {
      this.logger.error(`Error rerunning webhook for order ${orderNumber}:`, error);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `Failed to rerun webhook: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // The following methods have been moved to WebhookSalesService for better separation of concerns
  // and to implement the new business logic (anti-fraud, auto-assignment, field preservation)
}