import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not } from 'typeorm';
import { WebhookMapping } from './entities/webhook-mapping.entity';

@Injectable()
export class WebhookMappingService {
  private readonly logger = new Logger(WebhookMappingService.name);

  constructor(
    @InjectRepository(WebhookMapping)
    private webhookMappingRepository: Repository<WebhookMapping>,
  ) {}

  /**
   * Extract Zap identifier from email
   * Example: <EMAIL> -> zap10
   */
  extractZapFromEmail(email: string): string | null {
    if (!email) return null;
    
    // Match pattern: @(something).com
    const match = email.match(/@([^.]+)\.com/i);
    if (!match) return null;
    
    // Return the extracted identifier
    return match[1];
  }

  /**
   * Discover new keys from webhook payload and create unmapped entries
   */
  async discoverKeys(payload: any, tenantId?: string): Promise<WebhookMapping[]> {
    const discoveredKeys = this.extractAllLeafKeys(payload);
    const newMappings: WebhookMapping[] = [];

    for (const { key, value } of discoveredKeys) {
      // Check if mapping already exists (system-wide)
      const existingMapping = await this.webhookMappingRepository.findOne({
        where: { payloadKey: key },
      });

      if (!existingMapping) {
        const dataType = this.inferDataType(value);

        const newMapping = new WebhookMapping();
        newMapping.payloadKey = key;
        newMapping.entityColumn = null;
        newMapping.entityType = 'Sale';
        newMapping.dataType = dataType || null;
        newMapping.sampleValue = this.serializeSampleValue(value) || null;
        newMapping.isActive = true;
        newMapping.tenantId = null; // Always null for system-wide mappings
        
        const savedMapping = await this.webhookMappingRepository.save(newMapping);
        newMappings.push(savedMapping);
        this.logger.log(`Discovered new webhook key: ${key} (${dataType})`);
      }
    }

    return newMappings;
  }

  /**
   * List all webhook mappings
   * Webhook mappings are system-wide, not tenant-specific
   */
  async listMappings(tenantId?: string): Promise<WebhookMapping[]> {
    // Ignore tenant ID for webhook mappings - they're system-wide
    return this.webhookMappingRepository.find({
      order: { payloadKey: 'ASC' },
    });
  }

  /**
   * Update a mapping's entity column
   */
  async updateMapping(
    id: string,
    entityColumn: string,
    description?: string,
  ): Promise<WebhookMapping> {
    const mapping = await this.webhookMappingRepository.findOne({
      where: { id },
    });

    if (!mapping) {
      throw new Error(`Mapping with id ${id} not found`);
    }

    mapping.entityColumn = entityColumn;
    if (description !== undefined) {
      mapping.description = description;
    }

    return this.webhookMappingRepository.save(mapping);
  }

  /**
   * Get active mappings with entity columns set
   * Webhook mappings are system-wide, not tenant-specific
   */
  async getActiveMappings(tenantId?: string): Promise<WebhookMapping[]> {
    // Ignore tenant ID for webhook mappings - they're system-wide
    return this.webhookMappingRepository.find({
      where: {
        isActive: true,
        entityColumn: Not(IsNull()),  // Use Not(IsNull()) for nullable columns
      },
    });
  }

  /**
   * Build entity object from payload using mappings
   */
  buildEntityObject(payload: any, mappings: WebhookMapping[]): any {
    const entityObject: any = {};

    this.logger.log(`Building entity object from ${mappings.length} mappings`);

    // Group mappings by entity column to handle multiple fields mapping to same column
    const mappingsByColumn = new Map<string, WebhookMapping[]>();
    
    for (const mapping of mappings) {
      if (mapping.entityColumn && mapping.entityColumn !== '_ignore') {
        if (!mappingsByColumn.has(mapping.entityColumn)) {
          mappingsByColumn.set(mapping.entityColumn, []);
        }
        mappingsByColumn.get(mapping.entityColumn)!.push(mapping);
      }
    }

    // Process each entity column with its potential multiple source fields
    for (const [entityColumn, columnMappings] of mappingsByColumn) {
      let valueFound = false;
      
      // Sort mappings by payloadKey to ensure consistent priority
      // Fields that appear earlier in alphabetical order get priority
      const sortedMappings = columnMappings.sort((a, b) => a.payloadKey.localeCompare(b.payloadKey));
      
      this.logger.log(`Processing ${sortedMappings.length} mappings for column: ${entityColumn}`);
      
      for (const mapping of sortedMappings) {
        const value = this.getNestedValue(payload, mapping.payloadKey);
        
        this.logger.log(`Trying mapping: ${mapping.payloadKey} -> ${mapping.entityColumn}, value: ${JSON.stringify(value)?.substring(0, 100)}`);
        
        // Use the first non-empty value found
        if (value !== undefined && value !== null && value !== '') {
          // TypeScript guard - we know entityColumn is not null here because of the outer check
          const entityColumnValue = mapping.entityColumn!;
          
          // Handle special entity columns
          if (entityColumnValue.startsWith('_')) {
            // Special handling for product/kit mappings
            if (!entityObject._special) {
              entityObject._special = {};
            }
            entityObject._special[entityColumnValue] = value;
            this.logger.log(`Added special mapping: _special.${entityColumnValue} = ${value}`);
          } else if (entityColumnValue.includes('.')) {
            // Handle nested entity columns like 'address.street'
            const convertedValue = this.convertValue(value, mapping.dataType || 'string');
            this.setNestedValue(entityObject, entityColumnValue, convertedValue);
            this.logger.log(`Added nested mapping: ${entityColumnValue} = ${convertedValue}`);
          } else {
            // Special handling for zapSourceId field - extract from email
            if (entityColumnValue === 'zapSourceId' && typeof value === 'string' && value.includes('@')) {
              const zapIdentifier = this.extractZapFromEmail(value);
              if (zapIdentifier) {
                entityObject[entityColumnValue] = zapIdentifier;
                this.logger.log(`Extracted Zap identifier: ${zapIdentifier} from email: ${value}`);
              }
            } else {
              // Use 'string' as default if dataType is null
              const convertedValue = this.convertValue(value, mapping.dataType || 'string');
              entityObject[entityColumnValue] = convertedValue;
              this.logger.log(`Added direct mapping: ${entityColumnValue} = ${convertedValue} (from ${mapping.payloadKey})`);
            }
          }
          
          valueFound = true;
          break; // Stop checking other fields once we found a value
        } else {
          this.logger.debug(`No value or empty value for key: ${mapping.payloadKey}`);
        }
      }
      
      if (!valueFound) {
        this.logger.warn(`No value found for entity column ${entityColumn} from any of the mapped fields: ${sortedMappings.map(m => m.payloadKey).join(', ')}`);
      }
    }

    this.logger.log(`Built entity object: ${JSON.stringify(entityObject)}`);
    return entityObject;
  }

  /**
   * Get value from nested object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    const keys = path.split('.');
    let value = obj;
    
    this.logger.debug(`Getting nested value for path: ${path}`);
    
    for (const key of keys) {
      // Handle array notation like 'offers[0]'
      const arrayMatch = key.match(/^(.+)\[(\d+)\]$/);
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch;
        value = value?.[arrayKey]?.[parseInt(index)];
        this.logger.debug(`Array access: ${arrayKey}[${index}] = ${JSON.stringify(value)?.substring(0, 50)}`);
      } else {
        value = value?.[key];
        this.logger.debug(`Object access: ${key} = ${JSON.stringify(value)?.substring(0, 50)}`);
      }
      
      if (value === undefined) {
        this.logger.debug(`Value undefined at key: ${key}`);
        return undefined;
      }
    }
    
    return value;
  }

  /**
   * Set value in nested object using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Extract events array if present
   */
  extractEvents(payload: any): any[] {
    // Common event field names
    const eventFieldNames = ['events', 'eventos', 'historico', 'tracking', 'history'];
    
    for (const fieldName of eventFieldNames) {
      if (payload[fieldName] && Array.isArray(payload[fieldName])) {
        return payload[fieldName];
      }
    }

    return [];
  }

  /**
   * Helper: Extract only leaf keys (actual values, not objects/arrays)
   */
  private extractAllLeafKeys(payload: any, prefix = ''): Array<{key: string, value: any}> {
    if (typeof payload !== 'object' || payload === null) {
      return [];
    }

    const leafKeys: Array<{key: string, value: any}> = [];
    
    for (const key in payload) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      const value = payload[key];
      
      if (Array.isArray(value)) {
        // For arrays, we'll track it as a leaf with array indicator
        leafKeys.push({ key: fullKey, value: `[Array with ${value.length} items]` });
        // Optionally, process first item to get structure
        if (value.length > 0 && typeof value[0] === 'object') {
          // Extract keys from first array item with [0] notation
          leafKeys.push(...this.extractAllLeafKeys(value[0], `${fullKey}[0]`));
        }
      } else if (typeof value === 'object' && value !== null) {
        // For objects, don't add the object itself, just recurse into it
        leafKeys.push(...this.extractAllLeafKeys(value, fullKey));
      } else {
        // For primitive values (string, number, boolean, null)
        leafKeys.push({ key: fullKey, value });
      }
    }
    
    return leafKeys;
  }

  /**
   * Helper: Infer data type from value
   */
  private inferDataType(value: any): string {
    if (value === null || value === undefined) {
      return 'string';
    }

    if (typeof value === 'string') {
      // Check if it's a date string
      if (this.isISODateString(value)) {
        return 'date';
      }
      return 'string';
    }

    if (typeof value === 'number') {
      return 'number';
    }

    if (typeof value === 'boolean') {
      return 'boolean';
    }

    if (typeof value === 'object') {
      return 'json';
    }

    return 'string';
  }

  /**
   * Helper: Serialize sample value for storage
   */
  private serializeSampleValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }

    if (typeof value === 'object') {
      return JSON.stringify(value).substring(0, 500);
    }

    return String(value).substring(0, 500);
  }

  /**
   * Helper: Convert value based on data type
   */
  private convertValue(value: any, dataType: string): any {
    if (value === null || value === undefined) {
      return null;
    }

    switch (dataType) {
      case 'date':
        if (typeof value === 'string' && this.isISODateString(value)) {
          return new Date(value);
        }
        return value;

      case 'number':
        const num = Number(value);
        // If converting for 'total' field and value seems to be in cents, convert to decimal
        if (!isNaN(num) && num > 1000) {
          // Likely in cents, convert to decimal (R$ format)
          return num / 100;
        }
        return isNaN(num) ? value : num;

      case 'boolean':
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true' || value === '1';
        }
        return Boolean(value);

      case 'json':
        if (typeof value === 'string') {
          try {
            return JSON.parse(value);
          } catch {
            return value;
          }
        }
        return value;

      default:
        return value;
    }
  }

  /**
   * Helper: Check if string is ISO date
   */
  private isISODateString(value: string): boolean {
    const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    return isoDateRegex.test(value);
  }

  /**
   * Clean up webhook mappings that have JSON objects in sampleValue
   */
  async cleanupJsonSampleValues(): Promise<{ updated: number; deleted: number }> {
    const allMappings = await this.webhookMappingRepository.find();
    let updated = 0;
    let deleted = 0;

    for (const mapping of allMappings) {
      const sampleValue = mapping.sampleValue?.trim();
      
      if (sampleValue && sampleValue.startsWith('{')) {
        try {
          // Parse the JSON and check if it's a complex object
          const parsed = JSON.parse(sampleValue);
          
          // Check if this object has nested properties (leaf fields)
          const hasLeafFields = Object.values(parsed).some(val => 
            typeof val !== 'object' || val === null
          );
          
          if (!hasLeafFields) {
            // This is a parent object with only nested objects, delete it
            await this.webhookMappingRepository.delete(mapping.id);
            this.logger.log(`Deleted parent object mapping: ${mapping.payloadKey}`);
            deleted++;
          } else {
            // Update to show it's an object type
            mapping.sampleValue = '[Object]';
            mapping.dataType = 'object';
            await this.webhookMappingRepository.save(mapping);
            updated++;
          }
        } catch (e) {
          // If can't parse, just update to [Object]
          mapping.sampleValue = '[Object]';
          await this.webhookMappingRepository.save(mapping);
          updated++;
        }
      } else if (sampleValue && sampleValue.startsWith('[')) {
        // For arrays, update the display value
        mapping.sampleValue = '[Array]';
        mapping.dataType = 'array';
        await this.webhookMappingRepository.save(mapping);
        updated++;
      }
    }

    return { updated, deleted };
  }

  /**
   * Delete a specific mapping
   */
  async deleteMapping(id: string): Promise<void> {
    const result = await this.webhookMappingRepository.delete(id);
    if (result.affected === 0) {
      throw new Error(`Mapping with id ${id} not found`);
    }
    this.logger.log(`Deleted mapping with id: ${id}`);
  }

  /**
   * Bulk delete mappings
   */
  async bulkDeleteMappings(ids: string[]): Promise<void> {
    const result = await this.webhookMappingRepository.delete(ids);
    this.logger.log(`Bulk deleted ${result.affected} mappings`);
  }
}