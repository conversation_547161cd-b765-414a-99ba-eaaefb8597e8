import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { WebhookMappingService } from './webhook-mapping.service';
import { EventStatusMappingService } from './event-status-mapping.service';
import { AntifraudService } from '../antifraud/antifraud.service';
import { ZapsService } from '../zaps/zaps.service';
import { OrderStatus, RiskLevel, DuplicateStatus } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class WebhookSalesService {
  private readonly logger = new Logger(WebhookSalesService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly webhookMappingService: WebhookMappingService,
    private readonly eventStatusMappingService: EventStatusMappingService,
    private readonly antifraudService: AntifraudService,
    private readonly zapsService: ZapsService,
  ) {}

  /**
   * Process webhook data and create/update sales
   */
  async processWebhookSale(payload: any, tenantId?: string, webhookLogId?: string) {
    // Check if this is an event-based status update
    const event = payload.event;
    const correiosDescription = payload.correios_description || payload.sale?.correios_description;
    
    if (event) {
      this.logger.log(`[WebhookLog ${webhookLogId}] Received event: ${event}, description: ${correiosDescription}`);
      
      // Get the mapped internal status based on event and description
      const mappedStatus = await this.eventStatusMappingService.getInternalStatus(event, correiosDescription, payload);
      
      if (mappedStatus) {
        this.logger.log(`[WebhookLog ${webhookLogId}] Event mapping found: ${event} → ${mappedStatus}`);
        // Override any status in the payload with the event-mapped status
        if (payload.sale) {
          payload.sale.status = mappedStatus;
        } else {
          payload.status = mappedStatus;
        }
      }
    }
    // Step 1: Get active mappings
    const activeMappings = await this.webhookMappingService.getActiveMappings(tenantId);
    
    if (activeMappings.length === 0) {
      this.logger.warn(`[WebhookLog ${webhookLogId}] No active mappings found - webhook data will not be processed`);
      return {
        success: false,
        message: 'No webhook mappings configured',
        mappingsUsed: 0,
      };
    }

    this.logger.log(`[WebhookLog ${webhookLogId}] Found ${activeMappings.length} active mappings`);

    // Step 2: Build entity object from mapped fields
    const mappedData = this.webhookMappingService.buildEntityObject(payload, activeMappings);
    
    // Step 2.5: Preserve event-mapped status if present
    let eventMappedStatus: OrderStatus | null = null;
    if (event) {
      eventMappedStatus = await this.eventStatusMappingService.getInternalStatus(event, correiosDescription, payload);
      if (eventMappedStatus) {
        this.logger.log(`[WebhookLog ${webhookLogId}] Preserving event-mapped status: ${eventMappedStatus}`);
        mappedData.status = eventMappedStatus;
      }
    }
    
    // Log the mapped data for debugging
    this.logger.log(`[WebhookLog ${webhookLogId}] Mapped data from webhook: ${JSON.stringify(mappedData)}`);
    
    // Step 2.6: Process Zap source if mapped
    if (mappedData.zapSourceId) {
      this.logger.log(`[WebhookLog ${webhookLogId}] Processing Zap source: ${mappedData.zapSourceId}`);
      
      // If no tenantId provided, try to get it from the seller
      let effectiveTenantId = tenantId;
      if (!effectiveTenantId && mappedData.sellerId) {
        const seller = await this.prisma.user.findUnique({
          where: { id: mappedData.sellerId },
          select: { tenantId: true }
        });
        if (seller) {
          effectiveTenantId = seller.tenantId;
          this.logger.log(`[WebhookLog ${webhookLogId}] Using seller's tenantId: ${effectiveTenantId}`);
        }
      }
      
      // If still no tenantId, try to get it from sellerEmail
      if (!effectiveTenantId && mappedData.sellerEmail) {
        const seller = await this.prisma.user.findFirst({
          where: { 
            email: mappedData.sellerEmail,
            role: 'VENDEDOR'
          },
          select: { tenantId: true }
        });
        if (seller) {
          effectiveTenantId = seller.tenantId;
          this.logger.log(`[WebhookLog ${webhookLogId}] Using seller's tenantId from email: ${effectiveTenantId}`);
        }
      }
      
      if (effectiveTenantId) {
        try {
          // The webhook mapping already extracted the Zap identifier (e.g., "zap10")
          // Now we need to find or create the Zap entity
          const zap = await this.zapsService.findOrCreateZap(mappedData.zapSourceId, effectiveTenantId);
          // Replace the identifier with the actual Zap ID
          mappedData.zapSourceId = zap.id;
          this.logger.log(`[WebhookLog ${webhookLogId}] Mapped to Zap entity: ${zap.name} (${zap.id})`);
        } catch (error) {
          this.logger.error(`[WebhookLog ${webhookLogId}] Error processing Zap source:`, error);
          // Remove invalid zapSourceId to prevent order creation failure
          delete mappedData.zapSourceId;
        }
      } else {
        this.logger.warn(`[WebhookLog ${webhookLogId}] Cannot process Zap source without tenantId yet, will try again when creating order`);
        // Keep the zapSourceId as a string for now, will be processed when order is created
        // DO NOT delete it - we'll try again later when we have the seller's tenantId
      }
    }
    
    // Step 3: Find identifier field
    const identifier = this.findIdentifier(mappedData, payload);
    if (!identifier) {
      const error = 'No identifier field found (need id, orderNumber, or similar)';
      this.logger.error(`[WebhookLog ${webhookLogId}] ${error}`);
      this.logger.error(`[WebhookLog ${webhookLogId}] MappedData: ${JSON.stringify(mappedData)}`);
      this.logger.error(`[WebhookLog ${webhookLogId}] Payload: ${JSON.stringify(payload)}`);
      
      return {
        success: false,
        message: error,
        error: error,
        mappingsUsed: activeMappings.length,
        mappedData: mappedData,
      };
    }

    this.logger.log(`[WebhookLog ${webhookLogId}] Using identifier: ${identifier.field} = ${identifier.value}`);

    // Step 4: Check if order exists
    const existingOrder = await this.findExistingOrder(identifier, tenantId);
    
    let result;
    let isNew = false;
    
    if (existingOrder) {
      this.logger.log(`[WebhookLog ${webhookLogId}] Found existing order: ${existingOrder.id}`);
      // Update existing order - preserve local fields
      result = await this.updateExistingOrder(existingOrder, mappedData, event, correiosDescription);
      this.logger.log(`[WebhookLog ${webhookLogId}] Updated existing order: ${result.id}`);
    } else {
      isNew = true;
      this.logger.log(`[WebhookLog ${webhookLogId}] Creating new order`);
      // Create new order with anti-fraud check
      result = await this.createNewOrder(mappedData, tenantId, webhookLogId, event, correiosDescription);
      this.logger.log(`[WebhookLog ${webhookLogId}] Created new order: ${result.id} with anti-fraud check`);
    }

    // Step 5: Process order items (products/kits) if this is a new order
    if (isNew) {
      this.logger.log(`[WebhookLog ${webhookLogId}] Processing order items for new order`);
      await this.processOrderItems(result.id, payload, tenantId);
    }

    // Step 6: Process tracking events if present
    const events = this.webhookMappingService.extractEvents(payload);
    if (events.length > 0) {
      this.logger.log(`[WebhookLog ${webhookLogId}] Processing ${events.length} tracking events`);
      await this.updateTrackingInfo(result.id, events);
    }

    return {
      success: true,
      orderId: result.id,
      isNew: isNew,
      antifraudRun: isNew,
      identifierField: identifier.field,
      identifierValue: identifier.value,
      mappingsUsed: activeMappings.length,
      mappedData: mappedData,
    };
  }

  /**
   * Find identifier in webhook data
   */
  private findIdentifier(mappedData: any, originalPayload: any): { field: string; value: any } | null {
    // Priority order for identifiers (skip 'id' as it's a primary key)
    const identifierFields = ['orderNumber', 'externalId', 'saleId', 'pedidoId'];

    // First check mapped data
    for (const field of identifierFields) {
      if (mappedData[field]) {
        return { field, value: String(mappedData[field]) };
      }
    }

    // Check if 'id' field contains a sale code (not a UUID)
    if (mappedData.id && !mappedData.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // If 'id' is not a UUID, use it as orderNumber
      return { field: 'orderNumber', value: String(mappedData.id) };
    }

    // Check original payload for common sale/order identifiers
    const payloadIdentifiers = [
      originalPayload.sale?.code,
      originalPayload.sale?.id,
      originalPayload.order?.number,
      originalPayload.order?.id,
      originalPayload.code,
      originalPayload.orderNumber,
      originalPayload.saleId,
      originalPayload.pedidoId,
    ];

    for (const value of payloadIdentifiers) {
      if (value) {
        return { field: 'orderNumber', value: String(value) };
      }
    }

    return null;
  }

  /**
   * Find existing order by identifier
   */
  private async findExistingOrder(identifier: { field: string; value: any }, tenantId?: string) {
    const where: any = {};
    
    if (identifier.field === 'id') {
      where.id = identifier.value;
    } else if (identifier.field === 'orderNumber') {
      where.orderNumber = identifier.value;
    } else {
      // For other identifiers, check if stored in observation or other fields
      where.OR = [
        { id: identifier.value },
        { orderNumber: identifier.value },
        { observation: { contains: `${identifier.field}:${identifier.value}` } },
      ];
    }

    if (tenantId) {
      where.tenantId = tenantId;
    }

    return await this.prisma.order.findFirst({ where });
  }

  /**
   * Update existing order - preserve local fields
   */
  private async updateExistingOrder(existingOrder: any, mappedData: any, event?: string, correiosDescription?: string) {
    // Fields that should NOT be overwritten by webhook updates (always preserved)
    const preserveFields = [
      'id',
      'collectorId',
      'commissionApprovalStatus',
      'commissionApproved',
      'paymentReceivedAmount',
      'paymentReceivedDate',
      'isDuplicate',
      'duplicateStatus',
      'riskScore',
      'riskLevel',
      'reviewedBy',
      'reviewedAt',
      'reviewDecision',
      'deletedAt',
      'deletedBy',
      'tenantId', // Never change tenant
      'sellerId', // Seller should not change after creation
    ];

    // Fields that should ALWAYS be updated from webhook (no protection)
    const alwaysUpdateFields = [
      'status',
      'statusCorreios',
      'lastContactDate',
      'observation',
    ];

    // Fields that follow "first value wins" protection
    // (only update if current value is empty/null)
    const protectedFields = [
      'customerName',
      'customerPhone',
      'customerCPF',
      'fullAddress',
      'total',
      'customerId',
      'nextPaymentDate',
      'zapId',
    ];

    // Remove preserved fields from update data
    const updateData = { ...mappedData };
    preserveFields.forEach(field => delete updateData[field]);

    // Remove nested objects and special fields that aren't valid Order fields
    delete updateData.address;
    delete updateData.tracking;
    delete updateData._special;
    delete updateData.sellerName;
    delete updateData.sellerEmail;
    delete updateData.sellerId;
    delete updateData.sellerCode;
    delete updateData.customerEmail; // Email is stored in Customer model, not Order
    
    // Remove any fields that start with underscore (special fields)
    Object.keys(updateData).forEach(key => {
      if (key.startsWith('_')) {
        delete updateData[key];
      }
    });

    // Fields that exist in Order model that can be updated
    const validOrderFields = [
      'orderNumber',
      'customerName',
      'customerPhone',
      'status',
      'statusCorreios',
      'total',
      'customerId',
      'nextPaymentDate',
      'lastContactDate',
      'zapId',
      'customerCPF',
      'fullAddress',
      'observation', // Allow observation updates from webhook
    ];

    // Filter to only include valid Order fields and apply protection logic
    const filteredUpdateData: any = {};
    validOrderFields.forEach(field => {
      if (updateData[field] !== undefined) {
        // Check if this field should be protected
        if (protectedFields.includes(field)) {
          // Only update if existing value is null, undefined, or empty string
          const currentValue = existingOrder[field];
          if (currentValue === null || currentValue === undefined || currentValue === '') {
            filteredUpdateData[field] = updateData[field];
            this.logger.log(`Field ${field} is empty, allowing update from webhook: ${updateData[field]}`);
          } else {
            this.logger.log(`Field ${field} already has value "${currentValue}", blocking webhook update attempt with "${updateData[field]}"`);
          }
        } else if (alwaysUpdateFields.includes(field)) {
          // Always update these fields
          filteredUpdateData[field] = updateData[field];
        } else {
          // For other fields (like orderNumber), apply normal logic
          filteredUpdateData[field] = updateData[field];
        }
      }
    });

    // IMPORTANT: Check if order is in a protected status - if so, prevent automatic status changes
    const protectedStatuses = [
      OrderStatus.DevolvidoCorreios,
      OrderStatus.Negociacao,
      OrderStatus.PagamentoPendente,
      OrderStatus.Frustrado,
      OrderStatus.Promessa,
      OrderStatus.Completo, // Finalizado
      OrderStatus.Recuperacao
    ];
    
    if (protectedStatuses.includes(existingOrder.status)) {
      this.logger.log(`Order ${existingOrder.id} is in protected status ${existingOrder.status} - preventing automatic status change`);
      delete filteredUpdateData.status; // Remove any status update
    } else {
      // Status mapping is handled by event status mapping service
      // Only validate that status is a valid enum value if present
      if (filteredUpdateData.status && typeof filteredUpdateData.status === 'string') {
        // Check if it's already a valid OrderStatus enum
        if (!Object.values(OrderStatus).includes(filteredUpdateData.status as OrderStatus)) {
          // If we have an event, try to map it to a valid status
          if (event) {
            const mappedStatus = await this.eventStatusMappingService.getInternalStatus(event, correiosDescription, mappedData);
            if (mappedStatus) {
              this.logger.log(`Remapping invalid status "${filteredUpdateData.status}" to event-mapped status: ${mappedStatus}`);
              filteredUpdateData.status = mappedStatus;
            } else {
              this.logger.warn(`Invalid status value: ${filteredUpdateData.status}, and no event mapping found. Skipping status update.`);
              delete filteredUpdateData.status; // Don't update status if we can't map it
            }
          } else {
            this.logger.warn(`Invalid status value: ${filteredUpdateData.status}, no event provided. Skipping status update.`);
            delete filteredUpdateData.status; // Don't update status if invalid
          }
        }
      }
    }

    // Convert total to Decimal if present
    if (filteredUpdateData.total !== undefined) {
      filteredUpdateData.total = Number(filteredUpdateData.total);
    }

    // Always update the updatedAt timestamp
    filteredUpdateData.updatedAt = new Date();
    
    // Store the last webhook event if present
    if (event) {
      filteredUpdateData.lastWebhookEvent = event;
    }

    // Log what fields are being updated for debugging
    this.logger.log(`Updating order ${existingOrder.id} with fields: ${Object.keys(filteredUpdateData).join(', ')}`);

    // Prepare activity log entry for webhook event
    if (event || (filteredUpdateData.status && filteredUpdateData.status !== existingOrder.status) || 
        (protectedStatuses.includes(existingOrder.status) && mappedData.status)) {
      const now = new Date();
      const dateStr = now.toLocaleDateString('pt-BR');
      const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
      
      let logEntry = `${dateStr} ${timeStr} - `;
      
      if (event) {
        logEntry += `Evento webhook: ${event}`;
        if (correiosDescription) {
          logEntry += ` - ${correiosDescription}`;
        }
      }
      
      // Check if status change was blocked due to protected status
      if (protectedStatuses.includes(existingOrder.status) && mappedData.status && mappedData.status !== existingOrder.status) {
        if (event) {
          logEntry += `\n${dateStr} ${timeStr} - `;
        }
        logEntry += `Mudança de status bloqueada: ${existingOrder.status} (mantido) - tentativa de alterar para ${mappedData.status} via webhook foi impedida`;
      }
      // Add status change if it happened
      else if (filteredUpdateData.status && filteredUpdateData.status !== existingOrder.status) {
        if (event) {
          logEntry += `\n${dateStr} ${timeStr} - `;
        }
        logEntry += `Status alterado: ${existingOrder.status} → ${filteredUpdateData.status} (via webhook)`;
      }
      
      // Append to existing observation
      filteredUpdateData.observation = existingOrder.observation 
        ? `${existingOrder.observation}\n${logEntry}`
        : logEntry;
    }

    // Process Zap source if we have one and it's not already a valid UUID
    if (filteredUpdateData.zapSourceId && typeof filteredUpdateData.zapSourceId === 'string' && !filteredUpdateData.zapSourceId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      try {
        this.logger.log(`Processing Zap source in updateExistingOrder: ${filteredUpdateData.zapSourceId} with tenantId: ${existingOrder.tenantId}`);
        const zap = await this.zapsService.findOrCreateZap(filteredUpdateData.zapSourceId, existingOrder.tenantId);
        filteredUpdateData.zapSourceId = zap.id;
        this.logger.log(`Successfully created/found Zap entity for update: ${zap.name} (${zap.id})`);
      } catch (error) {
        this.logger.error(`Error processing Zap source in updateExistingOrder:`, error);
        // Only delete if there's an actual error creating the Zap
        delete filteredUpdateData.zapSourceId;
      }
    }

    const updatedOrder = await this.prisma.order.update({
      where: { id: existingOrder.id },
      data: filteredUpdateData,
    });

    // Handle address components update with "first value wins" protection
    if (mappedData.address && Object.keys(mappedData.address).length > 0) {
      // Check if order has existing address components
      const existingAddressComponents = await this.prisma.orderAddressComponents.findUnique({
        where: { orderId: existingOrder.id }
      });

      if (!existingAddressComponents) {
        // No address components exist, create new ones
        await this.prisma.orderAddressComponents.create({
          data: {
            orderId: existingOrder.id,
            street: mappedData.address.street || '',
            streetNumber: mappedData.address.streetNumber || '',
            complement: mappedData.address.complement || null,
            neighborhood: mappedData.address.neighborhood || '',
            city: mappedData.address.city || '',
            state: mappedData.address.state || '',
            zipCode: mappedData.address.zipCode || '',
            streetNormalized: (mappedData.address.street || '').toLowerCase().trim(),
            streetSoundex: '', // These would be calculated by a proper function
            streetMetaphone: '',
            neighborhoodNorm: (mappedData.address.neighborhood || '').toLowerCase().trim(),
            neighborhoodSoundex: '',
            cityNormalized: (mappedData.address.city || '').toLowerCase().trim(),
          }
        });
        this.logger.log(`Created new address components for order ${existingOrder.id}`);
      } else {
        // Update only empty fields in existing address components
        const addressUpdateData: any = {};
        const addressFields = ['street', 'streetNumber', 'complement', 'neighborhood', 'city', 'state', 'zipCode'];
        
        addressFields.forEach(field => {
          const currentValue = existingAddressComponents[field];
          const newValue = mappedData.address[field];
          
          if (newValue !== undefined && (currentValue === null || currentValue === undefined || currentValue === '')) {
            addressUpdateData[field] = newValue;
            this.logger.log(`Address field ${field} is empty, allowing update from webhook: ${newValue}`);
          } else if (newValue !== undefined && currentValue !== null && currentValue !== '') {
            this.logger.log(`Address field ${field} already has value "${currentValue}", blocking webhook update attempt with "${newValue}"`);
          }
        });

        // Update normalized fields if base fields changed
        if (addressUpdateData.street) {
          addressUpdateData.streetNormalized = (addressUpdateData.street || '').toLowerCase().trim();
        }
        if (addressUpdateData.neighborhood) {
          addressUpdateData.neighborhoodNorm = (addressUpdateData.neighborhood || '').toLowerCase().trim();
        }
        if (addressUpdateData.city) {
          addressUpdateData.cityNormalized = (addressUpdateData.city || '').toLowerCase().trim();
        }

        if (Object.keys(addressUpdateData).length > 0) {
          await this.prisma.orderAddressComponents.update({
            where: { orderId: existingOrder.id },
            data: addressUpdateData
          });
          this.logger.log(`Updated address components for order ${existingOrder.id} with fields: ${Object.keys(addressUpdateData).join(', ')}`);
        }
      }
    }

    // Handle tracking update - tracking should ALWAYS be updated
    if (mappedData.tracking && mappedData.tracking.code) {
      await this.prisma.tracking.upsert({
        where: { orderId: existingOrder.id },
        update: {
          code: mappedData.tracking.code,
          status: mappedData.tracking.status || 'Em Trânsito',
          lastUpdate: new Date(),
          lastSync: new Date(),
          isDelivered: mappedData.tracking.status?.toLowerCase().includes('entreg') || false,
        },
        create: {
          orderId: existingOrder.id,
          code: mappedData.tracking.code,
          status: mappedData.tracking.status || 'Pendente',
          lastUpdate: new Date(),
          events: [],
          lastSync: new Date(),
          isDelivered: mappedData.tracking.status?.toLowerCase().includes('entreg') || false,
          hasAlert: false,
        }
      });
      this.logger.log(`Updated tracking for order ${existingOrder.id}: ${mappedData.tracking.code}`);
    }

    // Run anti-fraud check only if the order still requires review
    if (existingOrder.requiresReview !== false) {
      this.logger.log(`[updateExistingOrder] Running anti-fraud check for existing order ${existingOrder.id} (requiresReview: ${existingOrder.requiresReview})`);
      this.runAntifraudCheck(existingOrder.id).catch(error => {
        this.logger.error(`Anti-fraud check failed for order ${existingOrder.id}:`, error);
      });
    } else {
      this.logger.log(`[updateExistingOrder] Skipping anti-fraud check for order ${existingOrder.id} - already reviewed (requiresReview: false)`);
    }

    return updatedOrder;
  }

  /**
   * Create new order with automatic anti-fraud check
   */
  private async createNewOrder(mappedData: any, tenantId?: string, webhookLogId?: string, event?: string, correiosDescription?: string) {
    // Find seller ID based on various seller fields
    let sellerId = await this.findOrCreateSellerId(mappedData, tenantId);
    let sellerTenantId: string | undefined;
    
    if (!sellerId) {
      this.logger.warn(`Seller not found for order. Seller data: ${JSON.stringify({
        sellerName: mappedData.sellerName,
        sellerEmail: mappedData.sellerEmail,
        sellerId: mappedData.sellerId,
        sellerCode: mappedData.sellerCode,
      })}`);
      
      // Try to find a default seller or any seller in the system
      const defaultSeller = await this.prisma.user.findFirst({
        where: {
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        },
        orderBy: { createdAt: 'asc' }, // Get the first created seller
      });
      
      if (defaultSeller) {
        sellerId = defaultSeller.id;
        sellerTenantId = defaultSeller.tenantId;
        this.logger.log(`[WebhookLog ${webhookLogId}] Using default seller: ${defaultSeller.name} from tenant: ${defaultSeller.tenantId}`);
      } else {
        // If no seller exists, we need to handle this error
        throw new Error('No seller found in the system. At least one VENDEDOR user must exist.');
      }
    } else {
      // Get the tenant ID from the seller
      const seller = await this.prisma.user.findUnique({
        where: { id: sellerId },
        select: { tenantId: true, name: true }
      });
      if (seller) {
        sellerTenantId = seller.tenantId;
        this.logger.log(`[WebhookLog ${webhookLogId}] Found seller ${seller.name} with tenant: ${seller.tenantId}`);
      }
    }

    // Use the status from event mapping or default to Analise
    let orderStatus: OrderStatus = OrderStatus.Analise;
    if (mappedData.status) {
      // Status should already be mapped by event status mapping service
      if (Object.values(OrderStatus).includes(mappedData.status as OrderStatus)) {
        orderStatus = mappedData.status as OrderStatus;
      } else {
        this.logger.warn(`[WebhookLog ${webhookLogId}] Invalid status value: ${mappedData.status}, using default`);
      }
    }

    // Ensure required fields
    // IMPORTANT: Use seller's tenant ID to ensure proper data isolation
    const orderData: any = {
      ...mappedData,
      tenantId: sellerTenantId || tenantId || process.env.DEFAULT_TENANT_ID || 'default',
      status: orderStatus,
      customerName: mappedData.customerName || 'Cliente Webhook',
      customerPhone: mappedData.customerPhone || '',
      total: mappedData.total || 0,
      sellerId: sellerId, // Now guaranteed to have a value
      createdAt: mappedData.createdAt || new Date(),
    };

    // Debug logging for orderNumber
    this.logger.log(`[WebhookLog ${webhookLogId}] OrderNumber debug:`);
    this.logger.log(`[WebhookLog ${webhookLogId}] - mappedData.orderNumber: ${mappedData.orderNumber}`);
    this.logger.log(`[WebhookLog ${webhookLogId}] - mappedData.id: ${mappedData.id}`);
    this.logger.log(`[WebhookLog ${webhookLogId}] - orderData.orderNumber before: ${orderData.orderNumber}`);
    
    // Use the mapped orderNumber value
    if (mappedData.orderNumber) {
      orderData.orderNumber = mappedData.orderNumber;
      this.logger.log(`[WebhookLog ${webhookLogId}] Using mapped order number: ${orderData.orderNumber}`);
    } else {
      // If no orderNumber is mapped, don't generate one - this will cause validation error
      this.logger.error(`[WebhookLog ${webhookLogId}] No orderNumber mapped from webhook! Please map a field to 'orderNumber' in webhook mappings.`);
      throw new Error('No orderNumber mapped from webhook. Please configure webhook mapping to map a field to "orderNumber".');
    }

    // Process Zap source if we have one and couldn't process it earlier
    if (orderData.zapSourceId && typeof orderData.zapSourceId === 'string' && !orderData.zapSourceId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      const effectiveTenantId = sellerTenantId || tenantId || process.env.DEFAULT_TENANT_ID || 'default';
      // We should ALWAYS have a tenantId at this point since we set it above
      try {
        this.logger.log(`[WebhookLog ${webhookLogId}] Processing Zap source in createNewOrder: ${orderData.zapSourceId} with tenantId: ${effectiveTenantId}`);
        const zap = await this.zapsService.findOrCreateZap(orderData.zapSourceId, effectiveTenantId);
        orderData.zapSourceId = zap.id;
        this.logger.log(`[WebhookLog ${webhookLogId}] Successfully created/found Zap entity: ${zap.name} (${zap.id})`);
      } catch (error) {
        this.logger.error(`[WebhookLog ${webhookLogId}] Error processing Zap source in createNewOrder:`, error);
        // Only delete if there's an actual error creating the Zap
        // This should rarely happen since findOrCreateZap creates if not exists
        delete orderData.zapSourceId;
      }
    }

    // Auto-assign to an active operator from the seller's tenant
    const activeOperator = await this.findActiveOperator(sellerTenantId || tenantId);
    if (activeOperator) {
      orderData.collectorId = activeOperator.id;
      this.logger.log(`[WebhookLog ${webhookLogId}] Auto-assigned order to active cobrador: ${activeOperator.name} (${activeOperator.id})`);
    } else {
      // No active cobrador found - leave collectorId empty
      this.logger.warn(`[WebhookLog ${webhookLogId}] No active cobrador available. Order will be created without assignment.`);
      // Explicitly set to null to ensure it's not assigned
      orderData.collectorId = null;
    }

    // Extract nested data and unmapped fields
    const { 
      address, 
      tracking, 
      _special, 
      sellerName,
      sellerEmail,
      sellerId: sellerIdField,
      sellerCode,
      paymentMethod,
      vendedor,      // Remove this field - it's not in the Order model
      vendedorEmail, // Remove this field - it's not in the Order model
      ...orderDataWithoutNested 
    } = orderData;
    
    // Create the order with nested relations
    const createData: any = {
      ...orderDataWithoutNested,
      // Ensure sellerId is included (it's required for the order)
      sellerId: orderData.sellerId,
    };
    
    // Remove zapSourceId from createData if it's a string identifier (not a UUID)
    // It should have been converted to a Zap entity ID earlier, but if not, remove it
    if (createData.zapSourceId && typeof createData.zapSourceId === 'string' && !createData.zapSourceId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      this.logger.warn(`[WebhookLog ${webhookLogId}] Removing invalid zapSourceId: ${createData.zapSourceId} (not a valid UUID)`);
      delete createData.zapSourceId;
    }
    
    // Store vendor and payment info in observation field for now
    const additionalInfo: string[] = [];
    if (sellerName) additionalInfo.push(`Vendedor: ${sellerName}`);
    if (sellerEmail) additionalInfo.push(`Email: ${sellerEmail}`);
    if (sellerCode) additionalInfo.push(`Código: ${sellerCode}`);
    if (paymentMethod) additionalInfo.push(`Pagamento: ${paymentMethod}`);
    
    // Add webhook event log entry
    const now = new Date();
    const dateStr = now.toLocaleDateString('pt-BR');
    const timeStr = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    
    let logEntry = `${dateStr} ${timeStr} - Pedido criado via webhook`;
    if (event) {
      logEntry += ` - Evento: ${event}`;
      if (correiosDescription) {
        logEntry += ` - ${correiosDescription}`;
      }
    }
    logEntry += ` - Status inicial: ${orderStatus}`;
    
    if (additionalInfo.length > 0) {
      createData.observation = additionalInfo.join(' | ') + '\n' + logEntry;
    } else {
      createData.observation = logEntry;
    }
    
    // Add address components if present
    if (address && Object.keys(address).length > 0) {
      createData.addressComponents = {
        create: {
          street: address.street || '',
          streetNumber: address.streetNumber || '',
          complement: address.complement || null,
          neighborhood: address.neighborhood || '',
          city: address.city || '',
          state: address.state || '',
          zipCode: address.zipCode || '',
          streetNormalized: (address.street || '').toLowerCase().trim(),
          streetSoundex: '', // These would be calculated by a proper function
          streetMetaphone: '',
          neighborhoodNorm: (address.neighborhood || '').toLowerCase().trim(),
          neighborhoodSoundex: '',
          cityNormalized: (address.city || '').toLowerCase().trim(),
        },
      };
      
      // Build full address for the order
      createData.fullAddress = [
        address.street,
        address.streetNumber,
        address.complement,
        address.neighborhood,
        address.city,
        address.state,
        address.zipCode,
      ].filter(Boolean).join(', ');
    }
    
    // Add tracking if present
    if (tracking && tracking.code) {
      createData.tracking = {
        create: {
          code: tracking.code,
          status: tracking.status || 'Pendente',
          lastUpdate: new Date(),
          events: [],
          lastSync: new Date(),
          isDelivered: tracking.status?.toLowerCase().includes('entreg') || false,
          hasAlert: false,
        },
      };
    }
    
    // Create the order
    const newOrder = await this.prisma.order.create({
      data: createData,
    });

    // Always run anti-fraud check for new orders
    this.logger.log(`[WebhookLog ${webhookLogId}] Running anti-fraud check for new order`);
    this.runAntifraudCheck(newOrder.id).catch(error => {
      this.logger.error(`Anti-fraud check failed for order ${newOrder.id}:`, error);
    });

    return newOrder;
  }

  /**
   * Find an active operator for auto-assignment
   */
  private async findActiveOperator(tenantId?: string) {
    // Find ACTIVE operators ordered by least number of assigned orders - include admin and supervisor
    const operators = await this.prisma.user.findMany({
      where: {
        role: {
          in: ['COBRADOR', 'ADMIN', 'SUPERVISOR']
        },
        active: true, // Only consider active cobradores
        tenantId: tenantId || undefined,
      },
      include: {
        _count: {
          select: {
            ordersAsCollector: {
              where: {
                status: {
                  notIn: [OrderStatus.Completo, OrderStatus.Cancelado]
                }
              }
            }
          }
        }
      },
      orderBy: {
        ordersAsCollector: {
          _count: 'asc'
        }
      },
      take: 1,
    });

    if (operators.length === 0) {
      this.logger.warn(`No active cobradores found in tenant ${tenantId}. Order will not be assigned.`);
      return null;
    }

    return operators[0];
  }

  /**
   * Run anti-fraud check on new order
   */
  private async runAntifraudCheck(orderId: string) {
    try {
      const order = await this.prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: true,
          tracking: true,
        }
      });

      if (!order) return;

      // Process order for comprehensive duplicate check
      await this.antifraudService.processOrderForComprehensiveDuplicateCheck(
        order.tenantId,
        order.id,
        {
          customerCPF: order.customerCPF || undefined,
          fullAddress: order.fullAddress || undefined,
          customerPhone: order.customerPhone,
          customerName: order.customerName,
        }
      );
      
      // Assess order risk
      await this.antifraudService.assessOrderRisk(order.id, order.tenantId);

      // The antifraud methods update the order directly, so we just need to log
      this.logger.log(`Anti-fraud check completed for order ${orderId}`);
    } catch (error) {
      this.logger.error(`Failed to run anti-fraud check for order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Update tracking information
   */
  private async updateTrackingInfo(orderId: string, events: any[]) {
    const latestEvent = events[0];
    if (!latestEvent) return;

    const tracking = await this.prisma.tracking.upsert({
      where: {
        orderId: orderId,
      },
      update: {
        status: latestEvent.status || latestEvent.descricao || 'Em Trânsito',
        lastUpdate: new Date(),
        events: JSON.stringify(events),
        lastSync: new Date(),
      },
      create: {
        orderId: orderId,
        code: latestEvent.code || '',
        status: latestEvent.status || latestEvent.descricao || 'Em Trânsito',
        lastUpdate: new Date(),
        events: JSON.stringify(events),
        lastSync: new Date(),
      }
    });

    this.logger.log(`Updated tracking for order ${orderId}`);
  }

  /**
   * Process order items from webhook payload
   */
  private async processOrderItems(orderId: string, payload: any, tenantId?: string) {
    // Extract product/offer information from webhook
    const offers = this.extractOffers(payload);
    
    if (offers.length === 0) {
      this.logger.warn(`No products/offers found in webhook for order ${orderId}`);
      return;
    }

    for (const offer of offers) {
      try {
        // First try to find as a kit
        const kit = await this.findKitByWebhookData(offer, tenantId);
        
        if (kit) {
          // If it's a kit, add all kit items
          const kitItems = await this.prisma.kitItem.findMany({
            where: { kitId: kit.id },
            include: {
              productVariation: {
                include: {
                  product: true
                }
              }
            }
          });

          for (const kitItem of kitItems) {
            await this.prisma.orderItem.create({
              data: {
                orderId: orderId,
                productId: kitItem.productVariation.productId,
                productVariationId: kitItem.productVariationId,
                productName: `${kitItem.productVariation.product.name} (${kit.name})`,
                quantity: kitItem.quantity * (offer.quantity || 1),
                unitPrice: Number(kit.price) / kitItems.length, // Distribute kit price
              }
            });
          }
          
          this.logger.log(`Added kit ${kit.name} to order ${orderId}`);
        } else {
          // Try to find as a single product
          const product = await this.findProductByWebhookData(offer, tenantId);
          
          if (product) {
            // Create order item
            await this.prisma.orderItem.create({
              data: {
                orderId: orderId,
                productId: product.id,
                productName: product.name,
                quantity: offer.quantity || 1,
                unitPrice: offer.price || Number(product.price),
              }
            });
            
            this.logger.log(`Added product ${product.name} to order ${orderId}`);
          } else {
            // If neither kit nor product found, create placeholder
            const itemName = offer.productTitle || offer.title || 'Item Webhook';
            await this.prisma.orderItem.create({
              data: {
                orderId: orderId,
                productId: `webhook-${offer.productCode || offer.code || Date.now()}`,
                productName: itemName,
                quantity: offer.quantity || 1,
                unitPrice: offer.price || 0,
              }
            });
            
            this.logger.warn(`Kit/Product not found for ${itemName}, created placeholder item`);
          }
        }
      } catch (error) {
        this.logger.error(`Error processing order item for order ${orderId}:`, error);
      }
    }
  }

  /**
   * Extract offers/products from webhook payload
   */
  private extractOffers(payload: any): any[] {
    const offers: any[] = [];
    
    // Check common offer/product field locations
    const possiblePaths = [
      payload.offers,
      payload.sale?.offers,
      payload.products,
      payload.items,
      payload.order?.items,
    ];

    // Also check for single offer
    if (payload.offer_main || payload.sale?.offer_main) {
      const mainOffer = payload.offer_main || payload.sale?.offer_main;
      offers.push(this.normalizeOffer(mainOffer));
    }

    // Process arrays
    for (const path of possiblePaths) {
      if (Array.isArray(path)) {
        for (const item of path) {
          offers.push(this.normalizeOffer(item));
        }
      }
    }

    return offers;
  }

  /**
   * Normalize offer data from webhook
   */
  private normalizeOffer(offer: any): any {
    return {
      code: offer.code,
      productCode: offer.product?.code || offer.product_code,
      productId: offer.product?.id || offer.product_id,
      productTitle: offer.product?.title || offer.product_title || offer.title,
      title: offer.title,
      price: this.parsePrice(offer.price || offer.price_with_fee),
      quantity: offer.quantity || 1,
      type: offer.type,
    };
  }

  /**
   * Parse price from various formats
   */
  private parsePrice(price: any): number {
    if (typeof price === 'number') return price;
    if (typeof price === 'string') {
      // Remove currency symbols and convert
      const cleaned = price.replace(/[^0-9.,]/g, '').replace(',', '.');
      return parseFloat(cleaned) || 0;
    }
    return 0;
  }

  /**
   * Find product by webhook data
   */
  private async findProductByWebhookData(offer: any, tenantId?: string) {
    // Try to find by name first (most reliable)
    const productName = offer.productTitle || offer.title;
    
    if (productName) {
      const product = await this.prisma.product.findFirst({
        where: {
          name: {
            equals: productName,
            mode: 'insensitive',
          },
          tenantId: tenantId || undefined,
        }
      });
      
      if (product) return product;
    }

    // Try to find by product code if available
    if (offer.productCode) {
      const product = await this.prisma.product.findFirst({
        where: {
          OR: [
            { id: offer.productCode },
            { 
              name: {
                contains: offer.productCode,
                mode: 'insensitive',
              }
            },
          ],
          tenantId: tenantId || undefined,
        }
      });
      
      if (product) return product;
    }

    return null;
  }

  /**
   * Find or create seller ID based on various seller fields
   */
  private async findOrCreateSellerId(mappedData: any, tenantId?: string): Promise<string | null> {
    // Priority order: sellerId, sellerEmail, sellerCode, sellerName
    
    // 1. Try direct ID first
    if (mappedData.sellerId) {
      const seller = await this.prisma.user.findFirst({
        where: {
          id: mappedData.sellerId,
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        }
      });
      if (seller) return seller.id;
    }

    // 2. Try by email (most reliable after ID)
    if (mappedData.sellerEmail) {
      this.logger.log(`Looking for seller with email: ${mappedData.sellerEmail}`);
      
      // First try with tenant restriction
      let seller = await this.prisma.user.findFirst({
        where: {
          email: {
            equals: mappedData.sellerEmail,
            mode: 'insensitive',
          },
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        }
      });
      
      // If not found in specific tenant, search across all tenants
      if (!seller) {
        seller = await this.prisma.user.findFirst({
          where: {
            email: {
              equals: mappedData.sellerEmail,
              mode: 'insensitive',
            },
            role: 'VENDEDOR',
          }
        });
        if (seller) {
          this.logger.log(`Found seller by email in different tenant: ${seller.email} (tenant: ${seller.tenantId})`);
        } else {
          this.logger.warn(`No seller found with email: ${mappedData.sellerEmail}. Make sure a VENDEDOR user exists with this exact email.`);
        }
      } else {
        this.logger.log(`Found seller by email: ${seller.email} (tenant: ${seller.tenantId})`);
      }
      
      if (seller) return seller.id;
    }

    // 3. Try by code
    if (mappedData.sellerCode) {
      const seller = await this.prisma.user.findFirst({
        where: {
          OR: [
            { id: mappedData.sellerCode },
            { 
              name: {
                contains: mappedData.sellerCode,
                mode: 'insensitive',
              }
            },
          ],
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        }
      });
      if (seller) return seller.id;
    }

    // 4. Try by name
    if (mappedData.sellerName) {
      const seller = await this.prisma.user.findFirst({
        where: {
          OR: [
            { 
              name: {
                equals: mappedData.sellerName,
                mode: 'insensitive',
              }
            },
            { 
              email: {
                equals: mappedData.sellerName,
                mode: 'insensitive',
              }
            },
          ],
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        }
      });
      if (seller) return seller.id;
    }

    // 5. Fallback: try sellerId field if present
    if (mappedData.sellerId) {
      const seller = await this.prisma.user.findFirst({
        where: {
          id: mappedData.sellerId,
          role: 'VENDEDOR',
          tenantId: tenantId || undefined,
        }
      });
      if (seller) return seller.id;
    }

    // 6. If no seller found, try to create one if we have enough information
    if (mappedData.sellerEmail && mappedData.sellerName) {
      try {
        this.logger.log(`Creating new seller from webhook data: ${mappedData.sellerName} (${mappedData.sellerEmail})`);
        
        // Generate a temporary password (seller should reset it)
        const tempPassword = Math.random().toString(36).slice(-8) + 'Aa1!';
        const hashedPassword = await bcrypt.hash(tempPassword, 10);
        
        const newSeller = await this.prisma.user.create({
          data: {
            email: mappedData.sellerEmail.toLowerCase(),
            name: mappedData.sellerName,
            password: hashedPassword,
            role: 'VENDEDOR',
            tenantId: tenantId || process.env.DEFAULT_TENANT_ID || 'default',
            active: true,
          }
        });
        
        this.logger.log(`Created new seller: ${newSeller.name} (${newSeller.id})`);
        return newSeller.id;
      } catch (error) {
        // If creation fails (e.g., email already exists), log and continue
        this.logger.error(`Failed to create seller: ${error.message}`);
      }
    }

    return null;
  }

  /**
   * Find kit by webhook data
   */
  private async findKitByWebhookData(offer: any, tenantId?: string) {
    // Check if this is a kit/offer based on type or title
    const isKit = offer.type === 'MAIN' || 
                  offer.type === 'UPSELL' || 
                  (offer.title && offer.title !== offer.productTitle);
    
    if (!isKit) return null;
    
    // Try to find by kit name/title
    const kitName = offer.title || offer.productTitle;
    
    if (kitName) {
      const kit = await this.prisma.kit.findFirst({
        where: {
          name: {
            equals: kitName,
            mode: 'insensitive',
          },
          tenantId: tenantId || undefined,
        }
      });
      
      if (kit) return kit;
    }

    // Try to find by offer code
    if (offer.code) {
      const kit = await this.prisma.kit.findFirst({
        where: {
          OR: [
            { id: offer.code },
            { 
              name: {
                contains: offer.code,
                mode: 'insensitive',
              }
            },
          ],
          tenantId: tenantId || undefined,
        }
      });
      
      if (kit) return kit;
    }

    return null;
  }
}