import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderStatus } from '@prisma/client';

export interface EventMapping {
  event: string;
  correiosDescription?: string;
  internalStatus: OrderStatus;
  priority?: number;
}

@Injectable()
export class EventStatusMappingService {
  private readonly logger = new Logger(EventStatusMappingService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get the internal status based on event and description
   */
  async getInternalStatus(event: string, correiosDescription?: string, samplePayload?: any): Promise<OrderStatus | null> {
    // First try to find exact match (event + description)
    if (correiosDescription) {
      const exactMatch = await this.prisma.eventStatusMapping.findFirst({
        where: {
          event,
          correiosDescription,
          isActive: true,
        },
        orderBy: { priority: 'desc' },
      });

      if (exactMatch) {
        this.logger.log(`Found exact mapping: ${event} + "${correiosDescription}" → ${exactMatch.internalStatus}`);
        return exactMatch.internalStatus as OrderStatus;
      }
    }

    // Then try to find match by event only
    const eventMatch = await this.prisma.eventStatusMapping.findFirst({
      where: {
        event,
        correiosDescription: null,
        isActive: true,
      },
      orderBy: { priority: 'desc' },
    });

    if (eventMatch) {
      this.logger.log(`Found event mapping: ${event} → ${eventMatch.internalStatus}`);
      return eventMatch.internalStatus as OrderStatus;
    }

    // Try partial description match
    if (correiosDescription) {
      const partialMatches = await this.prisma.eventStatusMapping.findMany({
        where: {
          event,
          correiosDescription: {
            contains: correiosDescription,
            mode: 'insensitive',
          },
          isActive: true,
        },
        orderBy: { priority: 'desc' },
      });

      if (partialMatches.length > 0) {
        const match = partialMatches[0];
        this.logger.log(`Found partial description mapping: ${event} + "${correiosDescription}" → ${match.internalStatus}`);
        return match.internalStatus as OrderStatus;
      }
    }

    // No mapping found - track this unmapped combination
    this.logger.warn(`No mapping found for event: ${event}, description: ${correiosDescription}`);
    await this.trackUnmappedEvent(event, correiosDescription, samplePayload);
    return null;
  }

  /**
   * Track unmapped event+description combinations
   */
  async trackUnmappedEvent(event: string, correiosDescription?: string, samplePayload?: any) {
    try {
      const existing = await this.prisma.unmappedEventStatus.findUnique({
        where: {
          event_correiosDescription: {
            event,
            correiosDescription: correiosDescription || '',
          },
        },
      });

      if (existing) {
        // Update occurrence count and last seen date
        await this.prisma.unmappedEventStatus.update({
          where: { id: existing.id },
          data: {
            lastSeenAt: new Date(),
            occurrenceCount: { increment: 1 },
            // Update sample payload if not previously stored
            samplePayload: existing.samplePayload || samplePayload || undefined,
          },
        });
      } else {
        // Create new unmapped event record
        await this.prisma.unmappedEventStatus.create({
          data: {
            event,
            correiosDescription: correiosDescription || '',
            samplePayload: samplePayload || undefined,
          },
        });
      }
    } catch (error) {
      this.logger.error('Error tracking unmapped event:', error);
    }
  }

  /**
   * Create or update an event mapping
   */
  async upsertMapping(mapping: EventMapping) {
    const { event, correiosDescription, internalStatus, priority = 0 } = mapping;

    const existing = await this.prisma.eventStatusMapping.findFirst({
      where: {
        event,
        correiosDescription: correiosDescription || null,
      },
    });

    if (existing) {
      return await this.prisma.eventStatusMapping.update({
        where: { id: existing.id },
        data: {
          internalStatus,
          priority,
          isActive: true,
        },
      });
    } else {
      return await this.prisma.eventStatusMapping.create({
        data: {
          event,
          correiosDescription,
          internalStatus,
          priority,
        },
      });
    }
  }

  /**
   * Get all active mappings
   */
  async getActiveMappings() {
    // Return ALL mappings, not just active ones, so users can see and manage them
    return await this.prisma.eventStatusMapping.findMany({
      orderBy: [
        { event: 'asc' },
        { priority: 'desc' },
      ],
    });
  }

  /**
   * Seed default mappings
   */
  async seedDefaultMappings() {
    const defaultMappings: EventMapping[] = [
      // Delivery failures
      {
        event: 'INCORRECT_ADDRESS',
        correiosDescription: 'Objeto não entregue - endereço incorreto',
        internalStatus: OrderStatus.EntregaFalha,
        priority: 100,
      },
      {
        event: 'DELIVERY_FAILED',
        internalStatus: OrderStatus.EntregaFalha,
        priority: 90,
      },
      {
        event: 'FRUSTRATED',
        internalStatus: OrderStatus.Frustrado,
        priority: 90,
      },
      
      // In transit
      {
        event: 'IN_TRANSIT',
        internalStatus: OrderStatus.Transito,
        priority: 80,
      },
      {
        event: 'FORWARDED',
        internalStatus: OrderStatus.Transito,
        priority: 80,
      },
      
      // Delivered
      {
        event: 'DELIVERED',
        internalStatus: OrderStatus.Completo,
        priority: 100,
      },
      {
        event: 'CLIENT_RECEIVE_CONFIRMATION',
        internalStatus: OrderStatus.Completo,
        priority: 100,
      },
      
      // Processing
      {
        event: 'IN_PREPARATION',
        internalStatus: OrderStatus.Separacao,
        priority: 70,
      },
      {
        event: 'WAITING_POST',
        internalStatus: OrderStatus.Separacao,
        priority: 70,
      },
      
      // Cancelled
      {
        event: 'CANCELLED',
        internalStatus: OrderStatus.Cancelado,
        priority: 100,
      },
      
      // Return/Devolution
      {
        event: 'RETURN_REQUESTED',
        correiosDescription: 'Devolução solicitada',
        internalStatus: OrderStatus.DevolvidoCorreios,
        priority: 90,
      },
      {
        event: 'RETURNED',
        internalStatus: OrderStatus.DevolvidoCorreios,
        priority: 90,
      },
    ];

    for (const mapping of defaultMappings) {
      await this.upsertMapping(mapping);
    }

    this.logger.log(`Seeded ${defaultMappings.length} default event mappings`);
  }

  /**
   * Delete a mapping
   */
  async deleteMapping(id: string) {
    return await this.prisma.eventStatusMapping.delete({
      where: { id },
    });
  }

  /**
   * Toggle mapping active status
   */
  async toggleMapping(id: string) {
    const mapping = await this.prisma.eventStatusMapping.findUnique({
      where: { id },
    });

    if (!mapping) {
      throw new Error('Mapping not found');
    }

    return await this.prisma.eventStatusMapping.update({
      where: { id },
      data: { isActive: !mapping.isActive },
    });
  }

  /**
   * Get all unmapped event+description combinations
   */
  async getUnmappedEvents() {
    return await this.prisma.unmappedEventStatus.findMany({
      orderBy: [
        { occurrenceCount: 'desc' },
        { lastSeenAt: 'desc' },
      ],
    });
  }

  /**
   * Create mapping from unmapped event
   */
  async createMappingFromUnmapped(unmappedId: string, internalStatus: OrderStatus, priority: number = 0) {
    const unmapped = await this.prisma.unmappedEventStatus.findUnique({
      where: { id: unmappedId },
    });

    if (!unmapped) {
      throw new Error('Unmapped event not found');
    }

    // Create the mapping
    const mapping = await this.upsertMapping({
      event: unmapped.event,
      correiosDescription: unmapped.correiosDescription || undefined,
      internalStatus,
      priority,
    });

    // Delete the unmapped record
    await this.prisma.unmappedEventStatus.delete({
      where: { id: unmappedId },
    });

    return mapping;
  }

  /**
   * Delete an unmapped event record
   */
  async deleteUnmappedEvent(id: string) {
    return await this.prisma.unmappedEventStatus.delete({
      where: { id },
    });
  }

  /**
   * Reactivate all inactive mappings (recovery method)
   */
  async reactivateAllMappings() {
    const result = await this.prisma.eventStatusMapping.updateMany({
      where: { isActive: false },
      data: { isActive: true },
    });
    
    this.logger.log(`Reactivated ${result.count} inactive mappings`);
    return result;
  }
}