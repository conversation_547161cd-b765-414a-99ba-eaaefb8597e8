import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { AntifraudModule } from '../antifraud/antifraud.module';
import { ZapsModule } from '../zaps/zaps.module';
import { WebhookMapping } from './entities/webhook-mapping.entity';
import { WebhookMappingService } from './webhook-mapping.service';
import { WebhookSalesService } from './webhook-sales.service';
import { EventStatusMappingService } from './event-status-mapping.service';
import { ShipmentWebhookController } from './shipment-webhook.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([WebhookMapping]),
    ConfigModule,
    PrismaModule,
    AntifraudModule,
    ZapsModule,
  ],
  controllers: [
    ShipmentWebhookController,
  ],
  providers: [
    WebhookMappingService,
    WebhookSalesService,
    EventStatusMappingService,
  ],
  exports: [
    WebhookMappingService,
    WebhookSalesService,
    EventStatusMappingService,
  ],
})
export class WebhooksModule {}