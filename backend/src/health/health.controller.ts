import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PrismaService } from '../prisma/prisma.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private prisma: PrismaService) {}

  @Get()
  @ApiOperation({ summary: 'Verificar status da aplicação' })
  @ApiResponse({ 
    status: 200, 
    description: 'Aplicação funcionando corretamente',
    schema: {
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2023-01-01T00:00:00.000Z' },
        uptime: { type: 'number', example: 12345 },
        database: { type: 'string', example: 'connected' }
      }
    }
  })
  async check() {
    console.log('🏥 Health check endpoint called!');
    console.log('   - Method: GET');
    console.log('   - Path: /api/v1/health');
    console.log('   - Timestamp:', new Date().toISOString());
    console.log('   - Port:', process.env.PORT || 3000);
    
    // First, return basic health without database check
    const basicHealth = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        PORT: process.env.PORT,
        DATABASE_URL: process.env.DATABASE_URL ? 'configured' : 'missing',
        JWT_SECRET: process.env.JWT_SECRET ? 'configured' : 'missing',
        CORS_ORIGIN: process.env.CORS_ORIGIN,
      },
      version: process.env.npm_package_version || '1.0.0',
      node: process.version,
    };
    
    // Try database check, but don't fail the health check if it's not ready
    let databaseStatus = 'checking';
    let databaseError = null;
    
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      databaseStatus = 'connected';
      console.log('   - Database: ✅ Connected');
    } catch (error) {
      databaseStatus = 'disconnected';
      databaseError = error.message;
      console.error('   - Database: ❌ Error:', error.message);
      // Don't throw - just report the status
    }
    
    return {
      ...basicHealth,
      database: databaseStatus,
      databaseError,
    };
  }

  @Get('ready')
  @ApiOperation({ summary: 'Verificar se a aplicação está pronta para receber tráfego' })
  @ApiResponse({ status: 200, description: 'Aplicação pronta' })
  @ApiResponse({ status: 503, description: 'Aplicação não está pronta' })
  async ready() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return { status: 'ready' };
    } catch (error) {
      throw new Error('Database not ready');
    }
  }

  @Get('live')
  @ApiOperation({ summary: 'Verificar se a aplicação está viva' })
  @ApiResponse({ status: 200, description: 'Aplicação viva' })
  async live() {
    return { 
      status: 'alive',
      timestamp: new Date().toISOString()
    };
  }
}