import { Test, TestingModule } from '@nestjs/testing';
import { TrackingV2Service } from './tracking-v2.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationsService } from '../notifications/notifications.service';
import { CorreiosProvider } from './providers/correios.provider';
import { OrderStatus } from '@prisma/client';
import { NotFoundException } from '@nestjs/common';

describe('TrackingV2Service', () => {
  let service: TrackingV2Service;
  let prisma: PrismaService;
  let notificationsService: NotificationsService;
  let correiosProvider: CorreiosProvider;

  const mockPrismaService = {
    tracking: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      findMany: jest.fn(),
    },
    order: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    customer: {
      findUnique: jest.fn(),
    },
    orderAuditLog: {
      create: jest.fn(),
    },
    statusHistory: {
      create: jest.fn(),
    },
  };

  const mockNotificationsService = {
    sendNotification: jest.fn(),
  };

  const mockCorreiosProvider = {
    trackPackage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrackingV2Service,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: CorreiosProvider,
          useValue: mockCorreiosProvider,
        },
      ],
    }).compile();

    service = module.get<TrackingV2Service>(TrackingV2Service);
    prisma = module.get<PrismaService>(PrismaService);
    notificationsService = module.get<NotificationsService>(NotificationsService);
    correiosProvider = module.get<CorreiosProvider>(CorreiosProvider);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTracking', () => {
    it('should create tracking for an order', async () => {
      const orderId = 'order-123';
      const trackingCode = 'BR123456789BR';
      
      const mockOrder = {
        id: orderId,
        status: OrderStatus.Transito,
        customerId: 'customer-123',
      };

      const mockTracking = {
        id: 'tracking-123',
        orderId,
        code: trackingCode,
        status: 'Postado',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
        hasAlert: false,
      };

      mockPrismaService.order.findUnique.mockResolvedValue(mockOrder);
      mockPrismaService.tracking.create.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue({
        code: trackingCode,
        status: 'Postado',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
      });

      const result = await service.create({ orderId, code: trackingCode });

      expect(result).toEqual(mockTracking);
      expect(mockPrismaService.order.findUnique).toHaveBeenCalledWith({
        where: { id: orderId },
        include: { customer: true },
      });
      expect(mockPrismaService.tracking.create).toHaveBeenCalled();
    });

    it('should throw NotFoundException if order not found', async () => {
      mockPrismaService.order.findUnique.mockResolvedValue(null);

      await expect(
        service.create({ orderId: 'invalid-order', code: 'BR123456789BR' }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('syncTracking', () => {
    it('should sync tracking with Correios and update status', async () => {
      const trackingCode = 'BR123456789BR';
      
      const mockTracking = {
        id: 'tracking-123',
        code: trackingCode,
        orderId: 'order-123',
        status: 'Em trânsito',
        isDelivered: false,
        order: {
          id: 'order-123',
          status: OrderStatus.Transito,
          customerId: 'customer-123',
        },
      };

      const correiosResponse = {
        code: trackingCode,
        status: 'Entregue',
        lastUpdate: new Date(),
        events: [
          {
            date: new Date(),
            status: 'Entregue',
            location: 'São Paulo - SP',
            description: 'Objeto entregue ao destinatário',
          },
        ],
        isDelivered: true,
      };

      mockPrismaService.tracking.findFirst.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue(correiosResponse);
      mockPrismaService.tracking.update.mockResolvedValue({
        ...mockTracking,
        ...correiosResponse,
      });
      mockPrismaService.order.update.mockResolvedValue({
        ...mockTracking.order,
        status: OrderStatus.Completo,
      });

      const result = await service.syncOne(trackingCode);

      expect(result.status).toBe('Entregue');
      expect(result.isDelivered).toBe(true);
      expect(mockPrismaService.order.update).toHaveBeenCalledWith({
        where: { id: 'order-123' },
        data: { status: OrderStatus.Completo },
      });
    });

    it('should create alert for problematic status', async () => {
      const trackingCode = 'BR123456789BR';
      
      const mockTracking = {
        id: 'tracking-123',
        code: trackingCode,
        orderId: 'order-123',
        status: 'Em trânsito',
        hasAlert: false,
        order: {
          id: 'order-123',
          status: OrderStatus.Transito,
          customer: {
            id: 'customer-123',
            name: 'João Silva',
          },
        },
      };

      const correiosResponse = {
        code: trackingCode,
        status: 'Destinatário ausente',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
      };

      mockPrismaService.tracking.findFirst.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue(correiosResponse);
      mockPrismaService.tracking.update.mockResolvedValue({
        ...mockTracking,
        status: correiosResponse.status,
        hasAlert: true,
        alertReason: 'DESTINATÁRIO AUSENTE',
      });

      const result = await service.syncOne(trackingCode);

      expect(result.hasAlert).toBe(true);
      expect(result.alertReason).toBe('DESTINATÁRIO AUSENTE');
      expect(mockNotificationsService.sendNotification).toHaveBeenCalled();
    });
  });

  describe('findByCode', () => {
    it('should return tracking by code', async () => {
      const trackingCode = 'BR123456789BR';
      const mockTracking = {
        id: 'tracking-123',
        orderId: 'order-123',
        code: trackingCode,
        status: 'Em trânsito',
        events: [],
      };

      mockPrismaService.tracking.findUnique.mockResolvedValue(mockTracking);

      const result = await service.findByCode(trackingCode);

      expect(result).toEqual(mockTracking);
      expect(mockPrismaService.tracking.findUnique).toHaveBeenCalledWith({
        where: { code: trackingCode },
        include: {
          order: {
            include: {
              customer: true,
              items: true,
            },
          },
        },
      });
    });

    it('should throw NotFoundException if tracking not found', async () => {
      mockPrismaService.tracking.findUnique.mockResolvedValue(null);

      await expect(
        service.findByCode('BR999999999BR')
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('syncAllActiveTrackings', () => {
    it('should sync all active trackings', async () => {
      const activeTrackings = [
        {
          id: 'tracking-1',
          code: 'BR111111111BR',
          isDelivered: false,
        },
        {
          id: 'tracking-2',
          code: 'BR222222222BR',
          isDelivered: false,
        },
      ];

      mockPrismaService.tracking.findMany.mockResolvedValue(activeTrackings);
      
      // Mock syncTracking for each tracking
      jest.spyOn(service, 'syncOne').mockResolvedValue({} as any);

      await service.syncAll();

      expect(mockPrismaService.tracking.findMany).toHaveBeenCalledWith({
        where: {
          isDelivered: false,
          hasAlert: false,
        },
        select: {
          code: true,
        },
      });
      expect(service.syncOne).toHaveBeenCalledTimes(2);
    });
  });

  describe('getAlerts', () => {
    it('should return tracking alerts', async () => {
      const mockAlerts = [
        {
          id: 'tracking-1',
          code: 'BR111111111BR',
          hasAlert: true,
          alertReason: 'DESTINATÁRIO AUSENTE',
          order: {
            id: 'order-1',
            customerName: 'João Silva',
          },
        },
      ];

      mockPrismaService.tracking.findMany.mockResolvedValue(mockAlerts);

      const result = await service.getAlerts();

      expect(result).toEqual(mockAlerts);
      expect(mockPrismaService.tracking.findMany).toHaveBeenCalledWith({
        where: {
          hasAlert: true,
        },
        include: {
          order: {
            include: {
              customer: true,
            },
          },
        },
        orderBy: {
          lastUpdate: 'desc',
        },
      });
    });
  });
});