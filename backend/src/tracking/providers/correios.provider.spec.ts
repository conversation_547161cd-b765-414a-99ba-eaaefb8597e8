import { Test, TestingModule } from '@nestjs/testing';
import { CorreiosProvider } from './correios.provider';

describe('CorreiosProvider', () => {
  let provider: CorreiosProvider;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CorreiosProvider],
    }).compile();

    provider = module.get<CorreiosProvider>(CorreiosProvider);
  });

  describe('track', () => {
    it('should track a delivered package', async () => {
      const code = 'AB123456781BR';
      const result = await provider.track(code);

      expect(result.code).toBe(code);
      expect(result.isDelivered).toBe(true);
      expect(result.currentStatus).toContain('ENTREGUE');
      expect(result.events.length).toBeGreaterThan(0);
    });

    it('should track a package in transit', async () => {
      const code = 'CD987654322BR';
      const result = await provider.track(code);

      expect(result.code).toBe(code);
      expect(result.isDelivered).toBe(false);
      expect(result.currentStatus).toContain('TRANSFERÊNCIA');
      expect(result.events.length).toBeGreaterThan(0);
    });

    it('should detect critical status', async () => {
      const code = 'EF111222333BR';
      const result = await provider.track(code);

      expect(result.code).toBe(code);
      expect(result.isDelivered).toBe(false);
      expect(result.currentStatus).toContain('DESTINATÁRIO AUSENTE');
      expect(provider.hasAlertStatus(result.currentStatus)).toBe(true);
    });
  });

  describe('isValidTrackingCode', () => {
    it('should validate correct format', () => {
      expect(provider.isValidTrackingCode('AB123456789BR')).toBe(true);
      expect(provider.isValidTrackingCode('CD987654321BR')).toBe(true);
    });

    it('should reject invalid format', () => {
      expect(provider.isValidTrackingCode('123456789')).toBe(false);
      expect(provider.isValidTrackingCode('ABCDEFGHIJK')).toBe(false);
      expect(provider.isValidTrackingCode('AB12345678BR')).toBe(false); // Faltando 1 dígito
      expect(provider.isValidTrackingCode('AB1234567890BR')).toBe(false); // 1 dígito a mais
    });
  });

  describe('formatTrackingCode', () => {
    it('should format code correctly', () => {
      expect(provider.formatTrackingCode('ab123456789br')).toBe('AB123456789BR');
      expect(provider.formatTrackingCode('AB 123 456 789 BR')).toBe('AB123456789BR');
      expect(provider.formatTrackingCode(' AB123456789BR ')).toBe('AB123456789BR');
    });
  });

  describe('hasAlertStatus', () => {
    it('should detect critical statuses', () => {
      expect(provider.hasAlertStatus('FC - FLUXO CANCELADO')).toBe(true);
      expect(provider.hasAlertStatus('DESTINATÁRIO AUSENTE')).toBe(true);
      expect(provider.hasAlertStatus('DEVOLUÇÃO')).toBe(true);
      expect(provider.hasAlertStatus('EXTRAVIADO')).toBe(true);
    });

    it('should not alert on normal statuses', () => {
      expect(provider.hasAlertStatus('OBJETO POSTADO')).toBe(false);
      expect(provider.hasAlertStatus('EM TRANSFERÊNCIA')).toBe(false);
      expect(provider.hasAlertStatus('SAIU PARA ENTREGA')).toBe(false);
    });
  });

  describe('trackBatch', () => {
    it('should track multiple codes', async () => {
      const codes = ['AB123456781BR', 'CD987654322BR'];
      const results = await provider.trackBatch(codes);

      expect(results).toHaveLength(2);
      expect(results[0].code).toBe(codes[0]);
      expect(results[1].code).toBe(codes[1]);
    });
  });
});