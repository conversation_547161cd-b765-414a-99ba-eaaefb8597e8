import { Injectable, Logger } from '@nestjs/common';

export interface TrackingEvent {
  date: Date;
  location: string;
  status: string;
  description: string;
  details?: string;
}

export interface TrackingResult {
  code: string;
  isDelivered: boolean;
  currentStatus: string;
  lastUpdate: Date;
  events: TrackingEvent[];
  error?: string;
}

// Status críticos que geram alertas
export const CRITICAL_STATUS = [
  'FC - FLUXO CANCELADO',
  'LDE - LOCKER DESTINO CHEIO',
  'DESTINATÁRIO AUSENTE',
  'ENDEREÇO INCORRETO',
  'DEVOLUÇÃO',
  'EXTRAVIADO',
  'AVARIADO',
  'RECUSADO',
  'CARTEIRO NÃO ATENDIDO',
];

@Injectable()
export class CorreiosProvider {
  private readonly logger = new Logger(CorreiosProvider.name);

  /**
   * Rast<PERSON>ia um código dos Correios
   * NOTA: Esta é uma implementação mockada para desenvolvimento
   * Em produção, usar a API real dos Correios ou biblioteca como rastreio-correios
   */
  async track(trackingCode: string): Promise<TrackingResult> {
    try {
      this.logger.log(`[MOCK] Rastreando código: ${trackingCode}`);

      // Simula delay da API
      await this.delay(1000);

      // Gera eventos mockados baseados no código
      const mockEvents = this.generateMockEvents(trackingCode);
      
      if (mockEvents.length === 0) {
        throw new Error('Código de rastreamento não encontrado');
      }

      const lastEvent = mockEvents[0];
      const isDelivered = this.isDeliveryStatus(lastEvent.status);
      const hasAlert = this.hasAlertStatus(lastEvent.status);

      this.logger.debug(`[MOCK] Status atual: ${lastEvent.status}`);

      return {
        code: trackingCode,
        isDelivered,
        currentStatus: lastEvent.status,
        lastUpdate: lastEvent.date,
        events: mockEvents,
      };
    } catch (error: any) {
      this.logger.error(`[MOCK] Erro ao rastrear: ${error.message}`);
      
      return {
        code: trackingCode,
        isDelivered: false,
        currentStatus: 'ERRO',
        lastUpdate: new Date(),
        events: [],
        error: error.message,
      };
    }
  }

  /**
   * Rastreia múltiplos códigos em lote
   */
  async trackBatch(trackingCodes: string[]): Promise<TrackingResult[]> {
    const results: TrackingResult[] = [];
    
    // Processa em lotes de 5 para evitar sobrecarga
    const batchSize = 5;
    for (let i = 0; i < trackingCodes.length; i += batchSize) {
      const batch = trackingCodes.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(code => this.track(code))
      );
      results.push(...batchResults);
      
      // Delay entre lotes
      if (i + batchSize < trackingCodes.length) {
        await this.delay(500);
      }
    }
    
    return results;
  }

  /**
   * Verifica se o status indica entrega
   */
  isDeliveryStatus(status: string): boolean {
    const deliveryStatuses = [
      'ENTREGUE',
      'OBJETO ENTREGUE',
      'ENTREGA EFETUADA',
    ];
    
    return deliveryStatuses.some(s => 
      status.toUpperCase().includes(s)
    );
  }

  /**
   * Verifica se o status requer alerta
   */
  hasAlertStatus(status: string): boolean {
    return CRITICAL_STATUS.some(s => 
      status.toUpperCase().includes(s.toUpperCase())
    );
  }

  /**
   * Gera eventos mockados para desenvolvimento
   */
  private generateMockEvents(code: string): TrackingEvent[] {
    // Usa o código para gerar dados determinísticos
    const seed = code.charCodeAt(code.length - 1);
    const now = new Date();
    
    // Cenários baseados no último caractere do código
    if (code.endsWith('1') || code.endsWith('A')) {
      // Pedido entregue
      return [
        {
          date: new Date(now.getTime() - 1 * 60 * 60 * 1000),
          location: 'São Paulo/SP',
          status: 'OBJETO ENTREGUE AO DESTINATÁRIO',
          description: 'Objeto entregue ao destinatário',
          details: 'Recebido por: João Silva',
        },
        {
          date: new Date(now.getTime() - 6 * 60 * 60 * 1000),
          location: 'São Paulo/SP',
          status: 'SAIU PARA ENTREGA',
          description: 'Objeto saiu para entrega ao destinatário',
        },
        {
          date: new Date(now.getTime() - 24 * 60 * 60 * 1000),
          location: 'São Paulo/SP',
          status: 'OBJETO EM TRANSFERÊNCIA',
          description: 'Objeto em transferência - de Unidade de Distribuição em São Paulo/SP para Unidade de Distribuição em São Paulo/SP',
        },
        {
          date: new Date(now.getTime() - 48 * 60 * 60 * 1000),
          location: 'Curitiba/PR',
          status: 'OBJETO POSTADO',
          description: 'Objeto postado',
        },
      ];
    } else if (code.endsWith('2') || code.endsWith('B')) {
      // Em trânsito
      return [
        {
          date: new Date(now.getTime() - 3 * 60 * 60 * 1000),
          location: 'Rio de Janeiro/RJ',
          status: 'OBJETO EM TRANSFERÊNCIA',
          description: 'Objeto em transferência - de Unidade de Tratamento em São Paulo/SP para Unidade de Distribuição em Rio de Janeiro/RJ',
        },
        {
          date: new Date(now.getTime() - 24 * 60 * 60 * 1000),
          location: 'São Paulo/SP',
          status: 'OBJETO POSTADO',
          description: 'Objeto postado',
        },
      ];
    } else if (code.endsWith('3') || code.endsWith('C')) {
      // Destinatário ausente
      return [
        {
          date: new Date(now.getTime() - 2 * 60 * 60 * 1000),
          location: 'Belo Horizonte/MG',
          status: 'DESTINATÁRIO AUSENTE',
          description: 'Não foi possível entregar - destinatário ausente',
          details: 'Nova tentativa será realizada',
        },
        {
          date: new Date(now.getTime() - 8 * 60 * 60 * 1000),
          location: 'Belo Horizonte/MG',
          status: 'SAIU PARA ENTREGA',
          description: 'Objeto saiu para entrega ao destinatário',
        },
      ];
    } else if (code.endsWith('4') || code.endsWith('D')) {
      // Problema crítico
      return [
        {
          date: new Date(now.getTime() - 1 * 60 * 60 * 1000),
          location: 'Salvador/BA',
          status: 'FC - FLUXO CANCELADO',
          description: 'Fluxo de postagem cancelado',
          details: 'Entre em contato com os Correios',
        },
      ];
    } else {
      // Em processamento
      return [
        {
          date: new Date(now.getTime() - 12 * 60 * 60 * 1000),
          location: 'Porto Alegre/RS',
          status: 'OBJETO POSTADO',
          description: 'Objeto postado',
        },
      ];
    }
  }

  /**
   * Simula delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Formata código de rastreamento
   */
  formatTrackingCode(code: string): string {
    // Remove espaços e converte para maiúsculas
    return code.replace(/\s/g, '').toUpperCase();
  }

  /**
   * Valida formato do código
   */
  isValidTrackingCode(code: string): boolean {
    // Formato padrão: AA123456789BR
    const pattern = /^[A-Z]{2}\d{9}[A-Z]{2}$/;
    return pattern.test(this.formatTrackingCode(code));
  }
}