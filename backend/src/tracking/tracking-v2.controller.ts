import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { TrackingV2Service } from './tracking-v2.service';
import { CreateTrackingDto } from './dto/create-tracking.dto';
import { FilterTrackingDto } from './dto/filter-tracking.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('tracking')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TrackingV2Controller {
  constructor(private readonly trackingService: TrackingV2Service) {}

  // Criar novo rastreamento
  @Post()
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(@Body() createTrackingDto: CreateTrackingDto) {
    return this.trackingService.create(createTrackingDto);
  }

  // Listar rastreamentos com filtros
  @Get()
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async findAll(@Query() filters: FilterTrackingDto) {
    const trackings = await this.trackingService.findAll(filters);
    
    return {
      total: trackings.length,
      trackings: trackings.map(this.formatTracking),
    };
  }

  // Buscar por código
  @Get(':code')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findByCode(@Param('code') code: string) {
    const tracking = await this.trackingService.findByCode(code);
    return this.formatTrackingDetail(tracking);
  }

  // Listar alertas críticos
  @Get('alerts')
  @Roles('ADMIN', 'SUPERVISOR')
  async getAlerts() {
    const alerts = await this.trackingService.getAlerts();
    
    return {
      total: alerts.length,
      alerts: alerts.map(tracking => ({
        ...this.formatTracking(tracking),
        alertReason: tracking.alertReason,
      })),
    };
  }

  // Estatísticas
  @Get('stats')
  @Roles('ADMIN', 'SUPERVISOR')
  async getStats() {
    return this.trackingService.getStats();
  }

  // Forçar sincronização geral
  @Post('sync')
  @Roles('ADMIN')
  async syncAll() {
    await this.trackingService.syncAll();
    return { message: 'Sincronização iniciada' };
  }

  // Forçar sincronização de um código
  @Post('sync/:code')
  @Roles('ADMIN', 'SUPERVISOR')
  async syncOne(@Param('code') code: string) {
    const tracking = await this.trackingService.syncOne(code);
    return {
      message: 'Rastreamento sincronizado',
      tracking: this.formatTrackingDetail(tracking),
    };
  }

  // Formata tracking para listagem
  private formatTracking(tracking: any) {
    return {
      id: tracking.id,
      code: tracking.code,
      status: tracking.status,
      lastUpdate: tracking.lastUpdate,
      isDelivered: tracking.isDelivered,
      hasAlert: tracking.hasAlert,
      order: {
        id: tracking.order.id,
        customerName: tracking.order.customer?.name || tracking.order.customerName,
        total: tracking.order.total,
      },
    };
  }

  // Formata tracking completo
  private formatTrackingDetail(tracking: any) {
    return {
      id: tracking.id,
      code: tracking.code,
      status: tracking.status,
      lastUpdate: tracking.lastUpdate,
      lastSync: tracking.lastSync,
      isDelivered: tracking.isDelivered,
      hasAlert: tracking.hasAlert,
      alertReason: tracking.alertReason,
      events: tracking.events,
      order: {
        id: tracking.order.id,
        customerName: tracking.order.customer?.name || tracking.order.customerName,
        customerPhone: tracking.order.customer?.phone || tracking.order.customerPhone,
        total: tracking.order.total,
        status: tracking.order.status,
        items: tracking.order.items?.map((item: any) => ({
          productName: item.productName,
          quantity: item.quantity,
        })),
      },
    };
  }
}