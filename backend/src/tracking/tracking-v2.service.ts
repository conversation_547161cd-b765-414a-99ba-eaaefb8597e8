import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
// import { CorreiosProvider, TrackingEvent, CRITICAL_STATUS } from './providers/correios.provider';

// Temporary interfaces and constants while Correios integration is disabled
export interface TrackingEvent {
  date: Date;
  location: string;
  status: string;
  description: string;
  details?: string;
}

export interface TrackingResult {
  code: string;
  isDelivered: boolean;
  currentStatus: string;
  lastUpdate: Date;
  events: TrackingEvent[];
  error?: string;
}

// Status críticos que geram alertas
export const CRITICAL_STATUS = [
  'FC - FLUXO CANCELADO',
  'LDE - LOCKER DESTINO CHEIO',
  'DESTINATÁRIO AUSENTE',
  'ENDEREÇO INCORRETO',
  'DEVOLUÇÃO',
  'EXTRAVIADO',
  'AVARIADO',
  'RECUSADO',
  'CARTEIRO NÃO ATENDIDO',
];
import { NotificationsService } from '../notifications/notifications.service';
import { Tracking, Order, OrderStatus, NotificationType, NotificationChannel } from '@prisma/client';
import { Cron, CronExpression } from '@nestjs/schedule';

export interface CreateTrackingDto {
  orderId: string;
  code: string;
}

@Injectable()
export class TrackingV2Service {
  private readonly logger = new Logger(TrackingV2Service.name);
  private isSyncing = false;

  constructor(
    private prisma: PrismaService,
    // private correiosProvider: CorreiosProvider, // Disabled - Correios integration temporarily disabled
    private notificationsService: NotificationsService,
  ) {}

  /**
   * Cria um novo rastreamento para um pedido
   */
  async create(createTrackingDto: CreateTrackingDto): Promise<Tracking> {
    const { orderId, code } = createTrackingDto;

    // Valida código
    const formattedCode = this.formatTrackingCode(code);
    if (!this.isValidTrackingCode(formattedCode)) {
      throw new BadRequestException('Código de rastreamento inválido');
    }

    // Verifica se pedido existe
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: { tracking: true },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    if (order.tracking) {
      throw new ConflictException('Pedido já possui código de rastreamento');
    }

    // Busca status inicial - Temporarily disabled
    // const trackingResult = await this.correiosProvider.track(formattedCode);
    
    // Mock tracking result while Correios integration is disabled
    const trackingResult: TrackingResult = {
      code: formattedCode,
      isDelivered: false,
      currentStatus: 'OBJETO POSTADO',
      lastUpdate: new Date(),
      events: [{
        date: new Date(),
        location: 'UNIDADE DE TRATAMENTO',
        status: 'OBJETO POSTADO',
        description: 'Objeto postado',
      }],
    };

    // Cria tracking
    const tracking = await this.prisma.tracking.create({
      data: {
        orderId,
        code: formattedCode,
        status: trackingResult.currentStatus,
        lastUpdate: trackingResult.lastUpdate,
        events: trackingResult.events as any,
        isDelivered: trackingResult.isDelivered,
        hasAlert: this.hasAlertStatus(trackingResult.currentStatus),
        alertReason: this.getAlertReason(trackingResult.currentStatus),
        lastSync: new Date(),
      },
      include: {
        order: true,
      },
    });

    // Atualiza status do pedido se necessário
    await this.updateOrderStatus(tracking);

    this.logger.log(`Rastreamento criado: ${formattedCode} para pedido ${orderId}`);

    return tracking;
  }

  /**
   * Lista todos os rastreamentos
   */
  async findAll(filters?: {
    status?: string;
    hasAlert?: boolean;
    isDelivered?: boolean;
  }): Promise<Tracking[]> {
    const where: any = {};

    if (filters?.status) {
      where.status = { contains: filters.status, mode: 'insensitive' };
    }
    if (filters?.hasAlert !== undefined) {
      where.hasAlert = filters.hasAlert;
    }
    if (filters?.isDelivered !== undefined) {
      where.isDelivered = filters.isDelivered;
    }

    return this.prisma.tracking.findMany({
      where,
      include: {
        order: {
          include: {
            customer: true,
          },
        },
      },
      orderBy: {
        lastUpdate: 'desc',
      },
    });
  }

  /**
   * Busca rastreamento por código
   */
  async findByCode(code: string): Promise<Tracking> {
    const formattedCode = this.formatTrackingCode(code);
    
    const tracking = await this.prisma.tracking.findUnique({
      where: { code: formattedCode },
      include: {
        order: {
          include: {
            customer: true,
            items: true,
          },
        },
      },
    });

    if (!tracking) {
      throw new NotFoundException('Rastreamento não encontrado');
    }

    return tracking;
  }

  /**
   * Lista rastreamentos com alertas
   */
  async getAlerts(): Promise<Tracking[]> {
    return this.prisma.tracking.findMany({
      where: {
        hasAlert: true,
      },
      include: {
        order: {
          include: {
            customer: true,
          },
        },
      },
      orderBy: {
        lastUpdate: 'desc',
      },
    });
  }

  /**
   * Sincroniza todos os rastreamentos ativos
   * DISABLED: Using webhook system instead
   */
  // @Cron(CronExpression.EVERY_HOUR)
  async syncAll(): Promise<void> {
    if (this.isSyncing) {
      this.logger.warn('Sincronização já em andamento, pulando...');
      return;
    }

    this.isSyncing = true;
    this.logger.log('Iniciando sincronização de rastreamentos...');

    try {
      // Busca rastreamentos não entregues
      const trackings = await this.prisma.tracking.findMany({
        where: {
          isDelivered: false,
        },
        include: {
          order: true,
        },
      });

      this.logger.log(`${trackings.length} rastreamentos para sincronizar`);

      // Extrai códigos únicos
      const codes = [...new Set(trackings.map(t => t.code))];
      
      // Rastreia em lote - Temporarily disabled
      // const results = await this.correiosProvider.trackBatch(codes);
      
      // Return empty results while Correios integration is disabled
      const results: TrackingResult[] = [];
      this.logger.warn('Correios batch tracking is disabled');
      
      // Processa resultados
      let updated = 0;
      let alerts = 0;

      for (const result of results) {
        if (result.error) {
          this.logger.error(`Erro ao rastrear ${result.code}: ${result.error}`);
          continue;
        }

        const tracking = trackings.find(t => t.code === result.code);
        if (!tracking) continue;

        // Verifica se houve mudança
        if (tracking.status !== result.currentStatus || 
            tracking.isDelivered !== result.isDelivered) {
          
          const hasAlert = this.hasAlertStatus(result.currentStatus);
          if (hasAlert) alerts++;

          // Atualiza tracking
          const updatedTracking = await this.prisma.tracking.update({
            where: { id: tracking.id },
            data: {
              status: result.currentStatus,
              lastUpdate: result.lastUpdate,
              events: result.events as any,
              isDelivered: result.isDelivered,
              hasAlert,
              alertReason: hasAlert ? this.getAlertReason(result.currentStatus) : null,
              lastSync: new Date(),
            },
            include: {
              order: true,
            },
          });

          // Atualiza status do pedido
          await this.updateOrderStatus(updatedTracking);

          // Notifica mudança importante
          if (result.isDelivered || hasAlert) {
            await this.notifyTrackingUpdate(updatedTracking, tracking.status);
          }

          updated++;
        }
      }

      this.logger.log(`Sincronização concluída: ${updated} atualizados, ${alerts} alertas`);
    } catch (error: any) {
      this.logger.error(`Erro na sincronização: ${error.message}`);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Força sincronização de um código específico
   */
  async syncOne(code: string): Promise<Tracking> {
    const tracking = await this.findByCode(code);
    
    this.logger.log(`Sincronizando manualmente: ${tracking.code}`);

    // Temporarily disabled - return current tracking data
    // const result = await this.correiosProvider.track(tracking.code);
    
    this.logger.warn('Correios sync disabled - returning current tracking data');
    const result: TrackingResult = {
      code: tracking.code,
      isDelivered: tracking.isDelivered,
      currentStatus: tracking.status,
      lastUpdate: tracking.lastUpdate,
      events: (tracking.events as unknown) as TrackingEvent[],
    };
    
    if (result.error) {
      throw new BadRequestException(`Erro ao rastrear: ${result.error}`);
    }

    // Atualiza tracking
    const updated = await this.prisma.tracking.update({
      where: { id: tracking.id },
      data: {
        status: result.currentStatus,
        lastUpdate: result.lastUpdate,
        events: result.events as any,
        isDelivered: result.isDelivered,
        hasAlert: this.hasAlertStatus(result.currentStatus),
        alertReason: this.getAlertReason(result.currentStatus),
        lastSync: new Date(),
      },
      include: {
        order: true,
      },
    });

    // Atualiza status do pedido
    await this.updateOrderStatus(updated);

    // Notifica se necessário
    if (updated.status !== tracking.status && (updated.isDelivered || updated.hasAlert)) {
      await this.notifyTrackingUpdate(updated, tracking.status);
    }

    return updated;
  }

  /**
   * Atualiza status do pedido baseado no rastreamento
   */
  private async updateOrderStatus(tracking: Tracking & { order: Order }): Promise<void> {
    let newStatus: OrderStatus | null = null;

    if (tracking.isDelivered) {
      newStatus = OrderStatus.ConfirmarEntrega;
    } else if (tracking.hasAlert) {
      // Verifica tipo de alerta para determinar status
      if (tracking.status.includes('DESTINATÁRIO AUSENTE') || 
          tracking.status.includes('CARTEIRO NÃO ATENDIDO')) {
        newStatus = OrderStatus.EntregaFalha;
      } else if (tracking.status.includes('AGUARDANDO RETIRADA')) {
        newStatus = OrderStatus.RetirarCorreios;
      } else if (tracking.status.includes('DEVOLUÇÃO') || 
                 tracking.status.includes('FC - FLUXO CANCELADO')) {
        newStatus = OrderStatus.DevolvidoCorreios;
      }
    }

    if (newStatus && tracking.order.status !== newStatus) {
      await this.prisma.order.update({
        where: { id: tracking.orderId },
        data: {
          status: newStatus,
          statusHistory: {
            create: {
              previousStatus: tracking.order.status,
              newStatus,
              changedById: 'system', // Sistema automático
            },
          },
        },
      });

      this.logger.log(`Status do pedido ${tracking.orderId} atualizado para ${newStatus}`);
    }
  }

  /**
   * Notifica sobre mudanças importantes no rastreamento
   */
  private async notifyTrackingUpdate(
    tracking: Tracking & { order: Order },
    previousStatus: string,
  ): Promise<void> {
    try {
      if (!tracking.order.customerId) {
        this.logger.warn(`Pedido ${tracking.orderId} sem cliente para notificar`);
        return;
      }

      const customer = await this.prisma.customer.findUnique({
        where: { id: tracking.order.customerId },
      });

      if (!customer?.phone) {
        this.logger.warn(`Cliente ${tracking.order.customerId} sem telefone`);
        return;
      }

      let message = '';
      
      if (tracking.isDelivered) {
        message = `✅ Ótima notícia! Seu pedido #${tracking.order.id} foi entregue com sucesso!\n\nCódigo de rastreio: ${tracking.code}`;
      } else if (tracking.hasAlert) {
        message = `⚠️ Atenção! Houve uma atualização no seu pedido #${tracking.order.id}:\n\n${tracking.status}\n\nCódigo de rastreio: ${tracking.code}\n\nNossa equipe está acompanhando a situação.`;
      }

      if (message) {
        await this.notificationsService.createNotification(
          NotificationType.CUSTOM,
          NotificationChannel.WHATSAPP,
          {
            phone: customer.phone,
            name: customer.name,
          },
          {
            subject: 'Atualização de Rastreamento',
            message,
          },
        );
      }
    } catch (error: any) {
      this.logger.error(`Erro ao notificar rastreamento: ${error.message}`);
    }
  }

  /**
   * Retorna razão do alerta baseado no status
   */
  private getAlertReason(status: string): string | null {
    const upperStatus = status.toUpperCase();
    
    for (const critical of CRITICAL_STATUS) {
      if (upperStatus.includes(critical)) {
        return critical;
      }
    }
    
    return null;
  }

  /**
   * Estatísticas de rastreamento
   */
  async getStats() {
    const [total, delivered, alerts, syncing] = await Promise.all([
      this.prisma.tracking.count(),
      this.prisma.tracking.count({ where: { isDelivered: true } }),
      this.prisma.tracking.count({ where: { hasAlert: true } }),
      Promise.resolve(this.isSyncing),
    ]);

    const lastSync = await this.prisma.tracking.findFirst({
      where: { lastSync: { not: null } },
      orderBy: { lastSync: 'desc' },
      select: { lastSync: true },
    });

    return {
      total,
      delivered,
      alerts,
      pending: total - delivered,
      syncing,
      lastSync: lastSync?.lastSync || null,
    };
  }

  /**
   * Helper methods previously from CorreiosProvider
   */
  
  /**
   * Formata código de rastreamento
   */
  private formatTrackingCode(code: string): string {
    // Remove espaços e converte para maiúsculas
    return code.replace(/\s/g, '').toUpperCase();
  }

  /**
   * Valida formato do código
   */
  private isValidTrackingCode(code: string): boolean {
    // Formato padrão: AA123456789BR
    const pattern = /^[A-Z]{2}\d{9}[A-Z]{2}$/;
    return pattern.test(this.formatTrackingCode(code));
  }

  /**
   * Verifica se o status requer alerta
   */
  private hasAlertStatus(status: string): boolean {
    return CRITICAL_STATUS.some(s => 
      status.toUpperCase().includes(s.toUpperCase())
    );
  }
}