import { IsString, IsOptional, IsDateString, IsNumber, Min, IsArray, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

export class BatchTrackingDto {
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  trackingCodes: string[];
}

export class TrackingHistoryDto {
  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  offset?: number;
}