import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TrackingV2Service } from './tracking-v2.service';
import { TrackingV2Controller } from './tracking-v2.controller';
// import { CorreiosProvider } from './providers/correios.provider';
import { PrismaModule } from '../prisma/prisma.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    PrismaModule,
    NotificationsModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [TrackingV2Controller],
  providers: [TrackingV2Service],
  exports: [TrackingV2Service],
})
export class TrackingV2Module {}