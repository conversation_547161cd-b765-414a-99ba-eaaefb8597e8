import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const { method, originalUrl, headers } = req;
    
    // Log incoming request
    console.log(`📥 ${method} ${originalUrl}`);
    console.log(`   Host: ${headers.host}`);
    console.log(`   User-Agent: ${headers['user-agent']?.substring(0, 50)}...`);
    
    // Log response when finished
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      console.log(`📤 ${method} ${originalUrl} - ${res.statusCode} (${duration}ms)`);
    });
    
    next();
  }
}