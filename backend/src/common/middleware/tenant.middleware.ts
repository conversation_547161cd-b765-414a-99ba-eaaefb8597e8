import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class TenantMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Extract tenant ID from header
    const tenantId = req.headers['x-tenant-id'] as string;

    if (!tenantId) {
      throw new BadRequestException('Tenant ID is required');
    }

    // Validate tenant ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(tenantId)) {
      throw new BadRequestException('Invalid tenant ID format');
    }

    // Attach tenant ID to request object
    req.tenantId = tenantId;
    req.tenant = { id: tenantId }; // Can be expanded with more tenant data later

    // Log tenant context for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log(`[TenantMiddleware] Request for tenant: ${tenantId}`);
    }

    next();
  }
}