import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class SoapExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(SoapExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let details: any = null;

    // Handle SOAP faults
    if (exception.root && exception.root.Envelope && exception.root.Envelope.Body) {
      const fault = exception.root.Envelope.Body.Fault;
      if (fault) {
        message = fault.faultstring || 'SOAP Fault occurred';
        details = {
          code: fault.faultcode,
          detail: fault.detail,
        };
        status = HttpStatus.BAD_GATEWAY;
      }
    }
    // Handle HTTP exceptions
    else if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      message = typeof exceptionResponse === 'string' 
        ? exceptionResponse 
        : (exceptionResponse as any).message || message;
    }
    // Handle connection errors
    else if (exception.code === 'ECONNREFUSED' || exception.code === 'ETIMEDOUT') {
      status = HttpStatus.SERVICE_UNAVAILABLE;
      message = 'Correios service unavailable';
      details = {
        code: exception.code,
        service: 'correios',
      };
    }

    // Log the error
    this.logger.error(
      `${request.method} ${request.url} - ${status} - ${message}`,
      exception.stack,
    );

    // Send response
    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message,
      details,
    });
  }
}