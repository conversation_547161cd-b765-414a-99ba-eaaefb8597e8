import * as crypto from 'crypto';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EncryptionUtil {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;

  constructor(private configService: ConfigService) {
    const keyString = this.configService.get<string>('ENCRYPTION_KEY');
    if (!keyString || keyString.length !== 32) {
      throw new Error('ENCRYPTION_KEY must be 32 characters long');
    }
    this.key = Buffer.from(keyString);
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = (cipher as any).getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  decrypt(encryptedData: string): string {
    // If the data is not encrypted (doesn't have the expected format), return as is
    if (!encryptedData || !encryptedData.includes(':')) {
      return encryptedData;
    }
    
    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      // Log warning but return the original data instead of throwing
      console.warn('Invalid encrypted data format, returning original value');
      return encryptedData;
    }
    
    try {
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];
      
      const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);
      (decipher as any).setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.warn('Error decrypting data:', error.message);
      // Return the original data if decryption fails
      return encryptedData;
    }
  }

  hash(text: string): string {
    return crypto.createHash('sha256').update(text).digest('hex');
  }

  /**
   * Check if a string is already encrypted
   */
  isEncrypted(data: string): boolean {
    if (!data) return false;
    const parts = data.split(':');
    return parts.length === 3 && 
           parts[0].length === 32 && // IV is 16 bytes = 32 hex chars
           parts[1].length === 32 && // Auth tag is 16 bytes = 32 hex chars
           /^[0-9a-f]+$/i.test(parts[0]) && // All parts should be valid hex
           /^[0-9a-f]+$/i.test(parts[1]) &&
           /^[0-9a-f]+$/i.test(parts[2]);
  }
}
