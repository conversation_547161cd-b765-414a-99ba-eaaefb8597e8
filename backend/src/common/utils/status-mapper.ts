import { OrderStatus } from '@prisma/client';

/**
 * Maps frontend display status values to backend enum values
 */
export class StatusMapper {
  private static readonly statusMap: Record<string, OrderStatus> = {
    // Portuguese display names to enum values
    'Pagamento Pendente': OrderStatus.PagamentoPendente,
    'Completo': OrderStatus.Completo,
    'Parcial': OrderStatus.Parcial,
    'Cancelado': OrderStatus.Cancelado,
    'Trânsito': OrderStatus.Transito,
    'Análise': OrderStatus.Analise,
    'Separação': OrderStatus.Separacao,
    'Frustrado': OrderStatus.Frustrado,
    'Recuperação': OrderStatus.Recuperacao,
    'Negociação': OrderStatus.Negociacao,
    'Promessa': OrderStatus.Promessa,
    'Retirar Correios': OrderStatus.RetirarCorreios,
    'Entrega Falha': OrderStatus.EntregaFalha,
    'Confirmar Entrega': OrderStatus.ConfirmarEntrega,
    '<PERSON>ol<PERSON><PERSON>': OrderStatus.DevolvidoCorreios,
    'Confirmar Pagamento': OrderStatus.ConfirmarPagamento,
    'Deletado': OrderStatus.Cancelado, // Map deletado to cancelado
    'Liberação': OrderStatus.Analise, // Map liberação to análise as it's not in enum
    'Possíveis Duplicados': OrderStatus.Analise, // Map to análise
  };

  private static readonly enumToDisplayMap: Record<OrderStatus, string> = {
    [OrderStatus.PagamentoPendente]: 'Pagamento Pendente',
    [OrderStatus.Completo]: 'Completo',
    [OrderStatus.Parcial]: 'Parcial',
    [OrderStatus.Cancelado]: 'Cancelado',
    [OrderStatus.Transito]: 'Trânsito',
    [OrderStatus.Analise]: 'Análise',
    [OrderStatus.Separacao]: 'Separação',
    [OrderStatus.Frustrado]: 'Frustrado',
    [OrderStatus.Recuperacao]: 'Recuperação',
    [OrderStatus.Negociacao]: 'Negociação',
    [OrderStatus.Promessa]: 'Promessa',
    [OrderStatus.RetirarCorreios]: 'Retirar Correios',
    [OrderStatus.EntregaFalha]: 'Entrega Falha',
    [OrderStatus.ConfirmarEntrega]: 'Confirmar Entrega',
    [OrderStatus.DevolvidoCorreios]: 'Devolvido Correios',
    [OrderStatus.ConfirmarPagamento]: 'Confirmar Pagamento',
  };

  /**
   * Convert frontend display status to backend enum value
   */
  static toEnum(displayStatus: string): OrderStatus {
    // First try exact match
    const enumValue = this.statusMap[displayStatus];
    if (enumValue) {
      return enumValue;
    }

    // Try case-insensitive match
    const normalizedStatus = displayStatus.trim();
    for (const [key, value] of Object.entries(this.statusMap)) {
      if (key.toLowerCase() === normalizedStatus.toLowerCase()) {
        return value;
      }
    }

    // Try to match by removing accents and spaces
    const normalizedNoAccents = this.removeAccents(normalizedStatus.toLowerCase());
    for (const [key, value] of Object.entries(this.statusMap)) {
      if (this.removeAccents(key.toLowerCase()) === normalizedNoAccents) {
        return value;
      }
    }

    // If it's already an enum value, return it
    if (Object.values(OrderStatus).includes(displayStatus as OrderStatus)) {
      return displayStatus as OrderStatus;
    }

    // Default to Analise if no match found
    console.warn(`Unknown status: ${displayStatus}, defaulting to Analise`);
    return OrderStatus.Analise;
  }

  /**
   * Convert backend enum value to frontend display string
   */
  static toDisplay(enumStatus: OrderStatus): string {
    return this.enumToDisplayMap[enumStatus] || enumStatus;
  }

  /**
   * Remove accents from string
   */
  private static removeAccents(str: string): string {
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/\s+/g, '');
  }

  /**
   * Check if a status string is a valid enum value
   */
  static isEnumValue(status: string): boolean {
    return Object.values(OrderStatus).includes(status as OrderStatus);
  }

  /**
   * Get all valid display status values
   */
  static getDisplayStatuses(): string[] {
    return Object.keys(this.statusMap);
  }

  /**
   * Get all valid enum values
   */
  static getEnumStatuses(): OrderStatus[] {
    return Object.values(OrderStatus);
  }
}