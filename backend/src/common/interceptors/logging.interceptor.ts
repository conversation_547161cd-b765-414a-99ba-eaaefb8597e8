import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { LoggerService } from '../../logger/logger.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext('HTTP');
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url } = request;
    const now = Date.now();

    const userId = (request as any).user?.id;
    const tenantId = (request as any).user?.tenantId;
    const ip = request.ip || request.connection.remoteAddress;

    return next.handle().pipe(
      tap({
        next: () => {
          const response = context.switchToHttp().getResponse<Response>();
          const responseTime = Date.now() - now;
          
          this.logger.logHttpRequest(method, url, response.statusCode, responseTime, {
            userId,
            tenantId,
            ip,
            userAgent: request.headers['user-agent'],
          });
        },
        error: (error) => {
          const responseTime = Date.now() - now;
          const statusCode = error.status || 500;
          
          this.logger.error(`HTTP ${method} ${url} failed`, error.stack, {
            statusCode,
            responseTime,
            userId,
            tenantId,
            ip,
            error: error.message,
          });
        },
      }),
    );
  }
}