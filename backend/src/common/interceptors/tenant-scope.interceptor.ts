import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * Interceptor to ensure all database queries are scoped to the current tenant
 * This interceptor modifies Prisma queries to include tenantId filtering
 */
@Injectable()
export class TenantScopeInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const tenantId = request.tenantId;

    // Store tenant ID in async local storage for Prisma middleware to access
    // This will be used by Prisma middleware to automatically filter queries
    if (tenantId) {
      // We'll implement this with AsyncLocalStorage in the Prisma service
      (global as any).__currentTenantId = tenantId;
    }

    return next.handle().pipe(
      map((data) => {
        // Clean up after request
        delete (global as any).__currentTenantId;
        return data;
      }),
    );
  }
}