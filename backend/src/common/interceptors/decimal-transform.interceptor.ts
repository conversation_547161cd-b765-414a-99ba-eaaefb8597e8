import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON>ler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class DecimalTransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => this.transformDecimals(data)),
    );
  }

  private transformDecimals(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    // Handle Decimal instances
    if (obj instanceof Decimal) {
      return Number(obj);
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(item => this.transformDecimals(item));
    }

    // Handle objects
    if (typeof obj === 'object' && obj.constructor === Object) {
      const transformed: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          transformed[key] = this.transformDecimals(obj[key]);
        }
      }
      return transformed;
    }

    return obj;
  }
}