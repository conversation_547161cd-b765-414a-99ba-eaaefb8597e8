import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';

/**
 * Guard that combines JWT authentication with tenant validation
 * Ensures that:
 * 1. User is authenticated
 * 2. User belongs to the tenant specified in the request
 * 3. Tenant ID is present and valid
 */
@Injectable()
export class TenantAuthGuard extends JwtAuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // First, run the parent JWT auth guard
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      return false;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const tenantId = request.tenantId;

    // Validate tenant ID is present
    if (!tenantId) {
      throw new UnauthorizedException('Tenant ID is required');
    }

    // Validate user belongs to the tenant
    // Note: This assumes the user object has a tenantId property
    // You may need to fetch this from the database if not included in JWT
    if (user.tenantId && user.tenantId !== tenantId) {
      throw new ForbiddenException('Access denied: Invalid tenant');
    }

    // Optional: Add additional tenant validation here
    // For example, check if tenant is active, not suspended, etc.

    return true;
  }
}