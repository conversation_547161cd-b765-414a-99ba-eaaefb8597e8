import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('example')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ExampleController {
  
  @Get('admin-only')
  @Roles('ADMIN')
  adminOnly() {
    return { message: 'Esta rota só pode ser acessada por ADMIN' };
  }

  @Get('supervisor')
  @Roles('ADMIN', 'SUPERVISOR')
  supervisorOrAdmin() {
    return { message: 'Esta rota pode ser acessada por ADMIN ou SUPERVISOR' };
  }

  @Get('vendedor')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  vendedor() {
    return { message: 'Esta rota pode ser acessada por ADMIN, SUPERVISOR ou VENDEDOR' };
  }

  @Get('public')
  // Sem @Roles - qualquer usuário autenticado pode acessar
  publicAuthenticated() {
    return { message: 'Esta rota pode ser acessada por qualquer usuário autenticado' };
  }
}