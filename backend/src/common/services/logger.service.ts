import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';

@Injectable()
export class LoggerService implements NestLoggerService {
  log(message: string, context?: string) {
    console.log(`[${context || 'Application'}] ${message}`);
  }

  error(message: string, trace?: string, context?: string) {
    console.error(`[${context || 'Application'}] ${message}`, trace);
  }

  warn(message: string, context?: string) {
    console.warn(`[${context || 'Application'}] ${message}`);
  }

  debug(message: string, context?: string) {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`[${context || 'Application'}] ${message}`);
    }
  }

  verbose(message: string, context?: string) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[${context || 'Application'}] ${message}`);
    }
  }

  // Anti-fraud specific logging methods
  logDuplicateDetection(data: any) {
    this.log(`Duplicate detection: ${JSON.stringify(data)}`, 'AntifraudService');
  }

  logAddressParsing(data: any) {
    this.debug(`Address parsing: ${JSON.stringify(data)}`, 'AddressParser');
  }

  logPhoneticEncoding(data: any) {
    this.debug(`Phonetic encoding: ${JSON.stringify(data)}`, 'PhoneticEncoder');
  }

  logSecurityEvent(event: any) {
    this.warn(`Security event: ${JSON.stringify(event)}`, 'Security');
  }
}
