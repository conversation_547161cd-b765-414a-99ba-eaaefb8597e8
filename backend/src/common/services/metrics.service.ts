import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Counter, Histogram, Gauge, Registry, collectDefaultMetrics } from 'prom-client';
import { AsyncLocalStorage } from 'async_hooks';

@Injectable()
export class MetricsService implements OnModuleInit {
  private readonly registry: Registry;
  private tenantContext: AsyncLocalStorage<{ tenantId: string }>;
  
  // Anti-fraud specific metrics
  private duplicateDetectionCounter: Counter;
  private duplicateDetectionDuration: Histogram;
  private reviewQueueSize: Gauge;
  private reviewDecisionCounter: Counter;
  private reviewProcessingDuration: Histogram;
  private addressParsingDuration: Histogram;
  private fuzzyMatchingAccuracy: Gauge;
  private bulkReviewCounter: Counter;
  private antifraudErrorCounter: Counter;
  
  // General application metrics
  private httpRequestDuration: Histogram;
  private httpRequestTotal: Counter;
  private dbQueryDuration: Histogram;
  private activeConnections: Gauge;

  constructor(private configService: ConfigService) {
    this.registry = new Registry();
    this.tenantContext = new AsyncLocalStorage();
    
    // Set default labels
    this.registry.setDefaultLabels({
      app: 'zencash',
      service: 'antifraud',
      environment: this.configService.get('NODE_ENV', 'development'),
    });
  }

  onModuleInit() {
    // Collect default Node.js metrics
    collectDefaultMetrics({ register: this.registry });
    
    this.initializeMetrics();
  }

  private initializeMetrics() {
    // Duplicate Detection Metrics
    this.duplicateDetectionCounter = new Counter({
      name: 'antifraud_duplicate_detection_total',
      help: 'Total number of duplicate detection checks',
      labelNames: ['tenant_id', 'result', 'match_level'],
      registers: [this.registry],
    });

    this.duplicateDetectionDuration = new Histogram({
      name: 'antifraud_duplicate_detection_duration_seconds',
      help: 'Duration of duplicate detection checks',
      labelNames: ['tenant_id', 'result'],
      buckets: [0.1, 0.5, 1, 2, 5],
      registers: [this.registry],
    });

    // Review Queue Metrics
    this.reviewQueueSize = new Gauge({
      name: 'antifraud_review_queue_size',
      help: 'Current size of the review queue',
      labelNames: ['tenant_id', 'priority'],
      registers: [this.registry],
    });

    this.reviewDecisionCounter = new Counter({
      name: 'antifraud_review_decisions_total',
      help: 'Total number of review decisions made',
      labelNames: ['tenant_id', 'decision', 'reviewer_role'],
      registers: [this.registry],
    });

    this.reviewProcessingDuration = new Histogram({
      name: 'antifraud_review_processing_duration_seconds',
      help: 'Time taken to process reviews',
      labelNames: ['tenant_id', 'decision', 'is_bulk'],
      buckets: [1, 5, 10, 30, 60],
      registers: [this.registry],
    });

    // Address Processing Metrics
    this.addressParsingDuration = new Histogram({
      name: 'antifraud_address_parsing_duration_seconds',
      help: 'Duration of address parsing operations',
      labelNames: ['tenant_id', 'has_all_components'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.registry],
    });

    // Fuzzy Matching Metrics
    this.fuzzyMatchingAccuracy = new Gauge({
      name: 'antifraud_fuzzy_matching_accuracy',
      help: 'Accuracy of fuzzy matching algorithm',
      labelNames: ['tenant_id', 'component_type'],
      registers: [this.registry],
    });

    // Bulk Operations
    this.bulkReviewCounter = new Counter({
      name: 'antifraud_bulk_review_total',
      help: 'Total number of bulk review operations',
      labelNames: ['tenant_id', 'batch_size', 'decision'],
      registers: [this.registry],
    });

    // Error Tracking
    this.antifraudErrorCounter = new Counter({
      name: 'antifraud_errors_total',
      help: 'Total number of anti-fraud errors',
      labelNames: ['tenant_id', 'error_type', 'operation'],
      registers: [this.registry],
    });

    // HTTP Metrics
    this.httpRequestDuration = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests',
      labelNames: ['method', 'route', 'status_code', 'tenant_id'],
      buckets: [0.1, 0.5, 1, 2, 5, 10],
      registers: [this.registry],
    });

    this.httpRequestTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code', 'tenant_id'],
      registers: [this.registry],
    });

    // Database Metrics
    this.dbQueryDuration = new Histogram({
      name: 'db_query_duration_seconds',
      help: 'Duration of database queries',
      labelNames: ['operation', 'table', 'tenant_id'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 5],
      registers: [this.registry],
    });

    // Connection Metrics
    this.activeConnections = new Gauge({
      name: 'active_connections',
      help: 'Number of active connections',
      labelNames: ['type'],
      registers: [this.registry],
    });
  }

  // Duplicate Detection Metrics
  recordDuplicateDetection(
    result: 'duplicate' | 'unique',
    matchLevel: 'high' | 'medium' | 'low' | 'none',
    duration: number,
  ) {
    const tenant = this.getTenantId();
    
    this.duplicateDetectionCounter.inc({
      tenant_id: tenant,
      result,
      match_level: matchLevel,
    });
    
    this.duplicateDetectionDuration.observe(
      { tenant_id: tenant, result },
      duration / 1000, // Convert to seconds
    );
  }

  // Review Metrics
  updateReviewQueueSize(size: number, priority: 'high' | 'normal' = 'normal') {
    const tenant = this.getTenantId();
    this.reviewQueueSize.set({ tenant_id: tenant, priority }, size);
  }

  recordReviewDecision(
    decision: 'approved' | 'denied' | 'escalated',
    reviewerRole: string,
    processingTime: number,
    isBulk: boolean = false,
  ) {
    const tenant = this.getTenantId();
    
    this.reviewDecisionCounter.inc({
      tenant_id: tenant,
      decision,
      reviewer_role: reviewerRole,
    });
    
    this.reviewProcessingDuration.observe(
      {
        tenant_id: tenant,
        decision,
        is_bulk: isBulk ? 'true' : 'false',
      },
      processingTime / 1000,
    );
  }

  // Address Parsing Metrics
  recordAddressParsing(duration: number, hasAllComponents: boolean) {
    const tenant = this.getTenantId();
    
    this.addressParsingDuration.observe(
      {
        tenant_id: tenant,
        has_all_components: hasAllComponents ? 'true' : 'false',
      },
      duration / 1000,
    );
  }

  // Fuzzy Matching Metrics
  updateFuzzyMatchingAccuracy(accuracy: number, componentType: string) {
    const tenant = this.getTenantId();
    
    this.fuzzyMatchingAccuracy.set(
      {
        tenant_id: tenant,
        component_type: componentType,
      },
      accuracy,
    );
  }

  // Bulk Review Metrics
  recordBulkReview(batchSize: number, decision: string) {
    const tenant = this.getTenantId();
    
    this.bulkReviewCounter.inc({
      tenant_id: tenant,
      batch_size: this.getBatchSizeCategory(batchSize),
      decision,
    });
  }

  // Error Metrics
  recordError(errorType: string, operation: string) {
    const tenant = this.getTenantId();
    
    this.antifraudErrorCounter.inc({
      tenant_id: tenant,
      error_type: errorType,
      operation,
    });
  }

  // HTTP Metrics
  recordHttpRequest(
    method: string,
    route: string,
    statusCode: number,
    duration: number,
  ) {
    const tenant = this.getTenantId();
    const labels = {
      method,
      route: this.normalizeRoute(route),
      status_code: statusCode.toString(),
      tenant_id: tenant,
    };
    
    this.httpRequestTotal.inc(labels);
    this.httpRequestDuration.observe(labels, duration / 1000);
  }

  // Database Metrics
  recordDbQuery(operation: string, table: string, duration: number) {
    const tenant = this.getTenantId();
    
    this.dbQueryDuration.observe(
      {
        operation,
        table,
        tenant_id: tenant,
      },
      duration / 1000,
    );
  }

  // Connection Metrics
  updateActiveConnections(type: 'http' | 'database' | 'redis', count: number) {
    this.activeConnections.set({ type }, count);
  }

  // Get metrics for Prometheus endpoint
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  // Get metrics in JSON format
  async getMetricsJson() {
    return this.registry.getMetricsAsJSON();
  }

  // Helper methods
  private getTenantId(): string {
    const context = this.tenantContext.getStore();
    return context?.tenantId || 'unknown';
  }

  private normalizeRoute(route: string): string {
    // Replace dynamic segments with placeholders
    return route
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid');
  }

  private getBatchSizeCategory(size: number): string {
    if (size <= 10) return '1-10';
    if (size <= 50) return '11-50';
    if (size <= 100) return '51-100';
    return '100+';
  }

  // Set tenant context for metrics
  runWithTenant<T>(tenantId: string, callback: () => T): T {
    return this.tenantContext.run({ tenantId }, callback);
  }

  // Custom metrics for specific anti-fraud operations
  createAntifraudMetrics() {
    return {
      // Track duplicate patterns over time
      duplicatePatternGauge: new Gauge({
        name: 'antifraud_duplicate_pattern_score',
        help: 'Score indicating duplicate pattern strength',
        labelNames: ['tenant_id', 'pattern_type', 'time_window'],
        registers: [this.registry],
      }),

      // Track reviewer performance
      reviewerPerformanceHistogram: new Histogram({
        name: 'antifraud_reviewer_performance_seconds',
        help: 'Time taken by reviewers to make decisions',
        labelNames: ['tenant_id', 'reviewer_id', 'decision'],
        buckets: [5, 10, 30, 60, 120, 300],
        registers: [this.registry],
      }),

      // Track address matching quality
      addressMatchQualityGauge: new Gauge({
        name: 'antifraud_address_match_quality',
        help: 'Quality score of address matching',
        labelNames: ['tenant_id', 'match_type'],
        registers: [this.registry],
      }),
    };
  }
}