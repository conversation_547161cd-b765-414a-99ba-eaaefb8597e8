import { Injectable, OnModuleInit } from '@nestjs/common';
import * as Sentry from '@sentry/node';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SentryService implements OnModuleInit {
  constructor(private configService: ConfigService) {}

  onModuleInit() {
    const dsn = this.configService.get<string>('SENTRY_DSN');
    if (!dsn) {
      console.warn('Sentry DSN not configured. Error tracking disabled.');
      return;
    }

    Sentry.init({
      dsn,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
      tracesSampleRate: 0.1,
    });
  }

  setTenantContext(tenantId: string): void {
    Sentry.setTag('tenant_id', tenantId);
  }

  setUserContext(userId: string, email?: string, role?: string): void {
    Sentry.setUser({ id: userId, email, role });
  }

  captureException(error: Error, context?: any): void {
    Sentry.withScope((scope) => {
      if (context) {
        scope.setContext('additional', context);
      }
      Sentry.captureException(error);
    });
  }

  captureMessage(message: string, level: Sentry.SeverityLevel = 'info'): void {
    Sentry.captureMessage(message, level);
  }

  captureAntifraudError(error: Error, context: any): void {
    Sentry.withScope((scope) => {
      scope.setTag('antifraud', true);
      scope.setContext('antifraud', context);
      Sentry.captureException(error);
    });
  }

  startTransaction(name: string, op: string): any {
    // Return a dummy transaction for now
    return { finish: () => {} };
  }

  async flush(timeout?: number): Promise<boolean> {
    return Sentry.flush(timeout);
  }
}
