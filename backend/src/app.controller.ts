import { Controller, Get, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

@Controller()
@ApiTags('system')
export class AppController {
  @Get()
  @ApiOperation({ summary: 'API root endpoint' })
  getRoot() {
    return {
      message: 'ZenCash API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
    };
  }
  
  @Get('debug')
  @ApiOperation({ summary: 'Debug endpoint' })
  getDebug() {
    return {
      message: 'Debug info',
      timestamp: new Date().toISOString(),
      cors_origin: process.env.CORS_ORIGIN,
      frontend_url: process.env.FRONTEND_URL,
      api_prefix: process.env.API_PREFIX,
      node_env: process.env.NODE_ENV,
    };
  }

  @Post('test-body-size')
  @ApiOperation({ summary: 'Test body size limits' })
  testBodySize(@Body() body: any): any {
    const bodySize = JSON.stringify(body).length;
    return {
      message: 'Body received successfully',
      size: bodySize,
      sizeInMB: (bodySize / (1024 * 1024)).toFixed(2),
      maxAllowedMB: 10,
    };
  }
}