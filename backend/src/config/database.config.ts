import { Prisma } from '@prisma/client';

/**
 * Database configuration helper
 * Adds connection pooling and timeout parameters to DATABASE_URL
 */

export function getDatabaseUrl(): string {
  const baseUrl = process.env.DATABASE_URL;
  
  if (!baseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }
  
  // Parse the URL to add query parameters
  const url = new URL(baseUrl);
  
  // Connection pool settings for production
  if (process.env.NODE_ENV === 'production') {
    // Connection pool size (default is 10)
    url.searchParams.set('connection_limit', '10');
    
    // Max time to wait for a connection from the pool (in seconds)
    url.searchParams.set('pool_timeout', '10');
    
    // Connection timeout (in seconds)
    url.searchParams.set('connect_timeout', '30');
    
    // Statement timeout (in milliseconds)
    url.searchParams.set('statement_timeout', '60000');
    
    // Idle in transaction session timeout (in milliseconds)
    url.searchParams.set('idle_in_transaction_session_timeout', '60000');
  }
  
  return url.toString();
}

/**
 * Get database connection options for Prisma
 */
export function getDatabaseOptions(): Prisma.PrismaClientOptions {
  const logLevels: Prisma.LogLevel[] = process.env.NODE_ENV === 'development' 
    ? ['query', 'info', 'warn', 'error'] 
    : ['error', 'warn'];

  return {
    datasources: {
      db: {
        url: getDatabaseUrl(),
      },
    },
    log: logLevels,
  };
}