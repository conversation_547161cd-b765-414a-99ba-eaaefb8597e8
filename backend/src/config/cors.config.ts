import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

export function getCorsConfig(): CorsOptions {
  const allowedOrigins: string[] = [];
  
  // Parse CORS_ORIGINS from environment
  const corsOrigins = process.env.CORS_ORIGINS || process.env.CORS_ORIGIN;
  if (corsOrigins) {
    allowedOrigins.push(...corsOrigins.split(',').map(origin => origin.trim()));
  }
  
  // Add localhost for development
  if (process.env.NODE_ENV !== 'production') {
    allowedOrigins.push(
      'http://localhost:3000',
      'http://localhost:3001', 
      'http://localhost:5173',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001',
      'http://127.0.0.1:5173'
    );
  }

  console.log('🔒 CORS Configuration:');
  console.log('  - Environment:', process.env.NODE_ENV || 'development');
  console.log('  - Fixed Origins:', allowedOrigins);
  console.log('  - Vercel Previews:', 'Enabled');
  console.log('  - Production Domain:', process.env.PRODUCTION_DOMAIN || 'Not set');

  return {
    origin: (origin, callback) => {
      // Allow requests with no origin (Postman, mobile apps, server-side)
      if (!origin) {
        return callback(null, true);
      }

      // Check fixed allowed origins
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      // Allow all Vercel preview deployments
      if (origin.includes('.vercel.app')) {
        console.log(`✅ Allowing Vercel preview: ${origin}`);
        return callback(null, true);
      }

      // Allow Railway app deployments
      if (origin.includes('.railway.app')) {
        console.log(`✅ Allowing Railway app: ${origin}`);
        return callback(null, true);
      }

      // In production, check against production domain
      if (process.env.NODE_ENV === 'production' && process.env.PRODUCTION_DOMAIN) {
        // Allow the main production domain and its subdomains
        if (origin.includes(process.env.PRODUCTION_DOMAIN)) {
          console.log(`✅ Allowing production domain: ${origin}`);
          return callback(null, true);
        }
      }

      // Block all others
      console.warn(`⚠️  CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'x-tenant-id',
      'Accept',
      'Cache-Control',
      'X-Requested-With',
    ],
    exposedHeaders: ['set-cookie', 'X-Total-Count'],
    maxAge: 86400, // 24 hours
  };
}