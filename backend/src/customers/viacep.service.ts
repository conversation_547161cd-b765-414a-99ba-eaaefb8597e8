import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import axios from 'axios';

export interface ViaCepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  erro?: boolean;
}

export interface AddressFromCep {
  cep: string;
  street: string;
  neighborhood: string;
  city: string;
  state: string;
  complement?: string;
}

@Injectable()
export class ViaCepService {
  private readonly viaCepUrl = 'https://viacep.com.br/ws';

  async getAddressByCep(cep: string): Promise<AddressFromCep> {
    // Remove caracteres não numéricos do CEP
    const cleanCep = cep.replace(/\D/g, '');

    // Valida formato do CEP
    if (cleanCep.length !== 8) {
      throw new HttpException(
        'CEP deve conter 8 dígitos',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const response = await axios.get<ViaCepResponse>(
        `${this.viaCepUrl}/${cleanCep}/json/`,
      );

      const data = response.data;

      // ViaCEP retorna erro: true quando o CEP não existe
      if (data.erro) {
        throw new HttpException(
          'CEP não encontrado',
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        cep: this.formatCep(cleanCep),
        street: data.logradouro || '',
        neighborhood: data.bairro || '',
        city: data.localidade || '',
        state: data.uf || '',
        complement: data.complemento || undefined,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Erro ao consultar CEP. Tente novamente mais tarde.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  private formatCep(cep: string): string {
    // Formata CEP para 00000-000
    return `${cep.substr(0, 5)}-${cep.substr(5, 3)}`;
  }
}