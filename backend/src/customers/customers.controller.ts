import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
  ParseBoolPipe,
} from '@nestjs/common';
import { CustomersService } from './customers.service';
import { AddressesService } from './addresses.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { SearchCustomerDto } from './dto/search-customer.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('customers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CustomersController {
  constructor(
    private readonly customersService: CustomersService,
    private readonly addressesService: AddressesService,
  ) {}

  // Rotas de Clientes
  @Post()
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(
    @Body() createCustomerDto: CreateCustomerDto,
    @TenantId() tenantId: string,
  ) {
    const customer = await this.customersService.create(createCustomerDto, tenantId);
    return this.customersService.formatCustomer(customer);
  }

  @Get()
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findAll(
    @TenantId() tenantId: string,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    const customers = await this.customersService.findAll(tenantId, active);
    return customers.map(customer => this.customersService.formatCustomer(customer));
  }

  @Get('search')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async search(
    @Query() searchDto: SearchCustomerDto,
    @TenantId() tenantId: string,
  ) {
    const customers = await this.customersService.search(searchDto, tenantId);
    return customers.map(customer => this.customersService.formatCustomer(customer));
  }

  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findOne(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    const customer = await this.customersService.findOne(id, tenantId);
    return this.customersService.formatCustomer(customer);
  }

  @Patch(':id')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
    @TenantId() tenantId: string,
  ) {
    const customer = await this.customersService.update(id, updateCustomerDto, tenantId);
    return this.customersService.formatCustomer(customer);
  }

  @Delete(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async remove(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.customersService.remove(id, tenantId);
  }

  // Rotas de Endereços
  @Post(':id/addresses')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async addAddress(
    @Param('id') customerId: string,
    @Body() createAddressDto: CreateAddressDto,
    @TenantId() tenantId: string,
  ) {
    return this.addressesService.create(customerId, createAddressDto, tenantId);
  }

  @Get(':id/addresses')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async getAddresses(
    @Param('id') customerId: string,
    @TenantId() tenantId: string,
  ) {
    return this.addressesService.findAll(customerId, tenantId);
  }

  @Patch(':id/addresses/:addressId')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async updateAddress(
    @Param('id') customerId: string,
    @Param('addressId') addressId: string,
    @Body() updateAddressDto: UpdateAddressDto,
    @TenantId() tenantId: string,
  ) {
    return this.addressesService.update(customerId, addressId, updateAddressDto, tenantId);
  }

  @Delete(':id/addresses/:addressId')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  async removeAddress(
    @Param('id') customerId: string,
    @Param('addressId') addressId: string,
    @TenantId() tenantId: string,
  ) {
    await this.addressesService.remove(customerId, addressId, tenantId);
    return { message: 'Endereço removido com sucesso' };
  }

  @Patch(':id/addresses/:addressId/set-main')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  async setMainAddress(
    @Param('id') customerId: string,
    @Param('addressId') addressId: string,
    @TenantId() tenantId: string,
  ) {
    return this.addressesService.setAsMain(customerId, addressId, tenantId);
  }

  // Rota auxiliar para buscar endereço pelo CEP
  @Get('cep/:cep')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  async getAddressByCep(@Param('cep') cep: string) {
    return this.addressesService.getAddressByCep(cep);
  }
}