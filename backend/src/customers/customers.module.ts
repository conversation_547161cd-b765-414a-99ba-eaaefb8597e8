import { Module } from '@nestjs/common';
import { CustomersService } from './customers.service';
import { AddressesService } from './addresses.service';
import { ViaCepService } from './viacep.service';
import { CustomersController } from './customers.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [CustomersController],
  providers: [CustomersService, AddressesService, ViaCepService],
  exports: [CustomersService, AddressesService],
})
export class CustomersModule {}