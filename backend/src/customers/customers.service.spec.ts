import { Test, TestingModule } from '@nestjs/testing';
import { CustomersService } from './customers.service';
import { PrismaService } from '../prisma/prisma.service';

describe('CustomersService', () => {
  let service: CustomersService;
  let prisma: PrismaService;

  const mockPrismaService = {
    customer: {
      create: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<CustomersService>(CustomersService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new customer', async () => {
      const createCustomerDto = {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '11999999999',
        cpf: '12345678901',
      };
      const tenantId = 'tenant-1';

      const createdCustomer = {
        id: 'customer-1',
        ...createCustomerDto,
        tenantId,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.customer.create.mockResolvedValue(createdCustomer);

      const result = await service.create(createCustomerDto, tenantId);

      expect(result).toEqual(createdCustomer);
      expect(mockPrismaService.customer.create).toHaveBeenCalledWith({
        data: {
          ...createCustomerDto,
          tenantId,
        },
      });
    });
  });

  describe('findAll', () => {
    it('should return all customers for tenant', async () => {
      const tenantId = 'tenant-1';
      const customers = [
        {
          id: 'customer-1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '11999999999',
          cpf: '12345678901',
          tenantId,
          active: true,
        },
        {
          id: 'customer-2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '11888888888',
          cpf: '98765432109',
          tenantId,
          active: true,
        },
      ];

      mockPrismaService.customer.findMany.mockResolvedValue(customers);

      const result = await service.findAll(tenantId);

      expect(result).toEqual(customers);
      expect(mockPrismaService.customer.findMany).toHaveBeenCalledWith({
        where: { tenantId },
        include: {
          addresses: true,
          orders: false,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should filter by active status', async () => {
      const tenantId = 'tenant-1';
      const activeCustomers = [
        {
          id: 'customer-1',
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '11999999999',
          cpf: '12345678901',
          tenantId,
          active: true,
        },
      ];

      mockPrismaService.customer.findMany.mockResolvedValue(activeCustomers);

      const result = await service.findAll(tenantId, true);

      expect(result).toEqual(activeCustomers);
      expect(mockPrismaService.customer.findMany).toHaveBeenCalledWith({
        where: { tenantId, active: true },
        include: {
          addresses: true,
          orders: false,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });
  });

  describe('findOne', () => {
    it('should return a customer by id', async () => {
      const customerId = 'customer-1';
      const tenantId = 'tenant-1';
      const customer = {
        id: customerId,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '11999999999',
        cpf: '12345678901',
        tenantId,
        active: true,
      };

      mockPrismaService.customer.findFirst.mockResolvedValue(customer);

      const result = await service.findOne(customerId, tenantId);

      expect(result).toEqual(customer);
      expect(mockPrismaService.customer.findFirst).toHaveBeenCalledWith({
        where: { id: customerId, tenantId },
        include: {
          addresses: true,
          orders: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });
    });

    it('should throw NotFoundException when customer not found', async () => {
      mockPrismaService.customer.findFirst.mockResolvedValue(null);

      await expect(service.findOne('non-existent', 'tenant-1')).rejects.toThrow(
        'Customer not found',
      );
    });
  });

  describe('update', () => {
    it('should update a customer', async () => {
      const customerId = 'customer-1';
      const tenantId = 'tenant-1';
      const updateCustomerDto = {
        name: 'John Updated',
        phone: '11777777777',
      };

      const existingCustomer = {
        id: customerId,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '11999999999',
        cpf: '12345678901',
        tenantId,
        active: true,
      };

      const updatedCustomer = {
        ...existingCustomer,
        ...updateCustomerDto,
      };

      mockPrismaService.customer.findFirst.mockResolvedValue(existingCustomer);
      mockPrismaService.customer.update.mockResolvedValue(updatedCustomer);

      const result = await service.update(customerId, updateCustomerDto, tenantId);

      expect(result).toEqual(updatedCustomer);
      expect(mockPrismaService.customer.update).toHaveBeenCalledWith({
        where: { id: customerId },
        data: updateCustomerDto,
        include: {
          addresses: true,
        },
      });
    });
  });

  describe('remove', () => {
    it('should soft delete a customer', async () => {
      const customerId = 'customer-1';
      const tenantId = 'tenant-1';
      const customer = {
        id: customerId,
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '11999999999',
        cpf: '12345678901',
        tenantId,
        active: false,
      };

      mockPrismaService.customer.update.mockResolvedValue(customer);

      const result = await service.remove(customerId, tenantId);

      expect(result).toEqual(customer);
      expect(mockPrismaService.customer.update).toHaveBeenCalledWith({
        where: { id: customerId, tenantId },
        data: { active: false },
      });
    });
  });
});