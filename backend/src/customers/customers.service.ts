import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { SearchCustomerDto } from './dto/search-customer.dto';
import { Customer, Prisma } from '@prisma/client';
import { CpfValidator } from './utils/cpf.validator';

@Injectable()
export class CustomersService {
  constructor(private prisma: PrismaService) {}

  async create(createCustomerDto: CreateCustomerDto, tenantId: string): Promise<Customer> {
    // Limpa e valida CPF
    const cleanCpf = CpfValidator.clean(createCustomerDto.cpf);
    
    if (!CpfValidator.validate(cleanCpf)) {
      throw new BadRequestException('CPF inválido');
    }

    // Verifica se CPF já existe no mesmo tenant
    const existingCustomer = await this.prisma.customer.findFirst({
      where: { 
        cpf: cleanCpf,
        tenantId,
      },
    });

    if (existingCustomer) {
      throw new ConflictException('CPF já cadastrado');
    }

    // Limpa telefone (remove caracteres especiais)
    const cleanPhone = createCustomerDto.phone.replace(/\D/g, '');

    return this.prisma.customer.create({
      data: {
        ...createCustomerDto,
        cpf: cleanCpf,
        phone: cleanPhone,
        tenantId,
      },
      include: {
        addresses: true,
      },
    });
  }

  async findAll(tenantId: string, active?: boolean): Promise<Customer[]> {
    const where: Prisma.CustomerWhereInput = {
      tenantId,
    };
    
    if (active !== undefined) {
      where.active = active;
    }

    return this.prisma.customer.findMany({
      where,
      include: {
        addresses: {
          orderBy: {
            main: 'desc',
          },
        },
        _count: {
          select: {
            orders: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  async findOne(id: string, tenantId: string): Promise<Customer> {
    const customer = await this.prisma.customer.findFirst({
      where: { 
        id,
        tenantId,
      },
      include: {
        addresses: {
          orderBy: {
            main: 'desc',
          },
        },
        orders: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            items: true,
          },
        },
      },
    });

    if (!customer) {
      throw new NotFoundException('Cliente não encontrado');
    }

    return customer;
  }

  async findByCpf(cpf: string, tenantId: string): Promise<Customer | null> {
    const cleanCpf = CpfValidator.clean(cpf);
    
    return this.prisma.customer.findFirst({
      where: { 
        cpf: cleanCpf,
        tenantId,
      },
      include: {
        addresses: true,
      },
    });
  }

  async update(id: string, updateCustomerDto: UpdateCustomerDto, tenantId: string): Promise<Customer> {
    await this.findOne(id, tenantId); // Verifica se existe

    const data: any = { ...updateCustomerDto };

    // Se está atualizando CPF, valida
    if (updateCustomerDto.cpf) {
      const cleanCpf = CpfValidator.clean(updateCustomerDto.cpf);
      
      if (!CpfValidator.validate(cleanCpf)) {
        throw new BadRequestException('CPF inválido');
      }

      // Verifica se novo CPF já existe (exceto para o próprio cliente)
      const existingCustomer = await this.prisma.customer.findFirst({
        where: {
          cpf: cleanCpf,
          tenantId,
          NOT: { id },
        },
      });

      if (existingCustomer) {
        throw new ConflictException('CPF já cadastrado');
      }

      data.cpf = cleanCpf;
    }

    // Limpa telefone se estiver sendo atualizado
    if (updateCustomerDto.phone) {
      data.phone = updateCustomerDto.phone.replace(/\D/g, '');
    }

    return this.prisma.customer.update({
      where: { id },
      data,
      include: {
        addresses: true,
      },
    });
  }

  async remove(id: string, tenantId: string): Promise<Customer> {
    await this.findOne(id, tenantId); // Verifica se existe

    // Soft delete - apenas desativa
    return this.prisma.customer.update({
      where: { id },
      data: { active: false },
    });
  }

  async search(searchDto: SearchCustomerDto, tenantId: string): Promise<Customer[]> {
    const where: Prisma.CustomerWhereInput = {
      tenantId,
      AND: [],
    };

    if (searchDto.name) {
      (where.AND as any[]).push({
        name: {
          contains: searchDto.name,
          mode: 'insensitive',
        },
      });
    }

    if (searchDto.cpf) {
      const cleanCpf = CpfValidator.clean(searchDto.cpf);
      (where.AND as any[]).push({
        cpf: {
          contains: cleanCpf,
        },
      });
    }

    if (searchDto.phone) {
      const cleanPhone = searchDto.phone.replace(/\D/g, '');
      (where.AND as any[]).push({
        phone: {
          contains: cleanPhone,
        },
      });
    }

    return this.prisma.customer.findMany({
      where,
      include: {
        addresses: {
          where: { main: true },
          take: 1,
        },
        _count: {
          select: {
            orders: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
      take: 50, // Limita resultados
    });
  }

  // Formata dados do cliente para exibição
  formatCustomer(customer: Customer & { addresses?: any[] }): any {
    return {
      ...customer,
      cpf: CpfValidator.format(customer.cpf),
      phone: this.formatPhone(customer.phone),
      mainAddress: customer.addresses?.find(a => a.main) || null,
    };
  }

  private formatPhone(phone: string): string {
    const clean = phone.replace(/\D/g, '');
    
    if (clean.length === 11) {
      return `(${clean.substr(0, 2)}) ${clean.substr(2, 5)}-${clean.substr(7, 4)}`;
    } else if (clean.length === 10) {
      return `(${clean.substr(0, 2)}) ${clean.substr(2, 4)}-${clean.substr(6, 4)}`;
    }
    
    return phone;
  }
}