import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAddressDto } from './dto/create-address.dto';
import { UpdateAddressDto } from './dto/update-address.dto';
import { Address } from '@prisma/client';
import { ViaCepService } from './viacep.service';

@Injectable()
export class AddressesService {
  constructor(
    private prisma: PrismaService,
    private viaCepService: ViaCepService,
  ) {}

  async create(
    customerId: string,
    createAddressDto: CreateAddressDto, tenantId: string
  ): Promise<Address> {
    // Verifica se cliente existe
    await this.verifyCustomerExists(customerId, tenantId);

    // Limpa CEP
    const cleanCep = createAddressDto.cep.replace(/\D/g, '');

    // Se está marcando como principal, desmarca outros
    if (createAddressDto.main) {
      await this.unsetMainAddresses(customerId);
    }

    // Se é o primeiro endereço, marca como principal
    const addressCount = await this.prisma.address.count({
      where: { customerId },
    });

    const isFirstAddress = addressCount === 0;

    return this.prisma.address.create({
      data: {
        customerId,
        ...createAddressDto,
        cep: cleanCep,
        state: createAddressDto.state.toUpperCase(),
        main: createAddressDto.main || isFirstAddress,
      },
    });
  }

  async findAll(customerId: string, tenantId: string): Promise<Address[]> {
    await this.verifyCustomerExists(customerId, tenantId);

    return this.prisma.address.findMany({
      where: { customerId },
      orderBy: [
        { main: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  async findOne(customerId: string, addressId: string, tenantId: string): Promise<Address> {
    // First verify the customer belongs to the tenant
    await this.verifyCustomerExists(customerId, tenantId);
    
    const address = await this.prisma.address.findFirst({
      where: {
        id: addressId,
        customerId,
      },
    });

    if (!address) {
      throw new NotFoundException('Endereço não encontrado');
    }

    return address;
  }

  async update(
    customerId: string,
    addressId: string,
    updateAddressDto: UpdateAddressDto,
    tenantId: string,
  ): Promise<Address> {
    await this.findOne(customerId, addressId, tenantId); // Verifica se existe

    const data: any = { ...updateAddressDto };

    // Limpa CEP se estiver sendo atualizado
    if (updateAddressDto.cep) {
      data.cep = updateAddressDto.cep.replace(/\D/g, '');
    }

    // Normaliza estado
    if (updateAddressDto.state) {
      data.state = updateAddressDto.state.toUpperCase();
    }

    // Se está marcando como principal, desmarca outros
    if (updateAddressDto.main === true) {
      await this.unsetMainAddresses(customerId, addressId);
    }

    return this.prisma.address.update({
      where: { id: addressId },
      data,
    });
  }

  async remove(customerId: string, addressId: string, tenantId: string): Promise<void> {
    const address = await this.findOne(customerId, addressId, tenantId);

    // Se está removendo o endereço principal, marca outro como principal
    if (address.main) {
      const otherAddress = await this.prisma.address.findFirst({
        where: {
          customerId,
          NOT: { id: addressId },
        },
        orderBy: { createdAt: 'desc' },
      });

      if (otherAddress) {
        await this.prisma.address.update({
          where: { id: otherAddress.id },
          data: { main: true },
        });
      }
    }

    await this.prisma.address.delete({
      where: { id: addressId },
    });
  }

  async setAsMain(customerId: string, addressId: string, tenantId: string): Promise<Address> {
    await this.findOne(customerId, addressId, tenantId); // Verifica se existe

    // Desmarca outros endereços como principal
    await this.unsetMainAddresses(customerId, addressId);

    // Marca o endereço como principal
    return this.prisma.address.update({
      where: { id: addressId },
      data: { main: true },
    });
  }

  // Busca endereço pelo CEP usando ViaCEP
  async getAddressByCep(cep: string) {
    return this.viaCepService.getAddressByCep(cep);
  }

  // Métodos auxiliares
  private async verifyCustomerExists(customerId: string, tenantId: string): Promise<void> {
    const customer = await this.prisma.customer.findFirst({
      where: { 
        id: customerId,
        tenantId,
      },
    });

    if (!customer) {
      throw new NotFoundException('Cliente não encontrado');
    }
  }

  private async unsetMainAddresses(
    customerId: string,
    excludeId?: string,
  ): Promise<void> {
    const where: any = {
      customerId,
      main: true,
    };

    if (excludeId) {
      where.NOT = { id: excludeId };
    }

    await this.prisma.address.updateMany({
      where,
      data: { main: false },
    });
  }

  // Formata endereço para exibição
  formatAddress(address: Address): string {
    const parts = [
      address.street,
      address.number,
      address.complement,
      address.neighborhood,
      `${address.city}/${address.state}`,
      this.formatCep(address.cep),
    ].filter(Boolean);

    return parts.join(', ');
  }

  private formatCep(cep: string): string {
    const clean = cep.replace(/\D/g, '');
    
    if (clean.length === 8) {
      return `${clean.substr(0, 5)}-${clean.substr(5, 3)}`;
    }
    
    return cep;
  }
}