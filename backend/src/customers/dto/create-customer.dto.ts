import {
  IsS<PERSON>,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsEmail,
  <PERSON><PERSON>eng<PERSON>,
  Matches,
  IsBoolean,
} from 'class-validator';

export class CreateCustomerDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$/, {
    message: 'CPF deve estar no formato 000.000.000-00 ou conter apenas 11 dígitos',
  })
  cpf: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$|^\d{10,11}$/, {
    message: 'Telefone deve estar no formato (00) 00000-0000 ou conter 10-11 dígitos',
  })
  phone: string;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean = true;
}