import {
  IsString,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  Max<PERSON>ength,
  Matches,
  Length,
} from 'class-validator';

export class CreateAddressDto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{5}-?\d{3}$/, {
    message: 'CEP deve estar no formato 00000-000 ou conter 8 dígitos',
  })
  cep: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  street: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  number: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  neighborhood: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  city: string;

  @IsString()
  @IsNotEmpty()
  @Length(2, 2, { message: 'Estado deve conter 2 caracteres (ex: SP)' })
  state: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  complement?: string;

  @IsBoolean()
  @IsOptional()
  main?: boolean = false;
}