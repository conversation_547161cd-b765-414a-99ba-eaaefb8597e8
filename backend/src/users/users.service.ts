import { Injectable, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User, Role, Prisma } from '@prisma/client';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto, tenantId: string): Promise<Omit<User, 'password'>> {
    // Verificar se email já existe no mesmo tenant
    const existingUser = await this.findByEmailAndTenant(createUserDto.email, tenantId);
    if (existingUser) {
      throw new ConflictException('Email já está em uso');
    }

    // Criptografar senha
    const hashedPassword = await this.hashPassword(createUserDto.password);

    // Criar usuário
    const user = await this.prisma.user.create({
      data: {
        name: createUserDto.name,
        email: createUserDto.email,
        password: hashedPassword,
        role: createUserDto.role,
        active: createUserDto.active ?? true,
        tenantId,
      },
    });

    // Retornar sem a senha
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async findAll(
    tenantId: string,
    filters?: {
      role?: Role;
      active?: boolean;
      email?: string;
    },
  ): Promise<Omit<User, 'password'>[]> {
    const where: Prisma.UserWhereInput = {
      tenantId,
    };

    if (filters?.role) {
      where.role = filters.role;
    }

    if (filters?.active !== undefined) {
      where.active = filters.active;
    }

    if (filters?.email) {
      where.email = {
        contains: filters.email,
        mode: 'insensitive',
      };
    }

    const users = await this.prisma.user.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Remover senhas
    return users.map(({ password, ...user }) => user);
  }

  async findOne(id: string, tenantId: string): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findFirst({
      where: { 
        id,
        tenantId,
      },
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
    currentUserRole: Role,
    tenantId: string,
  ): Promise<Omit<User, 'password'>> {
    // Buscar usuário a ser editado
    const userToUpdate = await this.prisma.user.findFirst({
      where: { 
        id,
        tenantId,
      },
    });

    if (!userToUpdate) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // SUPERVISOR não pode editar ADMIN
    if (currentUserRole === 'SUPERVISOR' && userToUpdate.role === 'ADMIN') {
      throw new ForbiddenException('Supervisores não podem editar administradores');
    }

    // Verificar se novo email já existe (se estiver mudando)
    if (updateUserDto.email && updateUserDto.email !== userToUpdate.email) {
      const existingUser = await this.findByEmailAndTenant(updateUserDto.email, tenantId);
      if (existingUser) {
        throw new ConflictException('Email já está em uso');
      }
    }

    console.log('Updating user with data:', updateUserDto);
    console.log('Current user role:', userToUpdate.role);
    console.log('New role:', updateUserDto.role);

    // Atualizar usuário
    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: updateUserDto,
    });

    console.log('User updated successfully:', updatedUser);

    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  async softDelete(id: string, tenantId: string): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findFirst({
      where: { 
        id,
        tenantId,
      },
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Soft delete - apenas desativar
    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: { active: false },
    });

    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  // Métodos auxiliares existentes
  async findByEmail(email: string) {
    // This method is used by auth service and needs to find across all tenants
    // In a proper multi-tenant setup, you might want to include tenant selection during login
    return this.prisma.user.findFirst({
      where: { email },
    });
  }

  async findById(id: string) {
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async findByEmailAndTenant(email: string, tenantId: string) {
    return this.prisma.user.findFirst({
      where: { 
        email,
        tenantId,
      },
    });
  }
}