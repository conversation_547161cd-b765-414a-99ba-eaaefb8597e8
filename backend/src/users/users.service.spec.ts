import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { Role } from '@prisma/client';

describe('UsersService', () => {
  let service: UsersService;
  let prisma: PrismaService;

  const mockPrismaService = {
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('deve criar um novo usuário com sucesso', async () => {
      const createUserDto = {
        name: 'Teste',
        email: '<EMAIL>',
        password: 'senha123',
        role: 'VENDEDOR' as Role,
        active: true,
      };

      const hashedPassword = 'hashedPassword';
      const createdUser = {
        id: '1',
        ...createUserDto,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findUnique.mockResolvedValue(null);
      mockPrismaService.user.create.mockResolvedValue(createdUser);
      jest.spyOn(service, 'hashPassword').mockResolvedValue(hashedPassword);

      const tenantId = 'tenant-1';
      const result = await service.create(createUserDto, tenantId);

      expect(result).not.toHaveProperty('password');
      expect(result.email).toBe(createUserDto.email);
      expect(mockPrismaService.user.create).toHaveBeenCalled();
    });

    it('deve lançar ConflictException se email já existe', async () => {
      const createUserDto = {
        name: 'Teste',
        email: '<EMAIL>',
        password: 'senha123',
        role: 'VENDEDOR' as Role,
        active: true,
      };

      mockPrismaService.user.findUnique.mockResolvedValue({ id: '1', email: '<EMAIL>' });

      const tenantId = 'tenant-1';
      await expect(service.create(createUserDto, tenantId)).rejects.toThrow(ConflictException);
    });
  });

  describe('update', () => {
    it('deve permitir ADMIN editar qualquer usuário', async () => {
      const userId = '1';
      const updateDto = { name: 'Novo Nome' };
      const existingUser = {
        id: userId,
        name: 'Nome Antigo',
        email: '<EMAIL>',
        password: 'hash',
        role: 'VENDEDOR' as Role,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findFirst.mockResolvedValue(existingUser);
      mockPrismaService.user.update.mockResolvedValue({ ...existingUser, ...updateDto });

      const tenantId = 'tenant-1';
      const result = await service.update(userId, updateDto, Role.ADMIN, tenantId);

      expect(result).not.toHaveProperty('password');
      expect(mockPrismaService.user.update).toHaveBeenCalled();
    });

    it('deve lançar ForbiddenException quando SUPERVISOR tentar editar ADMIN', async () => {
      const userId = '1';
      const updateDto = { name: 'Novo Nome' };
      const adminUser = {
        id: userId,
        name: 'Admin',
        email: '<EMAIL>',
        password: 'hash',
        role: 'ADMIN' as Role,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findFirst.mockResolvedValue(adminUser);

      const tenantId = 'tenant-1';
      await expect(
        service.update(userId, updateDto, Role.SUPERVISOR, tenantId)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('softDelete', () => {
    it('deve desativar usuário ao invés de deletar', async () => {
      const userId = '1';
      const user = {
        id: userId,
        name: 'Teste',
        email: '<EMAIL>',
        password: 'hash',
        role: 'VENDEDOR' as Role,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrismaService.user.findFirst.mockResolvedValue(user);
      mockPrismaService.user.update.mockResolvedValue({ ...user, active: false });

      const tenantId = 'tenant-1';
      const result = await service.softDelete(userId, tenantId);

      expect(result.active).toBe(false);
      expect(mockPrismaService.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: { active: false },
      });
    });

    it('deve lançar NotFoundException se usuário não existe', async () => {
      mockPrismaService.user.findFirst.mockResolvedValue(null);

      const tenantId = 'tenant-1';
      await expect(service.softDelete('999', tenantId)).rejects.toThrow(NotFoundException);
    });
  });
});