import { IsOptional, IsString, Is<PERSON><PERSON>, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { Role } from '@prisma/client';

export class FindAllUsersDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  limit?: number = 10;
}
