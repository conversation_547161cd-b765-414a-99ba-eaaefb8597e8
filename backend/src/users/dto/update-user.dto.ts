import { IsString, IsEmail, IsEnum, IsBoolean, IsO<PERSON>al, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { Role } from '@prisma/client';

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEmail({}, { message: 'Email deve ser válido' })
  @IsOptional()
  email?: string;

  @IsEnum(Role, { message: 'Role deve ser ADMIN, SUPERVISOR, COBRADOR ou VENDEDOR' })
  @IsOptional()
  role?: Role;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(100)
  commissionRate?: number;

  // Não permitir alteração de senha nesta sprint
}