import {
  Is<PERSON><PERSON>,
  IsE<PERSON>,
  <PERSON><PERSON>num,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Role } from '@prisma/client';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty({ message: 'Nome é obrigatório' })
  name: string;

  @IsEmail({}, { message: 'Email deve ser válido' })
  @IsNotEmpty({ message: 'Email é obrigatório' })
  email: string;

  @IsString()
  @IsNotEmpty({ message: 'Senha é obrigatória' })
  @MinLength(6, { message: '<PERSON>ha deve ter pelo menos 6 caracteres' })
  password: string;

  @IsEnum(Role, { message: 'Role deve ser ADMIN, SUPERVISOR, COBRADOR ou VENDEDOR' })
  @IsNotEmpty({ message: 'Role é obrigatório' })
  role: Role;

  @IsOptional()
  @IsBoolean()
  active?: boolean = true;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  commissionRate?: number = 0;
}
