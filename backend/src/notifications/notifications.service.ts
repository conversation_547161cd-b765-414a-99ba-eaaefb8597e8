import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationQueue, NotificationJobData } from './queues/notification.queue';
import {
  NotificationType,
  NotificationChannel,
  NotificationStatus,
  NotificationJob,
  Order,
  Customer,
  OrderStatus,
} from '@prisma/client';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private prisma: PrismaService,
    private notificationQueue: NotificationQueue,
  ) {}

  /**
   * Cria e enfileira uma notificação
   */
  async createNotification(
    type: NotificationType,
    channel: NotificationChannel,
    recipient: {
      phone?: string;
      email?: string;
      name: string;
    },
    payload: Record<string, any>,
  ): Promise<NotificationJob> {
    // Cria registro no banco
    const notification = await this.prisma.notificationJob.create({
      data: {
        type,
        channel,
        payload: payload as any,
        status: NotificationStatus.PENDING,
      },
    });

    // Adiciona à fila
    await this.notificationQueue.addNotification({
      id: notification.id,
      type,
      channel,
      recipient,
      payload,
    });

    this.logger.log(`Notificação criada: ${notification.id} - Tipo: ${type}`);

    return notification;
  }

  /**
   * Notifica sobre criação de pedido
   */
  async notifyOrderCreated(order: Order & { customer?: Customer | null }) {
    if (!order.customer?.phone) {
      this.logger.warn(`Pedido ${order.id} sem telefone do cliente`);
      return;
    }

    // Busca dados completos do pedido
    const fullOrder = await this.prisma.order.findUnique({
      where: { id: order.id },
      include: {
        customer: true,
        seller: true,
        items: true,
      },
    });

    if (!fullOrder) return;

    const payload = {
      order: {
        id: fullOrder.id,
        total: fullOrder.total.toString(),
        items: fullOrder.items.map(item => ({
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: item.unitPrice.toString(),
        })),
      },
      customer: {
        name: fullOrder.customer?.name || fullOrder.customerName,
      },
      seller: {
        name: fullOrder.seller.name,
      },
    };

    return this.createNotification(
      NotificationType.ORDER_CREATED,
      NotificationChannel.WHATSAPP,
      {
        phone: fullOrder.customer?.phone || fullOrder.customerPhone,
        name: fullOrder.customer?.name || fullOrder.customerName,
      },
      payload,
    );
  }

  /**
   * Notifica sobre mudança de status do pedido
   */
  async notifyStatusChanged(
    orderId: string,
    previousStatus: OrderStatus,
    newStatus: OrderStatus,
  ) {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        customer: true,
      },
    });

    if (!order || !order.customer?.phone) {
      this.logger.warn(`Pedido ${orderId} sem dados do cliente para notificação`);
      return;
    }

    const payload = {
      order: {
        id: order.id,
      },
      customer: {
        name: order.customer.name,
      },
      previousStatus: this.formatStatus(previousStatus),
      newStatus: this.formatStatus(newStatus),
    };

    return this.createNotification(
      NotificationType.STATUS_CHANGED,
      NotificationChannel.WHATSAPP,
      {
        phone: order.customer.phone,
        name: order.customer.name,
      },
      payload,
    );
  }

  /**
   * Notifica sobre estoque baixo
   */
  async notifyLowStock(
    productName: string,
    variationName: string,
    currentQuantity: number,
    minAlert: number,
  ) {
    // Busca administradores para notificar
    const admins = await this.prisma.user.findMany({
      where: {
        role: { in: ['ADMIN', 'SUPERVISOR'] },
        active: true,
      },
    });

    const payload = {
      product: { name: productName },
      variation: { name: variationName },
      inventory: {
        quantity: currentQuantity,
        minAlert,
      },
    };

    // Cria notificações para cada admin
    const notifications: any[] = [];
    for (const admin of admins) {
      // Por enquanto, usa um telefone padrão (em produção, adicionar telefone ao User)
      const notification = await this.createNotification(
        NotificationType.LOW_STOCK,
        NotificationChannel.WHATSAPP,
        {
          phone: '11999999999', // TODO: Adicionar campo phone ao User
          name: admin.name,
        },
        payload,
      );
      notifications.push(notification);
    }

    return notifications;
  }

  /**
   * Lista histórico de notificações
   */
  async findAll(filters?: {
    status?: NotificationStatus;
    type?: NotificationType;
    channel?: NotificationChannel;
    startDate?: Date;
    endDate?: Date;
  }) {
    const where: any = {};

    if (filters?.status) where.status = filters.status;
    if (filters?.type) where.type = filters.type;
    if (filters?.channel) where.channel = filters.channel;

    if (filters?.startDate || filters?.endDate) {
      where.createdAt = {};
      if (filters.startDate) where.createdAt.gte = filters.startDate;
      if (filters.endDate) where.createdAt.lte = filters.endDate;
    }

    return this.prisma.notificationJob.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: 100,
    });
  }

  /**
   * Busca uma notificação específica
   */
  async findOne(id: string) {
    return this.prisma.notificationJob.findUnique({
      where: { id },
    });
  }

  /**
   * Envia notificação de teste
   */
  async sendTestNotification(
    phone: string,
    message: string = 'Esta é uma mensagem de teste do ZenCash! 🚀',
  ) {
    const testData: NotificationJobData = {
      type: NotificationType.CUSTOM,
      channel: NotificationChannel.WHATSAPP,
      recipient: {
        phone,
        name: 'Teste',
      },
      payload: {
        subject: 'Teste de Notificação',
        message,
      },
    };

    // Adiciona diretamente à fila sem criar registro
    const job = await this.notificationQueue.addNotification(testData);

    return {
      jobId: job?.id || 'redis-unavailable',
      message: job ? 'Notificação de teste enviada para fila' : 'Redis não disponível - notificação não enviada',
    };
  }

  /**
   * Retorna estatísticas da fila
   */
  async getQueueStats() {
    return this.notificationQueue.getQueueStats();
  }

  /**
   * Simplified sendNotification for Correios integration
   */
  async sendNotification(payload: {
    type: NotificationType | 'CUSTOM';
    recipient: string;
    channel?: NotificationChannel;
    data: any;
  }): Promise<void> {
    try {
      const channel = payload.channel || NotificationChannel.WHATSAPP;

      // Create notification job
      await this.prisma.notificationJob.create({
        data: {
          type: payload.type === 'CUSTOM' ? NotificationType.CUSTOM : payload.type,
          channel,
          payload: payload.data,
          status: 'PENDING',
        },
      });

      this.logger.log(
        `Notification queued: ${payload.type} to ${payload.recipient} via ${channel}`
      );
    } catch (error) {
      this.logger.error('Error sending notification:', error);
    }
  }

  /**
   * Formata status para exibição
   */
  private formatStatus(status: OrderStatus): string {
    const statusMap: Record<OrderStatus, string> = {
      Analise: 'Em Análise',
      Separacao: 'Em Separação',
      Transito: 'Em Trânsito',
      ConfirmarEntrega: 'Confirmar Entrega',
      PagamentoPendente: 'Pagamento Pendente',
      Negociacao: 'Em Negociação',
      Promessa: 'Promessa de Pagamento',
      Parcial: 'Pagamento Parcial',
      Completo: 'Completo',
      Recuperacao: 'Em Recuperação',
      Frustrado: 'Frustrado',
      EntregaFalha: 'Falha na Entrega',
      RetirarCorreios: 'Retirar nos Correios',
      DevolvidoCorreios: 'Devolvido pelos Correios',
      Cancelado: 'Cancelado',
      ConfirmarPagamento: 'Confirmar Pagamento',
    };

    return statusMap[status] || status;
  }
}