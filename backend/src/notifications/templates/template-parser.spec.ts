import { Template<PERSON>arser } from './template-parser';

describe('TemplateParser', () => {
  describe('parse', () => {
    it('should replace simple placeholders', () => {
      const template = '<PERSON><PERSON><PERSON> {{name}}, bem-vindo!';
      const context = { name: '<PERSON>' };
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('<PERSON><PERSON><PERSON> João, bem-vindo!');
    });

    it('should replace multiple placeholders', () => {
      const template = 'Pedido {{orderId}} - Cliente: {{customerName}} - Total: R$ {{total}}';
      const context = {
        orderId: '12345',
        customerName: '<PERSON>',
        total: '150.00',
      };
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('Pedido 12345 - Cliente: <PERSON> - Total: R$ 150.00');
    });

    it('should handle nested properties', () => {
      const template = 'O<PERSON><PERSON> {{customer.name}}, seu pedido {{order.id}} está {{order.status}}';
      const context = {
        customer: { name: '<PERSON>' },
        order: { id: '789', status: 'enviado' },
      };
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('Olá Carlos, seu pedido 789 está enviado');
    });

    it('should keep placeholder when value is missing', () => {
      const template = 'Olá {{name}}, seu CPF é {{cpf}}';
      const context = { name: 'João' };
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('Olá João, seu CPF é {{cpf}}');
    });

    it('should handle empty context', () => {
      const template = 'Olá {{name}}!';
      const context = {};
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('Olá {{name}}!');
    });

    it('should convert non-string values to string', () => {
      const template = 'Quantidade: {{quantity}} - Ativo: {{active}}';
      const context = {
        quantity: 10,
        active: true,
      };
      
      const result = TemplateParser.parse(template, context);
      
      expect(result).toBe('Quantidade: 10 - Ativo: true');
    });
  });

  describe('validateTemplate', () => {
    it('should return empty array when all placeholders have values', () => {
      const template = 'Olá {{name}}, pedido {{orderId}}';
      const context = {
        name: 'João',
        orderId: '123',
      };
      
      const missing = TemplateParser.validateTemplate(template, context);
      
      expect(missing).toEqual([]);
    });

    it('should return missing placeholders', () => {
      const template = 'Olá {{name}}, seu CPF {{cpf}} e telefone {{phone}}';
      const context = {
        name: 'João',
      };
      
      const missing = TemplateParser.validateTemplate(template, context);
      
      expect(missing).toEqual(['cpf', 'phone']);
    });

    it('should validate nested placeholders', () => {
      const template = 'Cliente: {{customer.name}} - Pedido: {{order.id}}';
      const context = {
        customer: { name: 'Maria' },
      };
      
      const missing = TemplateParser.validateTemplate(template, context);
      
      expect(missing).toEqual(['order.id']);
    });

    it('should handle template without placeholders', () => {
      const template = 'Mensagem sem placeholders';
      const context = {};
      
      const missing = TemplateParser.validateTemplate(template, context);
      
      expect(missing).toEqual([]);
    });
  });
});