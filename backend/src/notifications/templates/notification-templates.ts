import { NotificationType } from '@prisma/client';

export interface NotificationTemplate {
  subject?: string;
  body: string;
}

export const notificationTemplates: Record<NotificationType, NotificationTemplate> = {
  [NotificationType.ORDER_CREATED]: {
    subject: 'Pedido Confirmado',
    body: `🎉 Olá {{customer.name}}!

Seu pedido #{{order.id}} foi confirmado com sucesso!

📦 Itens do pedido:
{{order.itemsList}}

💰 Total: R$ {{order.total}}
📱 Vendedor: {{seller.name}}

Acompanhe o status do seu pedido respondendo esta mensagem.

Obrigado pela preferência!
ZenCash`,
  },

  [NotificationType.STATUS_CHANGED]: {
    subject: 'Status do Pedido Atualizado',
    body: `📢 Olá {{customer.name}}!

Seu pedido #{{order.id}} foi atualizado:

Status anterior: {{previousStatus}}
✅ Novo status: {{newStatus}}

{{statusMessage}}

Qualquer dúvida, entre em contato!
ZenCash`,
  },

  [NotificationType.LOW_STOCK]: {
    subject: 'Alerta de Estoque Baixo',
    body: `⚠️ ALERTA DE ESTOQUE

O produto abaixo está com estoque baixo:

📦 Produto: {{product.name}}
🔢 Variação: {{variation.name}}
📊 Quantidade atual: {{inventory.quantity}}
⚠️ Quantidade mínima: {{inventory.minAlert}}

Providencie a reposição o quanto antes!`,
  },

  [NotificationType.PAYMENT_REMINDER]: {
    subject: 'Lembrete de Pagamento',
    body: `💳 Olá {{customer.name}}!

Este é um lembrete amigável sobre o pagamento do pedido #{{order.id}}.

💰 Valor: R$ {{order.total}}
📅 Vencimento: {{dueDate}}

Para mais informações, entre em contato com {{collector.name}}.

ZenCash`,
  },

  [NotificationType.CUSTOM]: {
    subject: '{{subject}}',
    body: '{{message}}',
  },
};

// Mensagens específicas por status
export const statusMessages: Record<string, string> = {
  EM_SEPARACAO: '📦 Seu pedido está sendo preparado com carinho!',
  ENVIADO: '🚚 Seu pedido saiu para entrega e chegará em breve!',
  COMPLETO: '✅ Pedido entregue com sucesso! Esperamos que aproveite!',
  FALHA: '❌ Houve um problema na entrega. Entraremos em contato.',
  CANCELADO: '❌ Seu pedido foi cancelado. Entre em contato para mais informações.',
  RECUPERACAO: '🔄 Estamos tentando uma nova entrega do seu pedido.',
  NEGOCIACAO: '💬 Nosso time entrará em contato para negociar o pagamento.',
  PAGAMENTO_PARCIAL: '💰 Pagamento parcial recebido. Entre em contato para quitar o restante.',
};