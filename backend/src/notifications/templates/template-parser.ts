export class TemplateParser {
  /**
   * Substitui placeholders {{key}} com valores do contexto
   * @param template String do template com placeholders
   * @param context Objeto com valores para substituição
   * @returns String com placeholders substituídos
   */
  static parse(template: string, context: Record<string, any>): string {
    return template.replace(/\{\{(\w+(\.\w+)*)\}\}/g, (match, key) => {
      // Suporta nested properties como {{order.id}}
      const value = key.split('.').reduce((obj: any, prop: string) => {
        return obj?.[prop];
      }, context);

      // Se o valor não existir, mantém o placeholder
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Valida se todos os placeholders do template têm valores no contexto
   * @param template String do template
   * @param context Objeto com valores
   * @returns Array com placeholders faltando
   */
  static validateTemplate(template: string, context: Record<string, any>): string[] {
    const placeholders = template.match(/\{\{(\w+(\.\w+)*)\}\}/g) || [];
    const missingKeys: string[] = [];

    placeholders.forEach(placeholder => {
      const key = placeholder.replace(/\{\{|\}\}/g, '');
      const value = key.split('.').reduce((obj: any, prop: string) => {
        return obj?.[prop];
      }, context);

      if (value === undefined) {
        missingKeys.push(key);
      }
    });

    return missingKeys;
  }
}