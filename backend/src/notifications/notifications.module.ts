import { Module, Global } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { NotificationQueue } from './queues/notification.queue';
import { NotificationWorker } from './queues/notification.worker';
import { WhatsAppProvider } from './providers/whatsapp.provider';
import { PrismaModule } from '../prisma/prisma.module';

@Global() // Torna o módulo global para poder ser usado em outros módulos
@Module({
  imports: [PrismaModule],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    NotificationQueue,
    NotificationWorker,
    WhatsAppProvider,
  ],
  exports: [NotificationsService], // Exporta apenas o service
})
export class NotificationsModule {}