import { Queue, Worker, QueueEvents } from 'bullmq';
import { Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NotificationType, NotificationChannel } from '@prisma/client';

export interface NotificationJobData {
  id?: string;
  type: NotificationType;
  channel: NotificationChannel;
  recipient: {
    phone?: string;
    email?: string;
    name: string;
  };
  payload: Record<string, any>;
}

@Injectable()
export class NotificationQueue implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NotificationQueue.name);
  private queue: Queue<NotificationJobData> | null = null;
  private queueEvents: QueueEvents | null = null;
  private readonly queueName = 'notifications';

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      const redisUrl = this.configService.get('REDIS_URL');
      
      if (!redisUrl) {
        this.logger.warn('REDIS_URL not configured. Notifications disabled.');
        return;
      }

      // Parse Redis URL - Railway provides full redis:// URL
      const connectionOptions = new URL(redisUrl);
      const redisConfig = {
        host: connectionOptions.hostname,
        port: parseInt(connectionOptions.port, 10) || 6379,
        password: connectionOptions.password || connectionOptions.username,
      };

      // Cria a fila
      this.queue = new Queue<NotificationJobData>(this.queueName, {
        connection: redisConfig,
        defaultJobOptions: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000, // 5 segundos
          },
          removeOnComplete: {
            age: 24 * 3600, // 24 horas
            count: 100, // Mantém últimos 100 jobs completos
          },
          removeOnFail: {
            age: 7 * 24 * 3600, // 7 dias
          },
        },
      });

      // Cria eventos da fila para monitoramento
      this.queueEvents = new QueueEvents(this.queueName, {
        connection: redisConfig,
      });

      this.setupEventListeners();
      
      // Test Redis connection by getting job counts
      try {
        await this.queue.getJobCounts();
        this.logger.log('Fila de notificações inicializada com Redis');
      } catch (pingError) {
        throw new Error('Failed to connect to Redis');
      }
    } catch (error) {
      this.logger.warn('Redis não disponível. Notificações desabilitadas.');
      this.logger.debug(`Erro ao conectar ao Redis: ${error.message}`);
      // Clean up failed connections
      if (this.queue) {
        await this.queue.close().catch(() => {});
        this.queue = null;
      }
      if (this.queueEvents) {
        await this.queueEvents.close().catch(() => {});
        this.queueEvents = null;
      }
      // A aplicação continua funcionando sem Redis
    }
  }

  async onModuleDestroy() {
    await this.queue?.close();
    await this.queueEvents?.close();
  }

  /**
   * Adiciona uma notificação à fila
   */
  async addNotification(data: NotificationJobData) {
    if (!this.queue) {
      this.logger.warn('Fila de notificações não disponível - Redis não conectado');
      return null;
    }

    const job = await this.queue.add('send-notification', data, {
      delay: data.payload.delay || 0,
      priority: this.getPriority(data.type),
    });

    this.logger.log(`Notificação adicionada à fila: ${job.id} - Tipo: ${data.type}`);
    
    return job;
  }

  /**
   * Adiciona múltiplas notificações em lote
   */
  async addBulkNotifications(notifications: NotificationJobData[]) {
    if (!this.queue) {
      this.logger.warn('Fila de notificações não disponível - Redis não conectado');
      return [];
    }

    const jobs = notifications.map(data => ({
      name: 'send-notification',
      data,
      opts: {
        delay: data.payload.delay || 0,
        priority: this.getPriority(data.type),
      },
    }));

    const results = await this.queue.addBulk(jobs);
    
    this.logger.log(`${results.length} notificações adicionadas em lote`);
    
    return results;
  }

  /**
   * Retorna estatísticas da fila
   */
  async getQueueStats() {
    if (!this.queue) {
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        total: 0,
        redis: 'disconnected'
      };
    }

    const [waiting, active, completed, failed] = await Promise.all([
      this.queue.getWaitingCount(),
      this.queue.getActiveCount(),
      this.queue.getCompletedCount(),
      this.queue.getFailedCount(),
    ]);

    return {
      waiting,
      active,
      completed,
      failed,
      total: waiting + active + completed + failed,
      redis: 'connected'
    };
  }

  /**
   * Limpa jobs completos
   */
  async cleanCompleted() {
    if (!this.queue) {
      this.logger.warn('Fila não disponível - Redis não conectado');
      return 0;
    }
    const jobs = await this.queue.clean(1000, 0, 'completed');
    this.logger.log(`${jobs.length} jobs completos removidos`);
    return jobs.length;
  }

  /**
   * Limpa jobs falhados
   */
  async cleanFailed() {
    if (!this.queue) {
      this.logger.warn('Fila não disponível - Redis não conectado');
      return 0;
    }
    const jobs = await this.queue.clean(1000, 0, 'failed');
    this.logger.log(`${jobs.length} jobs falhados removidos`);
    return jobs.length;
  }

  /**
   * Pausa a fila
   */
  async pause() {
    if (!this.queue) {
      this.logger.warn('Fila não disponível - Redis não conectado');
      return;
    }
    await this.queue.pause();
    this.logger.warn('Fila de notificações pausada');
  }

  /**
   * Resume a fila
   */
  async resume() {
    if (!this.queue) {
      this.logger.warn('Fila não disponível - Redis não conectado');
      return;
    }
    await this.queue.resume();
    this.logger.log('Fila de notificações resumida');
  }

  /**
   * Define prioridade baseada no tipo de notificação
   */
  private getPriority(type: NotificationType): number {
    const priorities = {
      [NotificationType.ORDER_CREATED]: 1,
      [NotificationType.STATUS_CHANGED]: 2,
      [NotificationType.PAYMENT_REMINDER]: 3,
      [NotificationType.LOW_STOCK]: 4,
      [NotificationType.CUSTOM]: 5,
    };

    return priorities[type] || 5;
  }

  /**
   * Configura listeners de eventos da fila
   */
  private setupEventListeners() {
    if (!this.queueEvents) return;
    
    this.queueEvents.on('completed', ({ jobId }) => {
      this.logger.debug(`Job ${jobId} completado`);
    });

    this.queueEvents.on('failed', ({ jobId, failedReason }) => {
      this.logger.error(`Job ${jobId} falhou: ${failedReason}`);
    });

    this.queueEvents.on('stalled', ({ jobId }) => {
      this.logger.warn(`Job ${jobId} travado`);
    });
  }

  /**
   * Retorna a instância da fila (para o worker)
   */
  getQueue(): Queue<NotificationJobData> | null {
    return this.queue;
  }
}