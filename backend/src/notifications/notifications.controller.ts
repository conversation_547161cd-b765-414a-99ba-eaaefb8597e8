import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { SendTestNotificationDto } from './dto/send-test-notification.dto';
import { FilterNotificationsDto } from './dto/filter-notifications.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  // Listar histórico de notificações (apenas ADMIN e SUPERVISOR)
  @Get()
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async findAll(@Query() filters: FilterNotificationsDto) {
    const filterOptions = {
      ...filters,
      startDate: filters.startDate ? new Date(filters.startDate) : undefined,
      endDate: filters.endDate ? new Date(filters.endDate) : undefined,
    };

    const notifications = await this.notificationsService.findAll(filterOptions);

    return {
      total: notifications.length,
      notifications: notifications.map(this.formatNotification),
    };
  }

  // Buscar notificação específica
  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async findOne(@Param('id') id: string) {
    const notification = await this.notificationsService.findOne(id);
    return notification ? this.formatNotification(notification) : null;
  }

  // Enviar notificação de teste
  @Post('test')
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async sendTest(@Body() dto: SendTestNotificationDto) {
    return this.notificationsService.sendTestNotification(
      dto.phone,
      dto.message,
    );
  }

  // Estatísticas da fila
  @Get('queue/stats')
  @Roles('ADMIN', 'SUPERVISOR')
  async getQueueStats() {
    return this.notificationsService.getQueueStats();
  }

  // Formata notificação para resposta
  private formatNotification(notification: any) {
    return {
      id: notification.id,
      type: notification.type,
      channel: notification.channel,
      status: notification.status,
      retries: notification.retries,
      error: notification.error,
      sentAt: notification.sentAt,
      createdAt: notification.createdAt,
      payload: notification.payload,
    };
  }
}