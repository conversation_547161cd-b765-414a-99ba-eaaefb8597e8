import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsService } from './notifications.service';
import { PrismaService } from '../prisma/prisma.service';
import { WhatsAppProvider } from './providers/whatsapp.provider';
import { TemplateParser } from './templates/template-parser';
import { ConfigurationService } from '../configuration/configuration.service';
import {
  NotificationType,
  NotificationChannel,
  NotificationStatus,
} from '@prisma/client';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let prisma: PrismaService;
  let whatsappProvider: WhatsAppProvider;
  let templateParser: TemplateParser;
  let configService: ConfigurationService;

  const mockPrismaService = {
    notificationJob: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockWhatsAppProvider = {
    send: jest.fn(),
  };


  const mockTemplateParser = {
    parse: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: WhatsAppProvider,
          useValue: mockWhatsAppProvider,
        },
        {
          provide: TemplateParser,
          useValue: mockTemplateParser,
        },
        {
          provide: ConfigurationService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    prisma = module.get<PrismaService>(PrismaService);
    whatsappProvider = module.get<WhatsAppProvider>(WhatsAppProvider);
    templateParser = module.get<TemplateParser>(TemplateParser);
    configService = module.get<ConfigurationService>(ConfigurationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });


});