import { Test, TestingModule } from '@nestjs/testing';
import { WhatsAppProvider } from './whatsapp.provider';

describe('WhatsAppProvider', () => {
  let provider: WhatsAppProvider;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [WhatsAppProvider],
    }).compile();

    provider = module.get<WhatsAppProvider>(WhatsAppProvider);
  });

  describe('sendMessage', () => {
    it('should send message successfully', async () => {
      const message = {
        to: '11987654321',
        message: 'Teste de mensagem',
      };

      const result = await provider.sendMessage(message);

      expect(result.success).toBe(true);
      expect(result.messageId).toBeDefined();
      expect(result.messageId).toMatch(/^mock_/);
    });

    it('should format phone number correctly', async () => {
      const message = {
        to: '(11) 98765-4321',
        message: 'Teste',
      };

      const result = await provider.sendMessage(message);

      expect(result.success).toBe(true);
    });

    it('should add country code when missing', async () => {
      const message = {
        to: '11987654321',
        message: 'Teste',
      };

      const result = await provider.sendMessage(message);

      expect(result.success).toBe(true);
    });

    it('should reject invalid phone number', async () => {
      const message = {
        to: '123', // Número muito curto
        message: 'Teste',
      };

      const result = await provider.sendMessage(message);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Número de telefone inválido');
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format Brazilian mobile number', () => {
      const formatted = provider.formatPhoneNumber('5511987654321');
      expect(formatted).toBe('+55 11 98765-4321');
    });

    it('should return original if format does not match', () => {
      const formatted = provider.formatPhoneNumber('123456');
      expect(formatted).toBe('123456');
    });
  });

  describe('checkMessageStatus', () => {
    it('should return delivered status', async () => {
      const status = await provider.checkMessageStatus('mock_123');
      expect(status).toBe('delivered');
    });
  });
});