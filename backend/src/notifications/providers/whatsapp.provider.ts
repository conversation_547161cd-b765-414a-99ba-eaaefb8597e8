import { Injectable, Logger } from '@nestjs/common';

export interface WhatsAppMessage {
  to: string;
  message: string;
  mediaUrl?: string;
}

export interface WhatsAppResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

@Injectable()
export class WhatsAppProvider {
  private readonly logger = new Logger(WhatsAppProvider.name);

  /**
   * Envia mensagem via WhatsApp
   * NOTA: Esta é uma implementação mockada para desenvolvimento
   * Em produção, substituir por integração real (Twilio, Zapvoice, etc)
   */
  async sendMessage(data: WhatsAppMessage): Promise<WhatsAppResponse> {
    try {
      // Valida número de telefone
      const cleanPhone = this.cleanPhoneNumber(data.to);
      
      if (!this.isValidPhoneNumber(cleanPhone)) {
        throw new Error('Número de telefone inválido');
      }

      // Simula delay de envio
      await this.simulateDelay();

      // Log da mensagem que seria enviada
      this.logger.log(`[MOCK] Enviando WhatsApp para ${cleanPhone}`);
      this.logger.debug(`[MOCK] Mensagem: ${data.message.substring(0, 100)}...`);

      // Simula sucesso/falha aleatória (90% sucesso)
      const success = Math.random() > 0.1;

      if (success) {
        const messageId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        this.logger.log(`[MOCK] WhatsApp enviado com sucesso. ID: ${messageId}`);
        
        return {
          success: true,
          messageId,
        };
      } else {
        throw new Error('Falha simulada no envio');
      }
    } catch (error: any) {
      this.logger.error(`[MOCK] Erro ao enviar WhatsApp: ${error.message}`);
      
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Envia mensagem com mídia (imagem, documento, etc)
   */
  async sendMediaMessage(
    to: string,
    message: string,
    mediaUrl: string,
  ): Promise<WhatsAppResponse> {
    return this.sendMessage({
      to,
      message,
      mediaUrl,
    });
  }

  /**
   * Verifica status de entrega da mensagem
   */
  async checkMessageStatus(messageId: string): Promise<string> {
    // Mock: sempre retorna 'delivered'
    await this.simulateDelay(500);
    return 'delivered';
  }

  /**
   * Limpa número de telefone removendo caracteres especiais
   */
  private cleanPhoneNumber(phone: string): string {
    // Remove todos os caracteres não numéricos
    let cleaned = phone.replace(/\D/g, '');

    // Se não tem código do país, adiciona +55 (Brasil)
    if (!cleaned.startsWith('55') && cleaned.length <= 11) {
      cleaned = '55' + cleaned;
    }

    return cleaned;
  }

  /**
   * Valida se o número de telefone é válido
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Verifica se tem pelo menos 12 dígitos (55 + DDD + número)
    return phone.length >= 12 && phone.length <= 15;
  }

  /**
   * Simula delay de rede
   */
  private simulateDelay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Formata número para exibição
   */
  formatPhoneNumber(phone: string): string {
    const cleaned = this.cleanPhoneNumber(phone);
    
    if (cleaned.length === 13) {
      // +55 11 98765-4321
      return `+${cleaned.substr(0, 2)} ${cleaned.substr(2, 2)} ${cleaned.substr(4, 5)}-${cleaned.substr(9, 4)}`;
    }
    
    return phone;
  }
}