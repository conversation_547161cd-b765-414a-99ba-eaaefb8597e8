import * as crypto from "crypto";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma, DuplicateStatus, ReviewDecision, AuditAction, RiskLevel, OrderStatus } from '@prisma/client';
import { DuplicateDetectionService } from './services/duplicate-detection.service';
import { BrazilianAddressParser } from './services/brazilian-address.parser';
import { EncryptionUtil } from '../common/utils/encryption.util';
import { ConfigService } from '@nestjs/config';

export interface ReviewDuplicateDto {
  orderId: string;
  decision: ReviewDecision;
  reviewerId: string;
  reviewerName: string;
  reviewerRole: string;
  notes?: string;
}

export interface DuplicateReviewQueueItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: Date;
  duplicateMatchScore: number;
  matchedOrders: Array<{
    orderId: string;
    orderNumber: string;
    createdAt: Date;
  }>;
}

export interface RiskAssessment {
  riskScore: number;
  riskLevel: RiskLevel;
  riskFactors: string[];
  requiresReview: boolean;
}

export interface OrderReviewItem {
  id: string;
  orderNumber: string | null;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: Date;
  riskScore: number;
  riskLevel: RiskLevel;
  riskFactors: string[];
  status: string;
  isDuplicate: boolean;
  duplicateMatchScore?: number | null;
}

@Injectable()
export class AntifraudService {
  private readonly logger = new Logger(AntifraudService.name);
  private readonly encryptionKey: string;
  private readonly signingKey: string;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private duplicateDetection: DuplicateDetectionService,
    private addressParser: BrazilianAddressParser,
    private encryptionUtil: EncryptionUtil,
  ) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY', '');
    this.signingKey = this.configService.get<string>('SIGNING_KEY', '');
  }

  /**
   * Comprehensive duplicate detection that works with any available data
   */
  async processOrderForComprehensiveDuplicateCheck(
    tenantId: string,
    orderId: string,
    customerData: {
      customerCPF?: string;
      fullAddress?: string;
      customerPhone: string;
      customerName: string;
    },
  ): Promise<void> {
    try {
      const order = await this.prisma.order.findUnique({
        where: { id: orderId },
      });

      if (!order) {
        throw new Error('Order not found');
      }

      let isDuplicate = false;
      let duplicateMatchScore = 0;
      const matchedOrderIds: string[] = [];

      this.logger.log('Checking for duplicates', {
        orderId,
        customerPhone: customerData.customerPhone,
        customerName: customerData.customerName,
        hasCPF: !!customerData.customerCPF,
        hasAddress: !!customerData.fullAddress,
      });

      // Check for phone + name duplicates (most reliable for basic duplicate detection)
      const phoneNameDuplicates = await this.prisma.order.findMany({
        where: {
          tenantId,
          customerPhone: customerData.customerPhone,
          customerName: {
            equals: customerData.customerName,
            mode: 'insensitive',
          },
          id: { not: orderId },
          deletedAt: null, // Exclude soft-deleted orders
          // Check against ALL orders in database, not just recent ones
        },
        select: {
          id: true,
          orderNumber: true,
          createdAt: true,
        },
      });

      if (phoneNameDuplicates.length > 0) {
        isDuplicate = true;
        duplicateMatchScore = 100; // Exact phone + name match
        matchedOrderIds.push(...phoneNameDuplicates.map(o => o.id));
        this.logger.warn('Phone + name duplicate detected', {
          orderId,
          matchedCount: phoneNameDuplicates.length,
          matchedOrderIds: phoneNameDuplicates.map(o => o.orderNumber || o.id),
        });
      }

      // If CPF and address are provided, do the traditional duplicate check
      if (customerData.customerCPF && customerData.fullAddress && !isDuplicate) {
        const duplicateResult = await this.duplicateDetection.checkForDuplicates(
          tenantId,
          customerData.customerCPF,
          customerData.fullAddress,
          orderId,
        );

        if (duplicateResult.isDuplicate) {
          isDuplicate = true;
          duplicateMatchScore = Math.max(duplicateMatchScore, Math.round(duplicateResult.matchScore * 100));
          matchedOrderIds.push(...duplicateResult.matchedOrders.map(m => m.orderId));
        }
      }

      // Additional check: Similar address in recent orders (even without CPF)
      if (customerData.fullAddress && !isDuplicate) {
        // Extract key address components for fuzzy matching
        const addressParts = customerData.fullAddress.toLowerCase().split(/[\s,]+/);
        const streetName = addressParts.find(part => part.length > 4 && /^[a-z]/.test(part));
        
        // Look for orders with similar addresses
        const recentOrders = await this.prisma.order.findMany({
          where: {
            tenantId,
            id: { not: orderId },
            deletedAt: null,
            // Check against ALL orders in database, not just recent ones
          },
          select: {
            id: true,
            orderNumber: true,
            customerName: true,
            customerPhone: true,
            fullAddress: true,
          },
        });

        // Filter for similar addresses
        const addressDuplicates = recentOrders.filter(order => {
          if (!order.fullAddress) return false;
          const orderAddressLower = order.fullAddress.toLowerCase();
          
          // Check if addresses share the same street name
          if (streetName && orderAddressLower.includes(streetName)) {
            // Calculate similarity score
            const commonParts = addressParts.filter(part => 
              part.length > 2 && orderAddressLower.includes(part)
            );
            
            // If more than 60% of address parts match, consider it similar
            return commonParts.length / addressParts.length > 0.6;
          }
          return false;
        });

        if (addressDuplicates.length > 0) {
          // Check if any have same or similar phone/name
          const similarOrders = addressDuplicates.filter(dup => {
            const nameSimilar = dup.customerName.toLowerCase().includes(customerData.customerName.toLowerCase()) ||
                               customerData.customerName.toLowerCase().includes(dup.customerName.toLowerCase());
            const phoneSimilar = dup.customerPhone === customerData.customerPhone;
            return nameSimilar || phoneSimilar;
          });

          if (similarOrders.length > 0) {
            isDuplicate = true;
            duplicateMatchScore = Math.max(duplicateMatchScore, 85); // High score for address + name/phone match
            matchedOrderIds.push(...similarOrders.map(o => o.id));
            this.logger.warn('Similar address duplicate detected', {
              orderId,
              matchedCount: similarOrders.length,
              fullAddress: customerData.fullAddress,
              matchedAddresses: similarOrders.map(o => o.fullAddress),
            });
          }
        }
      }

      // Update order with results
      await this.prisma.$transaction(async (tx) => {
        const updateData: any = {
          isDuplicate,
          duplicateMatchScore: isDuplicate ? duplicateMatchScore : null,
          duplicateStatus: isDuplicate ? DuplicateStatus.PENDING_REVIEW : null,
          originalOrderIds: matchedOrderIds,
        };

        // Only update CPF if provided
        if (customerData.customerCPF) {
          const encryptedCPF = this.encryptionUtil.encrypt(customerData.customerCPF);
          const cpfHash = this.encryptionUtil.hash(customerData.customerCPF);
          updateData.customerCPF = encryptedCPF;
          updateData.customerCPFHash = cpfHash;
        }

        // Only update address if provided
        if (customerData.fullAddress) {
          updateData.fullAddress = customerData.fullAddress;
          const parsedAddress = this.addressParser.parseAddress(customerData.fullAddress);
          await this.duplicateDetection.saveAddressComponents(orderId, parsedAddress);
        }

        await tx.order.update({
          where: { id: orderId },
          data: updateData,
        });

        // Create audit log
        await this.createAuditLog(tx, {
          orderId,
          tenantId,
          action: isDuplicate ? AuditAction.DUPLICATE_DETECTED : AuditAction.ORDER_CREATED,
          performedBy: 'SYSTEM',
          performedByName: 'Anti-Fraud System',
          performedByRole: 'SYSTEM',
          metadata: {
            duplicateCheckMethod: customerData.customerCPF && customerData.fullAddress ? 'CPF_ADDRESS' : 'PHONE_NAME',
            isDuplicate,
            matchScore: duplicateMatchScore,
            matchedOrders: matchedOrderIds.length,
          },
        });
      });

      if (isDuplicate) {
        this.logger.warn('Duplicate order detected', {
          orderId,
          method: customerData.customerCPF && customerData.fullAddress ? 'CPF_ADDRESS' : 'PHONE_NAME',
          matchScore: duplicateMatchScore,
          matchedOrders: matchedOrderIds.length,
        });
      }
    } catch (error) {
      this.logger.error('Error in comprehensive duplicate check', error);
      throw error;
    }
  }

  /**
   * Process an order for duplicate detection (legacy method)
   */
  async processOrderForDuplicates(
    tenantId: string,
    orderId: string,
    customerCPF: string,
    fullAddress: string,
  ): Promise<void> {
    try {
      // Encrypt CPF
      const encryptedCPF = this.encryptionUtil.encrypt(customerCPF);
      const cpfHash = this.encryptionUtil.hash(customerCPF);

      // Parse address
      const parsedAddress = this.addressParser.parseAddress(fullAddress);
      
      // Check for duplicates
      const duplicateResult = await this.duplicateDetection.checkForDuplicates(
        tenantId,
        customerCPF,
        fullAddress,
        orderId,
      );

      // Update order with duplicate check results
      await this.prisma.$transaction(async (tx) => {
        // Update order
        await tx.order.update({
          where: { id: orderId },
          data: {
            customerCPF: encryptedCPF,
            customerCPFHash: cpfHash,
            fullAddress,
            isDuplicate: duplicateResult.isDuplicate,
            duplicateStatus: duplicateResult.isDuplicate
              ? DuplicateStatus.PENDING_REVIEW
              : null,
            duplicateMatchScore: duplicateResult.isDuplicate
              ? Math.round(duplicateResult.matchScore * 100)
              : null,
            duplicateCheckVersion: duplicateResult.algorithmVersion,
            originalOrderIds: duplicateResult.matchedOrders.map(m => m.orderId),
          },
        });

        // Save address components
        await this.duplicateDetection.saveAddressComponents(orderId, parsedAddress);

        // Create audit log
        await this.createAuditLog(tx, {
          orderId,
          tenantId,
          action: duplicateResult.isDuplicate
            ? AuditAction.DUPLICATE_DETECTED
            : AuditAction.ORDER_CREATED,
          performedBy: 'SYSTEM',
          performedByName: 'Anti-Fraud System',
          performedByRole: 'SYSTEM',
          metadata: {
            duplicateCheckResult: duplicateResult,
          },
        });
      });

      if (duplicateResult.isDuplicate) {
        this.logger.warn('Duplicate order detected', {
          orderId,
          matchScore: duplicateResult.matchScore,
          matchedOrders: duplicateResult.matchedOrders.length,
        });
      }
    } catch (error) {
      this.logger.error('Error processing order for duplicates', error);
      throw error;
    }
  }

  /**
   * Get duplicate orders pending review
   */
  async getDuplicateReviewQueue(
    tenantId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    items: DuplicateReviewQueueItem[];
    total: number;
    pages: number;
  }> {
    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.prisma.order.findMany({
        where: {
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
          deletedAt: null, // Exclude soft-deleted orders
        },
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          customerPhone: true,
          total: true,
          createdAt: true,
          duplicateMatchScore: true,
          originalOrderIds: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.order.count({
        where: {
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
          deletedAt: null, // Exclude soft-deleted orders
        },
      }),
    ]);

    // Fetch matched order details
    const itemsWithMatches = await Promise.all(
      items.map(async (item) => {
        const matchedOrders = await this.prisma.order.findMany({
          where: {
            id: { in: item.originalOrderIds },
          },
          select: {
            id: true,
            orderNumber: true,
            createdAt: true,
          },
        });

        return {
          ...item,
          orderNumber: item.orderNumber || 'N/A',
          total: item.total.toNumber(),
          duplicateMatchScore: item.duplicateMatchScore || 0,
          matchedOrders: matchedOrders.map(order => ({
            orderId: order.id,
            orderNumber: order.orderNumber || 'N/A',
            createdAt: order.createdAt,
          })),
        };
      }),
    );

    return {
      items: itemsWithMatches,
      total,
      pages: Math.ceil(total / limit),
    };
  }

  /**
   * Review a duplicate order
   */
  async reviewDuplicate(
    tenantId: string,
    reviewData: ReviewDuplicateDto,
  ): Promise<void> {
    const startTime = Date.now();

    await this.prisma.$transaction(async (tx) => {
      // Get the order
      const order = await tx.order.findFirst({
        where: {
          id: reviewData.orderId,
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        },
      });

      if (!order) {
        throw new Error('Order not found or already reviewed');
      }

      // Update order with review decision
      const newStatus =
        reviewData.decision === ReviewDecision.APPROVE_ORDER
          ? DuplicateStatus.APPROVED
          : DuplicateStatus.DENIED;

      await tx.order.update({
        where: { id: reviewData.orderId },
        data: {
          duplicateStatus: newStatus,
          reviewedBy: reviewData.reviewerId,
          reviewedByName: reviewData.reviewerName,
          reviewedByRole: reviewData.reviewerRole,
          reviewedAt: new Date(),
          reviewDecision: reviewData.decision,
          reviewDuration: Date.now() - startTime,
          requiresReview: false, // Mark as reviewed
          // Only cancel orders if denied, preserve status for approved orders
          ...(reviewData.decision === ReviewDecision.DENY_ORDER && {
            status: OrderStatus.Cancelado
          })
        },
      });

      // Create audit log
      await this.createAuditLog(tx, {
        orderId: reviewData.orderId,
        tenantId,
        action:
          reviewData.decision === ReviewDecision.APPROVE_ORDER
            ? AuditAction.DUPLICATE_APPROVED
            : AuditAction.DUPLICATE_DENIED,
        performedBy: reviewData.reviewerId,
        performedByName: reviewData.reviewerName,
        performedByRole: reviewData.reviewerRole,
        metadata: {
          decision: reviewData.decision,
          notes: reviewData.notes,
          reviewDuration: Date.now() - startTime,
        },
      });
    });

    this.logger.log('Duplicate order reviewed', {
      orderId: reviewData.orderId,
      decision: reviewData.decision,
      reviewer: reviewData.reviewerId,
    });
  }

  /**
   * Get order audit trail
   */
  async getOrderAuditTrail(
    tenantId: string,
    orderId: string,
  ): Promise<any[]> {
    const logs = await this.prisma.orderAuditLog.findMany({
      where: {
        orderId,
        tenantId,
      },
      orderBy: {
        performedAt: 'desc',
      },
    });

    // Verify signatures
    return logs.map(log => {
      const isValid = this.verifyAuditLogSignature(log);
      return {
        ...log,
        signatureValid: isValid,
      };
    });
  }

  /**
   * Create an audit log entry with cryptographic signature
   */
  private async createAuditLog(
    tx: Prisma.TransactionClient,
    data: {
      orderId: string;
      tenantId: string;
      action: AuditAction;
      performedBy: string;
      performedByName: string;
      performedByRole: string;
      previousData?: any;
      newData?: any;
      metadata?: any;
    },
  ): Promise<void> {
    const auditData = {
      ...data,
      performedAt: new Date(),
    };

    // Generate signature
    const signature = this.generateSignature(
      JSON.stringify(auditData),
      this.signingKey,
    );

    await tx.orderAuditLog.create({
      data: {
        ...auditData,
        signature,
        signatureAlgorithm: 'SHA256',
      },
    });
  }

  /**
   * Verify audit log signature
   */
  private verifyAuditLogSignature(log: any): boolean {
    try {
      const { signature, signatureAlgorithm, ...data } = log;
      
      const expectedSignature = this.generateSignature(
        JSON.stringify(data),
        this.signingKey,
      );
      
      return signature === expectedSignature;
    } catch (error) {
      this.logger.warn('Error verifying audit log signature', error);
      return false;
    }
  }

  private generateSignature(data: string, key: string): string {
    return crypto.createHmac('sha256', key).update(data).digest('hex');
  }

  /**
   * Calculate risk score for an order
   */
  async calculateRiskScore(orderId: string, tenantId: string): Promise<RiskAssessment> {
    const order = await this.prisma.order.findFirst({
      where: { id: orderId, tenantId },
      include: {
        customer: true,
        items: {
          include: {
            productVariation: {
              include: {
                product: true
              }
            }
          }
        }
      }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    const riskFactors: string[] = [];
    let riskScore = 0;

    // Factor 1: Duplicate order check (45 points - increased weight)
    if (order.isDuplicate) {
      const score = Math.min(45, Math.round((order.duplicateMatchScore || 0) * 0.45));
      riskScore += score;
      riskFactors.push(`Pedido duplicado detectado (score: ${order.duplicateMatchScore}%)`);
    }

    // Additional check: Same phone + name in entire database history (adds extra risk)
    const totalSameCustomer = await this.prisma.order.count({
      where: {
        tenantId,
        customerPhone: order.customerPhone,
        customerName: order.customerName,
        id: { not: order.id },
        deletedAt: null, // Exclude soft-deleted orders
        // Check against ALL orders in database history
      },
    });

    if (totalSameCustomer > 0) {
      // Add risk based on number of total orders in system
      const extraRisk = Math.min(30, totalSameCustomer * 15);
      riskScore += extraRisk;
      riskFactors.push(`Cliente com ${totalSameCustomer} pedido(s) no sistema`);
    }

    // Factor 2: High value order (20 points)
    const orderValue = Number(order.total);
    if (orderValue > 1000) {
      const valueScore = Math.min(20, Math.round((orderValue - 1000) / 100));
      riskScore += valueScore;
      if (valueScore > 10) {
        riskFactors.push('Pedido de alto valor');
      }
    }

    // Factor 3: First time customer - REMOVED (new customers are not high risk)

    // Factor 4: Multiple orders same day (20 points)
    const startOfDay = new Date(order.createdAt);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(order.createdAt);
    endOfDay.setHours(23, 59, 59, 999);

    const sameDayOrders = await this.prisma.order.count({
      where: {
        customerPhone: order.customerPhone,
        tenantId,
        deletedAt: null, // Exclude soft-deleted orders
        createdAt: { gte: startOfDay, lte: endOfDay },
        id: { not: order.id }
      }
    });

    if (sameDayOrders > 0) {
      const dayScore = Math.min(20, sameDayOrders * 10);
      riskScore += dayScore;
      riskFactors.push(`${sameDayOrders + 1} pedidos no mesmo dia`);
    }

    // Factor 5: Incomplete customer data (15 points)
    let dataScore = 0;
    if (!order.customerCPF) {
      dataScore += 8;
      riskFactors.push('CPF não informado');
    }
    if (!order.fullAddress || order.fullAddress.length < 20) {
      dataScore += 7;
      riskFactors.push('Endereço incompleto');
    }
    riskScore += dataScore;

    // Determine risk level
    let riskLevel: RiskLevel;
    if (riskScore >= 70) {
      riskLevel = 'CRITICAL';
    } else if (riskScore >= 50) {
      riskLevel = 'HIGH';
    } else if (riskScore >= 30) {
      riskLevel = 'MEDIUM';
    } else {
      riskLevel = 'LOW';
    }

    // Determine if review is required based on risk score or duplicate status
    const requiresReview = riskScore >= 50 || order.isDuplicate;

    return {
      riskScore,
      riskLevel,
      riskFactors,
      requiresReview
    };
  }

  /**
   * Update order with risk assessment
   */
  async assessOrderRisk(orderId: string, tenantId: string): Promise<void> {
    const assessment = await this.calculateRiskScore(orderId, tenantId);

    await this.prisma.order.update({
      where: { id: orderId },
      data: {
        riskScore: assessment.riskScore,
        riskLevel: assessment.riskLevel,
        riskFactors: assessment.riskFactors,
        requiresReview: assessment.requiresReview,
        fraudCheckCompletedAt: new Date()
      }
    });

    // Create audit log for risk assessment
    await this.prisma.$transaction(async (tx) => {
      await this.createAuditLog(tx, {
        orderId,
        tenantId,
        action: AuditAction.RISK_ASSESSED,
        performedBy: 'SYSTEM',
        performedByName: 'Anti-Fraud System',
        performedByRole: 'SYSTEM',
        metadata: assessment
      });
    });
  }

  /**
   * Get orders requiring review by risk level
   */
  async getOrdersForReview(
    tenantId: string,
    riskLevel?: RiskLevel,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    items: OrderReviewItem[];
    total: number;
    pages: number;
    byRiskLevel: Record<string, number>;
  }> {
    const skip = (page - 1) * limit;

    const whereClause: Prisma.OrderWhereInput = {
      tenantId,
      deletedAt: null, // Exclude soft-deleted orders
      AND: [
        {
          OR: [
            { requiresReview: true }, // High risk or duplicate orders
            { status: OrderStatus.Analise } // All orders in Analysis status
          ]
        },
        { reviewedAt: null } // Not yet reviewed
      ],
      ...(riskLevel && { riskLevel })
    };

    // Debug logging
    this.logger.log(`getOrdersForReview called with:`, {
      tenantId,
      riskLevel,
      page,
      limit
    });

    this.logger.log(`Where clause for items query:`, JSON.stringify(whereClause, null, 2));

    const [items, total] = await Promise.all([
      this.prisma.order.findMany({
        where: whereClause,
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          customerPhone: true,
          total: true,
          createdAt: true,
          riskScore: true,
          riskLevel: true,
          riskFactors: true,
          status: true,
          isDuplicate: true,
          duplicateMatchScore: true,
          originalOrderIds: true,
          requiresReview: true,
          reviewedAt: true
        },
        orderBy: [
          { riskScore: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      this.prisma.order.count({ where: whereClause })
    ]);

    // Debug logging for items found
    this.logger.log(`Found ${items.length} items for riskLevel: ${riskLevel || 'ALL'}`);
    this.logger.log(`Total count: ${total}`);

    if (riskLevel === 'HIGH') {
      this.logger.log(`HIGH risk items found:`, items.map(item => ({
        id: item.id,
        orderNumber: item.orderNumber,
        riskLevel: item.riskLevel,
        requiresReview: item.requiresReview,
        reviewedAt: item.reviewedAt,
        status: item.status
      })));
    }

    // Get counts by risk level
    const riskLevelCounts = await this.prisma.order.groupBy({
      by: ['riskLevel'],
      where: {
        tenantId,
        deletedAt: null, // Exclude soft-deleted orders
        AND: [
          {
            OR: [
              { requiresReview: true }, // High risk or duplicate orders
              { status: OrderStatus.Analise } // All orders in Analysis status
            ]
          },
          { reviewedAt: null } // Not yet reviewed
        ]
      },
      _count: {
        riskLevel: true
      }
    });
    
    // Log for debugging
    this.logger.log(`Risk level counts for tenant ${tenantId}:`, riskLevelCounts);

    // Debug: Get detailed info about HIGH risk orders
    if (riskLevelCounts.some(item => item.riskLevel === 'HIGH' && item._count.riskLevel > 0)) {
      const highRiskOrders = await this.prisma.order.findMany({
        where: {
          tenantId,
          deletedAt: null,
          riskLevel: 'HIGH',
          AND: [
            {
              OR: [
                { requiresReview: true },
                { status: OrderStatus.Analise }
              ]
            },
            { reviewedAt: null }
          ]
        },
        select: {
          id: true,
          orderNumber: true,
          riskLevel: true,
          requiresReview: true,
          reviewedAt: true,
          status: true,
          riskScore: true
        }
      });

      this.logger.log(`HIGH risk orders in count query:`, highRiskOrders);
    }

    // Count orders without risk level
    const nullRiskLevelCount = await this.prisma.order.count({
      where: {
        tenantId,
        deletedAt: null,
        riskLevel: null,
        AND: [
          {
            OR: [
              { requiresReview: true },
              { status: OrderStatus.Analise }
            ]
          },
          { reviewedAt: null }
        ]
      }
    });
    
    this.logger.log(`Orders without risk level: ${nullRiskLevelCount}`);

    // Initialize all risk levels with 0
    const byRiskLevel: Record<string, number> = {
      CRITICAL: 0,
      HIGH: 0,
      MEDIUM: 0,
      LOW: 0
    };
    
    // Update with actual counts
    riskLevelCounts.forEach((item) => {
      if (item.riskLevel) {
        byRiskLevel[item.riskLevel] = item._count.riskLevel;
      }
    });
    
    // If there are orders without risk level, add them to HIGH by default
    if (nullRiskLevelCount > 0) {
      byRiskLevel.HIGH += nullRiskLevelCount;
      this.logger.warn(`Added ${nullRiskLevelCount} orders without risk level to HIGH category`);
    }

    // Fetch matched order details for duplicates
    const itemsWithMatches = await Promise.all(
      items.map(async (item) => {
        let matchedOrders: any[] = [];
        
        if (item.isDuplicate && item.originalOrderIds && item.originalOrderIds.length > 0) {
          const orders = await this.prisma.order.findMany({
            where: {
              id: { in: item.originalOrderIds }
            },
            select: {
              id: true,
              orderNumber: true,
              createdAt: true
            }
          });
          matchedOrders = orders;
        }
        
        return {
          ...item,
          total: Number(item.total),
          riskScore: item.riskScore || 0,
          riskLevel: item.riskLevel || 'LOW',
          riskFactors: item.riskFactors || [],
          matchedOrders
        };
      })
    );

    return {
      items: itemsWithMatches,
      total,
      pages: Math.ceil(total / limit),
      byRiskLevel
    };
  }

  /**
   * Approve or reject an order after review
   */
  async reviewOrder(
    orderId: string,
    tenantId: string,
    review: ReviewDuplicateDto
  ): Promise<void> {
    const startTime = Date.now();

    await this.prisma.$transaction(async (tx) => {
      const order = await tx.order.findFirst({
        where: { id: orderId, tenantId }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      // Log the review decision
      console.log('AntifraudService.reviewOrder - Processing review:', {
        orderId,
        currentStatus: order.status,
        decision: review.decision,
        newStatus: review.decision === 'APPROVE_ORDER' ? 'Separacao' : review.decision === 'DENY_ORDER' ? 'Cancelado' : 'unchanged'
      });

      // Update order with review decision - DO NOT change status
      const updatedOrder = await tx.order.update({
        where: { id: orderId },
        data: {
          reviewedBy: review.reviewerId,
          reviewedByName: review.reviewerName,
          reviewedByRole: review.reviewerRole,
          reviewedAt: new Date(),
          reviewDecision: review.decision,
          reviewDuration: Date.now() - startTime,
          requiresReview: false,
          // Only cancel orders if denied, but preserve status for approved orders
          ...(review.decision === 'DENY_ORDER' && {
            status: OrderStatus.Cancelado
          })
        }
      });

      // Create status history entry only if status changed (only for denied orders)
      if (review.decision === 'DENY_ORDER') {
        await tx.orderStatusHistory.create({
          data: {
            orderId: orderId,
            previousStatus: order.status,
            newStatus: OrderStatus.Cancelado,
            changedById: review.reviewerId,
          }
        });
      }

      console.log('AntifraudService.reviewOrder - Order updated:', {
        orderId,
        previousStatus: order.status,
        newStatus: updatedOrder.status,
        decision: review.decision
      });

      // Create audit log
      await this.createAuditLog(tx, {
        orderId,
        tenantId,
        action: AuditAction.ORDER_REVIEWED,
        performedBy: review.reviewerId,
        performedByName: review.reviewerName,
        performedByRole: review.reviewerRole,
        metadata: {
          decision: review.decision,
          notes: review.notes,
          riskScore: order.riskScore,
          riskLevel: order.riskLevel
        }
      });
    });
  }

  /**
   * Get antifraud statistics
   */
  async getAntifraudStatistics(
    tenantId: string,
    startDate?: string,
    endDate?: string
  ): Promise<any> {
    const where: any = {
      tenantId,
      deletedAt: null
    };

    // Add date filters if provided
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // Get risk level distribution for orders requiring review
    const reviewQueueByRiskLevel = await this.prisma.order.groupBy({
      by: ['riskLevel'],
      where: {
        ...where,
        reviewedAt: null,
        OR: [
          { requiresReview: true },
          { status: 'Analise' }
        ]
      },
      _count: {
        riskLevel: true
      }
    });

    // Convert to object format
    const byRiskLevel: Record<string, number> = {
      CRITICAL: 0,
      HIGH: 0,
      MEDIUM: 0,
      LOW: 0
    };

    reviewQueueByRiskLevel.forEach((item) => {
      if (item.riskLevel) {
        byRiskLevel[item.riskLevel] = item._count.riskLevel;
      }
    });

    // Get total counts
    const [
      totalOrders,
      duplicatesDetected,
      ordersReviewed,
      pendingReview,
      ordersInAnalysis
    ] = await Promise.all([
      this.prisma.order.count({ where }),
      this.prisma.order.count({ where: { ...where, isDuplicate: true } }),
      this.prisma.order.count({ where: { ...where, reviewedAt: { not: null } } }),
      this.prisma.order.count({ 
        where: { 
          ...where, 
          reviewedAt: null,
          OR: [
            { requiresReview: true },
            { status: 'Analise' }
          ]
        } 
      }),
      this.prisma.order.count({ where: { ...where, status: 'Analise' } })
    ]);

    // Get review decisions distribution
    const reviewDecisions = await this.prisma.order.groupBy({
      by: ['reviewDecision'],
      where: {
        ...where,
        reviewDecision: { not: null }
      },
      _count: {
        reviewDecision: true
      }
    });

    const decisionStats: Record<string, number> = {};
    reviewDecisions.forEach((item) => {
      if (item.reviewDecision) {
        decisionStats[item.reviewDecision] = item._count.reviewDecision;
      }
    });

    return {
      summary: {
        totalOrders,
        duplicatesDetected,
        ordersReviewed,
        pendingReview,
        ordersInAnalysis
      },
      riskDistribution: byRiskLevel,
      reviewDecisions: decisionStats,
      period: {
        startDate: startDate || 'all time',
        endDate: endDate || 'current'
      }
    };
  }
}
