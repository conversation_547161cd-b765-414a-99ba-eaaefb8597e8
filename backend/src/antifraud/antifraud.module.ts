import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { BrazilianAddressParser } from './services/brazilian-address.parser';
import { PhoneticEncoderService } from './services/phonetic-encoder.service';
import { FuzzyMatchingService } from './services/fuzzy-matching.service';
import { DuplicateDetectionService } from './services/duplicate-detection.service';
import { AntifraudService } from './antifraud.service';
import { AntifraudController } from './antifraud.controller';
import { EncryptionUtil } from '../common/utils/encryption.util';

@Module({
  imports: [PrismaModule, ConfigModule],
  providers: [
    BrazilianAddressParser,
    PhoneticEncoderService,
    FuzzyMatchingService,
    DuplicateDetectionService,
    AntifraudService,
    EncryptionUtil,
  ],
  controllers: [AntifraudController],
  exports: [
    DuplicateDetectionService,
    AntifraudService,
    BrazilianAddressParser,
  ],
})
export class AntifraudModule {}