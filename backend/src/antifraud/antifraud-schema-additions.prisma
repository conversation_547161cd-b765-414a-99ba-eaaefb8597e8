// Anti-Fraud System Models
// These models will be appended to the main schema

model OrderAddressComponents {
  id                    String   @id @default(uuid())
  orderId               String   @unique
  order                 Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  // Parsed Components
  street                String
  streetNumber          String
  complement            String?
  neighborhood          String
  city                  String
  state                 String   @db.VarChar(2)
  zipCode               String   @db.VarChar(8)
  
  // Normalized for Matching
  streetNormalized      String
  streetSoundex         String
  streetMetaphone       String
  neighborhoodNorm      String
  neighborhoodSoundex   String
  cityNormalized        String
  
  // Geocoding (Optional)
  latitude              Float?
  longitude             Float?
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  @@index([streetSoundex, cityNormalized])
  @@index([zipCode])
  @@index([streetNormalized, streetNumber])
}

model OrderAuditLog {
  id                    String   @id @default(uuid())
  orderId               String
  order                 Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  tenantId              String
  
  // Audit Data
  action                AuditAction
  performedBy           String
  performedByName       String
  performedByRole       String
  performedAt           DateTime @default(now())
  
  // Change Details
  previousData          Json?
  newData               Json?
  metadata              Json?    // IP, user agent, etc.
  
  // Security
  signature             String   // Cryptographic signature
  signatureAlgorithm    String   @default("SHA256")
  
  @@index([orderId, performedAt])
  @@index([tenantId, action, performedAt])
  @@index([performedBy, performedAt])
}

enum DuplicateStatus {
  PENDING_REVIEW
  APPROVED
  DENIED
  AUTO_APPROVED  // For future ML-based approvals
}

enum ReviewDecision {
  APPROVE_ORDER
  DENY_ORDER
  MERGE_ORDERS
  INVESTIGATE_FURTHER
}

enum AuditAction {
  ORDER_CREATED
  ORDER_UPDATED
  DUPLICATE_DETECTED
  DUPLICATE_REVIEWED
  DUPLICATE_APPROVED
  DUPLICATE_DENIED
  ADDRESS_PARSED
  ADDRESS_GEOCODED
  MANUAL_OVERRIDE
  SYSTEM_OVERRIDE
}