import { Test, TestingModule } from '@nestjs/testing';
import { BrazilianAddressParser } from './services/brazilian-address.parser';
import { PhoneticEncoderService } from './services/phonetic-encoder.service';
import { FuzzyMatchingService } from './services/fuzzy-matching.service';

describe('Anti-Fraud Services', () => {
  let addressParser: BrazilianAddressParser;
  let phoneticEncoder: PhoneticEncoderService;
  let fuzzyMatcher: FuzzyMatchingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrazilianAddressParser,
        PhoneticEncoderService,
        FuzzyMatchingService,
      ],
    }).compile();

    addressParser = module.get<BrazilianAddressParser>(BrazilianAddressParser);
    phoneticEncoder = module.get<PhoneticEncoderService>(PhoneticEncoderService);
    fuzzyMatcher = module.get<FuzzyMatchingService>(FuzzyMatchingService);
  });

  describe('BrazilianAddressParser', () => {
    it('should parse a complete address', () => {
      const address = 'Rua das Flores, 123, Apt 45, Centro, São Paulo - SP, 01000-000';
      const parsed = addressParser.parseAddress(address);

      expect(parsed).toBeDefined();
      expect(parsed.street).toBe('RUA DAS FLORES');
      expect(parsed.streetNumber).toBe('123');
      expect(parsed.state).toBe('SP');
      expect(parsed.zipCode).toBe('01000000');
    });

    it('should handle address abbreviations', () => {
      const address = 'R. das Flores, 123, Apto 45, Centro, São Paulo - SP';
      const parsed = addressParser.parseAddress(address);

      expect(parsed.street).toBe('RUA DAS FLORES');
      expect(parsed.state).toBe('SP');
    });

    it('should extract CEP correctly', () => {
      const addresses = [
        { input: '01000-000', expected: '01000000' },
        { input: '01310200', expected: '01310200' },
        { input: 'CEP 01000-000', expected: '01000000' },
      ];

      addresses.forEach(({ input, expected }) => {
        const parsed = addressParser.parseAddress(`Rua X, 123, ${input}`);
        expect(parsed.zipCode).toBe(expected);
      });
    });

    it('should validate parsed addresses', () => {
      const valid = addressParser.parseAddress('Rua das Flores, 123, Centro, São Paulo - SP, 01000-000');
      const invalid = addressParser.parseAddress('Invalid address string');

      expect(addressParser.isValidAddress(valid)).toBe(true);
      expect(addressParser.isValidAddress(invalid)).toBe(false);
    });
  });

  describe('PhoneticEncoderService', () => {
    it('should generate consistent Soundex codes for similar names', () => {
      const similar = [
        ['JOSE', 'JOZE', 'JOSÉ'],
        ['SILVA', 'CILVA'],
        ['SANTOS', 'SANTUS'],
      ];

      similar.forEach(group => {
        const codes = group.map(name => phoneticEncoder.soundexPT(name));
        expect(new Set(codes).size).toBe(1); // All should be the same
      });
    });

    it('should generate different Soundex codes for different names', () => {
      const different = [
        ['MARIA', 'JOSE'],
        ['SILVA', 'SANTOS'],
        ['PEDRO', 'PAULO'],
      ];

      different.forEach(([name1, name2]) => {
        const code1 = phoneticEncoder.soundexPT(name1);
        const code2 = phoneticEncoder.soundexPT(name2);
        expect(code1).not.toBe(code2);
      });
    });

    it('should handle Portuguese phonetics in Metaphone', () => {
      const tests = [
        { input: 'CENTRO', expected: 'SNTR' },
        { input: 'CHAVE', expected: 'XV' },
        { input: 'CARRO', expected: 'KR' },
        { input: 'GERAL', expected: 'JRL' },
      ];

      tests.forEach(({ input, expected }) => {
        const result = phoneticEncoder.metaphonePT(input);
        expect(result).toBe(expected);
      });
    });

    it('should calculate phonetic similarity correctly', () => {
      const highSimilarity = phoneticEncoder.phoneticSimilarity('JOSE', 'JOZE');
      const lowSimilarity = phoneticEncoder.phoneticSimilarity('JOSE', 'MARIA');

      expect(highSimilarity).toBeGreaterThan(0.8);
      expect(lowSimilarity).toBeLessThan(0.3);
    });
  });

  describe('FuzzyMatchingService', () => {
    const createAddress = (overrides = {}) => ({
      street: 'RUA DAS FLORES',
      streetNumber: '123',
      neighborhood: 'CENTRO',
      city: 'SAO PAULO',
      state: 'SP',
      zipCode: '01000000',
      ...overrides,
    });

    it('should match identical addresses with high score', () => {
      const addr1 = createAddress();
      const addr2 = createAddress();

      const result = fuzzyMatcher.calculateAddressMatch(addr1, addr2);

      expect(result.score).toBe(1);
      expect(result.confidence).toBe('HIGH');
      expect(result.matchedComponents).toContain('street');
      expect(result.matchedComponents).toContain('number');
    });

    it('should match similar addresses with medium score', () => {
      const addr1 = createAddress({ street: 'RUA DAS FLORES' });
      const addr2 = createAddress({ street: 'R DAS FLORES' });

      const result = fuzzyMatcher.calculateAddressMatch(addr1, addr2);

      expect(result.score).toBeGreaterThan(0.6);
      expect(result.score).toBeLessThan(0.9);
    });

    it('should not match different addresses', () => {
      const addr1 = createAddress({ street: 'RUA DAS FLORES', neighborhood: 'CENTRO' });
      const addr2 = createAddress({ street: 'AVENIDA PAULISTA', neighborhood: 'BELA VISTA' });

      const result = fuzzyMatcher.calculateAddressMatch(addr1, addr2);

      expect(result.score).toBeLessThan(0.5);
      expect(result.confidence).toBe('LOW');
    });

    it('should identify duplicate candidates correctly', () => {
      const addr1 = createAddress();
      const addr2 = createAddress({ streetNumber: '125' }); // Close number

      const result = fuzzyMatcher.calculateAddressMatch(addr1, addr2);
      const isDuplicate = fuzzyMatcher.isDuplicateCandidate(true, result);

      expect(isDuplicate).toBe(true); // CPF match + 2+ components
    });

    it('should handle missing components gracefully', () => {
      const addr1 = createAddress({ zipCode: '' });
      const addr2 = createAddress({ zipCode: '01000000' });

      const result = fuzzyMatcher.calculateAddressMatch(addr1, addr2);

      expect(result.details.zipCodeMatch).toBe(0);
      expect(result.score).toBeGreaterThan(0.7); // Other components still match
    });
  });
});