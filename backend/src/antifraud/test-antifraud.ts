import { BrazilianAddressParser } from './services/brazilian-address.parser';
import { PhoneticEncoderService } from './services/phonetic-encoder.service';
import { FuzzyMatchingService } from './services/fuzzy-matching.service';

// Test the anti-fraud services
async function testAntifraud() {
  const addressParser = new BrazilianAddressParser();
  const phoneticEncoder = new PhoneticEncoderService();
  const fuzzyMatcher = new FuzzyMatchingService(phoneticEncoder);

  console.log('=== Testing Brazilian Address Parser ===');
  
  const testAddresses = [
    'Rua das Flores, 123, Apt 45, Centro, São Paulo - SP, 01000-000',
    '<PERSON><PERSON>, 123, Apto 45, Centro, São Paulo - SP',
    'Av. Paulista, 1578, 10º andar, Bela Vista, São Paulo - SP, 01310-200',
    'Avenida Paulista, 1578, décimo andar, Bela Vista, São Paulo/SP CEP 01310200',
  ];

  const parsedAddresses = testAddresses.map(addr => {
    console.log(`\nOriginal: ${addr}`);
    const parsed = addressParser.parseAddress(addr);
    console.log('Parsed:', JSON.stringify(parsed, null, 2));
    return parsed;
  });

  console.log('\n=== Testing Phonetic Encoding ===');
  
  const testWords = [
    ['JOSE', 'JOZE', 'JOSÉ'],
    ['SILVA', 'CILVA', 'SYLVA'],
    ['CENTRO', 'SENTRO', 'CENTRO'],
  ];

  testWords.forEach(words => {
    console.log(`\nTesting variations of: ${words[0]}`);
    words.forEach(word => {
      console.log(`  ${word}:`);
      console.log(`    Soundex: ${phoneticEncoder.soundexPT(word)}`);
      console.log(`    Metaphone: ${phoneticEncoder.metaphonePT(word)}`);
    });
  });

  console.log('\n=== Testing Fuzzy Address Matching ===');
  
  // Test similar addresses
  const addr1 = addressParser.parseAddress(testAddresses[0]);
  const addr2 = addressParser.parseAddress(testAddresses[1]);
  
  const match = fuzzyMatcher.calculateAddressMatch(
    {
      street: addr1.street,
      streetNumber: addr1.streetNumber,
      neighborhood: addr1.neighborhood,
      city: addr1.city,
      state: addr1.state,
      zipCode: addr1.zipCode,
      streetNormalized: addr1.normalized.street,
      neighborhoodNorm: addr1.normalized.neighborhood,
      cityNormalized: addr1.normalized.city,
    },
    {
      street: addr2.street,
      streetNumber: addr2.streetNumber,
      neighborhood: addr2.neighborhood,
      city: addr2.city,
      state: addr2.state,
      zipCode: addr2.zipCode,
      streetNormalized: addr2.normalized.street,
      neighborhoodNorm: addr2.normalized.neighborhood,
      cityNormalized: addr2.normalized.city,
    }
  );

  console.log('\nMatch Result:');
  console.log(`  Score: ${match.score.toFixed(2)}`);
  console.log(`  Confidence: ${match.confidence}`);
  console.log(`  Matched Components: ${match.matchedComponents.join(', ')}`);
  console.log('  Component Scores:', match.details);
  
  console.log(`\nIs duplicate candidate (CPF match + 2+ address components)? ${
    fuzzyMatcher.isDuplicateCandidate(true, match) ? 'YES' : 'NO'
  }`);
}

// Run the test
testAntifraud().catch(console.error);