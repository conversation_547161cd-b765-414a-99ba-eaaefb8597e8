import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AntifraudService, ReviewDuplicateDto } from './antifraud.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';
import { TenantScopeInterceptor } from '../common/interceptors/tenant-scope.interceptor';
import { TransformInterceptor } from '../common/interceptors/transform.interceptor';

@Controller('antifraud')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(TenantScopeInterceptor, TransformInterceptor)
export class AntifraudController {
  constructor(private readonly antifraudService: AntifraudService) {}

  /**
   * Get duplicate orders pending review
   */
  @Get('duplicates/review-queue')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  async getDuplicateReviewQueue(
    @TenantId() tenantId: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = parseInt(page || '1', 10);
    const limitNum = parseInt(limit || '20', 10);
    
    return this.antifraudService.getDuplicateReviewQueue(
      tenantId,
      pageNum,
      limitNum,
    );
  }

  /**
   * Review a duplicate order
   */
  @Post('duplicates/:orderId/review')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async reviewDuplicate(
    @TenantId() tenantId: string,
    @Param('orderId') orderId: string,
    @Body() reviewData: Omit<ReviewDuplicateDto, 'orderId' | 'reviewerId' | 'reviewerName' | 'reviewerRole'>,
    @CurrentUser() user: any,
  ) {
    const fullReviewData: ReviewDuplicateDto = {
      orderId,
      ...reviewData,
      reviewerId: user.id,
      reviewerName: user.name,
      reviewerRole: user.role,
    };

    await this.antifraudService.reviewDuplicate(tenantId, fullReviewData);
    
    return {
      success: true,
      message: 'Duplicate order reviewed successfully',
    };
  }

  /**
   * Get order audit trail
   */
  @Get('orders/:orderId/audit-trail')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  async getOrderAuditTrail(
    @TenantId() tenantId: string,
    @Param('orderId') orderId: string,
  ) {
    return this.antifraudService.getOrderAuditTrail(tenantId, orderId);
  }

  /**
   * Get orders for review by risk level
   */
  @Get('orders/review-queue')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  async getOrdersForReview(
    @TenantId() tenantId: string,
    @Query('riskLevel') riskLevel?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const pageNum = parseInt(page || '1', 10);
    const limitNum = parseInt(limit || '20', 10);
    
    return this.antifraudService.getOrdersForReview(
      tenantId,
      riskLevel as any,
      pageNum,
      limitNum,
    );
  }

  /**
   * Calculate risk score for an order
   */
  @Post('orders/:orderId/assess-risk')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async assessOrderRisk(
    @TenantId() tenantId: string,
    @Param('orderId') orderId: string,
  ) {
    await this.antifraudService.assessOrderRisk(orderId, tenantId);
    
    return {
      success: true,
      message: 'Risk assessment completed',
    };
  }

  /**
   * Review an order (approve/reject)
   */
  @Post('orders/:orderId/review')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @HttpCode(HttpStatus.OK)
  async reviewOrder(
    @TenantId() tenantId: string,
    @Param('orderId') orderId: string,
    @Body() reviewData: Omit<ReviewDuplicateDto, 'orderId' | 'reviewerId' | 'reviewerName' | 'reviewerRole'>,
    @CurrentUser() user: any,
  ) {
    const fullReviewData: ReviewDuplicateDto = {
      orderId,
      ...reviewData,
      reviewerId: user.id,
      reviewerName: user.name,
      reviewerRole: user.role,
    };

    await this.antifraudService.reviewOrder(orderId, tenantId, fullReviewData);
    
    return {
      success: true,
      message: 'Order reviewed successfully',
    };
  }

  /**
   * Get duplicate detection statistics
   */
  @Get('statistics')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  async getDuplicateStatistics(
    @TenantId() tenantId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.antifraudService.getAntifraudStatistics(
      tenantId,
      startDate,
      endDate,
    );
  }
}