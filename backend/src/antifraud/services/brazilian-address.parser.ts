import { Injectable } from '@nestjs/common';

export interface ParsedAddress {
  street: string;
  streetNumber: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  normalized: {
    street: string;
    neighborhood: string;
    city: string;
  };
}

@Injectable()
export class BrazilianAddressParser {
  // Common Brazilian street type abbreviations
  private readonly streetTypes = {
    'R': 'RUA',
    'RUA': 'RUA',
    'AV': 'AVENIDA',
    'AVENIDA': 'AVENIDA',
    'AL': 'ALAMEDA',
    'ALAMEDA': 'ALAMEDA',
    'TV': 'TRAVESSA',
    'TRAVESSA': 'TRAVESSA',
    'EST': 'ESTRADA',
    'ESTRADA': 'ESTRADA',
    'ROD': 'RODOVIA',
    'RODOVIA': 'RODOVIA',
    'VL': 'VILA',
    'VILA': 'VILA',
    'PCA': 'PRACA',
    'PRACA': 'PRACA',
    'PC': 'PRACA',
    'LRG': 'LARGO',
    'LARGO': 'LARGO',
    'BC': 'BECO',
    'BECO': 'BECO',
    'PQ': 'PARQUE',
    'PARQUE': 'PARQUE',
  };

  // Common abbreviations in Brazilian addresses
  private readonly abbreviations = {
    'AP': 'APARTAMENTO',
    'APT': 'APARTAMENTO',
    'APTO': 'APARTAMENTO',
    'BL': 'BLOCO',
    'CS': 'CASA',
    'LJ': 'LOJA',
    'SL': 'SALA',
    'QD': 'QUADRA',
    'LT': 'LOTE',
    'FUND': 'FUNDOS',
    'FDS': 'FUNDOS',
    'ESQ': 'ESQUINA',
    'JD': 'JARDIM',
    'VL': 'VILA',
    'COND': 'CONDOMINIO',
    'RES': 'RESIDENCIAL',
  };

  // Brazilian states
  private readonly states = {
    'AC': 'ACRE',
    'AL': 'ALAGOAS',
    'AP': 'AMAPA',
    'AM': 'AMAZONAS',
    'BA': 'BAHIA',
    'CE': 'CEARA',
    'DF': 'DISTRITO FEDERAL',
    'ES': 'ESPIRITO SANTO',
    'GO': 'GOIAS',
    'MA': 'MARANHAO',
    'MT': 'MATO GROSSO',
    'MS': 'MATO GROSSO DO SUL',
    'MG': 'MINAS GERAIS',
    'PA': 'PARA',
    'PB': 'PARAIBA',
    'PR': 'PARANA',
    'PE': 'PERNAMBUCO',
    'PI': 'PIAUI',
    'RJ': 'RIO DE JANEIRO',
    'RN': 'RIO GRANDE DO NORTE',
    'RS': 'RIO GRANDE DO SUL',
    'RO': 'RONDONIA',
    'RR': 'RORAIMA',
    'SC': 'SANTA CATARINA',
    'SP': 'SAO PAULO',
    'SE': 'SERGIPE',
    'TO': 'TOCANTINS',
  };

  /**
   * Parse a Brazilian address string into components
   */
  parseAddress(fullAddress: string): ParsedAddress {
    // Normalize the address
    let normalized = this.normalizeString(fullAddress);
    
    // Extract CEP (ZIP code)
    const zipCode = this.extractZipCode(normalized);
    normalized = normalized.replace(/\d{5}-?\d{3}/g, '').trim();
    
    // Extract state
    const { state, address: addressWithoutState } = this.extractState(normalized);
    normalized = addressWithoutState;
    
    // Extract city (usually comes before state)
    const { city, address: addressWithoutCity } = this.extractCity(normalized);
    normalized = addressWithoutCity;
    
    // Extract neighborhood (bairro)
    const { neighborhood, address: addressWithoutNeighborhood } = this.extractNeighborhood(normalized);
    normalized = addressWithoutNeighborhood;
    
    // Parse street and number
    const { street, streetNumber, complement } = this.parseStreetAndNumber(normalized);
    
    return {
      street,
      streetNumber,
      complement,
      neighborhood,
      city,
      state,
      zipCode,
      normalized: {
        street: this.normalizeForMatching(street),
        neighborhood: this.normalizeForMatching(neighborhood),
        city: this.normalizeForMatching(city),
      },
    };
  }

  /**
   * Normalize string for parsing
   */
  private normalizeString(str: string): string {
    return str
      .toUpperCase()
      .replace(/[.,;]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Normalize string for matching (remove accents, special chars)
   */
  private normalizeForMatching(str: string): string {
    return str
      .toUpperCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove accents
      .replace(/[^A-Z0-9\s]/g, '') // Keep only letters, numbers, spaces
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract ZIP code (CEP) from address
   */
  private extractZipCode(address: string): string {
    const cepMatch = address.match(/\d{5}-?\d{3}/);
    if (cepMatch) {
      return cepMatch[0].replace('-', '');
    }
    return '';
  }

  /**
   * Extract state from address
   */
  private extractState(address: string): { state: string; address: string } {
    // Try to find state abbreviation
    for (const [abbr, fullName] of Object.entries(this.states)) {
      const regex = new RegExp(`\\b${abbr}\\b`, 'i');
      if (regex.test(address)) {
        return {
          state: abbr,
          address: address.replace(regex, '').trim(),
        };
      }
    }
    
    // Try to find full state name
    for (const [abbr, fullName] of Object.entries(this.states)) {
      if (address.includes(fullName)) {
        return {
          state: abbr,
          address: address.replace(fullName, '').trim(),
        };
      }
    }
    
    return { state: '', address };
  }

  /**
   * Extract city from address
   */
  private extractCity(address: string): { city: string; address: string } {
    // Common patterns: "cidade -" or just the last part before state
    const parts = address.split('-').map(p => p.trim());
    
    if (parts.length > 1) {
      // Assume last part is city
      const city = parts[parts.length - 1];
      const remainingAddress = parts.slice(0, -1).join(' - ');
      return { city, address: remainingAddress };
    }
    
    // Try to extract city from the end of the address
    const words = address.split(' ');
    if (words.length > 3) {
      // Take last 1-3 words as potential city
      const potentialCity = words.slice(-2).join(' ');
      const remainingAddress = words.slice(0, -2).join(' ');
      return { city: potentialCity, address: remainingAddress };
    }
    
    return { city: '', address };
  }

  /**
   * Extract neighborhood (bairro) from address
   */
  private extractNeighborhood(address: string): { neighborhood: string; address: string } {
    // Look for common neighborhood indicators
    const patterns = [
      /BAIRRO:?\s*([^-]+)/i,
      /B(?:AIRRO)?\.?\s*([^-]+)/i,
      /-\s*([^-]+)$/i, // Last part after dash
    ];
    
    for (const pattern of patterns) {
      const match = address.match(pattern);
      if (match) {
        const neighborhood = match[1].trim();
        const remainingAddress = address.replace(match[0], '').trim();
        return { neighborhood, address: remainingAddress };
      }
    }
    
    return { neighborhood: '', address };
  }

  /**
   * Parse street name, number, and complement
   */
  private parseStreetAndNumber(address: string): {
    street: string;
    streetNumber: string;
    complement?: string;
  } {
    // Normalize street type abbreviations
    let normalized = address;
    for (const [abbr, full] of Object.entries(this.streetTypes)) {
      const regex = new RegExp(`\\b${abbr}\\b\\.?`, 'gi');
      normalized = normalized.replace(regex, full);
    }
    
    // Extract number
    const numberMatch = normalized.match(/\b(\d+)\b/);
    let streetNumber = 'S/N';
    let street = normalized;
    
    if (numberMatch) {
      streetNumber = numberMatch[1];
      const numberIndex = normalized.indexOf(numberMatch[0]);
      
      // Street is everything before the number
      street = normalized.substring(0, numberIndex).trim();
      
      // Complement is everything after the number
      const complementPart = normalized.substring(numberIndex + numberMatch[0].length).trim();
      const complement = this.parseComplement(complementPart);
      
      return { street, streetNumber, complement };
    }
    
    // No number found, try to extract complement indicators
    const { street: streetWithoutComplement, complement } = this.extractComplement(street);
    
    return {
      street: streetWithoutComplement,
      streetNumber,
      complement,
    };
  }

  /**
   * Parse complement information
   */
  private parseComplement(complementStr: string): string | undefined {
    if (!complementStr || complementStr.length < 2) {
      return undefined;
    }
    
    // Normalize abbreviations in complement
    let normalized = complementStr;
    for (const [abbr, full] of Object.entries(this.abbreviations)) {
      const regex = new RegExp(`\\b${abbr}\\b\\.?`, 'gi');
      normalized = normalized.replace(regex, full);
    }
    
    return normalized.trim() || undefined;
  }

  /**
   * Extract complement from street string
   */
  private extractComplement(street: string): {
    street: string;
    complement?: string;
  } {
    // Look for common complement patterns
    const complementPatterns = [
      /(APARTAMENTO|APT?O?\.?\s*\d+)/i,
      /(CASA\s*\d+)/i,
      /(BLOCO\s*[A-Z0-9]+)/i,
      /(LOJA\s*\d+)/i,
      /(SALA\s*\d+)/i,
      /(FUNDOS)/i,
    ];
    
    for (const pattern of complementPatterns) {
      const match = street.match(pattern);
      if (match) {
        return {
          street: street.replace(match[0], '').trim(),
          complement: match[0].trim(),
        };
      }
    }
    
    return { street, complement: undefined };
  }

  /**
   * Validate if a parsed address has minimum required fields
   */
  isValidAddress(parsed: ParsedAddress): boolean {
    return !!(
      parsed.street &&
      parsed.neighborhood &&
      parsed.city &&
      parsed.state &&
      (parsed.zipCode || parsed.streetNumber !== 'S/N')
    );
  }
}