import { Injectable } from '@nestjs/common';
import { PhoneticEncoderService } from './phonetic-encoder.service';

export interface FuzzyMatchResult {
  score: number; // 0-1 score
  matchedComponents: string[];
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
  details: {
    streetMatch: number;
    numberMatch: number;
    neighborhoodMatch: number;
    cityMatch: number;
    stateMatch: number;
    zipCodeMatch: number;
  };
}

export interface AddressComponents {
  street: string;
  streetNumber: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  // Phonetic encodings
  streetSoundex?: string;
  streetMetaphone?: string;
  neighborhoodSoundex?: string;
  // Normalized versions
  streetNormalized?: string;
  neighborhoodNorm?: string;
  cityNormalized?: string;
}

@Injectable()
export class FuzzyMatchingService {
  constructor(private phoneticEncoder: PhoneticEncoderService) {}

  /**
   * Calculate fuzzy match score between two addresses
   * Returns detailed scoring information
   */
  calculateAddressMatch(
    address1: AddressComponents,
    address2: AddressComponents,
  ): FuzzyMatchResult {
    const details = {
      streetMatch: this.compareStreets(address1, address2),
      numberMatch: this.compareNumbers(address1.streetNumber, address2.streetNumber),
      neighborhoodMatch: this.compareNeighborhoods(address1, address2),
      cityMatch: this.compareCities(address1, address2),
      stateMatch: address1.state === address2.state ? 1 : 0,
      zipCodeMatch: this.compareZipCodes(address1.zipCode, address2.zipCode),
    };

    // Calculate weighted score
    const weights = {
      street: 0.25,
      number: 0.15,
      neighborhood: 0.20,
      city: 0.15,
      state: 0.10,
      zipCode: 0.15,
    };

    const score =
      details.streetMatch * weights.street +
      details.numberMatch * weights.number +
      details.neighborhoodMatch * weights.neighborhood +
      details.cityMatch * weights.city +
      details.stateMatch * weights.state +
      details.zipCodeMatch * weights.zipCode;

    // Count matched components (considering > 0.7 as a match)
    const matchedComponents: string[] = [];
    if (details.streetMatch > 0.7) matchedComponents.push('street');
    if (details.numberMatch > 0.7) matchedComponents.push('number');
    if (details.neighborhoodMatch > 0.7) matchedComponents.push('neighborhood');
    if (details.cityMatch > 0.7) matchedComponents.push('city');
    if (details.stateMatch > 0.7) matchedComponents.push('state');
    if (details.zipCodeMatch > 0.7) matchedComponents.push('zipCode');

    // Determine confidence level
    let confidence: 'HIGH' | 'MEDIUM' | 'LOW';
    if (score >= 0.8 || matchedComponents.length >= 4) {
      confidence = 'HIGH';
    } else if (score >= 0.6 || matchedComponents.length >= 3) {
      confidence = 'MEDIUM';
    } else {
      confidence = 'LOW';
    }

    return {
      score,
      matchedComponents,
      confidence,
      details,
    };
  }

  /**
   * Compare street names using multiple algorithms
   */
  private compareStreets(addr1: AddressComponents, addr2: AddressComponents): number {
    // If we have pre-computed phonetic encodings, use them
    if (addr1.streetSoundex && addr2.streetSoundex) {
      const soundexMatch = addr1.streetSoundex === addr2.streetSoundex ? 1 : 0;
      const metaphoneMatch =
        addr1.streetMetaphone === addr2.streetMetaphone ? 1 : 0;
      const normalizedMatch = this.compareNormalizedStrings(
        addr1.streetNormalized || addr1.street,
        addr2.streetNormalized || addr2.street,
      );

      // Weighted combination
      return soundexMatch * 0.3 + metaphoneMatch * 0.3 + normalizedMatch * 0.4;
    }

    // Fallback to runtime computation
    const phonetic1 = this.phoneticEncoder.soundexPT(addr1.street);
    const phonetic2 = this.phoneticEncoder.soundexPT(addr2.street);
    const phoneticMatch = phonetic1 === phonetic2 ? 1 : 0;

    const normalizedMatch = this.compareNormalizedStrings(
      addr1.street,
      addr2.street,
    );

    return phoneticMatch * 0.4 + normalizedMatch * 0.6;
  }

  /**
   * Compare street numbers with fuzzy logic
   */
  private compareNumbers(num1: string, num2: string): number {
    // Exact match
    if (num1 === num2) return 1;

    // Both are "no number" variations
    const noNumberPatterns = ['S/N', 'SN', 'S/Nº', '0', ''];
    if (
      noNumberPatterns.includes(num1.toUpperCase()) &&
      noNumberPatterns.includes(num2.toUpperCase())
    ) {
      return 1;
    }

    // Extract numeric parts
    const numeric1 = parseInt(num1.replace(/\D/g, ''), 10);
    const numeric2 = parseInt(num2.replace(/\D/g, ''), 10);

    if (!isNaN(numeric1) && !isNaN(numeric2)) {
      // Check if numbers are close (typos, adjacent buildings)
      const diff = Math.abs(numeric1 - numeric2);
      if (diff === 0) return 1;
      if (diff <= 2) return 0.8; // Adjacent numbers
      if (diff <= 10) return 0.5; // Same block
      if (diff <= 100) return 0.3; // Same street segment
    }

    return 0;
  }

  /**
   * Compare neighborhoods with phonetic matching
   */
  private compareNeighborhoods(
    addr1: AddressComponents,
    addr2: AddressComponents,
  ): number {
    // Use pre-computed values if available
    if (addr1.neighborhoodSoundex && addr2.neighborhoodSoundex) {
      const soundexMatch =
        addr1.neighborhoodSoundex === addr2.neighborhoodSoundex ? 1 : 0;
      const normalizedMatch = this.compareNormalizedStrings(
        addr1.neighborhoodNorm || addr1.neighborhood,
        addr2.neighborhoodNorm || addr2.neighborhood,
      );
      return soundexMatch * 0.4 + normalizedMatch * 0.6;
    }

    // Fallback to runtime computation
    return this.phoneticEncoder.phoneticSimilarity(
      addr1.neighborhood,
      addr2.neighborhood,
    );
  }

  /**
   * Compare cities with normalization
   */
  private compareCities(addr1: AddressComponents, addr2: AddressComponents): number {
    if (addr1.cityNormalized && addr2.cityNormalized) {
      return addr1.cityNormalized === addr2.cityNormalized ? 1 : 0;
    }

    return this.compareNormalizedStrings(addr1.city, addr2.city);
  }

  /**
   * Compare ZIP codes with partial matching
   */
  private compareZipCodes(zip1: string, zip2: string): number {
    // Remove formatting
    const clean1 = zip1.replace(/\D/g, '');
    const clean2 = zip2.replace(/\D/g, '');

    // Exact match
    if (clean1 === clean2) return 1;

    // Same region (first 5 digits)
    if (clean1.substring(0, 5) === clean2.substring(0, 5)) return 0.8;

    // Same area (first 3 digits)
    if (clean1.substring(0, 3) === clean2.substring(0, 3)) return 0.5;

    // Same city (first 2 digits)
    if (clean1.substring(0, 2) === clean2.substring(0, 2)) return 0.3;

    return 0;
  }

  /**
   * Compare normalized strings using Levenshtein distance
   */
  private compareNormalizedStrings(str1: string, str2: string): number {
    const norm1 = this.normalize(str1);
    const norm2 = this.normalize(str2);

    if (norm1 === norm2) return 1;

    // Calculate Levenshtein distance
    const distance = this.levenshteinDistance(norm1, norm2);
    const maxLen = Math.max(norm1.length, norm2.length);

    if (maxLen === 0) return 0;

    // Convert distance to similarity score
    const similarity = 1 - distance / maxLen;

    // Check for common abbreviations and variations
    if (this.areAbbreviationsEquivalent(norm1, norm2)) {
      return Math.max(similarity, 0.8);
    }

    return similarity;
  }

  /**
   * Normalize string for comparison
   */
  private normalize(str: string): string {
    return str
      .toUpperCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove accents
      .replace(/[^A-Z0-9\s]/g, '') // Keep only letters, numbers, spaces
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1, // insertion
            matrix[i - 1][j] + 1, // deletion
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Check if two strings are equivalent abbreviations
   */
  private areAbbreviationsEquivalent(str1: string, str2: string): boolean {
    const abbreviations: [string, string][] = [
      ['R', 'RUA'],
      ['AV', 'AVENIDA'],
      ['AL', 'ALAMEDA'],
      ['TV', 'TRAVESSA'],
      ['PCA', 'PRACA'],
      ['PC', 'PRACA'],
      ['JD', 'JARDIM'],
      ['VL', 'VILA'],
      ['PQ', 'PARQUE'],
      ['COND', 'CONDOMINIO'],
      ['RES', 'RESIDENCIAL'],
    ];

    for (const [abbr, full] of abbreviations) {
      if (
        (str1.includes(abbr) && str2.includes(full)) ||
        (str1.includes(full) && str2.includes(abbr))
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if address match meets the minimum criteria
   * (CPF match + at least 2 address components)
   */
  isDuplicateCandidate(
    cpfMatch: boolean,
    addressMatch: FuzzyMatchResult,
  ): boolean {
    if (!cpfMatch) return false;

    // Need at least 2 matching address components
    const significantMatches = addressMatch.matchedComponents.filter(
      component => !['state'].includes(component), // State alone is not significant
    );

    return significantMatches.length >= 2;
  }
}