import { Injectable } from '@nestjs/common';

@Injectable()
export class PhoneticEncoderService {
  /**
   * Soundex algorithm adapted for Portuguese
   * Converts words to a phonetic code for fuzzy matching
   */
  soundexPT(word: string): string {
    if (!word || word.length === 0) return '0000';
    
    // Normalize the word
    const normalized = this.normalizePortuguese(word).toUpperCase();
    if (normalized.length === 0) return '0000';
    
    // Soundex mapping for Portuguese
    const soundexMap: { [key: string]: string } = {
      'B': '1', 'P': '1', 'V': '1', 'F': '1',
      'C': '2', 'S': '2', 'Z': '2', 'X': '2', 'G': '2', 'J': '2', 'K': '2', 'Q': '2',
      'D': '3', 'T': '3',
      'L': '4',
      'M': '5', 'N': '5',
      'R': '6',
    };
    
    // Keep the first letter
    let code = normalized[0];
    let previousCode = soundexMap[normalized[0]] || '0';
    
    // Process remaining letters
    for (let i = 1; i < normalized.length && code.length < 4; i++) {
      const currentCode = soundexMap[normalized[i]] || '0';
      
      // Skip vowels and repeated codes
      if (currentCode !== '0' && currentCode !== previousCode) {
        code += currentCode;
        previousCode = currentCode;
      }
    }
    
    // Pad with zeros if necessary
    return code.padEnd(4, '0');
  }

  /**
   * Metaphone algorithm adapted for Portuguese
   * More sophisticated than Soundex, captures pronunciation patterns
   */
  metaphonePT(word: string): string {
    if (!word || word.length === 0) return '';
    
    // Normalize and prepare the word
    let normalized = this.normalizePortuguese(word).toUpperCase();
    let result = '';
    let i = 0;
    
    // Portuguese-specific transformations
    const transformations: [RegExp, string][] = [
      // Handle common Portuguese patterns
      [/^GN/, 'N'],
      [/^KN/, 'N'],
      [/^PN/, 'N'],
      [/^PS/, 'S'],
      [/^PT/, 'T'],
      [/^X/, 'S'],
      [/^CH/, 'X'],
      [/^LH/, 'L'],
      [/^NH/, 'N'],
      [/^RR/, 'R'],
      [/^SS/, 'S'],
      [/^SC[EI]/, 'S'],
      [/^QU/, 'K'],
      [/^GU[EI]/, 'G'],
      [/^C[EI]/, 'S'],
      [/^G[EI]/, 'J'],
      [/^PH/, 'F'],
    ];
    
    // Apply initial transformations
    for (const [pattern, replacement] of transformations) {
      if (pattern.test(normalized)) {
        normalized = normalized.replace(pattern, replacement);
      }
    }
    
    // Process each character
    while (i < normalized.length && result.length < 6) {
      const char = normalized[i];
      const next = normalized[i + 1] || '';
      const prev = i > 0 ? normalized[i - 1] : '';
      
      switch (char) {
        case 'A':
        case 'E':
        case 'I':
        case 'O':
        case 'U':
          // Keep vowels only at the beginning
          if (i === 0) result += char;
          break;
          
        case 'B':
          result += 'B';
          break;
          
        case 'C':
          if (next === 'H') {
            result += 'X';
            i++; // Skip H
          } else if (['E', 'I', 'Y'].includes(next)) {
            result += 'S';
          } else {
            result += 'K';
          }
          break;
          
        case 'D':
          result += 'T';
          break;
          
        case 'F':
          result += 'F';
          break;
          
        case 'G':
          if (['E', 'I', 'Y'].includes(next)) {
            result += 'J';
          } else {
            result += 'G';
          }
          break;
          
        case 'H':
          // H is usually silent in Portuguese
          if (prev === 'L' || prev === 'N') {
            // LH and NH are special
            result = result.slice(0, -1) + prev;
          }
          break;
          
        case 'J':
          result += 'J';
          break;
          
        case 'K':
          result += 'K';
          break;
          
        case 'L':
          if (next === 'H') {
            result += 'L';
            i++; // Skip H
          } else {
            result += 'L';
          }
          break;
          
        case 'M':
          result += 'M';
          break;
          
        case 'N':
          if (next === 'H') {
            result += 'N';
            i++; // Skip H
          } else {
            result += 'N';
          }
          break;
          
        case 'P':
          result += 'P';
          break;
          
        case 'Q':
          result += 'K';
          if (next === 'U') i++; // Skip U in QU
          break;
          
        case 'R':
          result += 'R';
          break;
          
        case 'S':
          if (next === 'C' && ['E', 'I'].includes(normalized[i + 2] || '')) {
            result += 'S';
            i += 2; // Skip CE/CI
          } else {
            result += 'S';
          }
          break;
          
        case 'T':
          result += 'T';
          break;
          
        case 'V':
          result += 'V';
          break;
          
        case 'W':
          result += 'V';
          break;
          
        case 'X':
          result += 'S';
          break;
          
        case 'Y':
          result += 'I';
          break;
          
        case 'Z':
          result += 'S';
          break;
      }
      
      i++;
    }
    
    return result;
  }

  /**
   * Double Metaphone for Portuguese
   * Returns primary and alternate encodings
   */
  doubleMetaphonePT(word: string): { primary: string; alternate: string } {
    const primary = this.metaphonePT(word);
    
    // Generate alternate encoding with different rules
    let normalized = this.normalizePortuguese(word).toUpperCase();
    
    // Apply alternate transformations
    normalized = normalized
      .replace(/C([EI])/g, 'SS$1')
      .replace(/G([EI])/g, 'ZH$1')
      .replace(/X/g, 'KS')
      .replace(/^H/g, '');
    
    const alternate = this.metaphonePT(normalized);
    
    return { primary, alternate };
  }

  /**
   * Normalize Portuguese text for phonetic encoding
   */
  private normalizePortuguese(text: string): string {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove accents
      .replace(/ç/gi, 'c')
      .replace(/[^a-zA-Z]/g, '') // Keep only letters
      .toUpperCase();
  }

  /**
   * Calculate phonetic similarity between two words
   * Returns a score between 0 and 1
   */
  phoneticSimilarity(word1: string, word2: string): number {
    const soundex1 = this.soundexPT(word1);
    const soundex2 = this.soundexPT(word2);
    
    const { primary: meta1p, alternate: meta1a } = this.doubleMetaphonePT(word1);
    const { primary: meta2p, alternate: meta2a } = this.doubleMetaphonePT(word2);
    
    let score = 0;
    
    // Soundex match (weight: 0.3)
    if (soundex1 === soundex2) score += 0.3;
    
    // Metaphone matches (weight: 0.7)
    if (meta1p === meta2p) {
      score += 0.5;
    } else if (meta1p === meta2a || meta1a === meta2p) {
      score += 0.3;
    } else if (meta1a === meta2a) {
      score += 0.2;
    }
    
    return score;
  }

  /**
   * Generate all phonetic variations of a word
   * Useful for indexing and searching
   */
  generatePhoneticVariations(word: string): string[] {
    const variations = new Set<string>();
    
    variations.add(this.soundexPT(word));
    
    const { primary, alternate } = this.doubleMetaphonePT(word);
    variations.add(primary);
    variations.add(alternate);
    
    // Also generate variations for common misspellings
    const normalized = this.normalizePortuguese(word);
    
    // Common letter substitutions in Portuguese
    const substitutions: [string, string][] = [
      ['SS', 'C'],
      ['C', 'SS'],
      ['Z', 'S'],
      ['S', 'Z'],
      ['X', 'S'],
      ['CH', 'X'],
      ['SC', 'C'],
      ['QU', 'K'],
      ['K', 'QU'],
      ['V', 'B'],
      ['B', 'V'],
    ];
    
    for (const [from, to] of substitutions) {
      const variant = normalized.replace(new RegExp(from, 'g'), to);
      if (variant !== normalized) {
        variations.add(this.soundexPT(variant));
        variations.add(this.metaphonePT(variant));
      }
    }
    
    return Array.from(variations);
  }
}