import { Test } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../../prisma/prisma.service';
import { AntifraudModule } from '../../antifraud.module';
import { OrdersService } from '../../../orders/orders.service';
import { TenantMiddleware } from '../../../common/middleware/tenant.middleware';
import * as crypto from 'crypto';
import { DuplicateStatus, ReviewDecision } from '@prisma/client';

describe('Antifraud Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let ordersService: OrdersService;
  const tenantId = 'test-tenant-integration';
  const authToken = 'Bearer test-token';

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [AntifraudModule],
    })
      .overrideProvider('FEATURE_FLAGS')
      .useValue({
        antifraud: { enabled: true },
      })
      .compile();

    app = moduleRef.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    // Apply tenant middleware
    app.use((req: any, res: any, next: any) => {
      req.headers['x-tenant-id'] = tenantId;
      req.user = { id: 'test-user', tenantId, role: 'ADMIN' };
      next();
    });

    await app.init();

    prisma = moduleRef.get<PrismaService>(PrismaService);
    ordersService = moduleRef.get<OrdersService>(OrdersService);

    // Clean up test data
    await cleanupTestData();
  });

  afterEach(async () => {
    await cleanupTestData();
  });

  afterAll(async () => {
    await app.close();
  });

  async function cleanupTestData() {
    await prisma.orderAuditLog.deleteMany({ where: { tenantId } });
    await prisma.orderAddressComponents.deleteMany({ where: { order: { tenantId } } });
    await prisma.order.deleteMany({ where: { tenantId } });
  }

  describe('Duplicate Detection Flow', () => {
    it('should detect duplicate orders with same CPF and similar address', async () => {
      // Create first order
      const firstOrder = await createTestOrder({
        customerCPF: '123.456.789-00',
        fullAddress: 'Rua das Flores, 123, Apt 45, Jardim Paulista, São Paulo - SP, 01234-567',
      });

      // Create second order with same CPF and similar address
      const secondOrderData = {
        orderNumber: 'TEST-002',
        customerName: 'João Silva',
        customerCPF: '123.456.789-00',
        customerEmail: '<EMAIL>',
        fullAddress: 'R. das Flores, 123, Apto 45, Jd. Paulista, São Paulo - SP, 01234-567',
        totalAmount: 200,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 2, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(secondOrderData);

      expect(response.status).toBe(201);
      expect(response.body.isDuplicate).toBe(true);
      expect(response.body.duplicateStatus).toBe(DuplicateStatus.PENDING_REVIEW);
      expect(response.body.duplicateMatchScore).toBeGreaterThanOrEqual(70);

      // Verify address components were created
      const addressComponents = await prisma.orderAddressComponents.findFirst({
        where: { orderId: response.body.id },
      });
      expect(addressComponents).toBeDefined();
      if (addressComponents) {
        expect(addressComponents.street).toBe('das flores');
        expect(addressComponents.streetNumber).toBe('123');
      }
    });

    it('should not flag orders with different CPF as duplicates', async () => {
      // Create first order
      const firstOrder = await createTestOrder({
        customerCPF: '123.456.789-00',
        fullAddress: 'Rua das Flores, 123, São Paulo - SP',
      });

      // Create second order with different CPF but same address
      const secondOrderData = {
        orderNumber: 'TEST-003',
        customerName: 'Maria Santos',
        customerCPF: '987.654.321-00',
        customerEmail: '<EMAIL>',
        fullAddress: 'Rua das Flores, 123, São Paulo - SP',
        totalAmount: 150,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 150 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(secondOrderData);

      expect(response.status).toBe(201);
      expect(response.body.isDuplicate).toBe(false);
      expect(response.body.duplicateStatus).toBeNull();
    });

    it('should not flag orders with same CPF but completely different address', async () => {
      // Create first order
      const firstOrder = await createTestOrder({
        customerCPF: '123.456.789-00',
        fullAddress: 'Rua das Flores, 123, São Paulo - SP',
      });

      // Create second order with same CPF but different address
      const secondOrderData = {
        orderNumber: 'TEST-004',
        customerName: 'João Silva',
        customerCPF: '123.456.789-00',
        customerEmail: '<EMAIL>',
        fullAddress: 'Av. Paulista, 1000, Bela Vista, São Paulo - SP',
        totalAmount: 300,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-2', quantity: 3, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(secondOrderData);

      expect(response.status).toBe(201);
      expect(response.body.isDuplicate).toBe(false);
    });
  });

  describe('Review Queue API', () => {
    it('should return pending duplicate orders in review queue', async () => {
      // Create multiple duplicate orders
      await createTestOrder({
        customerCPF: '111.222.333-44',
        fullAddress: 'Rua A, 100, São Paulo - SP',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 85,
      });

      await createTestOrder({
        orderNumber: 'TEST-006',
        customerCPF: '111.222.333-44',
        fullAddress: 'R. A, 100, São Paulo - SP',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 85,
      });

      const response = await request(app.getHttpServer())
        .get('/antifraud/duplicates/review-queue')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.items).toHaveLength(2);
      expect(response.body.total).toBe(2);
      expect(response.body.items[0].duplicateStatus).toBe(DuplicateStatus.PENDING_REVIEW);
    });

    it('should filter review queue by score range', async () => {
      // Create orders with different scores
      await createTestOrder({
        customerCPF: '222.333.444-55',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 75,
      });

      await createTestOrder({
        orderNumber: 'TEST-008',
        customerCPF: '333.444.555-66',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 95,
      });

      const response = await request(app.getHttpServer())
        .get('/antifraud/duplicates/review-queue')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .query({ minScore: 90, maxScore: 100 });

      expect(response.status).toBe(200);
      expect(response.body.items).toHaveLength(1);
      expect(response.body.items[0].duplicateMatchScore).toBe(95);
    });
  });

  describe('Review Decision Flow', () => {
    it('should approve a duplicate order', async () => {
      // Create duplicate order
      const order = await createTestOrder({
        customerCPF: '444.555.666-77',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 88,
      });

      const reviewData = {
        decision: ReviewDecision.APPROVE_ORDER,
        reason: 'Valid duplicate - different delivery dates',
      };

      const response = await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(reviewData);

      expect(response.status).toBe(200);

      // Verify order was updated
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id, tenantId },
      });
      if (updatedOrder) {
        expect(updatedOrder.duplicateStatus).toBe(DuplicateStatus.APPROVED);
        expect(updatedOrder.reviewDecision).toBe(ReviewDecision.APPROVE_ORDER);
        expect(updatedOrder.reviewedBy).toBe('test-user');
      }

      // Verify audit log was created
      const auditLog = await prisma.orderAuditLog.findFirst({
        where: { orderId: order.id, action: 'DUPLICATE_APPROVED', tenantId },
      });
      expect(auditLog).toBeDefined();
      expect((auditLog as any)?.metadata?.['reason']).toBe('Valid duplicate - different delivery dates');
    });

    it('should deny a duplicate order', async () => {
      // Create duplicate order
      const order = await createTestOrder({
        orderNumber: 'TEST-010',
        customerCPF: '555.666.777-88',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 92,
      });

      const reviewData = {
        decision: ReviewDecision.DENY_ORDER,
        reason: 'Confirmed fraud attempt',
      };

      const response = await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(reviewData);

      expect(response.status).toBe(200);

      // Verify order was updated
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id, tenantId },
      });
      if (updatedOrder) {
        expect(updatedOrder.duplicateStatus).toBe(DuplicateStatus.DENIED);
        expect(updatedOrder.reviewDecision).toBe(ReviewDecision.DENY_ORDER);
      }

      // Verify audit log
      const auditLog = await prisma.orderAuditLog.findFirst({
        where: { orderId: order.id, action: 'DUPLICATE_DENIED', tenantId },
      });
      expect(auditLog).toBeDefined();
    });

    it('should prevent reviewing already reviewed orders', async () => {
      // Create already reviewed order
      const order = await createTestOrder({
        orderNumber: 'TEST-011',
        customerCPF: '666.777.888-99',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.APPROVED,
        reviewDecision: ReviewDecision.APPROVE_ORDER,
        duplicateMatchScore: 85,
      });

      const reviewData = {
        decision: ReviewDecision.DENY_ORDER,
        reason: 'Trying to change decision',
      };

      const response = await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(reviewData);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('already been reviewed');
    });
  });

  describe('Bulk Review Operations', () => {
    it('should bulk approve multiple orders', async () => {
      // Create multiple pending orders
      const order1 = await createTestOrder({
        orderNumber: 'BULK-001',
        customerCPF: '777.888.999-00',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 78,
      });

      const order2 = await createTestOrder({
        orderNumber: 'BULK-002',
        customerCPF: '888.999.000-11',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 82,
      });

      const bulkReviewData = {
        orderIds: [order1.id, order2.id],
        review: {
          decision: ReviewDecision.APPROVE_ORDER,
          reason: 'Bulk approval - verified orders',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/antifraud/duplicates/bulk-review')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(bulkReviewData);

      expect(response.status).toBe(200);
      expect(response.body.processed).toBe(2);
      expect(response.body.successful).toBe(2);
      expect(response.body.failed).toBe(0);

      // Verify both orders were updated
      const updatedOrders = await prisma.order.findMany({
        where: { id: { in: [order1.id, order2.id] }, tenantId },
      });

      updatedOrders.forEach(order => {
        expect(order.duplicateStatus).toBe(DuplicateStatus.APPROVED);
        expect(order.reviewDecision).toBe(ReviewDecision.APPROVE_ORDER);
      });
    });
  });

  describe('Audit Trail', () => {
    it('should return complete audit trail for an order', async () => {
      // Create order and perform actions
      const order = await createTestOrder({
        orderNumber: 'AUDIT-001',
        customerCPF: '999.000.111-22',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 90,
      });

      // Review the order
      await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send({
          decision: ReviewDecision.APPROVE_ORDER,
          reason: 'Test approval',
        });

      // Get audit trail
      const response = await request(app.getHttpServer())
        .get(`/antifraud/orders/${order.id}/audit-trail`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId);

      expect(response.status).toBe(200);
      expect(response.body.length).toBeGreaterThan(0);
      
      const actions = response.body.map(log => log.action);
      expect(actions).toContain('ORDER_CREATED');
      expect(actions).toContain('DUPLICATE_DETECTED');
      expect(actions).toContain('DUPLICATE_APPROVED');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid CPF format', async () => {
      const orderData = {
        orderNumber: 'ERROR-001',
        customerName: 'Test User',
        customerCPF: 'invalid-cpf',
        customerEmail: '<EMAIL>',
        fullAddress: 'Test Address',
        totalAmount: 100,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(400);
    });

    it('should handle missing tenant ID', async () => {
      const response = await request(app.getHttpServer())
        .get('/antifraud/duplicates/review-queue')
        .set('Authorization', authToken);

      expect(response.status).toBe(400);
    });
  });

  // Helper function to create test orders
  async function createTestOrder(data: Partial<any>) {
    const encryptedCPF = data.customerCPF ? 
      crypto.createHash('sha256').update(data.customerCPF).digest('hex') : null;
    const cpfHash = data.customerCPF ? 
      crypto.createHash('sha256').update(data.customerCPF).digest('hex') : null;

    return prisma.order.create({
      data: {
        tenantId,
        orderNumber: data.orderNumber || 'TEST-001',
        customerName: data.customerName || 'Test Customer',
        customerCPF: encryptedCPF,
        customerCPFHash: cpfHash,
        customerPhone: data.customerPhone || '11999999999',
        fullAddress: data.fullAddress || 'Test Address',
        total: data.totalAmount || 100,
        sellerId: data.sellerId || 'seller-001',
        status: data.status || 'PENDING',
        isDuplicate: data.isDuplicate || false,
        duplicateStatus: data.duplicateStatus || null,
        duplicateMatchScore: data.duplicateMatchScore || null,
        reviewDecision: data.reviewDecision || null,
      },
    });
  }
});