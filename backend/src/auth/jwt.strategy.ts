import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private usersService: UsersService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET || 'supertokensecret',
    });
  }

  async validate(payload: any) {
    // Fetch complete user data from database
    const user = await this.usersService.findById(payload.sub);
    
    if (!user) {
      return null;
    }
    
    // Return user object with all necessary fields
    return { 
      id: user.id,
      userId: user.id, // Keep for backward compatibility
      name: user.name,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId 
    };
  }
}