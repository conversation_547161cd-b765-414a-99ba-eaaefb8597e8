import { Controller, Post, Body, Get, UseGuards, Request, HttpException, HttpStatus, Headers } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  async login(@Body() loginDto: { email: string; password: string }, @Headers('x-tenant-id') tenantId: string) {
    try {
      console.log('🔐 Login attempt for:', loginDto.email, 'tenant:', tenantId);
      const result = await this.authService.login(loginDto.email, loginDto.password);
      console.log('✅ Login successful for:', loginDto.email);
      return result;
    } catch (error) {
      console.error('❌ Login failed:', error.message);
      console.error('❌ Login error details:', {
        email: loginDto.email,
        tenantId: tenantId,
        error: error
      });
      throw new HttpException(
        error.message || 'Erro ao fazer login',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getProfile(@Request() req) {
    return this.authService.getProfile(req.user.userId);
  }

  // Temporary endpoint to reset admin password
  @Post('reset-admin')
  async resetAdmin(@Body() body: { secret: string }) {
    if (body.secret !== 'zencash-reset-2025') {
      throw new HttpException('Invalid secret', HttpStatus.FORBIDDEN);
    }

    return this.authService.resetAdminPassword();
  }
}