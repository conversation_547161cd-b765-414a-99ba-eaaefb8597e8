import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { Role } from '@prisma/client';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private prisma: PrismaService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    console.log('[Auth] Validating user:', email);
    const user = await this.usersService.findByEmail(email);
    
    if (!user) {
      console.log('[Auth] User not found:', email);
      throw new UnauthorizedException('Credenciais inválidas');
    }

    console.log('[Auth] User found:', {
      id: user.id,
      email: user.email,
      active: user.active,
      role: user.role
    });

    if (!user.active) {
      console.log('[Auth] User is inactive:', email);
      throw new UnauthorizedException('Usuário inativo');
    }

    const isPasswordValid = await this.usersService.validatePassword(password, user.password);
    console.log('[Auth] Password validation result:', isPasswordValid);
    
    if (!isPasswordValid) {
      console.log('[Auth] Invalid password for:', email);
      throw new UnauthorizedException('Credenciais inválidas');
    }

    const { password: _, ...result } = user;
    return result;
  }

  async login(email: string, password: string) {
    const user = await this.validateUser(email, password);
    
    const payload = { 
      sub: user.id, 
      role: user.role,
      tenantId: user.tenantId 
    };
    
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId,
      }
    };
  }

  async getProfile(userId: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('Usuário não encontrado');
    }
    
    const { password: _, ...result } = user;
    return result;
  }

  async resetAdminPassword() {
    const tenantId = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
    const email = '<EMAIL>';
    const newPassword = 'admin123';
    
    console.log('[Auth] Resetting admin password...');
    
    // Find admin user
    const adminUser = await this.usersService.findByEmailAndTenant(email, tenantId);
    
    if (!adminUser) {
      console.log('[Auth] Admin user not found, creating...');
      // Create admin user
      await this.usersService.create({
        email,
        password: newPassword,
        name: 'Admin',
        role: Role.ADMIN,
        active: true,
      }, tenantId);
      
      return { message: 'Admin user created with password: admin123' };
    }
    
    // Update password
    console.log('[Auth] Updating admin password...');
    const hashedPassword = await this.usersService.hashPassword(newPassword);
    
    // Update password directly using Prisma
    await this.prisma.user.update({
      where: { id: adminUser.id },
      data: {
        password: hashedPassword,
        active: true,
      },
    });
    
    return { message: 'Admin password reset to: admin123' };
  }
}