import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { PrismaService } from '../prisma/prisma.service';
import { AntifraudService } from '../antifraud/antifraud.service';
import { OrderStatus } from '@prisma/client';

async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const prisma = app.get(PrismaService);
  const antifraudService = app.get(AntifraudService);

  try {
    console.log('🔍 Processing antifraud checks for all orders...\n');

    // Get the default tenant
    const tenantResult = await prisma.$queryRaw<any[]>`SELECT id FROM Tenant LIMIT 1`;
    if (!tenantResult || tenantResult.length === 0) {
      throw new Error('No tenant found');
    }
    const tenantId = tenantResult[0].id;

    // Find all orders that need antifraud processing
    const ordersToProcess = await prisma.order.findMany({
      where: {
        tenantId,
        deletedAt: null,
        OR: [
          { riskScore: null },
          { duplicateMatchScore: null },
          { 
            AND: [
              { customerPhone: { not: null } },
              { customerName: { not: null } },
              { isDuplicate: false }
            ]
          }
        ]
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Found ${ordersToProcess.length} orders to process\n`);

    // Group orders by customer phone + name to find duplicates
    const customerGroups = new Map<string, any[]>();
    
    for (const order of ordersToProcess) {
      if (order.customerPhone && order.customerName) {
        const key = `${order.customerPhone.trim().toLowerCase()}_${order.customerName.trim().toLowerCase()}`;
        if (!customerGroups.has(key)) {
          customerGroups.set(key, []);
        }
        customerGroups.get(key)!.push(order);
      }
    }

    // Log duplicate groups found
    console.log('📊 Duplicate groups found:');
    for (const [key, orders] of customerGroups.entries()) {
      if (orders.length > 1) {
        const [phone, name] = key.split('_');
        console.log(`\n👥 Customer: ${name} | Phone: ${phone}`);
        console.log(`   Orders (${orders.length}):`);
        for (const order of orders) {
          console.log(`   - ${order.orderNumber || order.id} | ${order.total} | ${new Date(order.createdAt).toLocaleDateString()}`);
        }
      }
    }
    console.log('\n');

    // Process each order
    let processed = 0;
    let duplicatesFound = 0;
    let highRiskFound = 0;

    for (const order of ordersToProcess) {
      console.log(`\n📦 Processing order ${order.orderNumber || order.id}...`);
      console.log(`   Customer: ${order.customerName} | Phone: ${order.customerPhone}`);

      try {
        // First run comprehensive duplicate check
        await antifraudService.processOrderForComprehensiveDuplicateCheck(
          order.tenantId,
          order.id,
          {
            customerCPF: order.customerCPF || undefined,
            fullAddress: order.fullAddress || undefined,
            customerPhone: order.customerPhone,
            customerName: order.customerName,
          }
        );

        // Then assess risk
        await antifraudService.assessOrderRisk(order.id, order.tenantId);

        // Get updated order to see results
        const updatedOrder = await prisma.order.findUnique({
          where: { id: order.id },
          select: {
            orderNumber: true,
            isDuplicate: true,
            duplicateMatchScore: true,
            riskScore: true,
            riskLevel: true,
            riskFactors: true,
            originalOrderIds: true
          }
        });

        if (updatedOrder) {
          console.log(`   ✅ Processed successfully:`);
          console.log(`      - Duplicate: ${updatedOrder.isDuplicate ? 'YES' : 'NO'}`);
          if (updatedOrder.isDuplicate) {
            console.log(`      - Match Score: ${updatedOrder.duplicateMatchScore}%`);
            console.log(`      - Matched Orders: ${updatedOrder.originalOrderIds?.length || 0}`);
            duplicatesFound++;
          }
          console.log(`      - Risk Score: ${updatedOrder.riskScore}`);
          console.log(`      - Risk Level: ${updatedOrder.riskLevel}`);
          if (updatedOrder.riskFactors && updatedOrder.riskFactors.length > 0) {
            console.log(`      - Risk Factors:`);
            for (const factor of updatedOrder.riskFactors) {
              console.log(`        • ${factor}`);
            }
          }
          
          if (updatedOrder.riskLevel === 'HIGH' || updatedOrder.riskLevel === 'CRITICAL') {
            highRiskFound++;
          }
        }

        processed++;
      } catch (error) {
        console.error(`   ❌ Error processing order:`, error);
      }
    }

    console.log('\n\n📊 Summary:');
    console.log(`   - Total orders processed: ${processed}`);
    console.log(`   - Duplicates found: ${duplicatesFound}`);
    console.log(`   - High/Critical risk orders: ${highRiskFound}`);

    // Show current antifraud queue
    console.log('\n\n🔍 Current Antifraud Review Queue:');
    const reviewQueue = await antifraudService.getOrdersForReview(tenantId, undefined, 1, 100);
    
    console.log(`\nTotal orders requiring review: ${reviewQueue.total}`);
    console.log(`By risk level:`);
    Object.entries(reviewQueue.byRiskLevel).forEach(([level, count]) => {
      console.log(`   - ${level}: ${count}`);
    });

    if (reviewQueue.items.length > 0) {
      console.log('\nTop orders for review:');
      reviewQueue.items.slice(0, 10).forEach(item => {
        console.log(`\n   📦 ${item.orderNumber || item.id}`);
        console.log(`      Customer: ${item.customerName} | Phone: ${item.customerPhone}`);
        console.log(`      Risk: ${item.riskLevel} (score: ${item.riskScore})`);
        console.log(`      Duplicate: ${item.isDuplicate ? `YES (${item.duplicateMatchScore}% match)` : 'NO'}`);
        if (item.riskFactors.length > 0) {
          console.log(`      Factors: ${item.riskFactors.join(', ')}`);
        }
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await app.close();
  }
}

main();