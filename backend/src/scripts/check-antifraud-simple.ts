// Simple script to check antifraud issues directly
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Checking antifraud issues...\n');
  
  // Get all orders in review queue
  const orders = await prisma.order.findMany({
    where: {
      AND: [
        {
          OR: [
            { requiresReview: true },
            { status: 'Analise' }
          ]
        },
        { reviewedAt: null }
      ]
    },
    select: {
      id: true,
      orderNumber: true,
      customerName: true,
      customerPhone: true,
      total: true,
      createdAt: true,
      isDuplicate: true,
      duplicateMatchScore: true,
      riskScore: true,
      riskLevel: true,
      riskFactors: true,
      originalOrderIds: true
    },
    orderBy: [
      { riskScore: 'desc' },
      { createdAt: 'desc' }
    ],
    take: 10
  });
  
  console.log(`Found ${orders.length} orders in antifraud queue:\n`);
  
  // Group by customer to find duplicates
  const customerGroups = new Map<string, typeof orders>();
  
  for (const order of orders) {
    const key = `${order.customerPhone}|${order.customerName.toLowerCase()}`;
    if (!customerGroups.has(key)) {
      customerGroups.set(key, []);
    }
    customerGroups.get(key)!.push(order);
  }
  
  // Show groups with multiple orders
  for (const [key, groupOrders] of customerGroups) {
    if (groupOrders.length > 1) {
      const [phone, name] = key.split('|');
      console.log(`\n👥 DUPLICATE GROUP: ${name} - ${phone}`);
      console.log(`   Found ${groupOrders.length} orders:`);
      
      for (const order of groupOrders) {
        console.log(`\n   📦 Order: ${order.orderNumber}`);
        console.log(`      - Created: ${order.createdAt.toISOString()}`);
        console.log(`      - Total: R$ ${order.total}`);
        console.log(`      - Is Duplicate: ${order.isDuplicate}`);
        console.log(`      - Duplicate Score: ${order.duplicateMatchScore || 'null'}`);
        console.log(`      - Risk Score: ${order.riskScore || 'null'}`);
        console.log(`      - Risk Level: ${order.riskLevel || 'null'}`);
        console.log(`      - Risk Factors: ${JSON.stringify(order.riskFactors || [])}`);
        
        if (order.isDuplicate && order.originalOrderIds && order.originalOrderIds.length > 0) {
          console.log(`      - Matched with: ${order.originalOrderIds.length} orders`);
        }
      }
    }
  }
  
  // Check for orders that should be duplicates
  console.log('\n\n🔍 Checking for potential unmarked duplicates...\n');
  
  for (const order of orders) {
    // Find other orders with same phone + name
    const duplicates = await prisma.order.findMany({
      where: {
        customerPhone: order.customerPhone,
        customerName: {
          equals: order.customerName,
          mode: 'insensitive'
        },
        id: { not: order.id },
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        createdAt: true
      }
    });
    
    if (duplicates.length > 0) {
      console.log(`⚠️  Order ${order.orderNumber}:`);
      console.log(`   - Customer: ${order.customerName} - ${order.customerPhone}`);
      console.log(`   - Is marked as duplicate: ${order.isDuplicate}`);
      console.log(`   - Duplicate score: ${order.duplicateMatchScore || 'null'}`);
      console.log(`   - Risk score: ${order.riskScore || 'null'} (${order.riskLevel || 'null'})`);
      console.log(`   - Found ${duplicates.length} other orders with same customer:`);
      for (const dup of duplicates) {
        console.log(`     • ${dup.orderNumber} (${dup.createdAt.toISOString()})`);
      }
      console.log('');
    }
  }
  
  // Show summary of risk levels
  console.log('\n📊 RISK LEVEL SUMMARY:');
  const riskLevels = orders.reduce((acc, order) => {
    const level = order.riskLevel || 'UNKNOWN';
    acc[level] = (acc[level] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  for (const [level, count] of Object.entries(riskLevels)) {
    console.log(`   ${level}: ${count} orders`);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });