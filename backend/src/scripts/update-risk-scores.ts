import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AntifraudService } from '../antifraud/antifraud.service';
import { PrismaService } from '../prisma/prisma.service';

async function updateRiskScores() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const antifraudService = app.get(AntifraudService);
  const prisma = app.get(PrismaService);
  
  console.log('🔄 Updating risk scores for orders with outdated risk factors...\n');
  
  // Find orders that need risk score updates
  const orders = await prisma.order.findMany({
    where: {
      OR: [
        { riskFactors: { has: 'Cliente novo / primeira compra' } },
        { riskFactors: { hasSome: ['Cliente com 1 pedido(s) nos últimos 7 dias'] } },
        { orderNumber: { in: ['sal49041', 'sal09736'] } }
      ],
      requiresReview: true,
      reviewedAt: null
    },
    select: {
      id: true,
      orderNumber: true,
      tenantId: true,
      riskFactors: true,
      riskScore: true
    }
  });
  
  console.log(`Found ${orders.length} orders to update\n`);
  
  for (const order of orders) {
    try {
      console.log(`Updating order ${order.orderNumber || order.id}:`);
      console.log(`  Old score: ${order.riskScore}`);
      console.log(`  Old factors: ${JSON.stringify(order.riskFactors)}`);
      
      // Reassess risk
      await antifraudService.assessOrderRisk(order.id, order.tenantId);
      
      // Get updated order
      const updated = await prisma.order.findUnique({
        where: { id: order.id },
        select: {
          riskScore: true,
          riskFactors: true,
          riskLevel: true,
          requiresReview: true
        }
      });
      
      console.log(`  New score: ${updated?.riskScore}`);
      console.log(`  New factors: ${JSON.stringify(updated?.riskFactors)}`);
      console.log(`  Risk level: ${updated?.riskLevel}`);
      console.log(`  Requires review: ${updated?.requiresReview}`);
      console.log('---\n');
      
    } catch (error) {
      console.error(`❌ Error updating order ${order.orderNumber}:`, error);
    }
  }
  
  await app.close();
  console.log('✅ Risk score update completed');
}

updateRiskScores().catch(console.error);