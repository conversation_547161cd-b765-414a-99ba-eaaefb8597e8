import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AntifraudService } from '../antifraud/antifraud.service';
import { OrdersService } from '../orders/orders.service';

async function runAntifraudOnOrders() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const antifraudService = app.get(AntifraudService);
  const ordersService = app.get(OrdersService);
  
  const orderNumbers = ['sal49041', 'sal09736'];
  
  console.log('🔍 Running antifraud checks on duplicate orders...\n');
  
  for (const orderNumber of orderNumbers) {
    try {
      console.log(`Processing order: ${orderNumber}`);
      
      // Get the order
      const order = await ordersService.findByOrderNumber(orderNumber);
      
      if (!order) {
        console.log(`❌ Order ${orderNumber} not found`);
        continue;
      }
      
      console.log(`📋 Order details:
        - ID: ${order.id}
        - Customer: ${order.customerName}
        - Phone: ${order.customerPhone}
        - Address: ${order.fullAddress}
        - Status: ${order.status}
        - Is Duplicate: ${order.isDuplicate}
      `);
      
      // Run comprehensive duplicate check
      await antifraudService.processOrderForComprehensiveDuplicateCheck(
        order.tenantId,
        order.id,
        {
          customerCPF: order.customerCPF || undefined,
          fullAddress: order.fullAddress || undefined,
          customerPhone: order.customerPhone,
          customerName: order.customerName,
        }
      );
      
      // Assess risk
      await antifraudService.assessOrderRisk(order.id, order.tenantId);
      
      // Get updated order
      const updatedOrder = await ordersService.findByOrderNumber(orderNumber);
      
      console.log(`✅ Antifraud check completed:
        - Is Duplicate: ${updatedOrder.isDuplicate}
        - Duplicate Score: ${updatedOrder.duplicateMatchScore}
        - Risk Score: ${updatedOrder.riskScore}
        - Risk Level: ${updatedOrder.riskLevel}
        - Requires Review: ${updatedOrder.requiresReview}
      `);
      console.log('---\n');
      
    } catch (error) {
      console.error(`❌ Error processing order ${orderNumber}:`, error);
    }
  }
  
  await app.close();
  console.log('✅ Antifraud processing completed');
}

runAntifraudOnOrders().catch(console.error);