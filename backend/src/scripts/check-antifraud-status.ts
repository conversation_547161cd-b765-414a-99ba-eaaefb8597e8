import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔍 Checking antifraud status...\n');

    // Get orders with duplicate information
    const duplicateGroups = await prisma.$queryRaw<any[]>`
      SELECT 
        "customerPhone",
        "customerName",
        COUNT(*) as order_count,
        STRING_AGG("orderNumber", ', ') as order_numbers,
        STRING_AGG(CONCAT("riskLevel", '(', COALESCE("riskScore"::text, '0'), ')'), ', ') as risk_levels,
        STRING_AGG(CASE WHEN "isDuplicate" = true THEN 'DUP' ELSE 'NO' END, ', ') as dup_status
      FROM "Order"
      WHERE "deletedAt" IS NULL
        AND "customerPhone" IS NOT NULL
        AND "customerName" IS NOT NULL
      GROUP BY "customerPhone", "customerName"
      HAVING COUNT(*) > 1
      ORDER BY COUNT(*) DESC
    `;

    console.log('📊 Duplicate Customer Groups:\n');
    duplicateGroups.forEach(group => {
      console.log(`👥 ${group.customerName} | ${group.customerPhone}`);
      console.log(`   Orders (${group.order_count}): ${group.order_numbers}`);
      console.log(`   Risk Levels: ${group.risk_levels}`);
      console.log(`   Duplicate Status: ${group.dup_status}`);
      console.log('');
    });

    // Get orders in review queue
    const reviewQueue = await prisma.$queryRaw<any[]>`
      SELECT 
        "orderNumber",
        "customerName",
        "customerPhone",
        "riskLevel",
        "riskScore",
        "isDuplicate",
        "duplicateMatchScore",
        "status"
      FROM "Order"
      WHERE "deletedAt" IS NULL
        AND ("requiresReview" = true OR "status" = 'Analise')
        AND "reviewedAt" IS NULL
      ORDER BY "riskScore" DESC NULLS LAST
      LIMIT 10
    `;

    console.log('\n🚨 Current Review Queue:\n');
    reviewQueue.forEach(order => {
      console.log(`📦 ${order.orderNumber}`);
      console.log(`   Customer: ${order.customerName} | ${order.customerPhone}`);
      console.log(`   Risk: ${order.riskLevel || 'NOT SET'} (Score: ${order.riskScore || 0})`);
      console.log(`   Duplicate: ${order.isDuplicate ? `YES (${order.duplicateMatchScore}%)` : 'NO'}`);
      console.log(`   Status: ${order.status}`);
      console.log('');
    });

    // Count orders without risk assessment
    const nullRiskCount = await prisma.$queryRaw<any[]>`
      SELECT COUNT(*) as count FROM "Order"
      WHERE "deletedAt" IS NULL AND "riskScore" IS NULL
    `;

    console.log(`\n⚠️  Orders without risk assessment: ${Number(nullRiskCount[0].count)}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();