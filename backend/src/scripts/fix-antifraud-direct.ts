import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'mysql://root:@localhost:3306/zencash'
    }
  }
});

// Simple risk calculation without NestJS dependencies
async function calculateRiskScore(order: any, tenantId: string): Promise<{
  riskScore: number;
  riskLevel: string;
  riskFactors: string[];
}> {
  const riskFactors: string[] = [];
  let riskScore = 0;

  // Factor 1: Duplicate order check (45 points)
  if (order.isDuplicate) {
    const score = Math.min(45, Math.round((order.duplicateMatchScore || 0) * 0.45));
    riskScore += score;
    riskFactors.push(`Pedido duplicado detectado (score: ${order.duplicateMatchScore}%)`);
  }

  // Factor 2: Same customer in database (30 points)
  const totalSameCustomer = await prisma.order.count({
    where: {
      tenantId,
      customerPhone: order.customerPhone,
      customerName: order.customerName,
      id: { not: order.id },
      deletedAt: null,
    },
  });

  if (totalSameCustomer > 0) {
    const extraRisk = Math.min(30, totalSameCustomer * 15);
    riskScore += extraRisk;
    riskFactors.push(`Cliente com ${totalSameCustomer} pedido(s) no sistema`);
  }

  // Factor 3: High value order (20 points)
  const orderValue = Number(order.total);
  if (orderValue > 1000) {
    const valueScore = Math.min(20, Math.round((orderValue - 1000) / 100));
    riskScore += valueScore;
    if (valueScore > 10) {
      riskFactors.push('Pedido de alto valor');
    }
  }

  // Factor 4: Multiple orders same day (20 points)
  const startOfDay = new Date(order.createdAt);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(order.createdAt);
  endOfDay.setHours(23, 59, 59, 999);

  const sameDayOrders = await prisma.order.count({
    where: {
      customerPhone: order.customerPhone,
      tenantId,
      deletedAt: null,
      createdAt: { gte: startOfDay, lte: endOfDay },
      id: { not: order.id }
    }
  });

  if (sameDayOrders > 0) {
    const dayScore = Math.min(20, sameDayOrders * 10);
    riskScore += dayScore;
    riskFactors.push(`${sameDayOrders + 1} pedidos no mesmo dia`);
  }

  // Factor 5: Incomplete customer data (15 points)
  let dataScore = 0;
  if (!order.customerCPF) {
    dataScore += 8;
    riskFactors.push('CPF não informado');
  }
  if (!order.fullAddress || order.fullAddress.length < 20) {
    dataScore += 7;
    riskFactors.push('Endereço incompleto');
  }
  riskScore += dataScore;

  // Determine risk level
  let riskLevel: string;
  if (riskScore >= 70) {
    riskLevel = 'CRITICAL';
  } else if (riskScore >= 50) {
    riskLevel = 'HIGH';
  } else if (riskScore >= 30) {
    riskLevel = 'MEDIUM';
  } else {
    riskLevel = 'LOW';
  }

  return {
    riskScore,
    riskLevel,
    riskFactors
  };
}

async function main() {
  try {
    console.log('🔍 Processing antifraud checks for all orders...\n');

    // Get the default tenant
    const tenant = await prisma.tenant.findFirst();
    if (!tenant) {
      throw new Error('No tenant found');
    }
    const tenantId = tenant.id;

    // Find all orders
    const allOrders = await prisma.order.findMany({
      where: {
        tenantId,
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Found ${allOrders.length} total orders\n`);

    // Group orders by customer phone + name to find duplicates
    const customerGroups = new Map<string, any[]>();
    
    for (const order of allOrders) {
      if (order.customerPhone && order.customerName) {
        const key = `${order.customerPhone.trim().toLowerCase()}_${order.customerName.trim().toLowerCase()}`;
        if (!customerGroups.has(key)) {
          customerGroups.set(key, []);
        }
        customerGroups.get(key)!.push(order);
      }
    }

    // Mark duplicates
    console.log('📊 Processing duplicate detection...\n');
    let duplicatesMarked = 0;

    for (const [key, orders] of customerGroups.entries()) {
      if (orders.length > 1) {
        const [phone, name] = key.split('_');
        console.log(`\n👥 Duplicate group found - Customer: ${name} | Phone: ${phone}`);
        console.log(`   Orders (${orders.length}):`);
        
        // Sort by date to find original
        orders.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        
        for (let i = 0; i < orders.length; i++) {
          const order = orders[i];
          const isOriginal = i === 0;
          
          if (!isOriginal) {
            // Mark as duplicate if not already
            const matchedOrderIds = orders.slice(0, i).map(o => o.id);
            
            await prisma.order.update({
              where: { id: order.id },
              data: {
                isDuplicate: true,
                duplicateMatchScore: 100, // Exact phone + name match
                duplicateStatus: 'PENDING_REVIEW',
                originalOrderIds: matchedOrderIds
              }
            });
            
            duplicatesMarked++;
            console.log(`   - ${order.orderNumber || order.id} | ${order.total} | ${new Date(order.createdAt).toLocaleDateString()} [MARKED AS DUPLICATE]`);
          } else {
            console.log(`   - ${order.orderNumber || order.id} | ${order.total} | ${new Date(order.createdAt).toLocaleDateString()} [ORIGINAL]`);
          }
        }
      }
    }

    console.log(`\n✅ Marked ${duplicatesMarked} orders as duplicates\n`);

    // Now calculate risk scores for all orders
    console.log('📊 Calculating risk scores for all orders...\n');
    let processed = 0;
    let highRiskFound = 0;

    for (const order of allOrders) {
      try {
        // Get updated order (in case it was marked as duplicate)
        const currentOrder = await prisma.order.findUnique({
          where: { id: order.id }
        });

        if (!currentOrder) continue;

        const assessment = await calculateRiskScore(currentOrder, tenantId);

        await prisma.order.update({
          where: { id: order.id },
          data: {
            riskScore: assessment.riskScore,
            riskLevel: assessment.riskLevel as any,
            riskFactors: assessment.riskFactors,
            requiresReview: assessment.riskScore >= 50 || currentOrder.isDuplicate,
            fraudCheckCompletedAt: new Date()
          }
        });

        if (assessment.riskLevel === 'HIGH' || assessment.riskLevel === 'CRITICAL') {
          highRiskFound++;
          console.log(`⚠️  High risk order found: ${currentOrder.orderNumber} - ${currentOrder.customerName} (Risk: ${assessment.riskScore})`);
        }

        processed++;
        if (processed % 10 === 0) {
          console.log(`   Processed ${processed}/${allOrders.length} orders...`);
        }
      } catch (error) {
        console.error(`Error processing order ${order.id}:`, error);
      }
    }

    console.log(`\n✅ Processed ${processed} orders`);
    console.log(`   - High/Critical risk orders: ${highRiskFound}`);

    // Show current review queue
    const reviewQueue = await prisma.order.findMany({
      where: {
        tenantId,
        deletedAt: null,
        AND: [
          {
            OR: [
              { requiresReview: true },
              { status: 'Analise' }
            ]
          },
          { reviewedAt: null }
        ]
      },
      orderBy: [
        { riskScore: 'desc' },
        { createdAt: 'desc' }
      ],
      take: 10
    });

    console.log(`\n\n🔍 Current Antifraud Review Queue: ${reviewQueue.length} orders\n`);
    
    for (const item of reviewQueue) {
      console.log(`📦 ${item.orderNumber || item.id}`);
      console.log(`   Customer: ${item.customerName} | Phone: ${item.customerPhone}`);
      console.log(`   Risk: ${item.riskLevel} (score: ${item.riskScore})`);
      console.log(`   Duplicate: ${item.isDuplicate ? `YES (${item.duplicateMatchScore}% match)` : 'NO'}`);
      if (item.riskFactors && (item.riskFactors as string[]).length > 0) {
        console.log(`   Factors: ${(item.riskFactors as string[]).join(', ')}`);
      }
      console.log('');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();