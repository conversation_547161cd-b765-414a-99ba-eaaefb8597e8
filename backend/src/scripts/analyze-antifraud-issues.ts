import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AntifraudService } from '../antifraud/antifraud.service';
import { PrismaService } from '../prisma/prisma.service';

async function analyzeAntifraudIssues() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const antifraudService = app.get(AntifraudService);
  const prisma = app.get(PrismaService);
  
  console.log('🔍 Analyzing antifraud issues...\n');
  
  // Get all orders that require review (from antifraud dashboard)
  const orders = await prisma.order.findMany({
    where: {
      AND: [
        {
          OR: [
            { requiresReview: true },
            { status: 'Analise' }
          ]
        },
        { reviewedAt: null }
      ]
    },
    select: {
      id: true,
      orderNumber: true,
      customerName: true,
      customerPhone: true,
      customerCPF: true,
      fullAddress: true,
      total: true,
      createdAt: true,
      isDuplicate: true,
      duplicateMatchScore: true,
      riskScore: true,
      riskLevel: true,
      riskFactors: true,
      originalOrderIds: true,
      tenantId: true
    },
    orderBy: [
      { riskScore: 'desc' },
      { createdAt: 'desc' }
    ]
  });
  
  console.log(`Found ${orders.length} orders in antifraud queue\n`);
  
  // Group orders by customer phone + name to find potential duplicates
  const ordersByCustomer = new Map<string, typeof orders>();
  
  for (const order of orders) {
    const key = `${order.customerPhone}|${order.customerName.toLowerCase()}`;
    if (!ordersByCustomer.has(key)) {
      ordersByCustomer.set(key, []);
    }
    ordersByCustomer.get(key)!.push(order);
  }
  
  // Analyze each group
  console.log('=== ANALYZING DUPLICATE GROUPS ===\n');
  
  for (const [key, groupOrders] of ordersByCustomer) {
    if (groupOrders.length > 1) {
      const [phone, name] = key.split('|');
      console.log(`📱 Customer: ${name} - Phone: ${phone}`);
      console.log(`Found ${groupOrders.length} orders:\n`);
      
      for (const order of groupOrders) {
        console.log(`  Order: ${order.orderNumber || order.id}`);
        console.log(`    - Created: ${order.createdAt}`);
        console.log(`    - Total: R$ ${order.total}`);
        console.log(`    - Is Duplicate: ${order.isDuplicate}`);
        console.log(`    - Duplicate Score: ${order.duplicateMatchScore || 'N/A'}`);
        console.log(`    - Risk Score: ${order.riskScore}`);
        console.log(`    - Risk Level: ${order.riskLevel}`);
        console.log(`    - Risk Factors: ${JSON.stringify(order.riskFactors)}`);
        
        if (order.originalOrderIds && order.originalOrderIds.length > 0) {
          console.log(`    - Matched Orders: ${order.originalOrderIds.join(', ')}`);
        }
        
        // Recalculate risk score to see what it should be
        console.log('\n    🔄 Recalculating risk score...');
        const assessment = await antifraudService.calculateRiskScore(order.id, order.tenantId);
        console.log(`    - Recalculated Score: ${assessment.riskScore}`);
        console.log(`    - Recalculated Level: ${assessment.riskLevel}`);
        console.log(`    - Recalculated Factors: ${JSON.stringify(assessment.riskFactors)}`);
        console.log('');
      }
      console.log('---\n');
    }
  }
  
  // Check for orders that should be duplicates but aren't marked as such
  console.log('=== CHECKING FOR UNMARKED DUPLICATES ===\n');
  
  for (const order of orders) {
    if (!order.isDuplicate) {
      // Check if there are other orders with same phone + name
      const duplicates = await prisma.order.findMany({
        where: {
          customerPhone: order.customerPhone,
          customerName: {
            equals: order.customerName,
            mode: 'insensitive'
          },
          id: { not: order.id },
          deletedAt: null
        },
        select: {
          id: true,
          orderNumber: true,
          createdAt: true
        }
      });
      
      if (duplicates.length > 0) {
        console.log(`⚠️  Order ${order.orderNumber || order.id} NOT marked as duplicate but has matches:`);
        console.log(`   Customer: ${order.customerName} - Phone: ${order.customerPhone}`);
        console.log(`   Matched orders: ${duplicates.map(d => d.orderNumber || d.id).join(', ')}`);
        console.log('');
      }
    }
  }
  
  // Run comprehensive duplicate check on specific orders
  console.log('\n=== RUNNING COMPREHENSIVE DUPLICATE CHECK ON LOW RISK ORDERS ===\n');
  
  const lowRiskOrders = orders.filter(o => o.riskLevel === 'LOW' && (o.riskScore || 0) < 30);
  console.log(`Found ${lowRiskOrders.length} orders with LOW risk\n`);
  
  for (const order of lowRiskOrders.slice(0, 5)) { // Check first 5
    console.log(`🔄 Re-checking order ${order.orderNumber || order.id}:`);
    console.log(`   Customer: ${order.customerName} - Phone: ${order.customerPhone}`);
    console.log(`   Current risk score: ${order.riskScore}`);
    
    // Run comprehensive duplicate check
    await antifraudService.processOrderForComprehensiveDuplicateCheck(
      order.tenantId,
      order.id,
      {
        customerCPF: order.customerCPF || undefined,
        fullAddress: order.fullAddress || undefined,
        customerPhone: order.customerPhone,
        customerName: order.customerName,
      }
    );
    
    // Reassess risk
    await antifraudService.assessOrderRisk(order.id, order.tenantId);
    
    // Get updated order
    const updated = await prisma.order.findUnique({
      where: { id: order.id },
      select: {
        isDuplicate: true,
        duplicateMatchScore: true,
        riskScore: true,
        riskLevel: true,
        riskFactors: true,
        originalOrderIds: true
      }
    });
    
    console.log(`   Updated duplicate status: ${updated?.isDuplicate}`);
    console.log(`   Updated duplicate score: ${updated?.duplicateMatchScore || 'N/A'}`);
    console.log(`   Updated risk score: ${updated?.riskScore}`);
    console.log(`   Updated risk level: ${updated?.riskLevel}`);
    console.log(`   Updated risk factors: ${JSON.stringify(updated?.riskFactors)}`);
    console.log('---\n');
  }
  
  await app.close();
  console.log('✅ Analysis completed');
}

analyzeAntifraudIssues().catch(console.error);