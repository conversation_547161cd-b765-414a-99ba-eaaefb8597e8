import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { PrismaService } from '../prisma/prisma.service';
import { AntifraudService } from '../antifraud/antifraud.service';

async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const prisma = app.get(PrismaService);
  const antifraudService = app.get(AntifraudService);

  try {
    console.log('🔍 Checking current antifraud queue...\n');

    // Get the default tenant
    const tenantResult = await prisma.$queryRaw<any[]>`SELECT id FROM Tenant LIMIT 1`;
    if (!tenantResult || tenantResult.length === 0) {
      throw new Error('No tenant found');
    }
    const tenantId = tenantResult[0].id;

    // Get orders for review
    const reviewQueue = await antifraudService.getOrdersForReview(tenantId, undefined, 1, 100);
    
    console.log(`📊 Total orders requiring review: ${reviewQueue.total}`);
    console.log('\n📈 By risk level:');
    Object.entries(reviewQueue.byRiskLevel).forEach(([level, count]) => {
      console.log(`   ${level}: ${count}`);
    });

    // Group by risk level for detailed view
    const byRisk = {
      CRITICAL: reviewQueue.items.filter(i => i.riskLevel === 'CRITICAL'),
      HIGH: reviewQueue.items.filter(i => i.riskLevel === 'HIGH'),
      MEDIUM: reviewQueue.items.filter(i => i.riskLevel === 'MEDIUM'),
      LOW: reviewQueue.items.filter(i => i.riskLevel === 'LOW')
    };

    // Show critical and high risk orders
    if (byRisk.CRITICAL.length > 0) {
      console.log('\n🚨 CRITICAL Risk Orders:');
      byRisk.CRITICAL.forEach(item => {
        console.log(`\n   📦 ${item.orderNumber || item.id}`);
        console.log(`      Customer: ${item.customerName} | Phone: ${item.customerPhone}`);
        console.log(`      Total: R$ ${item.total.toFixed(2)}`);
        console.log(`      Risk Score: ${item.riskScore}`);
        console.log(`      Duplicate: ${item.isDuplicate ? `YES (${item.duplicateMatchScore}% match)` : 'NO'}`);
        console.log(`      Risk Factors:`);
        item.riskFactors.forEach(factor => console.log(`        • ${factor}`));
      });
    }

    if (byRisk.HIGH.length > 0) {
      console.log('\n⚠️  HIGH Risk Orders:');
      byRisk.HIGH.forEach(item => {
        console.log(`\n   📦 ${item.orderNumber || item.id}`);
        console.log(`      Customer: ${item.customerName} | Phone: ${item.customerPhone}`);
        console.log(`      Total: R$ ${item.total.toFixed(2)}`);
        console.log(`      Risk Score: ${item.riskScore}`);
        console.log(`      Duplicate: ${item.isDuplicate ? `YES (${item.duplicateMatchScore}% match)` : 'NO'}`);
        console.log(`      Risk Factors:`);
        item.riskFactors.forEach(factor => console.log(`        • ${factor}`));
      });
    }

    // Check for orders with null risk scores
    const nullRiskOrders = await prisma.order.count({
      where: {
        tenantId,
        deletedAt: null,
        riskScore: null
      }
    });

    if (nullRiskOrders > 0) {
      console.log(`\n⚠️  WARNING: ${nullRiskOrders} orders have no risk score calculated!`);
      console.log('   Run npm run antifraud:all to process them.');
    }

    // Check for potential duplicates not marked
    const potentialDuplicates = await prisma.$queryRaw<any[]>`
      SELECT 
        customerPhone,
        customerName,
        COUNT(*) as order_count,
        GROUP_CONCAT(orderNumber) as order_numbers,
        SUM(CASE WHEN isDuplicate = 1 THEN 1 ELSE 0 END) as marked_duplicates
      FROM \`Order\`
      WHERE tenantId = ${tenantId}
        AND deletedAt IS NULL
        AND customerPhone IS NOT NULL
        AND customerName IS NOT NULL
      GROUP BY customerPhone, customerName
      HAVING order_count > 1
        AND marked_duplicates < order_count
      ORDER BY order_count DESC
      LIMIT 10
    `;

    if (potentialDuplicates.length > 0) {
      console.log('\n🔍 Potential duplicates not properly marked:');
      potentialDuplicates.forEach(dup => {
        console.log(`\n   Customer: ${dup.customerName} | Phone: ${dup.customerPhone}`);
        console.log(`   Orders (${dup.order_count}): ${dup.order_numbers}`);
        console.log(`   Marked as duplicate: ${dup.marked_duplicates}/${dup.order_count}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await app.close();
  }
}

main();