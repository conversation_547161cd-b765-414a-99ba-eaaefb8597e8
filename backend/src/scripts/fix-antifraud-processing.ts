import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AntifraudService } from '../antifraud/antifraud.service';
import { PrismaService } from '../prisma/prisma.service';

async function fixAntifraudProcessing() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const antifraudService = app.get(AntifraudService);
  const prisma = app.get(PrismaService);
  
  console.log('🔧 Fixing antifraud processing for orders with missing risk assessments...\n');
  
  // Find orders that need antifraud processing
  const ordersToProcess = await prisma.order.findMany({
    where: {
      OR: [
        { riskScore: null },
        { riskLevel: null },
        { fraudCheckCompletedAt: null }
      ],
      reviewedAt: null,
      deletedAt: null
    },
    select: {
      id: true,
      orderNumber: true,
      tenantId: true,
      customerName: true,
      customerPhone: true,
      customerCPF: true,
      fullAddress: true,
      isDuplicate: true,
      duplicateMatchScore: true,
      riskScore: true,
      riskLevel: true
    }
  });
  
  console.log(`Found ${ordersToProcess.length} orders needing antifraud processing\n`);
  
  for (const order of ordersToProcess) {
    try {
      console.log(`\n📦 Processing order: ${order.orderNumber || order.id}`);
      console.log(`   Customer: ${order.customerName} - ${order.customerPhone}`);
      console.log(`   Current state:`);
      console.log(`   - Is Duplicate: ${order.isDuplicate}`);
      console.log(`   - Duplicate Score: ${order.duplicateMatchScore || 'null'}`);
      console.log(`   - Risk Score: ${order.riskScore || 'null'}`);
      console.log(`   - Risk Level: ${order.riskLevel || 'null'}`);
      
      // Step 1: Run comprehensive duplicate check
      console.log('\n   🔍 Running duplicate check...');
      await antifraudService.processOrderForComprehensiveDuplicateCheck(
        order.tenantId,
        order.id,
        {
          customerCPF: order.customerCPF || undefined,
          fullAddress: order.fullAddress || undefined,
          customerPhone: order.customerPhone,
          customerName: order.customerName,
        }
      );
      
      // Step 2: Assess risk
      console.log('   📊 Assessing risk...');
      await antifraudService.assessOrderRisk(order.id, order.tenantId);
      
      // Get updated order
      const updatedOrder = await prisma.order.findUnique({
        where: { id: order.id },
        select: {
          isDuplicate: true,
          duplicateMatchScore: true,
          riskScore: true,
          riskLevel: true,
          riskFactors: true,
          requiresReview: true,
          originalOrderIds: true
        }
      });
      
      console.log('\n   ✅ Updated state:');
      console.log(`   - Is Duplicate: ${updatedOrder?.isDuplicate}`);
      console.log(`   - Duplicate Score: ${updatedOrder?.duplicateMatchScore || 'null'}`);
      console.log(`   - Risk Score: ${updatedOrder?.riskScore}`);
      console.log(`   - Risk Level: ${updatedOrder?.riskLevel}`);
      console.log(`   - Requires Review: ${updatedOrder?.requiresReview}`);
      console.log(`   - Risk Factors: ${JSON.stringify(updatedOrder?.riskFactors || [])}`);
      
      if (updatedOrder?.isDuplicate && updatedOrder.originalOrderIds && updatedOrder.originalOrderIds.length > 0) {
        console.log(`   - Matched with ${updatedOrder.originalOrderIds.length} order(s)`);
      }
      
    } catch (error) {
      console.error(`\n❌ Error processing order ${order.orderNumber}:`, error);
    }
  }
  
  // Show summary
  console.log('\n\n📊 PROCESSING SUMMARY:\n');
  
  const processedOrders = await prisma.order.findMany({
    where: {
      id: { in: ordersToProcess.map(o => o.id) }
    },
    select: {
      orderNumber: true,
      riskLevel: true,
      isDuplicate: true,
      requiresReview: true
    }
  });
  
  const summary = processedOrders.reduce((acc, order) => {
    const level = order.riskLevel || 'UNKNOWN';
    acc.byRiskLevel[level] = (acc.byRiskLevel[level] || 0) + 1;
    if (order.isDuplicate) acc.duplicates++;
    if (order.requiresReview) acc.requiresReview++;
    return acc;
  }, {
    byRiskLevel: {} as Record<string, number>,
    duplicates: 0,
    requiresReview: 0
  });
  
  console.log('Risk Levels:');
  for (const [level, count] of Object.entries(summary.byRiskLevel)) {
    console.log(`   ${level}: ${count} orders`);
  }
  console.log(`\nDuplicates found: ${summary.duplicates}`);
  console.log(`Orders requiring review: ${summary.requiresReview}`);
  
  await app.close();
  console.log('\n✅ Antifraud processing completed');
}

fixAntifraudProcessing().catch(console.error);