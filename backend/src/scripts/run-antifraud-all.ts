// Script to run antifraud checks on all orders missing risk assessment
// Run with: cd backend && npm run antifraud:all

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { AntifraudService } from '../antifraud/antifraud.service';
import { PrismaService } from '../prisma/prisma.service';

// Add this to ensure proper environment loading
import * as dotenv from 'dotenv';
dotenv.config();

async function runAntifraudOnAll() {
  console.log('🚀 Starting antifraud processing...\n');
  
  // Ensure encryption key is set
  if (!process.env.ENCRYPTION_KEY) {
    console.error('❌ ENCRYPTION_KEY environment variable is not set!');
    console.log('Please set ENCRYPTION_KEY to a 32-character string in your .env file');
    process.exit(1);
  }
  
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn'],
  });
  
  const antifraudService = app.get(AntifraudService);
  const prisma = app.get(PrismaService);
  
  console.log('🔍 Finding orders that need antifraud processing...\n');
  
  // Find orders needing processing
  const ordersToProcess = await prisma.order.findMany({
    where: {
      OR: [
        { riskScore: null },
        { riskLevel: null },
        { fraudCheckCompletedAt: null }
      ],
      reviewedAt: null,
      deletedAt: null,
      status: { not: 'Cancelado' }
    },
    select: {
      id: true,
      orderNumber: true,
      tenantId: true,
      customerName: true,
      customerPhone: true,
      customerCPF: true,
      fullAddress: true,
      total: true,
      createdAt: true,
      isDuplicate: true,
      duplicateMatchScore: true,
      riskScore: true,
      riskLevel: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  });
  
  console.log(`Found ${ordersToProcess.length} orders to process\n`);
  
  let processed = 0;
  let errors = 0;
  
  for (const order of ordersToProcess) {
    try {
      console.log(`\n[${processed + 1}/${ordersToProcess.length}] Processing: ${order.orderNumber || order.id}`);
      console.log(`   Customer: ${order.customerName} - ${order.customerPhone}`);
      console.log(`   Total: R$ ${order.total}`);
      console.log(`   Created: ${order.createdAt.toISOString()}`);
      
      // Run duplicate check
      await antifraudService.processOrderForComprehensiveDuplicateCheck(
        order.tenantId,
        order.id,
        {
          customerCPF: order.customerCPF || undefined,
          fullAddress: order.fullAddress || undefined,
          customerPhone: order.customerPhone,
          customerName: order.customerName,
        }
      );
      
      // Assess risk
      await antifraudService.assessOrderRisk(order.id, order.tenantId);
      
      // Get result
      const result = await prisma.order.findUnique({
        where: { id: order.id },
        select: {
          isDuplicate: true,
          duplicateMatchScore: true,
          riskScore: true,
          riskLevel: true,
          requiresReview: true
        }
      });
      
      console.log(`   ✅ Processed:`);
      console.log(`      - Duplicate: ${result?.isDuplicate} (score: ${result?.duplicateMatchScore || 0})`);
      console.log(`      - Risk: ${result?.riskScore} - ${result?.riskLevel}`);
      console.log(`      - Review Required: ${result?.requiresReview}`);
      
      processed++;
    } catch (error) {
      console.error(`   ❌ Error: ${error.message}`);
      errors++;
    }
  }
  
  console.log('\n\n📊 FINAL SUMMARY:');
  console.log(`   Total orders: ${ordersToProcess.length}`);
  console.log(`   Successfully processed: ${processed}`);
  console.log(`   Errors: ${errors}`);
  
  // Get updated stats
  const stats = await prisma.order.groupBy({
    by: ['riskLevel'],
    where: {
      id: { in: ordersToProcess.map(o => o.id) }
    },
    _count: true
  });
  
  console.log('\n   Risk levels after processing:');
  for (const stat of stats) {
    console.log(`   - ${stat.riskLevel || 'UNKNOWN'}: ${stat._count} orders`);
  }
  
  await app.close();
  console.log('\n✅ Complete!');
}

runAntifraudOnAll().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});