import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('🔧 Fixing duplicate risk scores...\n');

    // Find all duplicate orders
    const duplicateOrders = await prisma.order.findMany({
      where: {
        isDuplicate: true,
        deletedAt: null
      }
    });

    console.log(`Found ${duplicateOrders.length} duplicate orders to review\n`);

    let updated = 0;

    for (const order of duplicateOrders) {
      const currentScore = order.riskScore || 0;
      const duplicateScore = order.duplicateMatchScore || 0;
      
      // Ensure duplicate orders have minimum HIGH risk
      // Base score from duplicate match (45% of match score)
      let newRiskScore = Math.round(duplicateScore * 0.45);
      
      // Add bonus for 100% match duplicates
      if (duplicateScore >= 100) {
        newRiskScore += 25; // Ensures at least 70 (CRITICAL)
      } else if (duplicateScore >= 85) {
        newRiskScore += 15; // Ensures at least 53 (HIGH)
      }

      // Check for same customer in database
      const totalSameCustomer = await prisma.order.count({
        where: {
          customerPhone: order.customerPhone,
          customerName: order.customerName,
          id: { not: order.id },
          deletedAt: null,
        },
      });

      if (totalSameCustomer > 0) {
        const extraRisk = Math.min(30, totalSameCustomer * 15);
        newRiskScore += extraRisk;
      }

      // Ensure minimum scores
      if (order.isDuplicate && newRiskScore < 50) {
        newRiskScore = 50; // Minimum HIGH for duplicates
      }

      // Determine new risk level
      let newRiskLevel: string;
      if (newRiskScore >= 70) {
        newRiskLevel = 'CRITICAL';
      } else if (newRiskScore >= 50) {
        newRiskLevel = 'HIGH';
      } else if (newRiskScore >= 30) {
        newRiskLevel = 'MEDIUM';
      } else {
        newRiskLevel = 'LOW';
      }

      // Update if changed
      if (currentScore !== newRiskScore) {
        await prisma.order.update({
          where: { id: order.id },
          data: {
            riskScore: newRiskScore,
            riskLevel: newRiskLevel as any,
            requiresReview: true
          }
        });

        console.log(`Updated ${order.orderNumber}: ${currentScore} -> ${newRiskScore} (${newRiskLevel})`);
        updated++;
      }
    }

    console.log(`\n✅ Updated ${updated} duplicate orders\n`);

    // Show updated review queue
    const reviewQueue = await prisma.$queryRaw<any[]>`
      SELECT 
        "orderNumber",
        "customerName",
        "customerPhone",
        "riskLevel",
        "riskScore",
        "isDuplicate",
        "duplicateMatchScore",
        "status"
      FROM "Order"
      WHERE "deletedAt" IS NULL
        AND ("requiresReview" = true OR "status" = 'Analise')
        AND "reviewedAt" IS NULL
      ORDER BY "riskScore" DESC NULLS LAST
      LIMIT 10
    `;

    console.log('🚨 Updated Review Queue:\n');
    reviewQueue.forEach(order => {
      console.log(`📦 ${order.orderNumber}`);
      console.log(`   Customer: ${order.customerName} | ${order.customerPhone}`);
      console.log(`   Risk: ${order.riskLevel || 'NOT SET'} (Score: ${order.riskScore || 0})`);
      console.log(`   Duplicate: ${order.isDuplicate ? `YES (${order.duplicateMatchScore}%)` : 'NO'}`);
      console.log('');
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();