import { PrismaClient, RiskLevel, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:48459/railway'
    }
  }
});

// Calculate risk score for an order
async function calculateRiskScore(order: any): Promise<{
  riskScore: number;
  riskLevel: RiskLevel;
  riskFactors: string[];
  requiresReview: boolean;
}> {
  const riskFactors: string[] = [];
  let riskScore = 0;

  // Factor 1: Check for duplicate orders (same phone + name)
  const duplicateCount = await prisma.order.count({
    where: {
      customerPhone: order.customerPhone,
      customerName: order.customerName,
      id: { not: order.id },
      deletedAt: null,
    },
  });

  if (duplicateCount > 0) {
    // Add significant risk for duplicates
    const duplicateScore = Math.min(45, duplicateCount * 20);
    riskScore += duplicateScore;
    riskFactors.push(`Cliente tem ${duplicateCount} pedido(s) anterior(es)`);
    
    // Mark as duplicate if found
    if (!order.isDuplicate) {
      await prisma.order.update({
        where: { id: order.id },
        data: {
          isDuplicate: true,
          duplicateMatchScore: 100
        }
      });
    }
  }

  // Factor 2: High value order (20 points)
  const orderValue = Number(order.total);
  if (orderValue > 1000) {
    const valueScore = Math.min(20, Math.round((orderValue - 1000) / 100));
    riskScore += valueScore;
    if (valueScore > 10) {
      riskFactors.push(`Pedido de alto valor: R$ ${orderValue.toFixed(2)}`);
    }
  }

  // Factor 3: Multiple orders same day (20 points)
  const startOfDay = new Date(order.createdAt);
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date(order.createdAt);
  endOfDay.setHours(23, 59, 59, 999);

  const sameDayOrders = await prisma.order.count({
    where: {
      customerPhone: order.customerPhone,
      createdAt: { gte: startOfDay, lte: endOfDay },
      id: { not: order.id },
      deletedAt: null,
    }
  });

  if (sameDayOrders > 0) {
    const dayScore = Math.min(20, sameDayOrders * 10);
    riskScore += dayScore;
    riskFactors.push(`${sameDayOrders + 1} pedidos no mesmo dia`);
  }

  // Factor 4: Incomplete customer data (15 points)
  if (!order.customerCPF) {
    riskScore += 8;
    riskFactors.push('CPF não informado');
  }
  if (!order.fullAddress || order.fullAddress.length < 20) {
    riskScore += 7;
    riskFactors.push('Endereço incompleto');
  }

  // Factor 5: Order in Analise status (add extra 10 points)
  if (order.status === OrderStatus.Analise) {
    riskScore += 10;
    riskFactors.push('Pedido em análise');
  }

  // Ensure minimum score of 1 for any order being analyzed
  if (riskScore === 0 && (order.status === OrderStatus.Analise || order.requiresReview)) {
    riskScore = 1;
  }

  // Determine risk level
  let riskLevel: RiskLevel;
  if (riskScore >= 70) {
    riskLevel = RiskLevel.CRITICAL;
  } else if (riskScore >= 50) {
    riskLevel = RiskLevel.HIGH;
  } else if (riskScore >= 30) {
    riskLevel = RiskLevel.MEDIUM;
  } else {
    riskLevel = RiskLevel.LOW;
  }

  // Determine if review is required
  const requiresReview = riskScore >= 30 || order.isDuplicate || order.status === OrderStatus.Analise;

  return {
    riskScore,
    riskLevel,
    riskFactors,
    requiresReview
  };
}

async function main() {
  try {
    console.log('🔍 Fixing antifraud scores for all orders...\n');

    // Find all orders that need risk assessment
    const orders = await prisma.order.findMany({
      where: {
        deletedAt: null,
        OR: [
          { status: OrderStatus.Analise },
          { requiresReview: true },
          { riskScore: null },
          { riskLevel: null }
        ]
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Found ${orders.length} orders to process\n`);

    const stats = {
      CRITICAL: 0,
      HIGH: 0,
      MEDIUM: 0,
      LOW: 0,
      total: 0,
      duplicates: 0
    };

    // Process each order
    for (const order of orders) {
      try {
        console.log(`Processing order ${order.orderNumber || order.id}...`);
        
        const assessment = await calculateRiskScore(order);
        
        // Update the order with the calculated risk assessment
        await prisma.order.update({
          where: { id: order.id },
          data: {
            riskScore: assessment.riskScore,
            riskLevel: assessment.riskLevel,
            riskFactors: assessment.riskFactors,
            requiresReview: assessment.requiresReview,
            fraudCheckCompletedAt: new Date()
          }
        });
        
        stats[assessment.riskLevel]++;
        stats.total++;
        if (order.isDuplicate) stats.duplicates++;
        
        console.log(`  ✓ Risk Score: ${assessment.riskScore} | Level: ${assessment.riskLevel}`);
        if (assessment.riskFactors.length > 0) {
          console.log(`  Factors: ${assessment.riskFactors.join(', ')}`);
        }
        
      } catch (error) {
        console.error(`  ✗ Error processing order ${order.id}:`, error);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('📊 SUMMARY:');
    console.log('='.repeat(60));
    console.log(`Total orders processed: ${stats.total}`);
    console.log(`Duplicates found: ${stats.duplicates}`);
    console.log('\nRisk Distribution:');
    console.log(`  🔴 CRITICAL: ${stats.CRITICAL}`);
    console.log(`  🟠 HIGH: ${stats.HIGH}`);
    console.log(`  🟡 MEDIUM: ${stats.MEDIUM}`);
    console.log(`  🟢 LOW: ${stats.LOW}`);

    // Now check orders specifically in Analise status to ensure they all have scores
    const analiseOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      }
    });

    console.log(`\n📝 Orders in Análise status: ${analiseOrders.length}`);
    
    const needsReview = await prisma.order.count({
      where: {
        requiresReview: true,
        reviewedAt: null,
        deletedAt: null
      }
    });
    
    console.log(`📋 Orders requiring review: ${needsReview}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();