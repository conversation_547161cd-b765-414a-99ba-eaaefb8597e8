import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { WinstonModuleOptions } from 'nest-winston';

const logLevels = {
  fatal: 0,
  error: 1,
  warn: 2,
  info: 3,
  debug: 4,
  trace: 5,
};

const logColors = {
  fatal: 'red',
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
  trace: 'magenta',
};

winston.addColors(logColors);

// Custom format for console output
const consoleFormat = winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
  const metaString = Object.keys(meta).length ? JSON.stringify(meta) : '';
  return `${timestamp} [${context || 'Application'}] ${level}: ${message} ${metaString} ${trace ? `\n${trace}` : ''}`;
});

// Custom format for file output
const fileFormat = winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
  const logObject: any = {
    timestamp,
    level,
    context: context || 'Application',
    message,
    ...meta,
  };
  
  if (trace) {
    logObject.trace = trace;
  }
  
  return JSON.stringify(logObject);
});

// Daily rotate file transport for errors
const errorFileTransport = new DailyRotateFile({
  filename: 'logs/error-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  level: 'error',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    fileFormat,
  ),
});

// Daily rotate file transport for all logs
const combinedFileTransport = new DailyRotateFile({
  filename: 'logs/combined-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    fileFormat,
  ),
});

// Console transport configuration
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.colorize(),
    consoleFormat,
  ),
});

// Export Winston configuration
export const winstonConfig: WinstonModuleOptions = {
  levels: logLevels,
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json(),
  ),
  transports: [
    consoleTransport,
    ...(process.env.NODE_ENV !== 'test' ? [errorFileTransport, combinedFileTransport] : []),
  ],
  exitOnError: false,
};