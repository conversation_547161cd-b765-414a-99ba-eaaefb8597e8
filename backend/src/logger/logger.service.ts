import { Injectable, Inject, LoggerService as NestLoggerService, Scope } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class LoggerService implements NestLoggerService {
  private context: string = 'Application';

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  setContext(context: string) {
    this.context = context;
  }

  log(message: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.info(message, { context, ...meta });
  }

  error(message: string, trace?: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.error(message, { context, trace, ...meta });
  }

  warn(message: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.warn(message, { context, ...meta });
  }

  debug(message: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.debug(message, { context, ...meta });
  }

  verbose(message: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.verbose(message, { context, ...meta });
  }

  fatal(message: string, ...optionalParams: any[]) {
    const { context, ...meta } = this.getContextAndMetadata(optionalParams);
    this.logger.error(message, { context, level: 'fatal', ...meta });
  }

  // Helper method to log HTTP requests
  logHttpRequest(method: string, url: string, statusCode: number, responseTime: number, metadata?: any) {
    const message = `${method} ${url} ${statusCode} ${responseTime}ms`;
    this.logger.info(message, {
      context: this.context || 'HTTP',
      method,
      url,
      statusCode,
      responseTime,
      ...metadata,
    });
  }

  // Helper method to log database queries
  logDatabaseQuery(query: string, params: any[], duration: number) {
    this.logger.debug(`Database query executed in ${duration}ms`, {
      context: this.context || 'Database',
      query,
      params,
      duration,
    });
  }

  // Helper method to log external API calls
  logExternalApiCall(service: string, method: string, url: string, statusCode?: number, duration?: number, error?: any) {
    const level = error ? 'error' : 'info';
    const message = error 
      ? `External API call to ${service} failed: ${error.message}`
      : `External API call to ${service} completed`;

    this.logger[level](message, {
      context: this.context || 'ExternalAPI',
      service,
      method,
      url,
      statusCode,
      duration,
      ...(error && { error: error.message, stack: error.stack }),
    });
  }

  // Helper method to extract context and metadata from optional parameters
  private getContextAndMetadata(optionalParams: any[]): { context: string; [key: string]: any } {
    const lastParam = optionalParams[optionalParams.length - 1];
    const isContextString = typeof lastParam === 'string';
    
    if (isContextString) {
      return {
        context: lastParam,
        ...(optionalParams.length > 1 ? optionalParams[0] : {}),
      };
    }

    return {
      context: this.context || 'Application',
      ...(lastParam || {}),
    };
  }
}