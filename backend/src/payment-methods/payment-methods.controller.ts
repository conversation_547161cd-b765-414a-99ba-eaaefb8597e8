import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { PaymentMethodsService } from './payment-methods.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('payment-methods')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PaymentMethodsController {
  constructor(private readonly paymentMethodsService: PaymentMethodsService) {}

  @Post()
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  create(
    @Body() createPaymentMethodDto: CreatePaymentMethodDto,
    @TenantId() tenantId: string,
  ) {
    return this.paymentMethodsService.create(createPaymentMethodDto, tenantId);
  }

  @Get()
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR', 'VENDEDOR')
  findAll(@TenantId() tenantId: string) {
    return this.paymentMethodsService.findAll(tenantId);
  }

  @Get('active')
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR', 'VENDEDOR')
  findAllActive(@TenantId() tenantId: string) {
    return this.paymentMethodsService.findAllActive(tenantId);
  }

  @Get(':id')
  @Roles('ADMIN')
  findOne(@Param('id') id: string, @TenantId() tenantId: string) {
    return this.paymentMethodsService.findOne(id, tenantId);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  update(
    @Param('id') id: string,
    @Body() updatePaymentMethodDto: UpdatePaymentMethodDto,
    @TenantId() tenantId: string,
  ) {
    return this.paymentMethodsService.update(id, updatePaymentMethodDto, tenantId);
  }

  @Delete(':id')
  @Roles('ADMIN')
  remove(@Param('id') id: string, @TenantId() tenantId: string) {
    return this.paymentMethodsService.remove(id, tenantId);
  }

  @Post('seed-defaults')
  @Roles('ADMIN')
  seedDefaults(@TenantId() tenantId: string) {
    return this.paymentMethodsService.seedDefaultPaymentMethods(tenantId);
  }
}