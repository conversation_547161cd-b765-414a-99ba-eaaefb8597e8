import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { PaymentMethod } from '@prisma/client';

@Injectable()
export class PaymentMethodsService {
  constructor(private prisma: PrismaService) {}

  async create(
    createPaymentMethodDto: CreatePaymentMethodDto,
    tenantId: string,
  ): Promise<PaymentMethod> {
    // Check if payment method with same name already exists for this tenant
    const existing = await this.prisma.paymentMethod.findUnique({
      where: {
        name_tenantId: {
          name: createPaymentMethodDto.name,
          tenantId,
        },
      },
    });

    if (existing) {
      throw new ConflictException('Método de pagamento com este nome já existe');
    }

    return this.prisma.paymentMethod.create({
      data: {
        ...createPaymentMethodDto,
        tenantId,
      },
    });
  }

  async findAll(tenantId: string): Promise<PaymentMethod[]> {
    return this.prisma.paymentMethod.findMany({
      where: { tenantId },
      orderBy: { name: 'asc' },
    });
  }

  async findAllActive(tenantId: string): Promise<PaymentMethod[]> {
    return this.prisma.paymentMethod.findMany({
      where: {
        tenantId,
        isActive: true,
      },
      orderBy: { name: 'asc' },
    });
  }

  async findOne(id: string, tenantId: string): Promise<PaymentMethod> {
    const paymentMethod = await this.prisma.paymentMethod.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!paymentMethod) {
      throw new NotFoundException('Método de pagamento não encontrado');
    }

    return paymentMethod;
  }

  async update(
    id: string,
    updatePaymentMethodDto: UpdatePaymentMethodDto,
    tenantId: string,
  ): Promise<PaymentMethod> {
    // Check if payment method exists for this tenant
    const existing = await this.findOne(id, tenantId);

    // If changing name, check if new name already exists
    if (updatePaymentMethodDto.name && updatePaymentMethodDto.name !== existing.name) {
      const nameExists = await this.prisma.paymentMethod.findFirst({
        where: {
          name: updatePaymentMethodDto.name,
          tenantId,
          NOT: { id },
        },
      });

      if (nameExists) {
        throw new ConflictException('Método de pagamento com este nome já existe');
      }
    }

    return this.prisma.paymentMethod.update({
      where: { id },
      data: updatePaymentMethodDto,
    });
  }

  async remove(id: string, tenantId: string): Promise<PaymentMethod> {
    // Check if payment method exists for this tenant
    await this.findOne(id, tenantId);

    // Check if payment method is being used in any payments
    const paymentsCount = await this.prisma.payment.count({
      where: { paymentMethodId: id },
    });

    if (paymentsCount > 0) {
      throw new ConflictException(
        'Não é possível excluir este método de pagamento pois existem pagamentos vinculados',
      );
    }

    return this.prisma.paymentMethod.delete({
      where: { id },
    });
  }

  async seedDefaultPaymentMethods(tenantId: string): Promise<void> {
    const defaultMethods = [
      { name: 'PIX', isActive: true },
      { name: 'Boleto', isActive: true },
      { name: 'Cartão de Crédito', isActive: true },
      { name: 'Cartão de Débito', isActive: true },
      { name: 'Dinheiro', isActive: true },
    ];

    for (const method of defaultMethods) {
      // Check if already exists
      const existing = await this.prisma.paymentMethod.findUnique({
        where: {
          name_tenantId: {
            name: method.name,
            tenantId,
          },
        },
      });

      if (!existing) {
        await this.prisma.paymentMethod.create({
          data: {
            ...method,
            tenantId,
          },
        });
      }
    }
  }
}