import { Injectable, Logger, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { PrismaService } from '../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { DashboardFilterDto, DashboardPeriod } from './dto/dashboard-filter.dto';
import {
  DashboardResponseDto,
  OrderKPI,
  PaymentKPI,
  CommissionKPI,
  DashboardAlert,
  StatusDistribution,
  TopPerformer,
  DashboardSummary,
} from './dto/dashboard-response.dto';
import { Prisma, OrderStatus } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  private readonly CACHE_TTL = 60; // 1 minuto

  constructor(
    private prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async getDashboard(filters: DashboardFilterDto, userId: string): Promise<DashboardResponseDto> {
    this.logger.log(`[getDashboard] Starting dashboard generation for user ${userId}`);
    this.logger.debug(`[getDashboard] Filters: ${JSON.stringify(filters)}`);

    // Gerar cache key baseado nos filtros
    const cacheKey = this.generateCacheKey('dashboard', filters, userId);
    
    // Tentar obter do cache
    const cached = await this.cacheManager.get<DashboardResponseDto>(cacheKey);
    if (cached) {
      this.logger.log(`[getDashboard] Returning cached dashboard for key: ${cacheKey}`);
      return cached;
    }

    // Processar período
    const { startDate, endDate } = this.processPeriod(filters);
    
    // Construir where clause base
    const whereClause = this.buildWhereClause(filters, startDate, endDate);
    
    this.logger.debug(`[getDashboard] Where clause: ${JSON.stringify(whereClause)}`);

    // Executar queries em paralelo para melhor performance
    const [summary, commissions, alerts] = await Promise.all([
      this.getSummary(whereClause, startDate, endDate, filters),
      filters.includeCommissions ? this.getCommissions(whereClause) : null,
      filters.includeAlerts ? this.getAlerts(whereClause) : null,
    ]);

    const response: DashboardResponseDto = {
      summary,
      commissions: commissions || undefined,
      alerts: alerts || undefined,
      timestamp: new Date(),
      cacheTime: this.CACHE_TTL,
    };

    // Salvar no cache
    await this.cacheManager.set(cacheKey, response, this.CACHE_TTL * 1000);
    
    this.logger.log(`[getDashboard] Dashboard generated successfully for user ${userId}`);
    return response;
  }

  private async getSummary(
    whereClause: Prisma.OrderWhereInput,
    startDate: Date,
    endDate: Date,
    filters: DashboardFilterDto,
  ): Promise<DashboardSummary> {
    this.logger.debug('[getSummary] Generating summary data');

    // Queries paralelas para KPIs
    const [
      orderStats,
      paymentStats,
      statusDistribution,
      topSellers,
      topCollectors,
    ] = await Promise.all([
      this.getOrderKPIs(whereClause),
      this.getPaymentKPIs(whereClause),
      this.getStatusDistribution(whereClause),
      !filters.sellerId ? this.getTopSellers(whereClause) : null,
      !filters.collectorId ? this.getTopCollectors(whereClause) : null,
    ]);

    return {
      periodStart: startDate,
      periodEnd: endDate,
      orders: orderStats,
      payments: paymentStats,
      statusDistribution,
      topSellers: topSellers || undefined,
      topCollectors: topCollectors || undefined,
    };
  }

  private async getOrderKPIs(whereClause: Prisma.OrderWhereInput): Promise<OrderKPI> {
    this.logger.debug('[getOrderKPIs] Calculating order KPIs');

    // Aggregação de pedidos por status
    const ordersByStatus = await this.prisma.order.groupBy({
      by: ['status'],
      where: whereClause,
      _count: true,
      _sum: {
        total: true,
      },
    });

    // Calcular totais
    let total = 0;
    let totalValue = new Decimal(0);
    const statusCounts = {
      delivered: 0,
      pending: 0,
      inTransit: 0,
      cancelled: 0,
      failed: 0,
    };

    ordersByStatus.forEach((group) => {
      const count = typeof group._count === 'number' ? group._count : (group._count as any)?._all || 0;
      
      // Only count non-cancelled orders in total sales
      if (group.status !== OrderStatus.Cancelado) {
        total += count;
        totalValue = totalValue.add(group._sum?.total || 0);
      }

      switch (group.status) {
        case OrderStatus.Completo:
          statusCounts.delivered = count;
          break;
        case OrderStatus.Analise:
        case OrderStatus.Separacao:
        case OrderStatus.PagamentoPendente:
          statusCounts.pending += count;
          break;
        case OrderStatus.Transito:
        case OrderStatus.ConfirmarEntrega:
          statusCounts.inTransit = count;
          break;
        case OrderStatus.Cancelado:
          statusCounts.cancelled = count;
          break;
        case OrderStatus.Frustrado:
        case OrderStatus.EntregaFalha:
        case OrderStatus.RetirarCorreios:
        case OrderStatus.DevolvidoCorreios:
          statusCounts.failed = count;
          break;
      }
    });

    const averageValue = total > 0 ? totalValue.div(total) : new Decimal(0);

    return {
      total,
      delivered: statusCounts.delivered,
      pending: statusCounts.pending,
      inTransit: statusCounts.inTransit,
      cancelled: statusCounts.cancelled,
      failed: statusCounts.failed,
      totalValue,
      averageValue,
    };
  }

  private async getPaymentKPIs(whereClause: Prisma.OrderWhereInput): Promise<PaymentKPI> {
    this.logger.debug('[getPaymentKPIs] Calculating payment KPIs');

    // Pedidos com pagamento recebido
    const paidOrders = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        status: OrderStatus.Completo,
      },
      _sum: {
        paymentReceivedAmount: true,
      },
      _count: true,
    });

    // Pedidos pendentes
    const pendingOrders = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        status: {
          in: [OrderStatus.PagamentoPendente, OrderStatus.Negociacao, OrderStatus.Parcial],
        },
      },
      _sum: {
        total: true,
        paymentReceivedAmount: true,
      },
      _count: true,
    });

    // Pagamentos de hoje
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    
    const todayPayments = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        paymentReceivedDate: {
          gte: todayStart,
        },
      },
      _sum: {
        paymentReceivedAmount: true,
      },
    });

    // Pagamentos parciais
    const partialPayments = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        status: OrderStatus.Parcial,
      },
      _sum: {
        paymentReceivedAmount: true,
      },
    });

    // Em negociação (assumindo que seja um status específico ou flag)
    const inNegotiation = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        // Adicionar condição específica para pedidos em negociação
        // Por exemplo: inNegotiation: true ou status específico
      },
      _sum: {
        total: true,
      },
    });

    const totalReceived = new Decimal(paidOrders._sum.paymentReceivedAmount || 0);
    const receivedToday = new Decimal(todayPayments._sum.paymentReceivedAmount || 0);
    const totalPending = new Decimal(pendingOrders._sum.total || 0)
      .sub(new Decimal(pendingOrders._sum.paymentReceivedAmount || 0));

    return {
      totalReceived,
      receivedToday,
      totalPending,
      inNegotiation: new Decimal(inNegotiation._sum.total || 0),
      partialPayments: new Decimal(partialPayments._sum.paymentReceivedAmount || 0),
      paidOrders: paidOrders._count,
      pendingOrders: pendingOrders._count,
    };
  }

  private async getCommissions(whereClause: Prisma.OrderWhereInput): Promise<CommissionKPI> {
    this.logger.debug('[getCommissions] Calculating commission KPIs');

    // Get date range from whereClause
    const dateFilter = whereClause.paymentReceivedDate as any;
    
    // Comissões pagas no período (excluding denied payments)
    const paidCommissions = await this.prisma.commissionPayment.aggregate({
      where: {
        paymentDate: dateFilter,
        order: {
          OR: [
            { paymentConfirmationStatus: null },
            { paymentConfirmationStatus: { not: 'DENIED' } }
          ]
        }
      },
      _sum: {
        commissionAmount: true,
      },
    });

    // Vendedores e cobradores com comissão no período (excluding denied payments)
    const [sellersWithCommission, collectorsWithCommission] = await Promise.all([
      this.prisma.commissionPayment.findMany({
        where: {
          userRole: 'VENDEDOR',
          paymentDate: dateFilter,
          order: {
            OR: [
              { paymentConfirmationStatus: null },
              { paymentConfirmationStatus: { not: 'DENIED' } }
            ]
          }
        },
        select: {
          userId: true,
        },
        distinct: ['userId'],
      }),
      this.prisma.commissionPayment.findMany({
        where: {
          userRole: 'COBRADOR',
          paymentDate: dateFilter,
          order: {
            OR: [
              { paymentConfirmationStatus: null },
              { paymentConfirmationStatus: { not: 'DENIED' } }
            ]
          }
        },
        select: {
          userId: true,
        },
        distinct: ['userId'],
      }),
    ]);

    // Estimar comissões a pagar com base em pedidos completos sem comissão aprovada
    const ordersPendingCommission = await this.prisma.order.aggregate({
      where: {
        ...whereClause,
        status: OrderStatus.Completo,
        commissionApproved: false,
      },
      _sum: {
        total: true,
      },
    });

    // Estimar 15% de comissão total (10% vendedor + 5% cobrador)
    const estimatedCommission = new Decimal(ordersPendingCommission._sum?.total || 0).mul(0.15);

    return {
      totalToPay: estimatedCommission,
      totalPaid: new Decimal(paidCommissions._sum?.commissionAmount || 0),
      pendingApproval: estimatedCommission, // Mesmo valor de totalToPay
      periodCommissions: new Decimal(paidCommissions._sum?.commissionAmount || 0),
      sellersWithCommission: sellersWithCommission.length,
      collectorsWithCommission: collectorsWithCommission.length,
    };
  }

  private async getAlerts(whereClause: Prisma.OrderWhereInput): Promise<DashboardAlert[]> {
    this.logger.debug('[getAlerts] Generating alerts');

    const alerts: DashboardAlert[] = [];

    // Pedidos críticos (pendentes há mais de X dias)
    const criticalDays = 7;
    const criticalDate = new Date();
    criticalDate.setDate(criticalDate.getDate() - criticalDays);

    const criticalOrders = await this.prisma.order.count({
      where: {
        ...whereClause,
        status: OrderStatus.Analise,
        createdAt: {
          lte: criticalDate,
        },
      },
    });

    if (criticalOrders > 0) {
      alerts.push({
        id: `critical-orders-${Date.now()}`,
        type: 'critical',
        title: 'Pedidos Críticos',
        description: `${criticalOrders} pedidos estão pendentes há mais de ${criticalDays} dias`,
        createdAt: new Date(),
        action: 'Revisar pedidos pendentes',
      });
    }

    // Pagamentos parciais
    const partialPayments = await this.prisma.order.count({
      where: {
        ...whereClause,
        status: OrderStatus.Parcial,
      },
    });

    if (partialPayments > 0) {
      alerts.push({
        id: `partial-payments-${Date.now()}`,
        type: 'warning',
        title: 'Pagamentos Parciais',
        description: `${partialPayments} pedidos com pagamento parcial aguardando quitação`,
        createdAt: new Date(),
        action: 'Verificar pagamentos pendentes',
      });
    }

    // Pedidos aguardando aprovação
    const pendingApproval = await this.prisma.order.count({
      where: {
        ...whereClause,
        status: OrderStatus.Analise,
        commissionApproved: false,
      },
    });

    if (pendingApproval > 0) {
      alerts.push({
        id: `pending-approval-${Date.now()}`,
        type: 'info',
        title: 'Aguardando Aprovação',
        description: `${pendingApproval} pedidos aguardando aprovação`,
        createdAt: new Date(),
        action: 'Aprovar pedidos pendentes',
      });
    }

    // Pedidos com problema de entrega
    const deliveryIssues = await this.prisma.order.count({
      where: {
        ...whereClause,
        status: {
          in: [OrderStatus.Frustrado, OrderStatus.EntregaFalha],
        },
      },
    });

    if (deliveryIssues > 0) {
      alerts.push({
        id: `delivery-issues-${Date.now()}`,
        type: 'warning',
        title: 'Problemas de Entrega',
        description: `${deliveryIssues} pedidos com falha na entrega`,
        createdAt: new Date(),
        action: 'Verificar endereços e reenviar',
      });
    }

    return alerts;
  }

  private async getStatusDistribution(
    whereClause: Prisma.OrderWhereInput,
  ): Promise<StatusDistribution[]> {
    this.logger.debug('[getStatusDistribution] Calculating status distribution');

    const distribution = await this.prisma.order.groupBy({
      by: ['status'],
      where: whereClause,
      _count: true,
      _sum: {
        total: true,
      },
    });

    const total = distribution.reduce((sum, item) => sum + item._count, 0);

    return distribution.map((item) => ({
      status: item.status,
      count: item._count,
      percentage: total > 0 ? (item._count / total) * 100 : 0,
      totalValue: new Decimal(item._sum.total || 0),
    }));
  }

  private async getTopSellers(
    whereClause: Prisma.OrderWhereInput,
  ): Promise<TopPerformer[]> {
    this.logger.debug('[getTopSellers] Getting top sellers');

    const topSellers = await this.prisma.order.groupBy({
      by: ['sellerId'],
      where: {
        ...whereClause,
        sellerId: { not: undefined },
      },
      _count: true,
      _sum: {
        total: true,
      },
      orderBy: {
        _sum: {
          total: 'desc',
        },
      },
      take: 5,
    });

    // Buscar nomes dos vendedores
    const sellerIds = topSellers.map((s) => s.sellerId).filter(Boolean) as string[];
    const sellers = await this.prisma.user.findMany({
      where: { id: { in: sellerIds } },
      select: { id: true, name: true },
    });

    const sellerMap = new Map(sellers.map((s) => [s.id, s.name]));

    return topSellers.map((seller) => ({
      id: seller.sellerId!,
      name: sellerMap.get(seller.sellerId!) || 'Unknown',
      totalValue: new Decimal(seller._sum?.total || 0),
      orderCount: typeof seller._count === 'number' ? seller._count : (seller._count as any)?._all || 0,
      conversionRate: 0, // Calcular baseado em leads se disponível
    }));
  }

  private async getTopCollectors(
    whereClause: Prisma.OrderWhereInput,
  ): Promise<TopPerformer[]> {
    this.logger.debug('[getTopCollectors] Getting top collectors');

    const topCollectors = await this.prisma.order.groupBy({
      by: ['collectorId'],
      where: {
        ...whereClause,
        collectorId: { not: undefined },
        status: OrderStatus.Completo,
      },
      _count: true,
      _sum: {
        paymentReceivedAmount: true,
      },
      orderBy: {
        _sum: {
          paymentReceivedAmount: 'desc',
        },
      },
      take: 5,
    });

    // Buscar nomes dos cobradores
    const collectorIds = topCollectors.map((c) => c.collectorId).filter(Boolean) as string[];
    const collectors = await this.prisma.user.findMany({
      where: { id: { in: collectorIds } },
      select: { id: true, name: true },
    });

    const collectorMap = new Map(collectors.map((c) => [c.id, c.name]));

    return topCollectors.map((collector) => ({
      id: collector.collectorId!,
      name: collectorMap.get(collector.collectorId!) || 'Unknown',
      totalValue: new Decimal(collector._sum?.paymentReceivedAmount || 0),
      orderCount: typeof collector._count === 'number' ? collector._count : (collector._count as any)?._all || 0,
      conversionRate: 0, // Calcular taxa de sucesso de cobrança se disponível
    }));
  }

  private buildWhereClause(
    filters: DashboardFilterDto,
    startDate: Date,
    endDate: Date,
  ): Prisma.OrderWhereInput {
    const where: Prisma.OrderWhereInput = {};

    // Filtro de data - incluir pedidos por data de criação OU data de pagamento
    where.OR = [
      {
        paymentReceivedDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      {
        paymentReceivedDate: null,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    ];

    // Filtros de escopo (já processados pelo interceptor)
    if (filters.sellerId) {
      where.sellerId = filters.sellerId;
    }
    if (filters.collectorId) {
      where.collectorId = filters.collectorId;
    }

    return where;
  }

  private processPeriod(filters: DashboardFilterDto): { startDate: Date; endDate: Date } {
    let startDate: Date;
    let endDate: Date = new Date();
    endDate.setHours(23, 59, 59, 999);

    if (filters.startDate && filters.endDate) {
      startDate = new Date(filters.startDate);
      endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);
    } else {
      switch (filters.period) {
        case DashboardPeriod.TODAY:
          startDate = new Date();
          startDate.setHours(0, 0, 0, 0);
          break;
        case DashboardPeriod.WEEK:
          startDate = new Date();
          startDate.setDate(startDate.getDate() - 7);
          startDate.setHours(0, 0, 0, 0);
          break;
        case DashboardPeriod.MONTH:
        default:
          startDate = new Date();
          startDate.setDate(1);
          startDate.setHours(0, 0, 0, 0);
          break;
      }
    }

    return { startDate, endDate };
  }

  private generateCacheKey(prefix: string, filters: DashboardFilterDto, userId: string): string {
    const filterStr = JSON.stringify({
      ...filters,
      userId,
    });
    return `${prefix}:${Buffer.from(filterStr).toString('base64')}`;
  }
}