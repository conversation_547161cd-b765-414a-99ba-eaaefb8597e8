import { Modu<PERSON> } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DashboardScopeInterceptor } from './interceptors/dashboard-scope.interceptor';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    CacheModule.register({
      ttl: 60, // 60 segundos (1 minuto)
      max: 100, // máximo de 100 itens no cache
    }),
  ],
  controllers: [DashboardController],
  providers: [DashboardService, DashboardScopeInterceptor],
  exports: [DashboardService],
})
export class DashboardModule {}