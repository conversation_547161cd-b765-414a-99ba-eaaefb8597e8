import { IsOptional, IsDateString, IsString, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum DashboardPeriod {
  TODAY = 'today',
  WEEK = 'week',
  MONTH = 'month',
  CUSTOM = 'custom',
}

export class DashboardFilterDto {
  @ApiPropertyOptional({
    description: 'Data inicial do período (formato: YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data final do período (formato: YYYY-MM-DD)',
    example: '2024-01-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Período pré-definido',
    enum: DashboardPeriod,
    example: DashboardPeriod.MONTH,
  })
  @IsOptional()
  @IsEnum(DashboardPeriod)
  period?: DashboardPeriod;

  @ApiPropertyOptional({
    description: 'ID do vendedor (aplicado automaticamente pelo interceptor)',
  })
  @IsOptional()
  @IsString()
  sellerId?: string;

  @ApiPropertyOptional({
    description: 'ID do cobrador (aplicado automaticamente pelo interceptor)',
  })
  @IsOptional()
  @IsString()
  collectorId?: string;

  @ApiPropertyOptional({
    description: 'Incluir alertas no dashboard',
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeAlerts?: boolean = true;

  @ApiPropertyOptional({
    description: 'Incluir comissões no dashboard',
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeCommissions?: boolean = true;
}