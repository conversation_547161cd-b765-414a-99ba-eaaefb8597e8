import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';
import { OrderStatus } from '@prisma/client';

export class OrderKPI {
  @ApiProperty({ description: 'Total de pedidos' })
  total: number;

  @ApiProperty({ description: 'Pedidos entregues' })
  delivered: number;

  @ApiProperty({ description: 'Pedidos pendentes' })
  pending: number;

  @ApiProperty({ description: 'Pedidos em trânsito' })
  inTransit: number;

  @ApiProperty({ description: 'Pedidos cancelados' })
  cancelled: number;

  @ApiProperty({ description: 'Pedidos com falha' })
  failed: number;

  @ApiProperty({ description: 'Valor total dos pedidos' })
  totalValue: Decimal;

  @ApiProperty({ description: 'Ticket médio' })
  averageValue: Decimal;
}

export class PaymentKPI {
  @ApiProperty({ description: 'Total recebido' })
  totalReceived: Decimal;

  @ApiProperty({ description: 'Recebido hoje' })
  receivedToday: Decimal;

  @ApiProperty({ description: 'Total pendente' })
  totalPending: Decimal;

  @ApiProperty({ description: 'Em negociação' })
  inNegotiation: Decimal;

  @ApiProperty({ description: 'Pagamentos parciais' })
  partialPayments: Decimal;

  @ApiProperty({ description: 'Total de pedidos pagos' })
  paidOrders: number;

  @ApiProperty({ description: 'Total de pedidos pendentes' })
  pendingOrders: number;
}

export class CommissionKPI {
  @ApiProperty({ description: 'Total de comissões a pagar' })
  totalToPay: Decimal;

  @ApiProperty({ description: 'Total de comissões pagas' })
  totalPaid: Decimal;

  @ApiProperty({ description: 'Comissões pendentes de aprovação' })
  pendingApproval: Decimal;

  @ApiProperty({ description: 'Comissões do período' })
  periodCommissions: Decimal;

  @ApiProperty({ description: 'Número de vendedores com comissão' })
  sellersWithCommission: number;

  @ApiProperty({ description: 'Número de cobradores com comissão' })
  collectorsWithCommission: number;
}

export class DashboardAlert {
  @ApiProperty({ description: 'ID do alerta' })
  id: string;

  @ApiProperty({ description: 'Tipo do alerta' })
  type: 'critical' | 'warning' | 'info';

  @ApiProperty({ description: 'Título do alerta' })
  title: string;

  @ApiProperty({ description: 'Descrição do alerta' })
  description: string;

  @ApiProperty({ description: 'ID do pedido relacionado', required: false })
  orderId?: string;

  @ApiProperty({ description: 'Data de criação do alerta' })
  createdAt: Date;

  @ApiProperty({ description: 'Ação sugerida', required: false })
  action?: string;
}

export class StatusDistribution {
  @ApiProperty({ description: 'Status do pedido', enum: OrderStatus })
  status: OrderStatus;

  @ApiProperty({ description: 'Quantidade de pedidos' })
  count: number;

  @ApiProperty({ description: 'Percentual do total' })
  percentage: number;

  @ApiProperty({ description: 'Valor total' })
  totalValue: Decimal;
}

export class TopPerformer {
  @ApiProperty({ description: 'ID do usuário' })
  id: string;

  @ApiProperty({ description: 'Nome do usuário' })
  name: string;

  @ApiProperty({ description: 'Total de vendas/cobranças' })
  totalValue: Decimal;

  @ApiProperty({ description: 'Número de pedidos' })
  orderCount: number;

  @ApiProperty({ description: 'Taxa de conversão' })
  conversionRate: number;
}

export class DashboardSummary {
  @ApiProperty({ description: 'Data de início do período' })
  periodStart: Date;

  @ApiProperty({ description: 'Data de fim do período' })
  periodEnd: Date;

  @ApiProperty({ description: 'KPIs de pedidos' })
  orders: OrderKPI;

  @ApiProperty({ description: 'KPIs de pagamentos' })
  payments: PaymentKPI;

  @ApiProperty({ description: 'Distribuição por status' })
  statusDistribution: StatusDistribution[];

  @ApiProperty({ description: 'Top vendedores', required: false })
  topSellers?: TopPerformer[];

  @ApiProperty({ description: 'Top cobradores', required: false })
  topCollectors?: TopPerformer[];
}

export class DashboardResponseDto {
  @ApiProperty({ description: 'Resumo geral do dashboard' })
  summary: DashboardSummary;

  @ApiProperty({ description: 'KPIs de comissões', required: false })
  commissions?: CommissionKPI;

  @ApiProperty({ description: 'Alertas ativos', required: false })
  alerts?: DashboardAlert[];

  @ApiProperty({ description: 'Timestamp da resposta' })
  timestamp: Date;

  @ApiProperty({ description: 'Tempo de cache em segundos' })
  cacheTime: number;
}