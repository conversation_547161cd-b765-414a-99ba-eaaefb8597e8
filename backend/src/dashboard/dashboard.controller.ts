import {
  Controller,
  Get,
  Query,
  UseGuards,
  UseInterceptors,
  Request,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';
import { DashboardService } from './dashboard.service';
import { DashboardFilterDto, DashboardPeriod } from './dto/dashboard-filter.dto';
import { DashboardResponseDto } from './dto/dashboard-response.dto';
import { DashboardScopeInterceptor } from './interceptors/dashboard-scope.interceptor';

@ApiTags('Dashboard')
@Controller('dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(DashboardScopeInterceptor)
@ApiBearerAuth()
export class DashboardController {
  private readonly logger = new Logger(DashboardController.name);

  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR, Role.COBRADOR)
  @ApiOperation({
    summary: 'Obter dados do dashboard',
    description: 'Retorna KPIs, alertas e resumo de acordo com o perfil do usuário',
  })
  @ApiResponse({
    status: 200,
    description: 'Dashboard retornado com sucesso',
    type: DashboardResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado',
  })
  @ApiQuery({
    name: 'period',
    enum: DashboardPeriod,
    required: false,
    description: 'Período pré-definido para o dashboard',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Data inicial (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'Data final (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'includeAlerts',
    required: false,
    type: Boolean,
    description: 'Incluir alertas no dashboard',
  })
  @ApiQuery({
    name: 'includeCommissions',
    required: false,
    type: Boolean,
    description: 'Incluir dados de comissões',
  })
  async getDashboard(
    @Query() filters: DashboardFilterDto,
    @Request() req: any,
  ): Promise<DashboardResponseDto> {
    try {
      const userId = req.user.id || req.user.userId || req.user.sub;
      
      this.logger.log(`[getDashboard] Request from user ${userId} with role ${req.user.role}`);
      this.logger.debug(`[getDashboard] Filters: ${JSON.stringify(filters)}`);

      const dashboard = await this.dashboardService.getDashboard(filters, userId);
      
      this.logger.log(`[getDashboard] Dashboard returned successfully for user ${userId}`);
      return dashboard;
    } catch (error) {
      this.logger.error(`[getDashboard] Error: ${error.message}`, error.stack);
      throw new HttpException(
        'Erro ao buscar dados do dashboard',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('kpis')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR, Role.COBRADOR)
  @ApiOperation({
    summary: 'Obter apenas KPIs do dashboard',
    description: 'Retorna apenas os indicadores principais sem alertas e comissões',
  })
  @ApiResponse({
    status: 200,
    description: 'KPIs retornados com sucesso',
  })
  async getKPIs(
    @Query() filters: DashboardFilterDto,
    @Request() req: any,
  ): Promise<any> {
    try {
      const userId = req.user.id || req.user.userId || req.user.sub;
      
      // Forçar a não incluir alertas e comissões para otimizar
      filters.includeAlerts = false;
      filters.includeCommissions = false;
      
      this.logger.log(`[getKPIs] Request from user ${userId}`);
      
      const dashboard = await this.dashboardService.getDashboard(filters, userId);
      
      // Retornar apenas o resumo
      return {
        summary: dashboard.summary,
        timestamp: dashboard.timestamp,
        cacheTime: dashboard.cacheTime,
      };
    } catch (error) {
      this.logger.error(`[getKPIs] Error: ${error.message}`, error.stack);
      throw new HttpException(
        'Erro ao buscar KPIs',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('alerts')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR, Role.COBRADOR)
  @ApiOperation({
    summary: 'Obter alertas ativos',
    description: 'Retorna apenas os alertas ativos do dashboard',
  })
  @ApiResponse({
    status: 200,
    description: 'Alertas retornados com sucesso',
  })
  async getAlerts(
    @Query() filters: DashboardFilterDto,
    @Request() req: any,
  ): Promise<any> {
    try {
      const userId = req.user.id || req.user.userId || req.user.sub;
      
      // Forçar a incluir apenas alertas
      filters.includeAlerts = true;
      filters.includeCommissions = false;
      
      this.logger.log(`[getAlerts] Request from user ${userId}`);
      
      const dashboard = await this.dashboardService.getDashboard(filters, userId);
      
      return {
        alerts: dashboard.alerts || [],
        timestamp: dashboard.timestamp,
      };
    } catch (error) {
      this.logger.error(`[getAlerts] Error: ${error.message}`, error.stack);
      throw new HttpException(
        'Erro ao buscar alertas',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('commissions')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({
    summary: 'Obter dados de comissões',
    description: 'Retorna dados de comissões (apenas admin e supervisor)',
  })
  @ApiResponse({
    status: 200,
    description: 'Comissões retornadas com sucesso',
  })
  @ApiResponse({
    status: 403,
    description: 'Acesso negado',
  })
  async getCommissions(
    @Query() filters: DashboardFilterDto,
    @Request() req: any,
  ): Promise<any> {
    try {
      const userId = req.user.id || req.user.userId || req.user.sub;
      
      // Forçar a incluir apenas comissões
      filters.includeAlerts = false;
      filters.includeCommissions = true;
      
      this.logger.log(`[getCommissions] Request from user ${userId}`);
      
      const dashboard = await this.dashboardService.getDashboard(filters, userId);
      
      return {
        commissions: dashboard.commissions,
        timestamp: dashboard.timestamp,
      };
    } catch (error) {
      this.logger.error(`[getCommissions] Error: ${error.message}`, error.stack);
      throw new HttpException(
        'Erro ao buscar comissões',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}