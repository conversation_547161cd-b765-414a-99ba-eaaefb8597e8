import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Zap, ZapStatus } from '@prisma/client';

@Injectable()
export class ZapsService {
  constructor(private prisma: PrismaService) {}

  /**
   * Extract Zap identifier from email
   * Example: <EMAIL> -> zap10
   */
  extractZapFromEmail(email: string): string | null {
    if (!email) return null;
    
    // Match pattern: @(something).com
    const match = email.match(/@([^.]+)\.com/i);
    if (!match) return null;
    
    // Normalize to lowercase for case-insensitive matching
    return match[1].toLowerCase();
  }

  /**
   * Find or create a Zap based on the extracted identifier
   */
  async findOrCreateZap(identifier: string, tenantId: string): Promise<Zap> {
    if (!identifier) {
      throw new Error('Zap identifier is required');
    }

    // Normalize identifier (case-insensitive)
    const normalizedName = identifier.toLowerCase();

    // Try to find existing Zap
    let zap = await this.prisma.zap.findFirst({
      where: {
        name: {
          equals: normalizedName,
          mode: 'insensitive',
        },
        tenantId,
      },
    });

    // If not found, create it
    if (!zap) {
      console.log(`[ZapsService] Creating new Zap: ${normalizedName} for tenant: ${tenantId}`);
      zap = await this.prisma.zap.create({
        data: {
          name: normalizedName,
          tenantId,
          status: ZapStatus.Ativo,
        },
      });
      console.log(`[ZapsService] Created new Zap with ID: ${zap.id}`);
    } else {
      console.log(`[ZapsService] Found existing Zap: ${zap.name} (${zap.id}) for tenant: ${tenantId}`);
    }

    return zap;
  }

  /**
   * Get all Zaps for a tenant
   */
  async findAll(tenantId: string) {
    return this.prisma.zap.findMany({
      where: { tenantId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Get a single Zap
   */
  async findOne(id: string, tenantId: string) {
    const zap = await this.prisma.zap.findFirst({
      where: { id, tenantId },
      include: {
        _count: {
          select: { orders: true },
        },
      },
    });

    if (!zap) {
      throw new NotFoundException('Zap not found');
    }

    return zap;
  }

  /**
   * Create a new Zap
   */
  async create(data: { name: string; phoneNumber?: string; status?: ZapStatus }, tenantId: string) {
    // Normalize name
    const normalizedName = data.name.toLowerCase();

    // Check if already exists
    const existing = await this.prisma.zap.findFirst({
      where: {
        name: {
          equals: normalizedName,
          mode: 'insensitive',
        },
        tenantId,
      },
    });

    if (existing) {
      throw new ConflictException('Zap with this name already exists');
    }

    return this.prisma.zap.create({
      data: {
        name: normalizedName,
        phoneNumber: data.phoneNumber || null,
        status: data.status || ZapStatus.Ativo,
        tenantId,
      },
    });
  }

  /**
   * Update a Zap
   */
  async update(id: string, data: { name?: string; phoneNumber?: string; status?: ZapStatus }, tenantId: string) {
    const zap = await this.findOne(id, tenantId);

    // If updating name, check for duplicates
    if (data.name && data.name.toLowerCase() !== zap.name) {
      const existing = await this.prisma.zap.findFirst({
        where: {
          name: {
            equals: data.name.toLowerCase(),
            mode: 'insensitive',
          },
          tenantId,
          NOT: { id },
        },
      });

      if (existing) {
        throw new ConflictException('Zap with this name already exists');
      }
    }

    return this.prisma.zap.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name.toLowerCase() }),
        ...(data.phoneNumber !== undefined && { phoneNumber: data.phoneNumber }),
        ...(data.status && { status: data.status }),
      },
    });
  }

  /**
   * Delete a Zap
   */
  async remove(id: string, tenantId: string) {
    await this.findOne(id, tenantId);
    
    // Check if there are orders associated
    const orderCount = await this.prisma.order.count({
      where: { zapSourceId: id },
    });

    if (orderCount > 0) {
      throw new ConflictException(`Cannot delete Zap with ${orderCount} associated orders`);
    }

    return this.prisma.zap.delete({
      where: { id },
    });
  }

  /**
   * Get Zap statistics
   */
  async getStats(zapId: string, tenantId: string, startDate?: Date, endDate?: Date) {
    await this.findOne(zapId, tenantId);

    const where: any = {
      zapSourceId: zapId,
      tenantId,
    };

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [totalOrders, totalRevenue, ordersByStatus] = await Promise.all([
      this.prisma.order.count({ where }),
      this.prisma.order.aggregate({
        where,
        _sum: { total: true },
      }),
      this.prisma.order.groupBy({
        by: ['status'],
        where,
        _count: true,
      }),
    ]);

    return {
      totalOrders,
      totalRevenue: totalRevenue._sum.total || 0,
      ordersByStatus: ordersByStatus.map(item => ({
        status: item.status,
        count: item._count,
      })),
    };
  }

  /**
   * Update all orders with Zap information retroactively
   */
  async updateOrdersRetroactively(tenantId: string) {
    // Get all orders with zapId (old WhatsApp field)
    const orders = await this.prisma.order.findMany({
      where: {
        tenantId,
        zapId: { not: null },
        zapSourceId: null,
      },
      select: {
        id: true,
        zapId: true,
      },
    });

    let updated = 0;
    let failed = 0;

    for (const order of orders) {
      try {
        // Extract Zap from the zapId field (assuming it contains email format)
        const zapIdentifier = this.extractZapFromEmail(order.zapId!);
        
        if (zapIdentifier) {
          // Find or create the Zap
          const zap = await this.findOrCreateZap(zapIdentifier, tenantId);
          
          // Update the order
          await this.prisma.order.update({
            where: { id: order.id },
            data: { zapSourceId: zap.id },
          });
          
          updated++;
        }
      } catch (error) {
        console.error(`Failed to update order ${order.id}:`, error);
        failed++;
      }
    }

    return {
      processed: orders.length,
      updated,
      failed,
    };
  }
}