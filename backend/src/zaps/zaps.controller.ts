import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Request,
  Query,
  UsePipes,
  ValidationPipe,
  UseGuards,
} from '@nestjs/common';
import { ZapsService } from './zaps.service';
import { Roles } from '../auth/decorators/roles.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { ZapStatus } from '@prisma/client';

@Controller('zaps')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ZapsController {
  constructor(private readonly zapsService: ZapsService) {}

  @Get()
  @Roles('ADMIN', 'SUPERVISOR')
  async findAll(@Request() req) {
    return this.zapsService.findAll(req.user.tenantId);
  }

  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async findOne(@Param('id') id: string, @Request() req) {
    return this.zapsService.findOne(id, req.user.tenantId);
  }

  @Get(':id/stats')
  @Roles('ADMIN', 'SUPERVISOR')
  async getStats(
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Request() req?,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.zapsService.getStats(id, req.user.tenantId, start, end);
  }

  @Post()
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(
    @Body() createZapDto: { name: string; phoneNumber?: string; status?: ZapStatus },
    @Request() req,
  ) {
    return this.zapsService.create(createZapDto, req.user.tenantId);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateZapDto: { name?: string; phoneNumber?: string; status?: ZapStatus },
    @Request() req,
  ) {
    return this.zapsService.update(id, updateZapDto, req.user.tenantId);
  }

  @Delete(':id')
  @Roles('ADMIN')
  async remove(@Param('id') id: string, @Request() req) {
    return this.zapsService.remove(id, req.user.tenantId);
  }

  @Post('update-retroactively')
  @Roles('ADMIN')
  async updateRetroactively(@Request() req) {
    return this.zapsService.updateOrdersRetroactively(req.user.tenantId);
  }
}