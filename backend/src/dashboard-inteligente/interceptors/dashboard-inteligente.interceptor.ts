import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Role } from '@prisma/client';

@Injectable()
export class DashboardInteligenteInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return next.handle();
    }

    const userId = user.id || user.userId || user.sub;

    // Aplicar filtros baseados no role
    switch (user.role) {
      case Role.VENDEDOR:
        request.dashboardFilters = {
          sellerId: userId,
        };
        break;

      case Role.COBRADOR:
        request.dashboardFilters = {
          collectorId: userId,
        };
        break;

      case Role.ADMIN:
      case Role.SUPERVISOR:
        // Não aplica filtros - pode ver tudo
        request.dashboardFilters = {};
        break;

      default:
        // Para qualquer outro role, bloqueia acesso
        request.dashboardFilters = {
          id: 'no-access',
        };
    }

    return next.handle();
  }
}