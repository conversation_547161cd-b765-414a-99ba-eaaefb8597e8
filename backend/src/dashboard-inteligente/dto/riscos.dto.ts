import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';

export class RiscosItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerName: string;

  @ApiProperty()
  customerPhone: string;

  @ApiProperty()
  total: Decimal;

  @ApiProperty()
  status: string;

  @ApiProperty()
  statusDescription: string;

  @ApiProperty()
  sellerName: string;

  @ApiProperty({ required: false })
  collectorName?: string;

  @ApiProperty({ required: false })
  lastContactDate?: Date;

  @ApiProperty()
  daysWithoutContact: number;

  @ApiProperty({ required: false })
  trackingCode?: string;
}

export class RiscosResponseDto {
  @ApiProperty({ type: [RiscosItemDto] })
  items: RiscosItemDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  totalValue: Decimal;
}