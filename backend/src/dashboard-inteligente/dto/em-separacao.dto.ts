import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';

export class EmSeparacaoItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerName: string;

  @ApiProperty()
  customerPhone: string;

  @ApiProperty()
  fullAddress: string;

  @ApiProperty()
  total: Decimal;

  @ApiProperty()
  sellerName: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  items: Array<{
    productName: string;
    quantity: number;
  }>;
}

export class EmSeparacaoResponseDto {
  @ApiProperty({ type: [EmSeparacaoItemDto] })
  items: EmSeparacaoItemDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  totalValue: Decimal;
}