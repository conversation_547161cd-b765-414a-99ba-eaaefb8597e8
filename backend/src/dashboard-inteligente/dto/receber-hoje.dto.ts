import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';

export class ReceberHojeItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerName: string;

  @ApiProperty()
  customerPhone: string;

  @ApiProperty()
  total: Decimal;

  @ApiProperty()
  status: string;

  @ApiProperty()
  sellerName: string;

  @ApiProperty({ required: false })
  collectorName?: string;

  @ApiProperty({ required: false })
  lastContactDate?: Date;

  @ApiProperty({ required: false })
  nextPaymentDate?: Date;

  @ApiProperty({ required: false })
  paymentReceivedDate?: Date;

  @ApiProperty({ required: false })
  isReceived?: boolean;
}

export class ReceberHojeResponseDto {
  @ApiProperty({ type: [ReceberHojeItemDto] })
  items: ReceberHojeItemDto[];

  @ApiProperty()
  total: number;

  @ApiProperty({ description: 'Total amount received today' })
  totalValue: Decimal;

  @ApiProperty({ description: 'Total amount scheduled for today', required: false })
  totalScheduled?: Decimal;

  @ApiProperty({ description: 'Number of orders that received payment today', required: false })
  receivedCount?: number;

  @ApiProperty({ description: 'Number of orders scheduled to receive payment today', required: false })
  scheduledCount?: number;
}