import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Decimal } from '@prisma/client/runtime/library';

export class AnaliseItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerName: string;

  @ApiProperty()
  customerPhone: string;

  @ApiProperty()
  total: Decimal;

  @ApiProperty()
  sellerName: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  daysPending: number;

  @ApiProperty()
  items: Array<{
    productName: string;
    quantity: number;
    unitPrice: Decimal;
  }>;
}

export class AnaliseResponseDto {
  @ApiProperty({ type: [AnaliseItemDto] })
  items: AnaliseItemDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  totalValue: Decimal;
}

export class AprovarReprovarDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  observation?: string;
}