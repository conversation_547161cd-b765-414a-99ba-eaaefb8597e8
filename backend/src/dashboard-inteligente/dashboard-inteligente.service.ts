import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderStatus, Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { ReceberHojeResponseDto } from './dto/receber-hoje.dto';
import { RiscosResponseDto } from './dto/riscos.dto';
import { EmSeparacaoResponseDto } from './dto/em-separacao.dto';
import { AnaliseResponseDto } from './dto/analise.dto';

@Injectable()
export class DashboardInteligenteService {
  constructor(private prisma: PrismaService) {}

  async getReceberHoje(filters: any): Promise<ReceberHojeResponseDto> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get orders with payments received today
    const ordersReceivedToday = await this.prisma.order.findMany({
      where: {
        ...filters,
        paymentReceivedDate: {
          gte: today,
          lt: tomorrow,
        },
        status: OrderStatus.Completo,
      },
      include: {
        seller: {
          select: { id: true, name: true },
        },
        collector: {
          select: { id: true, name: true },
        },
      },
      orderBy: {
        paymentReceivedDate: 'desc',
      },
    });

    // Calculate total received today
    const totalReceivedToday = ordersReceivedToday.reduce(
      (sum, order) => sum.add(order.paymentReceivedAmount || order.total),
      new Decimal(0),
    );

    // Also get orders scheduled to receive payment today (for visibility)
    const ordersScheduledToday = await this.prisma.order.findMany({
      where: {
        ...filters,
        nextPaymentDate: {
          gte: today,
          lt: tomorrow,
        },
        status: {
          in: [OrderStatus.Negociacao, OrderStatus.Promessa],
        },
      },
      include: {
        seller: {
          select: { id: true, name: true },
        },
        collector: {
          select: { id: true, name: true },
        },
      },
      orderBy: {
        nextPaymentDate: 'asc',
      },
    });

    const totalScheduled = ordersScheduledToday.reduce(
      (sum, order) => sum.add(order.total),
      new Decimal(0),
    );

    // Combine both lists
    const allOrders = [
      ...ordersReceivedToday.map(order => ({
        ...order,
        isReceived: true,
      })),
      ...ordersScheduledToday.map(order => ({
        ...order,
        isReceived: false,
      })),
    ];

    return {
      items: allOrders.map((order) => ({
        id: order.id,
        customerName: order.customerName,
        customerPhone: order.customerPhone,
        total: order.total,
        status: order.status,
        sellerName: order.seller.name,
        collectorName: order.collector?.name,
        lastContactDate: order.lastContactDate || undefined,
        nextPaymentDate: order.nextPaymentDate || undefined,
        paymentReceivedDate: order.paymentReceivedDate || undefined,
        isReceived: order.isReceived,
      })),
      total: allOrders.length,
      totalValue: totalReceivedToday, // Total actually received today
      totalScheduled, // Total scheduled for today
      receivedCount: ordersReceivedToday.length,
      scheduledCount: ordersScheduledToday.length,
    };
  }

  async getRiscos(filters: any): Promise<RiscosResponseDto> {
    const where: Prisma.OrderWhereInput = {
      ...filters,
      status: {
        in: [
          OrderStatus.ConfirmarEntrega,
          OrderStatus.EntregaFalha,
          OrderStatus.RetirarCorreios,
        ],
      },
    };

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        seller: {
          select: { id: true, name: true },
        },
        collector: {
          select: { id: true, name: true },
        },
        tracking: {
          select: { code: true },
        },
      },
      orderBy: {
        lastContactDate: 'asc', // Mais antigos primeiro
      },
    });

    const totalValue = orders.reduce(
      (sum, order) => sum.add(order.total),
      new Decimal(0),
    );

    const statusDescriptions = {
      [OrderStatus.ConfirmarEntrega]: 'Aguardando confirmação de entrega',
      [OrderStatus.EntregaFalha]: 'Falha na tentativa de entrega',
      [OrderStatus.RetirarCorreios]: 'Aguardando retirada nos Correios',
    };

    return {
      items: orders.map((order) => {
        const daysSinceLastContact = order.lastContactDate
          ? Math.floor(
              (new Date().getTime() - order.lastContactDate.getTime()) /
                (1000 * 60 * 60 * 24),
            )
          : 999; // Se nunca teve contato, coloca valor alto

        return {
          id: order.id,
          customerName: order.customerName,
          customerPhone: order.customerPhone,
          total: order.total,
          status: order.status,
          statusDescription: statusDescriptions[order.status] || order.status,
          sellerName: order.seller.name,
          collectorName: order.collector?.name,
          lastContactDate: order.lastContactDate || undefined,
          daysWithoutContact: daysSinceLastContact,
          trackingCode: order.tracking?.code,
        };
      }),
      total: orders.length,
      totalValue,
    };
  }

  async getEmSeparacao(filters: any): Promise<EmSeparacaoResponseDto> {
    const where: Prisma.OrderWhereInput = {
      ...filters,
      status: OrderStatus.Separacao,
    };

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        seller: {
          select: { id: true, name: true },
        },
        customer: {
          include: {
            addresses: {
              where: { main: true },
              take: 1,
            },
          },
        },
        items: {
          select: {
            productName: true,
            quantity: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    const totalValue = orders.reduce(
      (sum, order) => sum.add(order.total),
      new Decimal(0),
    );

    return {
      items: orders.map((order) => {
        const mainAddress = order.customer?.addresses[0];
        const address = mainAddress
          ? `${mainAddress.street}, ${mainAddress.number}${
              mainAddress.complement ? ` - ${mainAddress.complement}` : ''
            }, ${mainAddress.neighborhood}, ${mainAddress.city}/${
              mainAddress.state
            } - CEP: ${mainAddress.cep}`
          : 'Endereço não cadastrado';

        return {
          id: order.id,
          customerName: order.customerName,
          customerPhone: order.customerPhone,
          fullAddress: address,
          total: order.total,
          sellerName: order.seller.name,
          createdAt: order.createdAt,
          items: order.items,
        };
      }),
      total: orders.length,
      totalValue,
    };
  }

  async exportEmSeparacaoCSV(filters: any): Promise<string> {
    const data = await this.getEmSeparacao(filters);

    // Cabeçalho do CSV
    let csv = 'ID Pedido,Cliente,Telefone,Endereço,Valor Total,Vendedor,Data Criação,Produtos\n';

    // Dados
    data.items.forEach((item) => {
      const produtos = item.items
        .map((p) => `${p.productName} (${p.quantity})`)
        .join('; ');

      csv += `${item.id},"${item.customerName}","${item.customerPhone}","${item.fullAddress}",${item.total},"${item.sellerName}",${item.createdAt.toISOString()},"${produtos}"\n`;
    });

    return csv;
  }

  async getAnalise(filters: any): Promise<AnaliseResponseDto> {
    const where: Prisma.OrderWhereInput = {
      ...filters,
      status: OrderStatus.Analise,
    };

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        seller: {
          select: { id: true, name: true },
        },
        items: {
          select: {
            productName: true,
            quantity: true,
            unitPrice: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc', // Mais antigos primeiro
      },
    });

    const totalValue = orders.reduce(
      (sum, order) => sum.add(order.total),
      new Decimal(0),
    );

    return {
      items: orders.map((order) => {
        const daysPending = Math.floor(
          (new Date().getTime() - order.createdAt.getTime()) /
            (1000 * 60 * 60 * 24),
        );

        return {
          id: order.id,
          customerName: order.customerName,
          customerPhone: order.customerPhone,
          total: order.total,
          sellerName: order.seller.name,
          createdAt: order.createdAt,
          daysPending,
          items: order.items,
        };
      }),
      total: orders.length,
      totalValue,
    };
  }

  async aprovarPedido(
    orderId: string,
    userId: string,
    observation?: string,
  ): Promise<{ message: string; order: any }> {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    if (order.status !== OrderStatus.Analise) {
      throw new BadRequestException('Pedido não está em análise');
    }

    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.Separacao,
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Separacao,
            changedById: userId,
          },
        },
      },
      include: {
        seller: true,
        customer: true,
      },
    });

    return {
      message: 'Pedido aprovado com sucesso',
      order: updatedOrder,
    };
  }

  async reprovarPedido(
    orderId: string,
    userId: string,
    observation?: string,
  ): Promise<{ message: string; order: any }> {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    if (order.status !== OrderStatus.Analise) {
      throw new BadRequestException('Pedido não está em análise');
    }

    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: OrderStatus.Cancelado,
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Cancelado,
            changedById: userId,
          },
        },
      },
      include: {
        seller: true,
        customer: true,
      },
    });

    return {
      message: 'Pedido reprovado com sucesso',
      order: updatedOrder,
    };
  }
}