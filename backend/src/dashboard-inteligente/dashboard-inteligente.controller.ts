import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
  Request,
  Response,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';
import { DashboardInteligenteService } from './dashboard-inteligente.service';
import { DashboardInteligenteInterceptor } from './interceptors/dashboard-inteligente.interceptor';
import { ReceberHojeResponseDto } from './dto/receber-hoje.dto';
import { RiscosResponseDto } from './dto/riscos.dto';
import { EmSeparacaoResponseDto } from './dto/em-separacao.dto';
import { AnaliseResponseDto, AprovarReprovarDto } from './dto/analise.dto';
import { Response as ExpressResponse } from 'express';

@ApiTags('Dashboard Inteligente')
@Controller('dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(DashboardInteligenteInterceptor)
@ApiBearerAuth()
export class DashboardInteligenteController {
  constructor(private readonly dashboardService: DashboardInteligenteService) {}

  @Get('receber-hoje')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.COBRADOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Listar pedidos para receber hoje' })
  @ApiResponse({ status: 200, type: ReceberHojeResponseDto })
  async getReceberHoje(@Request() req: any): Promise<ReceberHojeResponseDto> {
    return this.dashboardService.getReceberHoje(req.dashboardFilters);
  }

  @Get('riscos')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.COBRADOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Listar pedidos em situação de risco' })
  @ApiResponse({ status: 200, type: RiscosResponseDto })
  async getRiscos(@Request() req: any): Promise<RiscosResponseDto> {
    return this.dashboardService.getRiscos(req.dashboardFilters);
  }

  @Get('em-separacao')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Listar pedidos em separação' })
  @ApiResponse({ status: 200, type: EmSeparacaoResponseDto })
  async getEmSeparacao(@Request() req: any): Promise<EmSeparacaoResponseDto> {
    return this.dashboardService.getEmSeparacao(req.dashboardFilters);
  }

  @Get('em-separacao/export')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Exportar CSV dos pedidos em separação' })
  @ApiResponse({ status: 200, description: 'Arquivo CSV' })
  async exportEmSeparacao(
    @Request() req: any,
    @Response() res: ExpressResponse,
  ): Promise<void> {
    const csv = await this.dashboardService.exportEmSeparacaoCSV(req.dashboardFilters);
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=pedidos-em-separacao.csv');
    res.status(HttpStatus.OK).send(csv);
  }

  @Get('analise')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Listar pedidos em análise' })
  @ApiResponse({ status: 200, type: AnaliseResponseDto })
  async getAnalise(@Request() req: any): Promise<AnaliseResponseDto> {
    return this.dashboardService.getAnalise(req.dashboardFilters);
  }

  @Post('analise/:orderId/aprovar')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Aprovar pedido em análise' })
  @ApiResponse({ status: 200, description: 'Pedido aprovado com sucesso' })
  async aprovarPedido(
    @Param('orderId') orderId: string,
    @Body() dto: AprovarReprovarDto,
    @Request() req: any,
  ): Promise<{ message: string; order: any }> {
    const userId = req.user.id || req.user.userId || req.user.sub;
    return this.dashboardService.aprovarPedido(orderId, userId, dto.observation);
  }

  @Post('analise/:orderId/reprovar')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Reprovar pedido em análise' })
  @ApiResponse({ status: 200, description: 'Pedido reprovado com sucesso' })
  async reprovarPedido(
    @Param('orderId') orderId: string,
    @Body() dto: AprovarReprovarDto,
    @Request() req: any,
  ): Promise<{ message: string; order: any }> {
    const userId = req.user.id || req.user.userId || req.user.sub;
    return this.dashboardService.reprovarPedido(orderId, userId, dto.observation);
  }
}