import { <PERSON>du<PERSON> } from '@nestjs/common';
import { DashboardInteligenteController } from './dashboard-inteligente.controller';
import { DashboardInteligenteService } from './dashboard-inteligente.service';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { DashboardInteligenteInterceptor } from './interceptors/dashboard-inteligente.interceptor';

@Module({
  imports: [PrismaModule, AuthModule],
  controllers: [DashboardInteligenteController],
  providers: [DashboardInteligenteService, DashboardInteligenteInterceptor],
  exports: [DashboardInteligenteService],
})
export class DashboardInteligenteModule {}