import { IsBoolean, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, IsString, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ApprovePaymentDto {
  @ApiProperty({ description: 'Whether to approve or reject the payment' })
  @IsBoolean()
  approved: boolean;

  @ApiProperty({ description: 'Payment amount received', example: 1500.00 })
  @IsNumber()
  @Min(0)
  paymentAmount: number;

  @ApiProperty({ description: 'Reason for rejection (if rejected)', required: false })
  @IsOptional()
  @IsString()
  rejectionReason?: string;
}