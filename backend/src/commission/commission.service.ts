import { Injectable, BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Role, OrderStatus, CommissionApprovalStatus, Prisma } from '@prisma/client';
import { CreateCommissionSettingDto } from './dto/create-commission-setting.dto';
import { UpdateCommissionSettingDto } from './dto/update-commission-setting.dto';
import { ApprovePaymentDto } from './dto/approve-payment.dto';
import { CommissionReportDto } from './dto/commission-report.dto';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class CommissionService {
  constructor(private prisma: PrismaService) {}

  async createCommissionSetting(data: CreateCommissionSettingDto) {
    const existingSetting = await this.prisma.commissionSetting.findUnique({
      where: { userId: data.userId },
    });

    if (existingSetting) {
      throw new BadRequestException('Commission setting already exists for this user');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: data.userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.prisma.commissionSetting.create({
      data: {
        userId: data.userId,
        percentage: data.percentage,
        role: user.role,
      },
    });
  }

  async updateCommissionSetting(userId: string, data: UpdateCommissionSettingDto) {
    const setting = await this.prisma.commissionSetting.findUnique({
      where: { userId },
    });

    if (!setting) {
      throw new NotFoundException('Commission setting not found');
    }

    return this.prisma.commissionSetting.update({
      where: { userId },
      data: {
        percentage: data.percentage,
      },
    });
  }

  async getCommissionSettings() {
    return this.prisma.commissionSetting.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            active: true,
          },
        },
      },
    });
  }

  async getCommissionSetting(userId: string) {
    const setting = await this.prisma.commissionSetting.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    if (!setting) {
      throw new NotFoundException('Commission setting not found');
    }

    return setting;
  }

  async approvePayment(approvedById: string, orderId: string, data: ApprovePaymentDto) {
    try {
      console.log('approvePayment called with:', { approvedById, orderId, data });
      
      const approver = await this.prisma.user.findUnique({
        where: { id: approvedById },
      });

      if (!approver || approver.role !== Role.SUPERVISOR) {
        throw new ForbiddenException('Only supervisors can approve payments');
      }

      const order = await this.prisma.order.findUnique({
        where: { id: orderId },
        include: {
          seller: true,
          collector: true,
        },
      });

      if (!order) {
        throw new NotFoundException('Order not found');
      }

      if (order.status !== OrderStatus.Completo && order.status !== OrderStatus.Parcial) {
        throw new BadRequestException('Order must be in COMPLETO or PARCIAL status');
      }

      if (order.commissionApprovalStatus === CommissionApprovalStatus.APPROVED) {
        throw new BadRequestException('Payment already approved for this order');
      }

      return this.prisma.$transaction(async (tx) => {
        // Create payment approval record
        const approval = await tx.paymentApproval.create({
          data: {
            orderId,
            approvedById,
            previousStatus: order.status,
            newStatus: order.status,
            paymentAmount: new Decimal(data.paymentAmount),
            paymentType: order.status === OrderStatus.Completo ? 'Completo' : 'Parcial',
            approvalStatus: data.approved ? CommissionApprovalStatus.APPROVED : CommissionApprovalStatus.REJECTED,
            rejectionReason: data.rejectionReason,
          },
        });

        // Update order with payment details
        await tx.order.update({
          where: { id: orderId },
          data: {
            commissionApprovalStatus: data.approved ? CommissionApprovalStatus.APPROVED : CommissionApprovalStatus.REJECTED,
            paymentReceivedAmount: data.approved ? new Decimal(data.paymentAmount) : undefined,
            paymentReceivedDate: data.approved ? new Date() : undefined,
            commissionApproved: data.approved,
          },
        });

        // If approved, calculate and create commission payments
        if (data.approved) {
          const commissions = await this.calculateCommissions(tx, order, data.paymentAmount);
          
          for (const commission of commissions) {
            await tx.commissionPayment.create({
              data: commission,
            });
          }
        }

        return approval;
      });
  } catch (error) {
    console.error('Error in approvePayment:', error);
    throw error;
  }
  }

  private async calculateCommissions(
    tx: Prisma.TransactionClient,
    order: any,
    paymentAmount: number,
  ): Promise<Prisma.CommissionPaymentCreateInput[]> {
    const commissions: Prisma.CommissionPaymentCreateInput[] = [];
    const paymentDate = new Date();

    // Get seller commission setting
    const sellerSetting = await tx.commissionSetting.findUnique({
      where: { userId: order.sellerId },
    });

    if (sellerSetting) {
      const sellerCommission = new Decimal(paymentAmount)
        .mul(sellerSetting.percentage)
        .div(100);

      commissions.push({
        order: { connect: { id: order.id } },
        user: { connect: { id: order.sellerId } },
        userRole: Role.VENDEDOR,
        baseAmount: paymentAmount,
        percentage: sellerSetting.percentage,
        commissionAmount: sellerCommission,
        paymentDate,
      });
    }

    // Get collector commission setting
    if (order.collectorId) {
      const collectorSetting = await tx.commissionSetting.findUnique({
        where: { userId: order.collectorId },
      });

      if (collectorSetting) {
        const collectorCommission = new Decimal(paymentAmount)
          .mul(collectorSetting.percentage)
          .div(100);

        commissions.push({
          order: { connect: { id: order.id } },
          user: { connect: { id: order.collectorId } },
          userRole: Role.COBRADOR,
          baseAmount: paymentAmount,
          percentage: collectorSetting.percentage,
          commissionAmount: collectorCommission,
          paymentDate,
        });
      }
    }

    return commissions;
  }

  async getCommissionReport(filters: CommissionReportDto) {
    const where: Prisma.CommissionPaymentWhereInput = {};

    if (filters.userId) {
      where.userId = filters.userId;
    }

    if (filters.userRole) {
      where.userRole = filters.userRole;
    }

    if (filters.startDate || filters.endDate) {
      where.paymentDate = {};
      if (filters.startDate) {
        where.paymentDate.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.paymentDate.lte = new Date(filters.endDate);
      }
    }

    const commissions = await this.prisma.commissionPayment.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        order: {
          select: {
            id: true,
            customerName: true,
            total: true,
            status: true,
          },
        },
      },
      orderBy: {
        paymentDate: 'desc',
      },
    });

    const totalCommissions = commissions.reduce(
      (sum, c) => sum.add(c.commissionAmount),
      new Decimal(0),
    );

    const byUser = commissions.reduce((acc, c) => {
      if (!acc[c.userId]) {
        acc[c.userId] = {
          user: c.user,
          totalCommission: new Decimal(0),
          count: 0,
        };
      }
      acc[c.userId].totalCommission = acc[c.userId].totalCommission.add(c.commissionAmount);
      acc[c.userId].count++;
      return acc;
    }, {} as Record<string, any>);

    return {
      commissions,
      summary: {
        totalCommissions: totalCommissions.toFixed(2),
        totalRecords: commissions.length,
        byUser: Object.values(byUser),
      },
    };
  }

  async getPendingApprovals() {
    return this.prisma.order.findMany({
      where: {
        status: {
          in: [OrderStatus.Completo, OrderStatus.Parcial],
        },
        commissionApprovalStatus: {
          in: [CommissionApprovalStatus.NONE, CommissionApprovalStatus.PENDING],
        },
      },
      include: {
        customer: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
  }

  async getPaymentApprovals(orderId?: string) {
    const where: Prisma.PaymentApprovalWhereInput = {};
    
    if (orderId) {
      where.orderId = orderId;
    }

    return this.prisma.paymentApproval.findMany({
      where,
      include: {
        order: {
          select: {
            id: true,
            customerName: true,
            total: true,
          },
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}