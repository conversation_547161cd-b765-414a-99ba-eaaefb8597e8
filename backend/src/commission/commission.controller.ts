import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { CommissionService } from './commission.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Role } from '@prisma/client';
import { CreateCommissionSettingDto } from './dto/create-commission-setting.dto';
import { UpdateCommissionSettingDto } from './dto/update-commission-setting.dto';
import { ApprovePaymentDto } from './dto/approve-payment.dto';
import { CommissionReportDto } from './dto/commission-report.dto';

@ApiTags('commission')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('commission')
export class CommissionController {
  constructor(private readonly commissionService: CommissionService) {}

  @Post('settings')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Create commission setting for a user' })
  async createCommissionSetting(@Body() data: CreateCommissionSettingDto) {
    return this.commissionService.createCommissionSetting(data);
  }

  @Put('settings/:userId')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Update commission setting for a user' })
  async updateCommissionSetting(
    @Param('userId') userId: string,
    @Body() data: UpdateCommissionSettingDto,
  ) {
    return this.commissionService.updateCommissionSetting(userId, data);
  }

  @Get('settings')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Get all commission settings' })
  async getCommissionSettings() {
    return this.commissionService.getCommissionSettings();
  }

  @Get('settings/:userId')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Get commission setting for a specific user' })
  async getCommissionSetting(@Param('userId') userId: string) {
    return this.commissionService.getCommissionSetting(userId);
  }

  @Post('approve/:orderId')
  @Roles(Role.SUPERVISOR)
  @ApiOperation({ summary: 'Approve or reject payment for an order' })
  async approvePayment(
    @Request() req: any,
    @CurrentUser() user: any,
    @Param('orderId') orderId: string,
    @Body() data: ApprovePaymentDto,
  ) {
    console.log('Request user:', req.user);
    console.log('User from CurrentUser decorator:', user);
    const userId = user?.id || user?.userId || req.user?.userId || req.user?.id;
    if (!userId) {
      throw new Error('User not properly authenticated');
    }
    return this.commissionService.approvePayment(userId, orderId, data);
  }

  @Get('pending-approvals')
  @Roles(Role.SUPERVISOR, Role.ADMIN)
  @ApiOperation({ summary: 'Get orders pending payment approval' })
  async getPendingApprovals() {
    return this.commissionService.getPendingApprovals();
  }

  @Get('approvals')
  @Roles(Role.SUPERVISOR, Role.ADMIN)
  @ApiOperation({ summary: 'Get payment approval history' })
  @ApiQuery({ name: 'orderId', required: false })
  async getPaymentApprovals(@Query('orderId') orderId?: string) {
    return this.commissionService.getPaymentApprovals(orderId);
  }

  @Get('report')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR, Role.COBRADOR)
  @ApiOperation({ summary: 'Get commission report' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'userRole', required: false, enum: Role })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getCommissionReport(@CurrentUser() user: any, @Query() filters: CommissionReportDto) {
    // If user is VENDEDOR or COBRADOR, only show their own commissions
    if (user.role === Role.VENDEDOR || user.role === Role.COBRADOR) {
      filters.userId = user.id || user.userId;
    }

    return this.commissionService.getCommissionReport(filters);
  }
}