import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateVariationDto } from './dto/create-variation.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { Product, ProductVariation, Inventory, Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class ProductsService {
  constructor(private prisma: PrismaService) {}

  // Produtos
  async create(createProductDto: CreateProductDto, tenantId: string): Promise<Product> {
    const { variations, ...productData } = createProductDto;

    return this.prisma.$transaction(async (prisma) => {
      const product = await prisma.product.create({
        data: {
          ...productData,
          tenantId,
        },
      });

      if (variations && variations.length > 0) {
        for (const variation of variations) {
          await prisma.productVariation.create({
            data: {
              productId: product.id,
              ...variation,
              inventory: {
                create: {
                  quantity: 0,
                  minAlert: 10,
                },
              },
            },
          });
        }
      }

      return product;
    });
  }

  async findAll(tenantId: string, active?: boolean): Promise<Product[]> {
    const where: Prisma.ProductWhereInput = {
      tenantId,
    };
    
    if (active !== undefined) {
      where.active = active;
    }

    return this.prisma.product.findMany({
      where,
      include: {
        variations: {
          include: {
            inventory: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  async findOne(id: string, tenantId: string): Promise<Product> {
    const product = await this.prisma.product.findFirst({
      where: { 
        id,
        tenantId,
      },
      include: {
        variations: {
          include: {
            inventory: true,
          },
          orderBy: {
            variation: 'asc',
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException('Produto não encontrado');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto, tenantId: string): Promise<Product> {
    await this.findOne(id, tenantId); // Verifica se existe

    return this.prisma.product.update({
      where: { id },
      data: updateProductDto,
      include: {
        variations: {
          include: {
            inventory: true,
          },
        },
      },
    });
  }

  async remove(id: string, tenantId: string): Promise<Product> {
    const product = await this.findOne(id, tenantId); // Verifica se existe

    // Buscar todos os kits do produto
    const kits = await this.prisma.kit.findMany({
      where: {
        items: {
          some: {
            productVariation: {
              productId: id,
            },
          },
        },
      },
    });

    // Para cada kit, verificar se tem vendas (orderItems)
    const kitsWithOrders = await Promise.all(
      kits.map(async (kit) => {
        const orderCount = await this.prisma.orderItem.count({
          where: {
            productName: kit.name, // OrderItem usa o nome do kit
          },
        });
        return { kit, orderCount };
      }),
    );

    // Verificar se algum kit tem vendas
    const hasOrders = kitsWithOrders.some(({ orderCount }) => orderCount > 0);

    if (hasOrders) {
      throw new BadRequestException(
        'Este produto possui kits com vendas registradas e não pode ser excluído. Use a opção de desativar.',
      );
    }

    // Se não tem vendas, pode excluir de verdade
    // Primeiro exclui os kit items
    await this.prisma.kitItem.deleteMany({
      where: {
        productVariation: {
          productId: id,
        },
      },
    });

    // Exclui os kits vazios (que só tinham itens deste produto)
    const emptyKits = await this.prisma.kit.findMany({
      where: {
        items: {
          none: {},
        },
      },
    });

    if (emptyKits.length > 0) {
      await this.prisma.kit.deleteMany({
        where: {
          id: {
            in: emptyKits.map(k => k.id),
          },
        },
      });
    }

    // Exclui inventários das variações
    await this.prisma.inventory.deleteMany({
      where: {
        productVariation: {
          productId: id,
        },
      },
    });

    // Exclui variações
    await this.prisma.productVariation.deleteMany({
      where: {
        productId: id,
      },
    });

    // Por fim, exclui o produto
    return this.prisma.product.delete({
      where: { id },
    });
  }

  // Variações
  async createVariation(
    productId: string,
    createVariationDto: CreateVariationDto,
    tenantId: string,
  ): Promise<ProductVariation> {
    // Verifica se produto existe
    await this.findOne(productId, tenantId);

    // Verifica se SKU já existe
    const existingSku = await this.prisma.productVariation.findUnique({
      where: { sku: createVariationDto.sku },
    });

    if (existingSku) {
      throw new ConflictException('SKU já está em uso');
    }

    // Cria variação com estoque inicial
    return this.prisma.productVariation.create({
      data: {
        productId,
        variation: createVariationDto.variation,
        price: new Decimal(createVariationDto.price),
        sku: createVariationDto.sku,
        active: createVariationDto.active ?? true,
        inventory: {
          create: {
            quantity: 0,
            minAlert: 10,
          },
        },
      },
      include: {
        inventory: true,
        product: true,
      },
    });
  }

  async updateVariation(
    variationId: string,
    data: { price?: number; active?: boolean },
  ): Promise<ProductVariation> {
    const variation = await this.prisma.productVariation.findUnique({
      where: { id: variationId },
    });

    if (!variation) {
      throw new NotFoundException('Variação não encontrada');
    }

    const updateData: any = {};
    if (data.price !== undefined) {
      updateData.price = new Decimal(data.price);
    }
    if (data.active !== undefined) {
      updateData.active = data.active;
    }

    return this.prisma.productVariation.update({
      where: { id: variationId },
      data: updateData,
      include: {
        inventory: true,
        product: true,
      },
    });
  }

  // Estoque
  async updateInventory(
    variationId: string,
    updateInventoryDto: UpdateInventoryDto,
  ): Promise<Inventory> {
    // Verifica se variação existe
    const variation = await this.prisma.productVariation.findUnique({
      where: { id: variationId },
      include: { inventory: true },
    });

    if (!variation) {
      throw new NotFoundException('Variação não encontrada');
    }

    if (!variation.inventory) {
      // Cria inventário se não existir
      return this.prisma.inventory.create({
        data: {
          productVariationId: variationId,
          quantity: updateInventoryDto.quantity,
          minAlert: updateInventoryDto.minAlert ?? 10,
        },
      });
    }

    // Atualiza inventário existente
    return this.prisma.inventory.update({
      where: { id: variation.inventory.id },
      data: updateInventoryDto,
    });
  }

  async checkStock(variationId: string): Promise<{
    available: boolean;
    quantity: number;
    minAlert: number;
    isLowStock: boolean;
  }> {
    const variation = await this.prisma.productVariation.findUnique({
      where: { id: variationId },
      include: { inventory: true },
    });

    if (!variation || !variation.inventory) {
      return {
        available: false,
        quantity: 0,
        minAlert: 0,
        isLowStock: true,
      };
    }

    const inventory = variation.inventory;
    return {
      available: inventory.quantity > 0,
      quantity: inventory.quantity,
      minAlert: inventory.minAlert,
      isLowStock: inventory.quantity <= inventory.minAlert,
    };
  }

  async adjustStock(
    variationId: string,
    quantity: number,
    operation: 'add' | 'subtract',
  ): Promise<Inventory> {
    const variation = await this.prisma.productVariation.findUnique({
      where: { id: variationId },
      include: { inventory: true },
    });

    if (!variation || !variation.inventory) {
      throw new NotFoundException('Variação ou estoque não encontrado');
    }

    const currentQuantity = variation.inventory.quantity;
    let newQuantity: number;

    if (operation === 'add') {
      newQuantity = currentQuantity + quantity;
    } else {
      newQuantity = currentQuantity - quantity;
      if (newQuantity < 0) {
        throw new BadRequestException('Estoque insuficiente');
      }
    }

    return this.prisma.inventory.update({
      where: { id: variation.inventory.id },
      data: { quantity: newQuantity },
    });
  }

  // Buscar variações com baixo estoque
  async getLowStockVariations(tenantId: string): Promise<ProductVariation[]> {
    return this.prisma.$queryRaw`
      SELECT pv.*, p.name as product_name, i.quantity, i."minAlert"
      FROM "ProductVariation" pv
      JOIN "Product" p ON p.id = pv."productId"
      JOIN "Inventory" i ON i."productVariationId" = pv.id
      WHERE pv.active = true 
        AND p.active = true
        AND p."tenantId" = ${tenantId}
        AND i.quantity <= i."minAlert"
      ORDER BY p.name, pv.variation
    `;
  }
}