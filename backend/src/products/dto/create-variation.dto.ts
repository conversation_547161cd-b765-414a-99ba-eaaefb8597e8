import { <PERSON>S<PERSON>, IsNotEmpty, IsNumber, IsBoolean, IsO<PERSON>al, Min, <PERSON>eng<PERSON> } from 'class-validator';

export class CreateVariationDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  variation: string; // ex: "500mg", "1kg"

  @IsNumber()
  @Min(0.01)
  price: number;

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  sku: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean = true;
}