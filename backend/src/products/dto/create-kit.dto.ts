import { IsString, IsNotEmpty, IsOptional, IsArray, IsNumber, Min, ValidateNested, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class KitItemDto {
  @ApiProperty({ description: 'Variation ID' })
  @IsString()
  @IsNotEmpty()
  variationId: string;

  @ApiProperty({ description: 'Quantity of this variation in the kit', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;
}

export class CreateKitDto {
  @ApiProperty({ description: 'Kit name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Kit description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Kit price', minimum: 0 })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ type: [KitItemDto], description: 'Items in the kit' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => KitItemDto)
  items: KitItemDto[];

  @ApiProperty({ description: 'Is kit active', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}