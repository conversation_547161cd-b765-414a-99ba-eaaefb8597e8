import { IsString, IsNotEmpty, IsOptional, IsBoolean, MaxLength, IsUrl, IsArray, ValidateNested, IsNumber, Min, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

class VariationDto {
  @IsString()
  @IsNotEmpty()
  variation: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsString()
  @IsNotEmpty()
  sku: string;
}

export class CreateProductDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  price?: number;

  @IsUrl()
  @IsOptional()
  imageUrl?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean = true;

  @IsArray()
  @ArrayMinSize(1, { message: 'Every product must have at least one variation' })
  @ValidateNested({ each: true })
  @Type(() => VariationDto)
  variations: VariationDto[];
}
