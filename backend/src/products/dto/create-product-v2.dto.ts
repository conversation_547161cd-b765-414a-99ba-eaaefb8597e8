import { IsString, IsNotEmpty, IsOptional, IsArray, IsEnum, IsNumber, Min, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum VariationType {
  CAPSULAS = 'CAPSULAS',
  GOTAS = 'GOTAS',
  GEL = 'GEL',
  SPRAY = 'SPRAY',
  CREME = 'CREME',
  CUSTOM = 'CUSTOM',
}

export class CreateVariationDto {
  @ApiProperty({ enum: VariationType, description: 'Type of variation' })
  @IsEnum(VariationType)
  type: VariationType;

  @ApiProperty({ description: 'Custom name for variation (required if type is CUSTOM)', required: false })
  @IsOptional()
  @IsString()
  customName?: string;

  @ApiProperty({ description: 'Cost price for this variation', minimum: 0 })
  @IsNumber()
  @Min(0)
  costPrice: number;
}

export class CreateProductV2Dto {
  @ApiProperty({ description: 'Product name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Product description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Product image URL', required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({ type: [CreateVariationDto], description: 'Product variations' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVariationDto)
  variations: CreateVariationDto[];
}