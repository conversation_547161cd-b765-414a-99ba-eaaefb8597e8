import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsBoolean, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOfferDto {
  @ApiProperty({ description: 'Offer name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Offer price', minimum: 0 })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiProperty({ description: 'Gel quantity', default: 0 })
  @IsNumber()
  @Min(0)
  @IsOptional()
  gelQuantity?: number;

  @ApiProperty({ description: 'Capsules quantity', default: 0 })
  @IsNumber()
  @Min(0)
  @IsOptional()
  capsulesQuantity?: number;

  @ApiProperty({ description: 'Is this a kit?', default: false })
  @IsBoolean()
  @IsOptional()
  isKit?: boolean;

  @ApiProperty({ description: 'Display order', default: 0 })
  @IsNumber()
  @Min(0)
  @IsOptional()
  displayOrder?: number;

  @ApiProperty({ description: 'Is offer active', default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}