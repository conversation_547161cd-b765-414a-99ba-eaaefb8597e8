import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient, Prisma } from '@prisma/client';
import { getDatabaseOptions } from '../config/database.config';
import { AsyncLocalStorage } from 'async_hooks';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly tenantContext = new AsyncLocalStorage<{ tenantId: string }>();

  constructor() {
    super(getDatabaseOptions());
    
    console.log('🔌 Initializing database connection...');
    console.log('   Database URL:', process.env.DATABASE_URL ? 'Configured ✓' : 'Not configured ✗');
    console.log('   Environment:', process.env.NODE_ENV || 'development');

    // Set up middleware for automatic tenant filtering
    this.setupTenantMiddleware();
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('✅ Database connected successfully');
      
      // Test the connection
      await this.$queryRaw`SELECT 1`;
      console.log('✅ Database connection verified');
    } catch (error) {
      console.error('❌ Failed to connect to database:', error);
      // Don't throw - let the app start anyway
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      console.log('🔌 Database disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting from database:', error);
    }
  }

  /**
   * Sets up Prisma middleware for automatic tenant filtering
   * This ensures all queries are automatically scoped to the current tenant
   */
  private setupTenantMiddleware() {
    // Models that should be filtered by tenant
    const tenantModels = ['User', 'Order', 'Product', 'Customer', 'Kit', 'Configuration'];

    this.$use(async (params, next) => {
      // Only apply to tenant-scoped models
      if (!tenantModels.includes(params.model || '')) {
        return next(params);
      }

      // Get tenant ID from async context or global fallback
      const context = this.tenantContext.getStore();
      const tenantId = context?.tenantId || (global as any).__currentTenantId;

      if (!tenantId) {
        // If no tenant ID, proceed without filtering (for system operations)
        return next(params);
      }

      // Apply tenant filtering based on the operation
      if (params.action === 'findUnique' || params.action === 'findFirst') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'findMany') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'create') {
        // Add tenant ID to data
        if (!params.args) params.args = {};
        if (!params.args.data) params.args.data = {};
        params.args.data = { ...params.args.data, tenantId };
      } else if (params.action === 'createMany') {
        // Add tenant ID to all records
        if (!params.args) params.args = {};
        if (!params.args.data) params.args.data = [];
        if (Array.isArray(params.args.data)) {
          params.args.data = params.args.data.map((item: any) => ({ ...item, tenantId }));
        }
      } else if (params.action === 'update') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'updateMany') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'delete') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'deleteMany') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      } else if (params.action === 'upsert') {
        // Add tenant ID to create and where if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
        if (!params.args.create) params.args.create = {};
        params.args.create = { ...params.args.create, tenantId };
      } else if (params.action === 'count') {
        // Add tenant filter to where clause if not already present
        if (!params.args) params.args = {};
        if (!params.args.where) params.args.where = {};
        if (!params.args.where.tenantId || typeof params.args.where.tenantId === 'object') {
          params.args.where.tenantId = tenantId;
        }
      }

      return next(params);
    });
  }

  /**
   * Run a query within a specific tenant context
   * This is useful for background jobs or system operations
   */
  async runInTenantContext<T>(tenantId: string, fn: () => Promise<T>): Promise<T> {
    return this.tenantContext.run({ tenantId }, fn);
  }

  /**
   * Get the current tenant ID from context
   */
  getCurrentTenantId(): string | undefined {
    const context = this.tenantContext.getStore();
    return context?.tenantId || (global as any).__currentTenantId;
  }
} 