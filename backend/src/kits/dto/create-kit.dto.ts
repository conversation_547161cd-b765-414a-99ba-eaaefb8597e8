import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsUUID,
  IsNumber,
  Min,
  MaxLength,
  IsPositive,
} from 'class-validator';
import { Type } from 'class-transformer';

export class KitItemDto {
  @IsUUID()
  @IsNotEmpty()
  productVariationId: string;

  @IsNumber()
  @Min(1)
  quantity: number;
}

export class CreateKitDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @IsNumber()
  @IsPositive()
  price: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean = true;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => KitItemDto)
  items: KitItemDto[];
}