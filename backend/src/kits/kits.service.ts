import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKitDto } from './dto/create-kit.dto';
import { UpdateKitDto } from './dto/update-kit.dto';
import { Kit, KitItem, Prisma } from '@prisma/client';

@Injectable()
export class KitsService {
  constructor(private prisma: PrismaService) {}

  async create(createKitDto: CreateKitDto, tenantId: string): Promise<Kit> {
    const { items, ...kitData } = createKitDto;

    // Valida se todos os produtos/variações existem
    await this.validateKitItems(items);

    // Cria o kit com seus itens
    const kit = await this.prisma.kit.create({
      data: {
        tenantId,
        ...kitData,
        items: {
          create: items.map(item => ({
            productVariationId: item.productVariationId,
            quantity: item.quantity,
          })),
        },
      },
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });

    return kit;
  }

  async findAll(active?: boolean, tenantId?: string): Promise<Kit[]> {
    const where: Prisma.KitWhereInput = {};
    
    if (active !== undefined) {
      where.active = active;
    }
    
    if (tenantId) {
      where.tenantId = tenantId;
    }

    const kits = await this.prisma.kit.findMany({
      where,
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return kits;
  }

  async findByProductId(productId: string, tenantId: string, active?: boolean): Promise<Kit[]> {
    const where: Prisma.KitWhereInput = {
      tenantId,
      items: {
        some: {
          productVariation: {
            productId,
          },
        },
      },
    };
    
    if (active !== undefined) {
      where.active = active;
    }

    const kits = await this.prisma.kit.findMany({
      where,
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return kits;
  }

  async findOne(id: string, tenantId: string): Promise<Kit> {
    const kit = await this.prisma.kit.findFirst({
      where: { id, tenantId },
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });

    if (!kit) {
      throw new NotFoundException('Kit não encontrado');
    }

    return kit;
  }

  async update(id: string, tenantId: string, updateKitDto: UpdateKitDto): Promise<Kit> {
    await this.findOne(id, tenantId);

    const kit = await this.prisma.kit.update({
      where: { id },
      data: updateKitDto,
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });

    return kit;
  }

  async remove(id: string, tenantId: string): Promise<Kit> {
    await this.findOne(id, tenantId);

    // Check if the kit has been used in any orders
    // Since there's no direct relationship, we check if any order items
    // reference products/variations that are part of this kit
    const kit = await this.prisma.kit.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            productVariation: true,
          },
        },
      },
    });

    if (!kit) {
      throw new NotFoundException('Kit não encontrado');
    }

    // Get all product variation IDs from the kit
    const variationIds = kit.items.map(item => item.productVariationId);

    // Check if any of these variations have been used in orders
    const orderItemsCount = await this.prisma.orderItem.count({
      where: {
        productVariationId: {
          in: variationIds,
        },
        order: {
          tenantId,
          // Exclude cancelled orders
          status: {
            not: 'Cancelado',
          },
        },
      },
    });

    // If the kit has been used in any orders, only deactivate it
    if (orderItemsCount > 0) {
      // Deactivate the kit instead of deleting
      const deactivatedKit = await this.prisma.kit.update({
        where: { id },
        data: {
          active: false,
        },
      });

      // Throw an informative exception about why it was deactivated
      throw new BadRequestException(
        `Kit "${kit.name}" foi desativado pois possui ${orderItemsCount} venda(s) associada(s). ` +
        `Kits com histórico de vendas não podem ser excluídos permanentemente.`
      );
    }

    // If the kit has never been used, permanently delete it
    // First delete all kit items, then delete the kit
    await this.prisma.kitItem.deleteMany({
      where: { kitId: id },
    });

    return this.prisma.kit.delete({
      where: { id },
    });
  }

  // Adicionar itens ao kit
  async addItems(kitId: string, tenantId: string, items: { productVariationId: string; quantity: number }[]): Promise<Kit> {
    await this.findOne(kitId, tenantId);
    await this.validateKitItems(items); // Valida novos itens

    // Adiciona novos itens
    await this.prisma.kitItem.createMany({
      data: items.map(item => ({
        kitId,
        productVariationId: item.productVariationId,
        quantity: item.quantity,
      })),
    });

    return this.findOne(kitId, tenantId);
  }

  // Remover item do kit
  async removeItem(kitId: string, itemId: string, tenantId: string): Promise<Kit> {
    const kit = await this.findOne(kitId, tenantId);
    
    const kitWithItems = kit as Kit & { items: any[] };
    const item = kitWithItems.items.find(i => i.id === itemId);
    if (!item) {
      throw new NotFoundException('Item não encontrado no kit');
    }

    await this.prisma.kitItem.delete({
      where: { id: itemId },
    });

    return this.findOne(kitId, tenantId);
  }

  // Atualizar quantidade de um item no kit
  async updateItemQuantity(kitId: string, itemId: string, quantity: number, tenantId: string): Promise<Kit> {
    if (quantity < 1) {
      throw new BadRequestException('Quantidade deve ser maior que zero');
    }

    const kit = await this.findOne(kitId, tenantId);
    
    const kitWithItems = kit as Kit & { items: any[] };
    const item = kitWithItems.items.find(i => i.id === itemId);
    if (!item) {
      throw new NotFoundException('Item não encontrado no kit');
    }

    await this.prisma.kitItem.update({
      where: { id: itemId },
      data: {
        quantity,
      },
    });

    return this.findOne(kitId, tenantId);
  }

  // Verificar disponibilidade do kit
  async checkAvailability(kitId: string, requestedQuantity: number = 1, tenantId: string): Promise<{
    available: boolean;
    maxAvailable: number;
    limitingItems: Array<{
      productName: string;
      variation: string;
      required: number;
      available: number;
    }>;
  }> {
    const kit = await this.findOne(kitId, tenantId);
    
    if (!kit.active) {
      return {
        available: false,
        maxAvailable: 0,
        limitingItems: [],
      };
    }

    let maxAvailable = Number.MAX_SAFE_INTEGER;
    const limitingItems: Array<{
      productName: string;
      variation: string;
      required: number;
      available: number;
    }> = [];

    // Calcula a quantidade máxima disponível baseada no estoque de cada item
    const kitWithItems = kit as Kit & { items: any[] };
    for (const item of kitWithItems.items) {
      const inventory = item.productVariation.inventory;
      const product = item.productVariation.product;
      
      if (!inventory) {
        maxAvailable = 0;
        limitingItems.push({
          productName: product.name,
          variation: item.productVariation.variation,
          required: item.quantity * requestedQuantity,
          available: 0,
        });
        continue;
      }

      const availableForThisItem = Math.floor(inventory.quantity / item.quantity);
      
      if (availableForThisItem < maxAvailable) {
        maxAvailable = availableForThisItem;
      }

      if (inventory.quantity < item.quantity * requestedQuantity) {
        limitingItems.push({
          productName: product.name,
          variation: item.productVariation.variation,
          required: item.quantity * requestedQuantity,
          available: inventory.quantity,
        });
      }
    }

    return {
      available: maxAvailable >= requestedQuantity,
      maxAvailable,
      limitingItems,
    };
  }

  // Reservar estoque do kit (para uso em vendas)
  async reserveStock(kitId: string, quantity: number, tenantId: string): Promise<void> {
    const availability = await this.checkAvailability(kitId, quantity, tenantId);
    
    if (!availability.available) {
      throw new BadRequestException(
        `Kit não disponível. Quantidade máxima: ${availability.maxAvailable}`
      );
    }

    const kit = await this.findOne(kitId, tenantId);

    // Usa transação para garantir atomicidade
    await this.prisma.$transaction(async (tx) => {
      const kitWithItems = kit as Kit & { items: any[] };
      for (const item of kitWithItems.items) {
        const totalRequired = item.quantity * quantity;
        
        await tx.inventory.update({
          where: { productVariationId: item.productVariationId },
          data: {
            quantity: {
              decrement: totalRequired,
            },
          },
        });
      }
    });
  }

  // Liberar estoque do kit (para cancelamento de vendas)
  async releaseStock(kitId: string, quantity: number, tenantId: string): Promise<void> {
    const kit = await this.findOne(kitId, tenantId);

    // Usa transação para garantir atomicidade
    await this.prisma.$transaction(async (tx) => {
      const kitWithItems = kit as Kit & { items: any[] };
      for (const item of kitWithItems.items) {
        const totalToReturn = item.quantity * quantity;
        
        await tx.inventory.update({
          where: { productVariationId: item.productVariationId },
          data: {
            quantity: {
              increment: totalToReturn,
            },
          },
        });
      }
    });
  }

  // Validar se variações existem
  private async validateKitItems(items: { productVariationId: string; quantity: number }[]): Promise<void> {
    if (!items || items.length === 0) {
      throw new BadRequestException('Kit deve ter pelo menos um item');
    }

    const variationIds = items.map(item => item.productVariationId);
    const variations = await this.prisma.productVariation.findMany({
      where: {
        id: { in: variationIds },
      },
      include: {
        product: true,
      },
    });

    if (variations.length !== variationIds.length) {
      throw new BadRequestException('Uma ou mais variações não foram encontradas');
    }

    // Verifica se há produtos inativos
    const inactiveProducts = variations.filter(v => !v.active || !v.product.active);
    if (inactiveProducts.length > 0) {
      throw new BadRequestException(
        `Produtos inativos: ${inactiveProducts.map(v => v.product.name).join(', ')}`
      );
    }
  }
}