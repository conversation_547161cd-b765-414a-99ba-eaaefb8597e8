import { IsString, IsOptional, IsN<PERSON>ber, IsBoolean, IsDateString } from 'class-validator';

export class ImportOrderDto {
  @IsString()
  idVenda: string;

  @IsString()
  cliente: string;

  @IsString()
  telefone: string;

  @IsNumber()
  valorVenda: number;

  @IsOptional()
  @IsString()
  dataVenda?: string;

  @IsOptional()
  @IsString()
  oferta?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  situacaoVenda?: string;

  @IsOptional()
  @IsNumber()
  valorRecebido?: number;

  @IsOptional()
  @IsString()
  historico?: string;

  @IsOptional()
  @IsString()
  ultimaAtualizacao?: string;

  @IsOptional()
  @IsString()
  codigoRastreio?: string;

  @IsOptional()
  @IsString()
  statusCorreios?: string;

  @IsOptional()
  @IsString()
  vendedor?: string;

  @IsOptional()
  @IsString()
  operador?: string;

  @IsOptional()
  @IsString()
  zap?: string;

  @IsOptional()
  @IsString()
  estadoDestinatario?: string;

  @IsOptional()
  @IsString()
  cidadeDestinatario?: string;

  @IsOptional()
  @IsString()
  ruaDestinatario?: string;

  @IsOptional()
  @IsString()
  cepDestinatario?: string;

  @IsOptional()
  @IsString()
  complementoDestinatario?: string;

  @IsOptional()
  @IsString()
  bairroDestinatario?: string;

  @IsOptional()
  @IsString()
  numeroEnderecoDestinatario?: string;

  @IsOptional()
  @IsString()
  dataEstimadaChegada?: string;

  @IsOptional()
  @IsString()
  codigoAfiliado?: string;

  @IsOptional()
  @IsString()
  nomeAfiliado?: string;

  @IsOptional()
  @IsString()
  emailAfiliado?: string;

  @IsOptional()
  @IsString()
  documentoAfiliado?: string;

  @IsOptional()
  @IsString()
  dataRecebimento?: string;

  @IsOptional()
  @IsString()
  dataNegociacao?: string;

  @IsOptional()
  @IsString()
  formaPagamento?: string;

  @IsOptional()
  @IsString()
  documentoCliente?: string;

  @IsOptional()
  @IsBoolean()
  parcial?: boolean;

  @IsOptional()
  @IsNumber()
  pagamentoParcial?: number;

  @IsOptional()
  @IsString()
  formaPagamentoParcial?: string;

  @IsOptional()
  @IsString()
  dataPagamentoParcial?: string;

  @IsOptional()
  @IsString()
  observacao?: string;
}