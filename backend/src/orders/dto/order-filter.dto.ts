import { IsOptional, IsEnum, IsDateString, IsUUID, IsBoolean, IsString } from 'class-validator';
import { OrderStatus } from '@prisma/client';

export class OrderFilterDto {
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @IsOptional()
  @IsUUID()
  sellerId?: string;

  @IsOptional()
  @IsUUID()
  collectorId?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsBoolean()
  includeDeleted?: boolean; // When true, includes deleted orders in results (admin only)

  @IsOptional()
  @IsString()
  search?: string; // Search by customer name or order number
}