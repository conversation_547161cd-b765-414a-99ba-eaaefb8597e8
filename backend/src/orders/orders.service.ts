import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import { ImportOrderDto } from './dto/import-order.dto';
import { Order, OrderStatus, Prisma, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { NotificationsService } from '../notifications/notifications.service';
import { AntifraudService } from '../antifraud/antifraud.service';
import { EncryptionUtil } from '../common/utils/encryption.util';
import { StatusMapper } from '../common/utils/status-mapper';
// Removed transformation utility - returning raw data now
// import * as crypto from 'crypto'; // Temporarily disabled for simplified delete

@Injectable()
export class OrdersService {
  constructor(
    private prisma: PrismaService,
    private notificationsService: NotificationsService,
    private antifraudService: AntifraudService,
    private encryptionUtil: EncryptionUtil,
  ) {}


  /**
   * Add computed fields for frontend display
   */
  private async addComputedFields(order: any): Promise<any> {
    // Create a copy to avoid mutating the original
    const result = { ...order };
    
    // Convert status enum to display value for frontend
    if (result.status && StatusMapper.isEnumValue(result.status)) {
      result.status = StatusMapper.toDisplay(result.status as OrderStatus);
    }
    
    // Add computed seller and collector names
    if (order.seller) {
      result.sellerName = order.seller.name;
    }
    if (order.collector) {
      result.collectorName = order.collector.name;
    }
    
    // Get last correios description update from webhook logs
    try {
      const lastCorreiosUpdate = await this.prisma.webhookLog.findFirst({
        where: {
          orderId: order.id,
          OR: [
            { payload: { path: ['correios_description'], not: Prisma.JsonNull } },
            { payload: { path: ['sale', 'correios_description'], not: Prisma.JsonNull } }
          ]
        },
        orderBy: { receivedAt: 'desc' },
        select: { receivedAt: true }
      });
      
      if (lastCorreiosUpdate && lastCorreiosUpdate.receivedAt) {
        // Calculate relative time for correios update
        result.lastCorreiosUpdateTime = this.getRelativeTime(lastCorreiosUpdate.receivedAt);
      }
    } catch (err) {
      console.error('Error fetching last correios update:', err);
    }
    
    return result;
  }
  
  /**
   * Get relative time string in Portuguese
   */
  private getRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) {
      return 'agora mesmo';
    } else if (diffMins < 60) {
      return `${diffMins} ${diffMins === 1 ? 'minuto' : 'minutos'} atrás`;
    } else if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? 'hora' : 'horas'} atrás`;
    } else if (diffDays < 7) {
      return `${diffDays} ${diffDays === 1 ? 'dia' : 'dias'} atrás`;
    } else {
      // Format date as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    }
  }

  private async generateOrderNumber(tenantId: string): Promise<string> {
    // Use a transaction to prevent race conditions
    return await this.prisma.$transaction(async (prisma) => {
      // Get the last order number for this tenant
      const lastOrder = await prisma.order.findFirst({
        where: {
          tenantId,
          orderNumber: {
            not: null
          }
        },
        orderBy: {
          createdAt: 'desc',
        },
        select: {
          orderNumber: true,
        },
      });

      let nextNumber = 1;
      
      if (lastOrder && lastOrder.orderNumber) {
        // Extract the numeric part from the last order number
        const matches = lastOrder.orderNumber.match(/ID(\d+)/);
        if (matches && matches[1]) {
          nextNumber = parseInt(matches[1], 10) + 1;
        }
      }

      // Generate the new order number
      const newOrderNumber = `ID${nextNumber.toString().padStart(5, '0')}`;

      // Verify it doesn't exist (double-check for race conditions)
      const existingOrder = await prisma.order.findFirst({
        where: {
          tenantId,
          orderNumber: newOrderNumber
        }
      });

      if (existingOrder) {
        // If it exists, try the next number
        return `ID${(nextNumber + 1).toString().padStart(5, '0')}`;
      }

      return newOrderNumber;
    });
  }

  async create(createOrderDto: CreateOrderDto, sellerId: string): Promise<Order> {
    // Get tenant ID from seller
    const seller = await this.prisma.user.findUnique({
      where: { id: sellerId },
      select: { tenantId: true },
    });
    
    if (!seller || !seller.tenantId) {
      throw new BadRequestException('Invalid seller or tenant');
    }
    
    const tenantId = seller.tenantId;

    // If no collector (operator) is provided, assign one using round-robin
    let collectorId = createOrderDto.collectorId;
    if (!collectorId) {
      // Get all active collectors
      const activeCollectors = await this.prisma.user.findMany({
        where: {
          tenantId,
          role: Role.COBRADOR,
          active: true,
        },
        orderBy: {
          name: 'asc', // Consistent ordering by name
        },
      });
      
      if (activeCollectors.length > 0) {
        // Get the count of orders for each collector today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const orderCounts = await this.prisma.order.groupBy({
          by: ['collectorId'],
          where: {
            tenantId,
            collectorId: { in: activeCollectors.map(c => c.id) },
            createdAt: { gte: today },
          },
          _count: {
            id: true,
          },
        });
        
        // Create a map of collector ID to order count
        const countMap = new Map<string, number>();
        orderCounts.forEach(item => {
          if (item.collectorId) {
            countMap.set(item.collectorId, item._count.id);
          }
        });
        
        // Find the collector with the least orders today
        let selectedCollector = activeCollectors[0];
        let minOrders = countMap.get(selectedCollector.id) || 0;
        
        for (const collector of activeCollectors) {
          const orderCount = countMap.get(collector.id) || 0;
          if (orderCount < minOrders) {
            selectedCollector = collector;
            minOrders = orderCount;
          }
        }
        
        collectorId = selectedCollector.id;
        console.log(`Assigned order to collector ${selectedCollector.name} (${minOrders} orders today)`);
      }
    }

    // Gerar número do pedido
    let orderNumber = await this.generateOrderNumber(seller.tenantId);
    
    // Calcular total do pedido
    const total = createOrderDto.items.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0,
    );

    // Build full address if provided
    let fullAddress = createOrderDto.fullAddress;
    if (!fullAddress && createOrderDto.address) {
      const addr = createOrderDto.address;
      fullAddress = `${addr.street}, ${addr.number}${addr.complement ? ' ' + addr.complement : ''}, ${addr.neighborhood}, ${addr.city} - ${addr.state}, CEP ${addr.cep}`;
    }

    // Criar pedido com itens e histórico inicial com retry logic
    let retries = 0;
    const maxRetries = 3;
    let order: Order | null = null;

    while (retries < maxRetries) {
      try {
        order = await this.prisma.order.create({
      data: {
        orderNumber,
        customerId: createOrderDto.customerId,
        customerName: createOrderDto.customerName,
        customerPhone: createOrderDto.customerPhone,
        customerCPF: createOrderDto.customerCPF,
        customerCPFHash: createOrderDto.customerCPF ? this.encryptionUtil.hash(createOrderDto.customerCPF) : null,
        total: new Decimal(total),
        sellerId,
        tenantId,
        collectorId: collectorId, // Use the assigned collector ID
        zapId: createOrderDto.zapId,
        fullAddress,
        observation: createOrderDto.observation,
        items: {
          create: createOrderDto.items.map((item) => ({
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            unitPrice: new Decimal(item.unitPrice),
          })),
        },
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Analise,
            changedById: sellerId,
          },
        },
        // Create address components if provided
        ...(createOrderDto.address && {
          addressComponents: {
            create: {
              street: createOrderDto.address.street,
              streetNumber: createOrderDto.address.number,
              complement: createOrderDto.address.complement,
              neighborhood: createOrderDto.address.neighborhood,
              city: createOrderDto.address.city,
              state: createOrderDto.address.state,
              zipCode: createOrderDto.address.cep,
              // Normalized fields - for now just use the same values
              streetNormalized: createOrderDto.address.street.toUpperCase(),
              streetSoundex: createOrderDto.address.street.substring(0, 4).toUpperCase(),
              streetMetaphone: createOrderDto.address.street.substring(0, 4).toUpperCase(),
              neighborhoodNorm: createOrderDto.address.neighborhood.toUpperCase(),
              neighborhoodSoundex: createOrderDto.address.neighborhood.substring(0, 4).toUpperCase(),
              cityNormalized: createOrderDto.address.city.toUpperCase(),
            },
          },
        }),
      },
      include: {
        items: true,
        customer: true,
        addressComponents: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

        break; // Success, exit the retry loop
      } catch (error) {
        if (error.code === 'P2002' && error.meta?.target?.includes('orderNumber')) {
          // Order number conflict - instead of retrying, create with a temporary unique number
          // and mark it for anti-fraud review
          retries = maxRetries; // Exit retry loop
          
          // Generate a unique temporary order number with timestamp
          const timestamp = Date.now();
          const tempOrderNumber = `${orderNumber}-DUP-${timestamp}`;
          
          console.log(`Order number conflict detected. Creating with temporary number: ${tempOrderNumber}`);
          
          try {
            // Create the order with the temporary number
            order = await this.prisma.order.create({
              data: {
                orderNumber: tempOrderNumber,
                customerId: createOrderDto.customerId,
                customerName: createOrderDto.customerName,
                customerPhone: createOrderDto.customerPhone,
                total: new Decimal(total),
                sellerId,
                tenantId,
                collectorId: collectorId,
                zapId: createOrderDto.zapId,
                fullAddress,
                observation: createOrderDto.observation,
                // Mark as requiring review due to duplicate number
                requiresReview: true,
                status: OrderStatus.Analise,
                items: {
                  create: createOrderDto.items.map((item) => ({
                    productId: item.productId,
                    productName: item.productName,
                    quantity: item.quantity,
                    unitPrice: new Decimal(item.unitPrice),
                  })),
                },
                statusHistory: {
                  create: {
                    previousStatus: OrderStatus.Analise,
                    newStatus: OrderStatus.Analise,
                    changedById: sellerId,
                  },
                },
                ...(createOrderDto.address && {
                  addressComponents: {
                    create: {
                      street: createOrderDto.address.street,
                      streetNumber: createOrderDto.address.number,
                      complement: createOrderDto.address.complement,
                      neighborhood: createOrderDto.address.neighborhood,
                      city: createOrderDto.address.city,
                      state: createOrderDto.address.state,
                      zipCode: createOrderDto.address.cep,
                      streetNormalized: createOrderDto.address.street.toUpperCase(),
                      streetSoundex: createOrderDto.address.street.substring(0, 4).toUpperCase(),
                      streetMetaphone: createOrderDto.address.street.substring(0, 4).toUpperCase(),
                      neighborhoodNorm: createOrderDto.address.neighborhood.toUpperCase(),
                      neighborhoodSoundex: createOrderDto.address.neighborhood.substring(0, 4).toUpperCase(),
                      cityNormalized: createOrderDto.address.city.toUpperCase(),
                    },
                  },
                }),
              },
              include: {
                items: true,
                customer: true,
                addressComponents: true,
                seller: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
                collector: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            });
            
            // The order is already marked with requiresReview: true
            // Log the duplicate for audit purposes
            console.log(`[ANTIFRAUD] Duplicate order number detected:`, {
              orderId: order.id,
              originalOrderNumber: orderNumber,
              temporaryOrderNumber: tempOrderNumber,
              tenantId,
              action: 'ORDER_CREATED_WITH_DUPLICATE_NUMBER'
            });
            
            break; // Success with temporary number
          } catch (tempError) {
            console.error('Failed to create order with temporary number:', tempError);
            throw new Error('Erro ao criar pedido. Por favor, tente novamente.');
          }
        }
        // If it's not an order number conflict, throw the error
        throw error;
      }
    }

    if (!order) {
      throw new Error('Failed to create order after multiple retries');
    }

    // Run anti-fraud checks - WAIT for them to complete before returning
    try {
      // 1. ALWAYS check for duplicates using all available data
      await this.antifraudService.processOrderForComprehensiveDuplicateCheck(
        tenantId,
        order.id,
        {
          customerCPF: createOrderDto.customerCPF,
          fullAddress: fullAddress || createOrderDto.fullAddress,
          customerPhone: createOrderDto.customerPhone,
          customerName: createOrderDto.customerName,
        },
      );

      // 2. Assess risk score for all orders
      await this.antifraudService.assessOrderRisk(order.id, tenantId);
    } catch (err) {
      console.error('Error in anti-fraud processing:', err);
      // Don't block order creation, but log the error
    }

    // Envia notificação de pedido criado (async, não bloqueia)
    this.notificationsService.notifyOrderCreated(order).catch(err => {
      console.error('Erro ao enviar notificação de pedido criado:', err);
    });

    // Fetch updated order with anti-fraud data
    const updatedOrder = await this.prisma.order.findUnique({
      where: { id: order.id },
      include: {
        items: true,
        customer: true,
        addressComponents: true,
        tracking: true,
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Return updated order with anti-fraud data
    const orderToReturn = updatedOrder || order;
    return await this.addComputedFields(orderToReturn);
  }

  async findAll(
    filters: OrderFilterDto,
    userId: string,
    userRole: Role,
  ): Promise<Order[]> {
    console.log('[OrdersService.findAll] Called with:', { filters, userId, userRole });
    
    // Get user's tenantId for filtering
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { tenantId: true },
    });
    
    if (!user || !user.tenantId) {
      throw new BadRequestException('Invalid user or tenant');
    }
    
    const where: Prisma.OrderWhereInput = {
      tenantId: user.tenantId, // CRITICAL: Filter by tenant to prevent data leakage
    };
    
    // Handle deleted orders based on includeDeleted parameter and user role
    if (filters.includeDeleted && (userRole === Role.ADMIN || userRole === Role.SUPERVISOR)) {
      // Admin and Supervisor want to see ALL orders including deleted ones
      // Don't add any deletedAt filter - this will return all orders
      console.log('[OrdersService.findAll] Admin/Supervisor viewing all orders including deleted');
    } else {
      // Non-admins or when includeDeleted is false/undefined
      // Only show non-deleted orders
      where.deletedAt = null;
    }

    // Aplicar filtros
    if (filters.status) {
      // Convert display status to enum value if needed
      where.status = StatusMapper.toEnum(filters.status);
      console.log('[OrdersService.findAll] Filtering by status:', filters.status, '=> enum:', where.status);
    }

    if (filters.sellerId) {
      where.sellerId = filters.sellerId;
    }

    if (filters.collectorId) {
      where.collectorId = filters.collectorId;
    }

    // Apply search filter for customer name or order number
    if (filters.search) {
      where.OR = [
        {
          customerName: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          orderNumber: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
      ];
    }

    if (filters.startDate || filters.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    // Aplicar restrições por role
    if (userRole === Role.VENDEDOR) {
      where.sellerId = userId;
      console.log('[OrdersService.findAll] VENDEDOR role - filtering by sellerId:', userId);
    } else if (userRole === Role.COBRADOR) {
      where.collectorId = userId;
      console.log('[OrdersService.findAll] COBRADOR role - filtering by collectorId:', userId);
    }
    // ADMIN e SUPERVISOR veem todos
    
    console.log('[OrdersService.findAll] Final where clause:', JSON.stringify(where, null, 2));

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        items: true,
        addressComponents: true,
        tracking: true, // Include tracking data
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    
    console.log('[OrdersService.findAll] Found', orders.length, 'orders');
    if (orders.length > 0) {
      console.log('[OrdersService.findAll] Sample order:', {
        id: orders[0].id,
        orderNumber: orders[0].orderNumber,
        status: orders[0].status,
        sellerId: orders[0].sellerId,
        collectorId: orders[0].collectorId
      });
    }

    // Return orders with seller and collector relations
    return await Promise.all(orders.map(order => this.addComputedFields(order)));
  }

  async findOne(id: string, userId: string, userRole: Role): Promise<Order> {
    const order = await this.prisma.order.findFirst({
      where: { 
        id,
        deletedAt: null // Exclude soft-deleted orders
      },
      include: {
        items: true,
        addressComponents: true,
        tracking: true, // Include tracking data
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        statusHistory: {
          include: {
            changedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            changedAt: 'desc',
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Verificar permissões
    if (userRole === Role.VENDEDOR && order.sellerId !== userId) {
      throw new ForbiddenException('Você não tem permissão para ver este pedido');
    }

    if (userRole === Role.COBRADOR && order.collectorId !== userId) {
      throw new ForbiddenException('Você não tem permissão para ver este pedido');
    }

    // Return order with seller and collector relations
    return await this.addComputedFields(order);
  }

  async updateStatus(
    id: string,
    updateStatusDto: UpdateStatusDto,
    userId: string,
    userRole: Role,
  ): Promise<Order> {
    // Convert display status to enum if needed
    const statusEnum = StatusMapper.toEnum(updateStatusDto.status);
    
    // Buscar pedido atual
    const order = await this.prisma.order.findUnique({
      where: { id },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Verificar permissões
    if (userRole === Role.VENDEDOR) {
      throw new ForbiddenException('Vendedores não podem alterar status de pedidos');
    }

    if (userRole === Role.COBRADOR) {
      // Cobradores só podem alterar pedidos atribuídos a eles
      if (order.collectorId !== userId) {
        throw new ForbiddenException('Você não tem permissão para alterar este pedido');
      }
      // Cobradores podem alterar para os seguintes status (using enum values)
      const allowedStatusesForCollector: OrderStatus[] = [
        OrderStatus.Completo,
        OrderStatus.Parcial,
        OrderStatus.Frustrado,
        OrderStatus.EntregaFalha,
        OrderStatus.Recuperacao,
        OrderStatus.Negociacao,
        OrderStatus.Promessa,
        OrderStatus.Transito,
        OrderStatus.PagamentoPendente,
        OrderStatus.ConfirmarEntrega
      ];
      
      if (!allowedStatusesForCollector.includes(statusEnum)) {
        throw new ForbiddenException('Cobradores só podem alterar para status permitidos');
      }
    }

    // Validar transição de status (skip for ADMIN and COBRADOR)
    if (userRole !== Role.ADMIN && userRole !== Role.COBRADOR) {
      if (!this.isValidStatusTransition(order.status, statusEnum)) {
        // Get valid transitions for current status
        const validTransitions = this.getValidTransitionsForStatus(order.status);
        const validStatusNames = validTransitions.map(s => StatusMapper.toDisplay(s)).join(', ');
        
        throw new BadRequestException(
          `Transição inválida de ${StatusMapper.toDisplay(order.status)} para ${updateStatusDto.status}. Status válidos a partir de ${StatusMapper.toDisplay(order.status)}: ${validStatusNames || 'nenhum (status final)'}`,
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      status: statusEnum,
      statusHistory: {
        create: {
          previousStatus: order.status,
          newStatus: statusEnum,
          changedById: userId,
        },
      },
    };

    // Add observation if provided
    if (updateStatusDto.observation !== undefined) {
      updateData.observation = updateStatusDto.observation;
    }

    // Atualizar status e criar histórico
    const updatedOrder = await this.prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    // Envia notificação de mudança de status (async, não bloqueia)
    this.notificationsService.notifyStatusChanged(
      id,
      order.status,
      statusEnum,
    ).catch(err => {
      console.error('Erro ao enviar notificação de mudança de status:', err);
    });

    return await this.addComputedFields(updatedOrder);
  }

  async update(
    id: string,
    updateData: any,
    userId: string,
    userRole: Role,
  ): Promise<Order> {
    console.log('[OrdersService.update] Called with:', { id, updateData, userId, userRole });
    
    // Find the order first
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: {
        seller: true,
        collector: true,
      },
    });

    console.log('[OrdersService.update] Found order:', order ? 'YES' : 'NO');
    if (order) {
      console.log('[OrdersService.update] Order details:', {
        id: order.id,
        tenantId: order.tenantId,
        customerName: order.customerName,
        total: order.total?.toString(),
      });
    }

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Check permissions - ADMIN and SUPERVISOR can update orders
    if (userRole !== Role.ADMIN && userRole !== Role.SUPERVISOR) {
      throw new ForbiddenException('Você não tem permissão para editar pedidos');
    }

    // Prepare update data
    const dataToUpdate: any = {};
    
    // Always update the updatedAt field to ensure database recognizes the change
    dataToUpdate.updatedAt = new Date();

    // Update basic fields if provided
    if (updateData.customerName !== undefined) {
      dataToUpdate.customerName = updateData.customerName;
    }
    if (updateData.customerPhone !== undefined) {
      dataToUpdate.customerPhone = updateData.customerPhone;
    }
    if (updateData.fullAddress !== undefined) {
      dataToUpdate.fullAddress = updateData.fullAddress;
    }
    if (updateData.total !== undefined) {
      dataToUpdate.total = new Decimal(updateData.total);
    }
    if (updateData.paymentReceivedAmount !== undefined) {
      dataToUpdate.paymentReceivedAmount = new Decimal(updateData.paymentReceivedAmount);
      
      // Automatically set status to Negociação for partial payments
      const newPaymentAmount = new Decimal(updateData.paymentReceivedAmount);
      const totalAmount = order.total;
      
      console.log('[OrdersService.update] Payment check:', {
        newPaymentAmount: newPaymentAmount.toString(),
        totalAmount: totalAmount.toString(),
        currentStatus: order.status,
        isPartialPayment: newPaymentAmount.greaterThan(0) && newPaymentAmount.lessThan(totalAmount)
      });
      
      if (newPaymentAmount.greaterThan(0) && newPaymentAmount.lessThan(totalAmount)) {
        // This is a partial payment, set status to Negociação if not already
        if (order.status !== OrderStatus.Negociacao && order.status !== OrderStatus.Completo) {
          console.log('[OrdersService.update] Partial payment detected from status:', order.status);
          console.log('[OrdersService.update] Automatically setting status to Negociação');
          dataToUpdate.status = OrderStatus.Negociacao;
          
          // Create status history entry for automatic status change
          dataToUpdate.statusHistory = {
            create: {
              previousStatus: order.status,
              newStatus: OrderStatus.Negociacao,
              changedById: userId,
            },
          };
        } else {
          console.log('[OrdersService.update] Status already Negociacao or Completo, skipping automatic change');
        }
      }
    }
    // Handle trackingCode update via Tracking model
    let trackingOperation: any = {};
    if (updateData.trackingCode !== undefined) {
      console.log('[OrdersService.update] Updating trackingCode:', updateData.trackingCode);
      
      if (updateData.trackingCode) {
        // Check if tracking exists
        const existingTracking = await this.prisma.tracking.findUnique({
          where: { orderId: id }
        });
        
        if (existingTracking) {
          // Update existing tracking
          trackingOperation = {
            tracking: {
              update: {
                code: updateData.trackingCode,
                lastUpdate: new Date()
              }
            }
          };
        } else {
          // Create new tracking
          trackingOperation = {
            tracking: {
              create: {
                code: updateData.trackingCode,
                status: 'Postado',
                lastUpdate: new Date(),
                events: []
              }
            }
          };
        }
      } else {
        // Delete tracking if code is empty
        trackingOperation = {
          tracking: {
            delete: true
          }
        };
      }
    }
    
    // Handle customerCPF if provided (no encryption needed)
    if (updateData.customerCPF !== undefined) {
      if (updateData.customerCPF) {
        dataToUpdate.customerCPF = updateData.customerCPF;
        dataToUpdate.customerCPFHash = this.encryptionUtil.hash(updateData.customerCPF);
      } else {
        dataToUpdate.customerCPF = null;
        dataToUpdate.customerCPFHash = null;
      }
    }
    
    // Handle paymentReceivedDate if provided
    if (updateData.paymentReceivedDate !== undefined) {
      dataToUpdate.paymentReceivedDate = updateData.paymentReceivedDate ? new Date(updateData.paymentReceivedDate) : null;
    }
    
    // Handle observation if provided
    if (updateData.observation !== undefined) {
      dataToUpdate.observation = updateData.observation;
    }
    
    // Update seller if provided
    if (updateData.sellerId !== undefined) {
      dataToUpdate.sellerId = updateData.sellerId;
    }
    
    // Update collector if provided
    if (updateData.collectorId !== undefined) {
      dataToUpdate.collectorId = updateData.collectorId;
    }
    
    // Handle nextPaymentDate if provided
    if (updateData.nextPaymentDate !== undefined) {
      dataToUpdate.nextPaymentDate = updateData.nextPaymentDate ? new Date(updateData.nextPaymentDate) : null;
    }

    // Update status if provided
    if (updateData.status !== undefined && updateData.status !== order.status) {
      // Check if status was already set by automatic partial payment logic
      const statusAlreadySet = dataToUpdate.status !== undefined;
      
      // For ADMIN, skip validation and allow any status change
      if (userRole === Role.ADMIN) {
        // Only update if not already set by automatic logic
        if (!statusAlreadySet) {
          // Convert display status to enum if needed
          const statusEnum = StatusMapper.toEnum(updateData.status);
          dataToUpdate.status = statusEnum;
          
          // Create status history entry
          dataToUpdate.statusHistory = {
            create: {
              previousStatus: order.status,
              newStatus: statusEnum,
              changedById: userId,
            },
          };
        }
      } else if (userRole === Role.SUPERVISOR || userRole === Role.COBRADOR) {
        // For SUPERVISOR and COBRADOR users, validate status transition
        if (!statusAlreadySet) {
          // Convert display status to enum if needed
          const statusEnum = StatusMapper.toEnum(updateData.status);
          
          if (!this.isValidStatusTransition(order.status, statusEnum)) {
            const validTransitions = this.getValidTransitionsForStatus(order.status);
            const validStatusNames = validTransitions.map(s => StatusMapper.toDisplay(s)).join(', ');
            
            throw new BadRequestException(
              `Transição inválida de ${StatusMapper.toDisplay(order.status)} para ${updateData.status}. Status válidos: ${validStatusNames || 'nenhum'}`,
            );
          }
          
          dataToUpdate.status = statusEnum;
          
          // Create status history entry
          dataToUpdate.statusHistory = {
            create: {
              previousStatus: order.status,
              newStatus: statusEnum,
              changedById: userId,
            },
          };
        }
      }
    }

    // Log what we're about to update
    console.log('[OrdersService.update] dataToUpdate:', JSON.stringify(dataToUpdate, null, 2));
    
    // Check if there's anything to update (besides updatedAt)
    if (Object.keys(dataToUpdate).length === 1 && dataToUpdate.updatedAt) {
      console.log('[OrdersService.update] No fields to update (only updatedAt), returning current order');
      return await this.addComputedFields(order);
    }
    
    console.log('[OrdersService.update] Fields to update:', Object.keys(dataToUpdate).join(', '));
    
    // Use a transaction to ensure data consistency
    try {
      const result = await this.prisma.$transaction(async (prisma) => {
        console.log('[OrdersService.update] Starting transaction...');
        console.log('[OrdersService.update] Update query - where:', { id });
        console.log('[OrdersService.update] Update query - data:', JSON.stringify(dataToUpdate, (key, value) => {
          if (value && typeof value === 'object' && value.constructor.name === 'Decimal') {
            return value.toString();
          }
          return value;
        }, 2));
        
        // Update the order
        const updatedOrder = await prisma.order.update({
          where: { id },
          data: Object.keys(trackingOperation).length > 0 
            ? { ...dataToUpdate, ...trackingOperation }
            : dataToUpdate,
          include: {
            items: true,
            tracking: true, // Include tracking for trackingCode backward compatibility
            seller: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            collector: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            statusHistory: {
              include: {
                changedBy: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
              orderBy: {
                changedAt: 'desc',
              },
            },
          },
        });
        
        console.log('[OrdersService.update] Update completed in transaction. Order ID:', updatedOrder.id);
        console.log('[OrdersService.update] Updated values - total:', updatedOrder.total?.toString(), 'paymentReceivedAmount:', updatedOrder.paymentReceivedAmount?.toString());
        
        return updatedOrder;
      }, {
        maxWait: 5000, // 5 seconds max wait
        timeout: 10000, // 10 seconds timeout
      });
      
      // Verify the update by re-querying the order outside the transaction
      const verifyOrder = await this.prisma.order.findUnique({
        where: { id },
        select: {
          id: true,
          customerName: true,
          total: true,
          paymentReceivedAmount: true,
          fullAddress: true,
          sellerId: true,
          collectorId: true,
        }
      });
      
      console.log('[OrdersService.update] Verification query - order from DB:', JSON.stringify({
        ...verifyOrder,
        total: verifyOrder?.total?.toString(),
        paymentReceivedAmount: verifyOrder?.paymentReceivedAmount?.toString(),
      }, null, 2));
      
      // Double check by querying with a fresh connection
      const freshVerify = await this.prisma.$queryRaw`
        SELECT id, "customerName", total, "paymentReceivedAmount", "fullAddress", "sellerId", "collectorId"
        FROM "Order"
        WHERE id = ${id}
      `;
      
      console.log('[OrdersService.update] Raw SQL verification:', freshVerify);
      
      // One more check - let's see what's actually in the database
      const finalCheck = await this.prisma.order.findUnique({
        where: { id },
        include: {
          items: true,
          tracking: true,
          seller: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          collector: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          statusHistory: {
            include: {
              changedBy: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              changedAt: 'desc',
            },
          },
        },
      });
      
      console.log('[OrdersService.update] Final check - returning order with customerName:', finalCheck?.customerName);
      
      return await this.addComputedFields(finalCheck || result);
    } catch (error) {
      console.error('[OrdersService.update] Transaction failed:', error);
      console.error('[OrdersService.update] Error details:', {
        message: error.message,
        code: error.code,
        meta: error.meta,
      });
      
      // Check if it's a Prisma error
      if (error.code === 'P2002') {
        throw new BadRequestException('Duplicate value error');
      } else if (error.code === 'P2025') {
        throw new NotFoundException('Record not found');
      }
      
      throw error;
    }
  }

  async cancel(id: string, userId: string, userRole: Role): Promise<Order> {
    const order = await this.prisma.order.findUnique({
      where: { id },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Apenas ADMIN e SUPERVISOR podem cancelar pedidos
    if (userRole === Role.VENDEDOR) {
      throw new ForbiddenException('Vendedores não podem cancelar pedidos');
    } else if (userRole === Role.COBRADOR) {
      throw new ForbiddenException('Cobradores não podem cancelar pedidos');
    } else if (userRole === Role.SUPERVISOR && order.status !== OrderStatus.Analise) {
      throw new BadRequestException('Supervisores só podem cancelar pedidos em análise');
    }

    // Atualizar para CANCELADO
    const cancelledOrder = await this.prisma.order.update({
      where: { id },
      data: {
        status: OrderStatus.Cancelado,
        statusHistory: {
          create: {
            previousStatus: order.status,
            newStatus: OrderStatus.Cancelado,
            changedById: userId,
          },
        },
      },
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return cancelledOrder;
  }

  private getValidTransitionsForStatus(status: OrderStatus): OrderStatus[] {
    const validTransitions: { [key in OrderStatus]?: OrderStatus[] } = {
      Analise: [OrderStatus.Separacao, OrderStatus.Cancelado],
      Separacao: [OrderStatus.Transito, OrderStatus.Cancelado],
      Transito: [OrderStatus.ConfirmarEntrega, OrderStatus.EntregaFalha, OrderStatus.RetirarCorreios, OrderStatus.DevolvidoCorreios],
      ConfirmarEntrega: [OrderStatus.Completo, OrderStatus.PagamentoPendente, OrderStatus.Negociacao, OrderStatus.Promessa, OrderStatus.Parcial],
      PagamentoPendente: [OrderStatus.Completo, OrderStatus.Parcial, OrderStatus.Negociacao, OrderStatus.Promessa, OrderStatus.Frustrado],
      Negociacao: [OrderStatus.Recuperacao, OrderStatus.Cancelado, OrderStatus.Completo, OrderStatus.Parcial, OrderStatus.Frustrado, OrderStatus.Promessa],
      Promessa: [OrderStatus.Recuperacao, OrderStatus.Cancelado, OrderStatus.Completo, OrderStatus.Parcial, OrderStatus.Frustrado, OrderStatus.Negociacao],
      Parcial: [OrderStatus.Completo, OrderStatus.Negociacao],
      Completo: [], // Estado final
      Recuperacao: [OrderStatus.Transito, OrderStatus.Negociacao, OrderStatus.Cancelado, OrderStatus.Completo, OrderStatus.Parcial],
      Frustrado: [], // Estado final
      EntregaFalha: [OrderStatus.Recuperacao, OrderStatus.Negociacao, OrderStatus.Frustrado],
      RetirarCorreios: [OrderStatus.Frustrado, OrderStatus.Cancelado],
      DevolvidoCorreios: [OrderStatus.Frustrado, OrderStatus.Cancelado],
      Cancelado: [], // Estado final
    };

    return validTransitions[status] || [];
  }

  private isValidStatusTransition(from: OrderStatus, to: OrderStatus): boolean {
    const validTransitions = this.getValidTransitionsForStatus(from);
    return validTransitions.includes(to);
  }

  async softDelete(id: string, userId: string): Promise<Order> {
    const order = await this.prisma.order.findFirst({
      where: { 
        id,
        deletedAt: null // Ensure we're not deleting an already deleted order
      },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Soft delete the order
    const deletedOrder = await this.prisma.order.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        deletedBy: userId,
        // Also create an audit log entry
        statusHistory: {
          create: {
            previousStatus: order.status,
            newStatus: order.status, // Status doesn't change on soft delete
            changedById: userId,
          },
        },
      },
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    // Create audit log for deletion
    // For now, we'll skip the audit log creation since it's a private method in antifraud service
    // In a production environment, we would either:
    // 1. Make createAuditLog a public method in antifraud service
    // 2. Create a separate audit service
    // 3. Add audit log creation directly here

    return deletedOrder;
  }

  async updateObservation(
    id: string,
    observation: string,
    userId: string,
    userRole: Role,
  ): Promise<Order> {
    console.log('[OrdersService.updateObservation] Called with:', { id, observation, userId, userRole });
    
    // Find the order first
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: {
        seller: true,
        collector: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Check permissions - COBRADOR, ADMIN and SUPERVISOR can update observations
    if (userRole !== Role.ADMIN && userRole !== Role.SUPERVISOR && userRole !== Role.COBRADOR) {
      throw new ForbiddenException('Você não tem permissão para adicionar observações');
    }

    // For COBRADOR, check if they are assigned to this order
    if (userRole === Role.COBRADOR) {
      if (order.collectorId !== userId) {
        throw new ForbiddenException('Você só pode adicionar observações aos seus próprios pedidos');
      }
    }

    // Update only the observation field
    const updatedOrder = await this.prisma.order.update({
      where: { id },
      data: {
        observation: observation,
        updatedAt: new Date(),
      },
      include: {
        items: true,
        tracking: true,
        zapSource: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        statusHistory: {
          include: {
            changedBy: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            changedAt: 'desc',
          },
        },
      },
    });

    console.log('[OrdersService.updateObservation] Observation updated successfully');
    return await this.addComputedFields(updatedOrder);
  }

  async permanentDelete(id: string, userId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('[PermanentDelete] Starting simplified deletion for order:', id, 'by user:', userId);
      
      // First check if the order exists
      const order = await this.prisma.order.findFirst({
        where: { id },
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          tenantId: true,
        }
      });

      if (!order) {
        console.log('[PermanentDelete] Order not found with ID:', id);
        throw new NotFoundException('Pedido não encontrado');
      }
      
      console.log('[PermanentDelete] Order found:', order);

      // Simplified deletion without audit log for now
      console.log('[PermanentDelete] Starting transaction to delete order...');
      
      await this.prisma.$transaction(async (tx) => {
        // Delete related records first
        console.log('[PermanentDelete] Deleting related records...');
        
        // Delete order items
        const itemsDeleted = await tx.orderItem.deleteMany({ where: { orderId: id } });
        console.log('[PermanentDelete] Deleted items:', itemsDeleted.count);
        
        // Delete status history
        const statusDeleted = await tx.orderStatusHistory.deleteMany({ where: { orderId: id } });
        console.log('[PermanentDelete] Deleted status history:', statusDeleted.count);
        
        // Delete address components
        const addressDeleted = await tx.orderAddressComponents.deleteMany({ where: { orderId: id } });
        console.log('[PermanentDelete] Deleted address components:', addressDeleted.count);
        
        // Delete tracking
        const trackingDeleted = await tx.tracking.deleteMany({ where: { orderId: id } });
        console.log('[PermanentDelete] Deleted tracking:', trackingDeleted.count);
        
        // Delete audit logs
        const auditDeleted = await tx.orderAuditLog.deleteMany({ where: { orderId: id } });
        console.log('[PermanentDelete] Deleted audit logs:', auditDeleted.count);
        
        // Delete the order itself
        console.log('[PermanentDelete] Deleting order...');
        await tx.order.delete({ where: { id } });
        console.log('[PermanentDelete] Order deleted successfully');
      });

      console.log('[PermanentDelete] Transaction completed successfully');
      
      return {
        success: true,
        message: `Pedido ${order.orderNumber || id} foi permanentemente excluído`,
      };
    } catch (error: any) {
      console.error('[PermanentDelete] Error occurred:', error);
      console.error('[PermanentDelete] Error stack:', error.stack);
      console.error('[PermanentDelete] Error details:', {
        message: error.message,
        code: error.code,
        meta: error.meta,
        name: error.name
      });
      
      // Re-throw with more context
      throw new Error(`Falha ao excluir pedido: ${error.message}`);
    }
  }

  async checkOrderExists(orderNumber: string, tenantId: string): Promise<boolean> {
    const order = await this.prisma.order.findFirst({
      where: {
        orderNumber,
        tenantId,
      },
    });
    return !!order;
  }

  async bulkDelete(
    orderIds: string[],
    userId: string,
    tenantId: string,
    permanent: boolean = false,
  ): Promise<{ success: number; failed: number; details: any[] }> {
    const results = {
      success: 0,
      failed: 0,
      details: [] as any[],
    };

    // Process orders in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < orderIds.length; i += batchSize) {
      const batch = orderIds.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchResults = await Promise.allSettled(
        batch.map(async (orderId) => {
          try {
            // First verify the order belongs to the tenant
            const order = await this.prisma.order.findFirst({
              where: {
                id: orderId,
                tenantId: tenantId,
              },
            });

            if (!order) {
              throw new NotFoundException(`Order ${orderId} not found or access denied`);
            }

            if (permanent) {
              // Permanent delete
              await this.permanentDelete(orderId, userId);
            } else {
              // Soft delete
              await this.softDelete(orderId, userId);
            }

            return { orderId, success: true };
          } catch (error: any) {
            return { orderId, success: false, error: error.message };
          }
        }),
      );

      // Process results
      batchResults.forEach((result, index) => {
        const orderId = batch[index];
        if (result.status === 'fulfilled' && result.value.success) {
          results.success++;
          results.details.push({
            orderId,
            status: 'deleted',
            permanent,
          });
        } else {
          results.failed++;
          results.details.push({
            orderId,
            status: 'failed',
            error: result.status === 'rejected' 
              ? result.reason?.message || 'Unknown error'
              : result.value?.error || 'Unknown error',
          });
        }
      });
    }

    console.log(`[BulkDelete] Completed: ${results.success} deleted, ${results.failed} failed`);
    return results;
  }

  async importOrder(
    importOrderDto: ImportOrderDto,
    userId: string,
    tenantId: string,
  ): Promise<Order> {
    console.log('[ImportOrder] Starting import with data:', {
      idVenda: importOrderDto.idVenda,
      cliente: importOrderDto.cliente,
      telefone: importOrderDto.telefone,
      valorVenda: importOrderDto.valorVenda,
      tenantId,
      userId,
    });
    
    console.log('[ImportOrder] Full DTO received:', JSON.stringify(importOrderDto, null, 2));
    
    try {
    // Find or create seller by name
    let sellerId = userId; // Default to current user
    if (importOrderDto.vendedor) {
      const seller = await this.prisma.user.findFirst({
        where: {
          tenantId,
          name: importOrderDto.vendedor,
          role: Role.VENDEDOR,
        },
      });
      if (seller) {
        sellerId = seller.id;
      }
    }

    // Find collector by name
    let collectorId: string | undefined;
    if (importOrderDto.operador) {
      const collector = await this.prisma.user.findFirst({
        where: {
          tenantId,
          name: importOrderDto.operador,
          role: Role.COBRADOR,
        },
      });
      if (collector) {
        collectorId = collector.id;
      }
    }

    // Map status from frontend format to backend format
    let status: OrderStatus = OrderStatus.Analise; // Default
    console.log('[ImportOrder] Status mapping - situacaoVenda:', importOrderDto.situacaoVenda);
    console.log('[ImportOrder] Status mapping - status:', importOrderDto.status);
    
    // Check both situacaoVenda and status fields (CSV might have either)
    const statusValue = importOrderDto.situacaoVenda || importOrderDto.status;
    
    if (statusValue) {
      // Use StatusMapper for consistent status conversion
      status = StatusMapper.toEnum(statusValue);
      console.log('[ImportOrder] Mapped to OrderStatus:', status);
    } else {
      console.log('[ImportOrder] No situacaoVenda provided, using default Analise');
    }

    // Build address string
    let fullAddress = '';
    console.log('[ImportOrder] Address fields:', {
      rua: importOrderDto.ruaDestinatario,
      numero: importOrderDto.numeroEnderecoDestinatario,
      complemento: importOrderDto.complementoDestinatario,
      bairro: importOrderDto.bairroDestinatario,
      cidade: importOrderDto.cidadeDestinatario,
      estado: importOrderDto.estadoDestinatario,
      cep: importOrderDto.cepDestinatario,
    });
    
    if (importOrderDto.ruaDestinatario) {
      const parts = [
        importOrderDto.ruaDestinatario,
        importOrderDto.numeroEnderecoDestinatario,
        importOrderDto.complementoDestinatario,
        importOrderDto.bairroDestinatario,
        importOrderDto.cidadeDestinatario,
        importOrderDto.estadoDestinatario,
        importOrderDto.cepDestinatario ? `CEP ${importOrderDto.cepDestinatario}` : '',
      ].filter(Boolean);
      fullAddress = parts.join(', ');
      console.log('[ImportOrder] Built fullAddress:', fullAddress);
    }

    // Parse dates
    const parseDate = (dateStr?: string): Date | null => {
      if (!dateStr) return null;
      try {
        // Try different date formats
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) return date;
        
        // Try Brazilian format DD/MM/YYYY
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          const brazilDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
          if (!isNaN(brazilDate.getTime())) return brazilDate;
        }
      } catch {
        // Ignore parse errors
      }
      return null;
    };

    // Create order
    console.log('[ImportOrder] Creating order with processed data:', {
      orderNumber: importOrderDto.idVenda,
      customerName: importOrderDto.cliente,
      customerPhone: importOrderDto.telefone,
      customerCPF: importOrderDto.documentoCliente,
      total: importOrderDto.valorVenda,
      status,
      sellerId,
      collectorId,
      fullAddress,
      observation: importOrderDto.historico,
      valorRecebido: importOrderDto.valorRecebido,
    });
    
    const order = await this.prisma.order.create({
      data: {
        orderNumber: importOrderDto.idVenda,
        customerName: importOrderDto.cliente,
        customerPhone: importOrderDto.telefone?.replace(/\D/g, '') || '',
        customerCPF: importOrderDto.documentoCliente?.replace(/\D/g, ''),
        total: new Decimal(importOrderDto.valorVenda),
        status: status,
        sellerId,
        collectorId,
        tenantId,
        fullAddress,
        observation: importOrderDto.observacao || importOrderDto.historico,
        zapId: importOrderDto.zap, // Add WhatsApp field
        statusCorreios: importOrderDto.statusCorreios, // Add statusCorreios field
        // trackingCode field removed - will create tracking relation below
        paymentReceivedAmount: importOrderDto.valorRecebido
          ? new Decimal(importOrderDto.valorRecebido)
          : new Decimal(0),
        paymentReceivedDate: parseDate(importOrderDto.dataRecebimento) || // Use dataRecebimento for payment received date
          (importOrderDto.valorRecebido && importOrderDto.valorRecebido > 0 ? new Date() : undefined),
        lastContactDate: parseDate(importOrderDto.dataNegociacao), // Map dataNegociacao to lastContactDate
        createdAt: parseDate(importOrderDto.dataVenda) || new Date(),
        updatedAt: parseDate(importOrderDto.ultimaAtualizacao) || new Date(),
        // Create default item since we don't have product details
        items: {
          create: {
            productId: 'imported-product',
            productName: importOrderDto.oferta || 'Produto Importado',
            quantity: 1,
            unitPrice: new Decimal(importOrderDto.valorVenda),
          },
        },
        // Create address components if we have address data
        ...(importOrderDto.ruaDestinatario && {
          addressComponents: {
            create: {
              street: importOrderDto.ruaDestinatario,
              streetNumber: importOrderDto.numeroEnderecoDestinatario || '',
              complement: importOrderDto.complementoDestinatario,
              neighborhood: importOrderDto.bairroDestinatario || '',
              city: importOrderDto.cidadeDestinatario || '',
              state: importOrderDto.estadoDestinatario || '',
              zipCode: importOrderDto.cepDestinatario?.replace(/\D/g, '') || '',
              // Normalized fields
              streetNormalized: importOrderDto.ruaDestinatario.toUpperCase(),
              streetSoundex: importOrderDto.ruaDestinatario.substring(0, 4).toUpperCase(),
              streetMetaphone: importOrderDto.ruaDestinatario.substring(0, 4).toUpperCase(),
              neighborhoodNorm: (importOrderDto.bairroDestinatario || '').toUpperCase(),
              neighborhoodSoundex: (importOrderDto.bairroDestinatario || '').substring(0, 4).toUpperCase(),
              cityNormalized: (importOrderDto.cidadeDestinatario || '').toUpperCase(),
            },
          },
        }),
        // Create initial status history
        statusHistory: {
          create: {
            previousStatus: status,
            newStatus: status,
            changedById: sellerId,
          },
        },
        // Create tracking if tracking code is provided
        ...(importOrderDto.codigoRastreio && {
          tracking: {
            create: {
              code: importOrderDto.codigoRastreio,
              status: importOrderDto.statusCorreios || 'Postado',
              lastUpdate: parseDate(importOrderDto.ultimaAtualizacao) || new Date(),
              events: [],
              // Set delivery status based on statusCorreios
              isDelivered: importOrderDto.statusCorreios?.toLowerCase().includes('entregue') || false,
              hasAlert: importOrderDto.statusCorreios?.toLowerCase().includes('falha') || 
                       importOrderDto.statusCorreios?.toLowerCase().includes('retirar') || false,
            },
          },
        }),
      },
      include: {
        items: true,
        addressComponents: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        tracking: true,
      },
    });

    // Run anti-fraud checks asynchronously for imported orders
    // 1. Check for duplicates
    this.antifraudService.processOrderForComprehensiveDuplicateCheck(
      tenantId,
      order.id,
      {
        customerCPF: order.customerCPF || undefined,
        fullAddress: order.fullAddress || undefined,
        customerPhone: order.customerPhone,
        customerName: order.customerName,
      },
    ).catch(err => {
      console.error('[ImportOrder] Error processing duplicate check:', err);
      console.error('Order ID:', order.id);
    });

    // 2. Assess risk score
    this.antifraudService.assessOrderRisk(order.id, tenantId).catch(err => {
      console.error('[ImportOrder] Error assessing order risk:', err);
    });

    // Return order with seller and collector relations
    return order;
    } catch (error: any) {
      console.error('[ImportOrder] Error importing order:', {
        error: error.message,
        stack: error.stack,
        code: error.code,
        meta: error.meta,
        idVenda: importOrderDto.idVenda,
      });
      
      // Provide more specific error messages
      if (error.code === 'P2002') {
        throw new BadRequestException(`Pedido ${importOrderDto.idVenda} já existe`);
      }
      
      if (error.code === 'P2003') {
        throw new BadRequestException('Vendedor ou operador não encontrado');
      }
      
      throw new BadRequestException(`Erro ao importar pedido: ${error.message}`);
    }
  }

  async runAntifraudCheck(orderId: string): Promise<{ success: boolean; message: string }> {
    try {
      const order = await this.prisma.order.findUnique({
        where: { id: orderId },
        include: {
          addressComponents: true,
        }
      });

      if (!order) {
        throw new NotFoundException('Pedido não encontrado');
      }

      // Run comprehensive duplicate check
      await this.antifraudService.processOrderForComprehensiveDuplicateCheck(
        order.tenantId,
        order.id,
        {
          customerCPF: order.customerCPF || undefined,
          fullAddress: order.fullAddress || undefined,
          customerPhone: order.customerPhone,
          customerName: order.customerName,
        }
      );

      // Assess order risk
      await this.antifraudService.assessOrderRisk(order.id, order.tenantId);

      return {
        success: true,
        message: 'Verificação antifraude concluída com sucesso'
      };
    } catch (error) {
      console.error('Erro ao executar verificação antifraude:', error);
      throw new BadRequestException(`Erro na verificação antifraude: ${error.message}`);
    }
  }

  async findByOrderNumber(orderNumber: string): Promise<any> {
    const order = await this.prisma.order.findFirst({
      where: { orderNumber },
      include: {
        addressComponents: true,
        tracking: true,
      }
    });
    return order;
  }

  async getPendingPaymentConfirmations(tenantId: string) {
    console.log('[OrdersService] Getting pending payment confirmations for tenant:', tenantId);
    
    const orders = await this.prisma.order.findMany({
      where: {
        tenantId,
        paymentConfirmationStatus: 'PENDING',
        deletedAt: null,
      },
      include: {
        seller: {
          select: { id: true, name: true },
        },
        collector: {
          select: { id: true, name: true },
        },
      },
      orderBy: {
        paymentReceivedDate: 'desc',
      },
    });

    // Add computed fields to each order
    const ordersWithComputedFields = await Promise.all(
      orders.map(order => this.addComputedFields(order))
    );

    return ordersWithComputedFields;
  }

  async confirmPayment(
    orderId: string,
    userId: string,
    approve: boolean,
    reason?: string,
  ) {
    console.log('[OrdersService] Confirming payment:', {
      orderId,
      userId,
      approve,
      reason,
    });

    // Get user details
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { name: true, role: true },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get current order
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        seller: { select: { id: true, name: true } },
        collector: { select: { id: true, name: true } },
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Validate that order is pending payment confirmation
    if (order.paymentConfirmationStatus !== 'PENDING') {
      throw new BadRequestException('Order is not pending payment confirmation');
    }

    // Determine new status based on approval
    let newStatus: OrderStatus;
    let paymentConfirmationStatus: string;
    
    if (approve) {
      newStatus = order.status; // Keep current status if approved
      paymentConfirmationStatus = 'APPROVED';
    } else {
      newStatus = OrderStatus.ConfirmarPagamento;
      paymentConfirmationStatus = 'DENIED';
    }

    // Update order
    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: newStatus,
        paymentConfirmationStatus,
        paymentConfirmedBy: userId,
        paymentConfirmationDate: new Date(),
        paymentDenialReason: approve ? null : reason,
      },
      include: {
        seller: { select: { id: true, name: true } },
        collector: { select: { id: true, name: true } },
      },
    });

    // Create status history entry
    await this.prisma.orderStatusHistory.create({
      data: {
        orderId,
        previousStatus: order.status,
        newStatus,
        changedById: userId,
      },
    });

    // Create payment approval record
    await this.prisma.paymentApproval.create({
      data: {
        orderId,
        approvedById: userId,
        previousStatus: order.status,
        newStatus,
        paymentAmount: order.paymentReceivedAmount || new Decimal(0),
        paymentType: approve ? 'FULL' : 'DENIED',
        approvalStatus: approve ? 'APPROVED' : 'REJECTED',
        rejectionReason: reason,
      },
    });

    // If payment was denied, recalculate commissions
    if (!approve) {
      await this.recalculateCommissions(orderId);
    }

    // Send notification
    await this.notificationsService.createNotification(
      'STATUS_CHANGED',
      'WHATSAPP',
      {
        phone: order.customerPhone,
        name: order.customerName,
      },
      {
        orderId,
        orderNumber: order.orderNumber,
        previousStatus: order.status,
        newStatus,
        paymentConfirmationStatus,
        confirmedBy: user.name,
        reason,
      },
    );

    return this.addComputedFields(updatedOrder);
  }

  private async recalculateCommissions(orderId: string) {
    console.log('[OrdersService] Recalculating commissions for order:', orderId);

    // Delete existing commission payments for this order
    await this.prisma.commissionPayment.deleteMany({
      where: { orderId },
    });

    // Get order details
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        seller: true,
        collector: true,
      },
    });

    if (!order) {
      return;
    }

    // Only calculate commissions for approved payments
    if (order.paymentConfirmationStatus === 'APPROVED' && order.paymentReceivedAmount) {
      // Calculate seller commission
      if (order.seller && order.seller.commissionRate) {
        const sellerCommission = order.paymentReceivedAmount
          .mul(order.seller.commissionRate)
          .div(100);

        await this.prisma.commissionPayment.create({
          data: {
            orderId,
            userId: order.sellerId,
            userRole: order.seller.role,
            baseAmount: order.paymentReceivedAmount,
            percentage: order.seller.commissionRate,
            commissionAmount: sellerCommission,
            paymentDate: order.paymentReceivedDate || new Date(),
          },
        });
      }

      // Calculate collector commission if different from seller
      if (order.collector && order.collectorId && order.collectorId !== order.sellerId && order.collector.commissionRate) {
        const collectorCommission = order.paymentReceivedAmount
          .mul(order.collector.commissionRate)
          .div(100);

        await this.prisma.commissionPayment.create({
          data: {
            orderId,
            userId: order.collectorId,
            userRole: order.collector.role,
            baseAmount: order.paymentReceivedAmount,
            percentage: order.collector.commissionRate,
            commissionAmount: collectorCommission,
            paymentDate: order.paymentReceivedDate || new Date(),
          },
        });
      }
    }
  }
}