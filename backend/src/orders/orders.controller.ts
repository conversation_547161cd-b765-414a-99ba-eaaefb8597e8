import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { SkipThrottle } from '@nestjs/throttler';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import { ImportOrderDto } from './dto/import-order.dto';
import { BulkDeleteOrdersDto } from './dto/bulk-delete.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('orders')
@UseGuards(JwtAuthGuard, RolesGuard)
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(@Body() createOrderDto: CreateOrderDto, @Request() req) {
    return this.ordersService.create(createOrderDto, req.user.userId);
  }

  @Get('check/:orderNumber')
  @Roles('VENDEDOR', 'ADMIN', 'SUPERVISOR')
  @SkipThrottle() // Skip rate limiting for check endpoint as it's used during import
  async checkExists(@Param('orderNumber') orderNumber: string, @Request() req) {
    const exists = await this.ordersService.checkOrderExists(orderNumber, req.user.tenantId);
    return { exists };
  }

  @Post('import')
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @SkipThrottle() // Skip rate limiting for import endpoint as we handle it client-side
  async importOrder(@Body() importOrderDto: ImportOrderDto, @Request() req) {
    return this.ordersService.importOrder(importOrderDto, req.user.userId, req.user.tenantId);
  }

  @Get()
  @Roles('VENDEDOR', 'COBRADOR', 'ADMIN', 'SUPERVISOR')
  async findAll(@Query(ValidationPipe) filters: OrderFilterDto, @Request() req) {
    console.log('[OrdersController.findAll] Called with filters:', filters);
    console.log('[OrdersController.findAll] User:', { userId: req.user.userId, role: req.user.role });
    
    const orders = await this.ordersService.findAll(filters, req.user.userId, req.user.role);
    
    console.log('[OrdersController.findAll] Returning', orders.length, 'orders');
    if (orders.length > 0) {
      console.log('[OrdersController.findAll] Order statuses:', orders.map(o => ({ id: o.id, status: o.status })));
    }
    
    return orders;
  }

  @Get(':id')
  @Roles('VENDEDOR', 'COBRADOR', 'ADMIN', 'SUPERVISOR')
  async findOne(@Param('id') id: string, @Request() req) {
    return this.ordersService.findOne(id, req.user.userId, req.user.role);
  }

  @Patch(':id/status')
  @Roles('COBRADOR', 'ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateStatusDto,
    @Request() req,
  ) {
    return this.ordersService.updateStatus(
      id,
      updateStatusDto,
      req.user.userId,
      req.user.role,
    );
  }

  @Patch(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateData: any,
    @Request() req,
  ) {
    console.log('[OrdersController] PATCH /orders/:id called');
    console.log('[OrdersController] Order ID:', id);
    console.log('[OrdersController] Update data:', JSON.stringify(updateData, null, 2));
    console.log('[OrdersController] User:', { userId: req.user.userId, role: req.user.role });
    
    try {
      const result = await this.ordersService.update(id, updateData, req.user.userId, req.user.role);
      console.log('[OrdersController] Update successful');
      return result;
    } catch (error) {
      console.error('[OrdersController] Update error:', error);
      console.error('[OrdersController] Error stack:', error.stack);
      throw error;
    }
  }

  @Patch(':id/observation')
  @Roles('COBRADOR', 'ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async updateObservation(
    @Param('id') id: string,
    @Body('observation') observation: string,
    @Request() req,
  ) {
    console.log('[OrdersController] PATCH /orders/:id/observation called');
    console.log('[OrdersController] Order ID:', id);
    console.log('[OrdersController] Observation:', observation);
    console.log('[OrdersController] User:', { userId: req.user.userId, role: req.user.role });
    
    try {
      const result = await this.ordersService.updateObservation(id, observation, req.user.userId, req.user.role);
      console.log('[OrdersController] Observation update successful');
      return result;
    } catch (error) {
      console.error('[OrdersController] Observation update error:', error);
      console.error('[OrdersController] Error stack:', error.stack);
      throw error;
    }
  }

  // Test endpoint to verify database updates
  @Get(':id/test-update')
  @Roles('ADMIN')
  async testUpdate(@Param('id') id: string) {
    console.log('[OrdersController] Testing database update for order:', id);
    
    try {
      // First, get the current order
      const currentOrder = await this.ordersService.findOne(id, 'ace20c64-fcdc-4ac7-933d-a58e388ce60f', 'ADMIN' as any);
      console.log('[OrdersController] Current order customerName:', currentOrder.customerName);
      
      // Update with a test value
      const testName = `TEST_${Date.now()}`;
      const updateResult = await this.ordersService.update(
        id,
        { customerName: testName },
        'ace20c64-fcdc-4ac7-933d-a58e388ce60f',
        'ADMIN' as any
      );
      console.log('[OrdersController] Update result customerName:', updateResult.customerName);
      
      // Fetch again to verify
      const verifyOrder = await this.ordersService.findOne(id, 'ace20c64-fcdc-4ac7-933d-a58e388ce60f', 'ADMIN' as any);
      console.log('[OrdersController] Verify order customerName:', verifyOrder.customerName);
      
      return {
        success: verifyOrder.customerName === testName,
        original: currentOrder.customerName,
        updated: testName,
        verified: verifyOrder.customerName,
      };
    } catch (error) {
      console.error('[OrdersController] Test update error:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Patch(':id/cancel')
  @Roles('VENDEDOR', 'ADMIN', 'SUPERVISOR')
  async cancel(@Param('id') id: string, @Request() req) {
    return this.ordersService.cancel(id, req.user.userId, req.user.role);
  }

  @Post(':id/run-antifraud')
  @Roles('ADMIN')
  async runAntifraud(@Param('id') id: string) {
    return this.ordersService.runAntifraudCheck(id);
  }

  @Delete(':id')
  @Roles('ADMIN')
  async remove(@Param('id') id: string, @Request() req) {
    return this.ordersService.softDelete(id, req.user.userId);
  }

  @Delete(':id/permanent')
  @Roles('ADMIN')
  async permanentDelete(@Param('id') id: string, @Request() req) {
    return this.ordersService.permanentDelete(id, req.user.userId);
  }

  @Post('bulk-delete')
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async bulkDelete(@Body() bulkDeleteDto: BulkDeleteOrdersDto, @Request() req) {
    return this.ordersService.bulkDelete(
      bulkDeleteDto.orderIds,
      req.user.userId,
      req.user.tenantId,
      bulkDeleteDto.permanent || false,
    );
  }

  @Get('payment-confirmations/pending')
  @Roles('ADMIN', 'SUPERVISOR')
  async getPendingPaymentConfirmations(@Request() req) {
    return this.ordersService.getPendingPaymentConfirmations(req.user.tenantId);
  }

  @Post(':id/confirm-payment')
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async confirmPayment(
    @Param('id') id: string,
    @Body() confirmData: { approve: boolean; reason?: string },
    @Request() req,
  ) {
    return this.ordersService.confirmPayment(
      id,
      req.user.userId,
      confirmData.approve,
      confirmData.reason,
    );
  }
}