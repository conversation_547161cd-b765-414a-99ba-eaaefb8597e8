import { Test, TestingModule } from '@nestjs/testing';
import { OrdersService } from './orders.service';
import { PrismaService } from '../prisma/prisma.service';
import { OrderStatus, Role } from '@prisma/client';
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';

describe('OrdersService', () => {
  let service: OrdersService;
  let prisma: PrismaService;

  const mockPrismaService = {
    order: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrdersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<OrdersService>(OrdersService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('deve criar um pedido com sucesso', async () => {
      const createOrderDto = {
        customerName: 'Cliente Teste',
        customerPhone: '11999999999',
        customerCPF: '12345678901',
        items: [
          {
            productId: 'prod1',
            productName: 'Produto 1',
            quantity: 2,
            unitPrice: 50,
          },
          {
            productId: 'prod2',
            productName: 'Produto 2',
            quantity: 1,
            unitPrice: 100,
          },
        ],
      };

      const sellerId = 'seller123';
      const expectedTotal = 200; // (2 * 50) + (1 * 100)

      const createdOrder = {
        id: 'order123',
        customerName: 'Cliente Teste',
        customerPhone: '11999999999',
        total: new Decimal(expectedTotal),
        status: OrderStatus.Analise,
        sellerId,
        collectorId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        items: createOrderDto.items,
        seller: { id: sellerId, name: 'Vendedor', email: '<EMAIL>' },
        collector: null,
      };

      mockPrismaService.order.create.mockResolvedValue(createdOrder);

      const result = await service.create(createOrderDto, sellerId);

      expect(result).toEqual(createdOrder);
      expect(mockPrismaService.order.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          customerName: createOrderDto.customerName,
          customerPhone: createOrderDto.customerPhone,
          total: new Decimal(expectedTotal),
          sellerId,
        }),
        include: expect.any(Object),
      });
    });
  });

  describe('updateStatus', () => {
    it('deve permitir transição válida de PENDENTE para SEPARACAO', async () => {
      const orderId = 'order123';
      const userId = 'user123';
      const userRole = Role.COBRADOR;

      const existingOrder = {
        id: orderId,
        status: OrderStatus.Analise,
        collectorId: userId,
      };

      const updateStatusDto = {
        status: OrderStatus.Separacao,
      };

      mockPrismaService.order.findUnique.mockResolvedValue(existingOrder);
      mockPrismaService.order.update.mockResolvedValue({
        ...existingOrder,
        status: OrderStatus.Separacao,
      });

      await service.updateStatus(orderId, updateStatusDto, userId, userRole);

      expect(mockPrismaService.order.update).toHaveBeenCalled();
    });

    it('deve rejeitar transição inválida de ENTREGUE para PENDENTE', async () => {
      const orderId = 'order123';
      const userId = 'user123';
      const userRole = Role.ADMIN;

      const existingOrder = {
        id: orderId,
        status: OrderStatus.Completo,
      };

      const updateStatusDto = {
        status: OrderStatus.Analise,
      };

      mockPrismaService.order.findUnique.mockResolvedValue(existingOrder);

      await expect(
        service.updateStatus(orderId, updateStatusDto, userId, userRole)
      ).rejects.toThrow(BadRequestException);
    });

    it('deve impedir VENDEDOR de alterar status', async () => {
      const orderId = 'order123';
      const userId = 'user123';
      const userRole = Role.VENDEDOR;

      const existingOrder = {
        id: orderId,
        status: OrderStatus.Analise,
        sellerId: userId,
      };

      const updateStatusDto = {
        status: OrderStatus.Separacao,
      };

      mockPrismaService.order.findUnique.mockResolvedValue(existingOrder);

      await expect(
        service.updateStatus(orderId, updateStatusDto, userId, userRole)
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('findAll', () => {
    it('VENDEDOR deve ver apenas seus próprios pedidos', async () => {
      const userId = 'seller123';
      const userRole = Role.VENDEDOR;
      const filters = {};

      await service.findAll(filters, userId, userRole);

      expect(mockPrismaService.order.findMany).toHaveBeenCalledWith({
        where: { sellerId: userId },
        include: expect.any(Object),
        orderBy: { createdAt: 'desc' },
      });
    });

    it('COBRADOR deve ver apenas pedidos atribuídos a ele', async () => {
      const userId = 'collector123';
      const userRole = Role.COBRADOR;
      const filters = {};

      await service.findAll(filters, userId, userRole);

      expect(mockPrismaService.order.findMany).toHaveBeenCalledWith({
        where: { collectorId: userId },
        include: expect.any(Object),
        orderBy: { createdAt: 'desc' },
      });
    });

    it('ADMIN deve ver todos os pedidos', async () => {
      const userId = 'admin123';
      const userRole = Role.ADMIN;
      const filters = {};

      await service.findAll(filters, userId, userRole);

      expect(mockPrismaService.order.findMany).toHaveBeenCalledWith({
        where: {},
        include: expect.any(Object),
        orderBy: { createdAt: 'desc' },
      });
    });
  });
});