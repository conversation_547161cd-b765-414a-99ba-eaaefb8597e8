import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Payment, OrderStatus, Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

@Injectable()
export class PaymentsService {
  constructor(private prisma: PrismaService) {}

  async create(
    createPaymentDto: CreatePaymentDto,
    collectorId: string,
  ): Promise<Payment> {
    // Start a transaction to ensure data consistency
    return await this.prisma.$transaction(async (prisma) => {
      // Get the order with current payment information
      const order = await prisma.order.findUnique({
        where: { id: createPaymentDto.orderId },
        include: {
          payments: true,
        },
      });

      if (!order) {
        throw new NotFoundException('Pedido não encontrado');
      }

      // Verify payment method exists
      const paymentMethod = await prisma.paymentMethod.findUnique({
        where: { id: createPaymentDto.paymentMethodId },
      });

      if (!paymentMethod) {
        throw new NotFoundException('Método de pagamento não encontrado');
      }

      // Create the payment record with the current collector
      const payment = await prisma.payment.create({
        data: {
          orderId: createPaymentDto.orderId,
          amount: createPaymentDto.amount,
          paymentMethodId: createPaymentDto.paymentMethodId,
          collectorId: collectorId, // Use the logged-in user as collector
          notes: createPaymentDto.notes,
        },
        include: {
          paymentMethod: true,
          collector: true,
        },
      });

      // Calculate total paid amount
      const totalPaid = order.payments.reduce((sum, p) => sum.add(p.amount), new Decimal(0));
      const newTotalPaid = totalPaid.add(new Decimal(createPaymentDto.amount));

      // Update order payment information
      const updateData: Prisma.OrderUpdateInput = {
        paymentReceivedAmount: newTotalPaid,
        paymentReceivedDate: new Date(),
      };

      // Update order status based on payment
      if (newTotalPaid.gte(order.total)) {
        // Full payment
        updateData.status = OrderStatus.Completo;
      } else if (newTotalPaid.gt(0)) {
        // Partial payment
        updateData.status = OrderStatus.Parcial;
      }

      // Update the order
      await prisma.order.update({
        where: { id: createPaymentDto.orderId },
        data: updateData,
      });

      // Calculate and store commission for the collector
      if (collectorId) {
        const collector = await prisma.user.findUnique({
          where: { id: collectorId },
        });

        if (collector && collector.commissionRate) {
          const commissionAmount = new Decimal(createPaymentDto.amount)
            .mul(collector.commissionRate)
            .div(100);

          await prisma.commissionPayment.create({
            data: {
              orderId: createPaymentDto.orderId,
              userId: collectorId,
              userRole: collector.role,
              baseAmount: createPaymentDto.amount,
              percentage: collector.commissionRate,
              commissionAmount: commissionAmount,
              paymentDate: new Date(),
            },
          });
        }
      }

      return payment;
    });
  }

  async findByOrder(orderId: string): Promise<Payment[]> {
    return this.prisma.payment.findMany({
      where: { orderId },
      include: {
        paymentMethod: true,
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getPaymentsByCollector(
    collectorId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<Payment[]> {
    const where: Prisma.PaymentWhereInput = {
      collectorId,
    };

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    return this.prisma.payment.findMany({
      where,
      include: {
        order: true,
        paymentMethod: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getPaymentsByMethod(
    tenantId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    // Get all payment methods for the tenant
    const paymentMethods = await this.prisma.paymentMethod.findMany({
      where: { tenantId },
    });

    const where: Prisma.PaymentWhereInput = {
      order: {
        tenantId,
      },
    };

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    // Get payments grouped by method
    const payments = await this.prisma.payment.groupBy({
      by: ['paymentMethodId'],
      where,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    // Map results with payment method names
    return paymentMethods.map((method) => {
      const payment = payments.find((p) => p.paymentMethodId === method.id);
      return {
        paymentMethod: method,
        totalAmount: payment?._sum.amount || 0,
        count: payment?._count.id || 0,
      };
    });
  }

  async getCommissionReport(
    tenantId: string,
    startDate?: Date,
    endDate?: Date,
    userId?: string,
  ): Promise<any> {
    const where: Prisma.CommissionPaymentWhereInput = {
      order: {
        tenantId,
      },
    };

    if (startDate || endDate) {
      where.paymentDate = {};
      if (startDate) where.paymentDate.gte = startDate;
      if (endDate) where.paymentDate.lte = endDate;
    }

    if (userId) {
      where.userId = userId;
    }

    return this.prisma.commissionPayment.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        order: {
          select: {
            orderNumber: true,
            customerName: true,
          },
        },
      },
      orderBy: { paymentDate: 'desc' },
    });
  }
}