import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('payments')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  create(
    @Body() createPaymentDto: CreatePaymentDto,
    @Request() req,
  ) {
    // Use the logged-in user as the collector
    return this.paymentsService.create(createPaymentDto, req.user.userId);
  }

  @Get('order/:orderId')
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR', 'VENDEDOR')
  findByOrder(@Param('orderId') orderId: string) {
    return this.paymentsService.findByOrder(orderId);
  }

  @Get('collector/:collectorId')
  @Roles('ADMIN', 'SUPERVISOR')
  getPaymentsByCollector(
    @Param('collectorId') collectorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.paymentsService.getPaymentsByCollector(
      collectorId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('by-method')
  @Roles('ADMIN', 'SUPERVISOR')
  getPaymentsByMethod(
    @TenantId() tenantId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.paymentsService.getPaymentsByMethod(
      tenantId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get('commission-report')
  @Roles('ADMIN', 'SUPERVISOR')
  getCommissionReport(
    @TenantId() tenantId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
  ) {
    return this.paymentsService.getCommissionReport(
      tenantId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      userId,
    );
  }

  @Get('my-commissions')
  @Roles('COBRADOR', 'VENDEDOR')
  getMyCommissions(
    @Request() req,
    @TenantId() tenantId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.paymentsService.getCommissionReport(
      tenantId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
      req.user.userId,
    );
  }
}