import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as cookieParser from 'cookie-parser';
import helmet from 'helmet';
import * as express from 'express';
import { checkDatabaseConnection } from './startup-check';


async function bootstrap() {
  // Check database connection in the background (non-blocking)
  checkDatabaseConnection().catch(error => {
    console.error('❌ Database connection check failed:', error);
    console.log('⚠️  Continuing without database - app will retry connections');
  });
  const logger = new Logger('Bootstrap');
  
  // Parse PORT as integer, Railway provides this dynamically
  const port = parseInt(process.env.PORT || '3000', 10);
  const apiPrefix = process.env.API_PREFIX || 'api/v1';
  const corsOrigin = process.env.CORS_ORIGIN || 'https://zencash-sand.vercel.app';
  
  // Expected Railway environment variables:
  // CORS_ORIGIN = https://zencash-sand.vercel.app
  // API_PREFIX = api/v1
  
  console.log('========================================');
  console.log('🚀 NESTJS STARTUP SEQUENCE INITIATED');
  console.log('========================================');
  console.log(`📍 Port: ${port}`);
  console.log(`🔗 API Prefix: ${apiPrefix}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
  console.log(`🌐 CORS Origin: ${corsOrigin}`);
  console.log(`🗄️  Database URL: ${process.env.DATABASE_URL ? 'Set' : 'NOT SET!'}`);
  console.log('========================================');
  
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log'],
    rawBody: true,
    bodyParser: false, // Disable default body parser to configure custom limits
  });
  
  // STEP 1: Get the raw Express server instance IMMEDIATELY
  console.log('🔧 Getting Express instance for health endpoints...');
  const server = app.getHttpAdapter().getInstance();
  
  // Configure body parser with increased limits (10MB)
  server.use(express.json({ limit: '10mb' }));
  server.use(express.urlencoded({ limit: '10mb', extended: true }));
  server.use(express.raw({ limit: '10mb', type: 'application/octet-stream' }));
  
  console.log('📦 Body parser configured with 10MB limit');
  
  // Serve static files from uploads directory
  server.use('/uploads', express.static('uploads'));
  console.log('📁 Static file serving enabled for /uploads');
  
  // STEP 2: Register health endpoints DIRECTLY on Express (no NestJS routing)
  // These MUST be registered BEFORE any other middleware or global prefix
  console.log('🏥 Registering health endpoints...');
  server.get('/health', (_req, res) => res.sendStatus(200));
  server.get('/ping', (_req, res) => res.sendStatus(200));
  server.get(`/${apiPrefix}/health`, (_req, res) => res.json({status:'ok'}));
  server.get(`/${apiPrefix}/health/ready`, (_req, res) => res.sendStatus(200));
  server.get(`/${apiPrefix}/health/live`, (_req, res) => res.sendStatus(200));
  
  // Test endpoint for CORS debugging
  server.get(`/${apiPrefix}/test-cors`, (req, res) => {
    res.json({
      status: 'ok',
      origin: req.headers.origin || 'no-origin',
      timestamp: new Date().toISOString(),
      cors: 'enabled'
    });
  });
  
  console.log(`✅ Health endpoints registered: /health, /ping, /${apiPrefix}/health, /${apiPrefix}/health/ready, /${apiPrefix}/health/live`);
  
  // STEP 4: ONLY AFTER health endpoints are registered, set global prefix
  app.setGlobalPrefix(apiPrefix);
  
  // Request logging middleware (AFTER health endpoints to avoid logging health checks)
  app.use((req, res, next) => {
    // Skip logging for health endpoints
    if (req.url.includes('/health') || req.url === '/ping') {
      return next();
    }
    
    const start = Date.now();
    res.on('finish', () => {
      const duration = Date.now() - start;
      logger.log(`${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`);
    });
    next();
  });
  
  // Security middleware
  app.use(cookieParser());
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
  }));
  
  // Configure CORS - allow Vercel origin and localhost for development
  const corsOriginEnv = process.env.CORS_ORIGIN || 'https://zencash-sand.vercel.app';
  
  // Log CORS configuration
  console.log('🌐 CORS Configuration:');
  console.log('   Primary origin:', corsOriginEnv);
  console.log('   Credentials:', 'true');
  
  app.enableCors({
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) {
        return callback(null, true);
      }
      
      // List of allowed origins
      const allowedOrigins = [
        corsOriginEnv,
        'http://localhost:3000',
        'http://localhost:3001',
        // Railway URLs
        'https://zencash-production.up.railway.app',
        'https://zencash-frontend-production.up.railway.app',
        'https://zencash-production-1ccd.up.railway.app', // Your actual frontend URL
        // Add your actual Railway frontend URL here
        process.env.FRONTEND_URL || 'https://your-frontend-service.up.railway.app',
      ];
      
      // Check exact matches first
      if (allowedOrigins.includes(origin)) {
        console.log(`✅ CORS: Allowed origin ${origin}`);
        callback(null, true);
      } 
      // Allow all Vercel preview deployments
      else if (origin.includes('.vercel.app') && origin.includes('zencash')) {
        console.log(`✅ CORS: Allowed Vercel preview ${origin}`);
        callback(null, true);
      }
      // Allow webhook requests from any origin (webhooks typically come from external services)
      // For webhooks, we allow any origin since they come from external services
      else if (origin) {
        console.log(`✅ CORS: Allowed external origin ${origin} (webhook support)`);
        callback(null, true);
      }
      else {
        console.warn(`❌ CORS: Blocked origin ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-tenant-id', 'Accept', 'Origin', 'X-Webhook-Signature'],
    exposedHeaders: ['X-Total-Count'],
    maxAge: 86400, // 24 hours
  });
  
  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
  }));
  
  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('ZenCash API')
    .setDescription('ZenCash Backend API Documentation')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('auth', 'Authentication endpoints')
    .addTag('products', 'Product management')
    .addTag('orders', 'Order management')
    .addTag('customers', 'Customer management')
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);
  
  // STEP 5: Listen on 0.0.0.0 for Railway external access
  console.log(`🎯 Starting server on 0.0.0.0:${port}...`);
  await app.listen(port, '0.0.0.0');
  
  console.log('========================================');
  console.log('✅ NESTJS APPLICATION STARTED SUCCESSFULLY!');
  console.log('========================================');
  console.log(`🌐 Server: http://0.0.0.0:${port}`);
  console.log(`🏥 Health: http://0.0.0.0:${port}/${apiPrefix}/health`);
  console.log(`📚 Swagger: http://0.0.0.0:${port}/docs`);
  console.log('========================================');
  logger.log(`Application listening on http://0.0.0.0:${port}/${apiPrefix}`, 'Bootstrap');
}

bootstrap().catch(err => {
  console.error('❌ Failed to start application:', err);
  process.exit(1);
});