import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { OrdersModule } from './orders/orders.module';
import { ProductsModule } from './products/products.module';
import { KitsModule } from './kits/kits.module';
import { CustomersModule } from './customers/customers.module';
import { NotificationsModule } from './notifications/notifications.module';
import { TrackingV2Module } from './tracking/tracking-v2.module';
import { ReportsModule } from './reports/reports.module';
import { ConfigurationModule } from './configuration/configuration.module';
import { HealthModule } from './health/health.module';
import { CommissionModule } from './commission/commission.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { DashboardInteligenteModule } from './dashboard-inteligente/dashboard-inteligente.module';
import { RequestLoggerMiddleware } from './common/middleware/request-logger.middleware';
import { TenantMiddleware } from './common/middleware/tenant.middleware';
import { TenantModule } from './common/module/tenant.module';
import { AntifraudModule } from './antifraud/antifraud.module';
import { UploadModule } from './upload/upload.module';
import { WebhooksModule } from './webhooks/webhooks.module';
import { PaymentMethodsModule } from './payment-methods/payment-methods.module';
import { PaymentsModule } from './payments/payments.module';
import { ZapsModule } from './zaps/zaps.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebhookMapping } from './webhooks/entities/webhook-mapping.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ThrottlerModule.forRoot([{
      ttl: parseInt(process.env.THROTTLE_TTL || '60000'), // 60 segundos
      limit: parseInt(process.env.THROTTLE_LIMIT || '100'), // 100 requisições
    }]),
    PrismaModule,
    AuthModule,
    UsersModule,
    OrdersModule,
    ProductsModule,
    KitsModule,
    CustomersModule,
    NotificationsModule,
    TrackingV2Module,
    ReportsModule,
    ConfigurationModule,
    HealthModule,
    CommissionModule,
    DashboardModule,
    DashboardInteligenteModule,
    TenantModule, // Global tenant module
    AntifraudModule,
    UploadModule,
    WebhooksModule,
    PaymentMethodsModule,
    PaymentsModule,
    ZapsModule,
    TypeOrmModule.forRoot({
      type: 'postgres',
      url: process.env.DATABASE_URL,
      entities: [WebhookMapping],
      synchronize: true, // Auto-create tables (be careful in production!)
      logging: true, // Enable logging to see SQL queries
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      extra: {
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      },
    }),
    TypeOrmModule.forFeature([WebhookMapping]),
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply tenant middleware to all routes except health checks and webhooks
    consumer
      .apply(TenantMiddleware)
      .exclude(
        'health', 
        'health/(.*)',
        'api/v1/webhooks/(.*)',
        'webhooks/(.*)'
      )
      .forRoutes('*');
    
    // Apply request logger to all routes
    consumer
      .apply(RequestLoggerMiddleware)
      .forRoutes('*');
  }
}