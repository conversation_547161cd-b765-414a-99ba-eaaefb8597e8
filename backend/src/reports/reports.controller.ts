import {
  Controller,
  Get,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
  Request,
  UseInterceptors,
} from '@nestjs/common';
import { 
  ReportsService,
  SummaryReport,
  CollectorReport,
  SellerReport,
  StatusReport,
  ProductReport,
  TrackingAlert
} from './reports.service';
import { FilterReportDto } from './dto/filter-report.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { ReportScopeInterceptor } from './interceptors/report-scope.interceptor';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

@ApiTags('reports')
@ApiBearerAuth()
@Controller('reports')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(ReportScopeInterceptor)
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  /**
   * Relatório resumo geral
   */
  @Get('summary')
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR', 'VENDEDOR')
  @ApiOperation({ 
    summary: 'Obter relatório resumo geral',
    description: 'Retorna um resumo geral das vendas. Vendedores e cobradores só veem dados de suas próprias vendas/cobranças.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getSummary(@Query() filters: FilterReportDto): Promise<SummaryReport> {
    return this.reportsService.getSummary(filters);
  }

  /**
   * Relatório por cobrador
   */
  @Get('collector')
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR')
  @ApiOperation({ 
    summary: 'Obter relatório por cobrador',
    description: 'Retorna relatório agrupado por cobrador. Cobradores só veem seus próprios dados.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getByCollector(@Query() filters: FilterReportDto): Promise<{ total: number; collectors: CollectorReport[]; summary: any }> {
    const report = await this.reportsService.getByCollector(filters);
    
    return {
      total: report.length,
      collectors: report,
      summary: {
        totalRevenue: report.reduce((sum, c) => sum.add(c.totalRevenue), new Decimal(0)),
        totalOrders: report.reduce((sum, c) => sum + c.totalOrders, 0),
      },
    };
  }

  /**
   * Relatório por vendedor
   */
  @Get('seller')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @ApiOperation({ 
    summary: 'Obter relatório por vendedor',
    description: 'Retorna relatório agrupado por vendedor. Vendedores só veem seus próprios dados.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getBySeller(@Query() filters: FilterReportDto): Promise<{ total: number; sellers: SellerReport[]; summary: any }> {
    const report = await this.reportsService.getBySeller(filters);
    
    return {
      total: report.length,
      sellers: report,
      summary: {
        totalRevenue: report.reduce((sum, s) => sum.add(s.totalRevenue), new Decimal(0)),
        totalOrders: report.reduce((sum, s) => sum + s.totalOrders, 0),
      },
    };
  }

  /**
   * Relatório por status
   */
  @Get('status')
  @Roles('ADMIN', 'SUPERVISOR', 'COBRADOR', 'VENDEDOR')
  @ApiOperation({ 
    summary: 'Obter relatório por status',
    description: 'Retorna relatório agrupado por status do pedido. Vendedores e cobradores só veem dados de suas próprias vendas/cobranças.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getByStatus(@Query() filters: FilterReportDto): Promise<{ total: number; statuses: StatusReport[]; chart: any }> {
    const report = await this.reportsService.getByStatus(filters);
    
    return {
      total: report.length,
      statuses: report,
      chart: {
        labels: report.map(s => this.formatStatus(s.status)),
        data: report.map(s => s.count),
        percentages: report.map(s => Number(s.percentage.toFixed(2))),
      },
    };
  }

  /**
   * Relatório por produto
   */
  @Get('product')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  @ApiOperation({ 
    summary: 'Obter relatório por produto',
    description: 'Retorna relatório de vendas agrupado por produto. Vendedores só veem dados de suas próprias vendas.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getByProduct(@Query() filters: FilterReportDto): Promise<{ total: number; products: ProductReport[]; topProducts: ProductReport[] }> {
    const report = await this.reportsService.getByProduct(filters);
    
    return {
      total: report.length,
      products: report.slice(0, 50), // Top 50 produtos
      topProducts: report.slice(0, 10), // Top 10 produtos
    };
  }

  /**
   * Alertas de rastreamento
   */
  @Get('alerts')
  @Roles('ADMIN', 'SUPERVISOR')
  @ApiOperation({ 
    summary: 'Obter alertas de rastreamento',
    description: 'Retorna alertas de problemas com entregas. Apenas admins e supervisores têm acesso.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getTrackingAlerts(@Query() filters: FilterReportDto): Promise<{ total: number; alerts: TrackingAlert[]; criticalCount: number }> {
    const alerts = await this.reportsService.getTrackingAlerts(filters);
    
    return {
      total: alerts.length,
      alerts,
      criticalCount: alerts.filter(a => 
        a.alertReason?.includes('CANCELADO') || 
        a.alertReason?.includes('EXTRAVIADO')
      ).length,
    };
  }

  /**
   * Histórico completo
   */
  @Get('history')
  @Roles('ADMIN', 'SUPERVISOR')
  @ApiOperation({ 
    summary: 'Obter histórico completo de pedidos',
    description: 'Retorna histórico detalhado de todos os pedidos. Apenas admins e supervisores têm acesso.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getHistory(@Query() filters: FilterReportDto) {
    const history = await this.reportsService.getHistory(filters);
    
    return {
      total: history.length,
      orders: history,
      filters: {
        startDate: filters.startDate,
        endDate: filters.endDate,
        status: filters.status,
      },
    };
  }

  /**
   * Métricas de performance
   */
  @Get('performance')
  @Roles('ADMIN', 'SUPERVISOR')
  @ApiOperation({ 
    summary: 'Obter métricas de performance',
    description: 'Retorna métricas de performance do sistema. Apenas admins e supervisores têm acesso.'
  })
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getPerformance(@Query() filters: FilterReportDto) {
    return this.reportsService.getPerformanceStats(filters);
  }

  /**
   * Formata status para exibição
   */
  private formatStatus(status: string): string {
    const statusMap: Record<string, string> = {
      Analise: 'Em Análise',
      Separacao: 'Em Separação',
      Transito: 'Em Trânsito',
      ConfirmarEntrega: 'Confirmar Entrega',
      PagamentoPendente: 'Pagamento Pendente',
      Negociacao: 'Em Negociação',
      Parcial: 'Pagamento Parcial',
      Completo: 'Completo',
      Recuperacao: 'Em Recuperação',
      Frustrado: 'Frustrado',
      EntregaFalha: 'Falha na Entrega',
      RetirarCorreios: 'Retirar nos Correios',
      DevolvidoCorreios: 'Devolvido pelos Correios',
      Cancelado: 'Cancelado',
    };

    return statusMap[status] || status;
  }
}