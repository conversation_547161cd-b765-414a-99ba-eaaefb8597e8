import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Role } from '@prisma/client';

@Injectable()
export class ReportScopeInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    try {
      const request = context.switchToHttp().getRequest();
      const user = request.user;

      if (!user) {
        return next.handle();
      }

      // Aplicar restrições baseadas no role do usuário
      const userId = user.id || user.userId || user.sub;
      
      console.log('Before filter application:', {
        userId,
        role: user.role,
        originalFilters: { ...request.query }
      });
      
      switch (user.role) {
        case Role.VENDEDOR:
          // Vendedor só pode ver suas próprias vendas
          // IMPORTANTE: Sempre sobrescrever com o ID do usuário logado
          if (!request.query) {
            request.query = {};
          }
          request.query.sellerId = userId;
          // Remover qualquer collectorId que possa ter sido enviado
          if (request.query.collectorId) {
            delete request.query.collectorId;
          }
          break;

        case Role.COBRADOR:
          // Cobrador só pode ver suas próprias cobranças
          // IMPORTANTE: Sempre sobrescrever com o ID do usuário logado
          if (!request.query) {
            request.query = {};
          }
          request.query.collectorId = userId;
          // Remover qualquer sellerId que possa ter sido enviado
          if (request.query.sellerId) {
            delete request.query.sellerId;
          }
          break;

        case Role.ADMIN:
        case Role.SUPERVISOR:
          // Admin e Supervisor podem ver tudo
          // Não aplicar nenhuma restrição
          break;

        default:
          // Para qualquer outro role não esperado, não retornar dados
          request.query.sellerId = 'no-access';
          request.query.collectorId = 'no-access';
      }

      // Log para debug
      console.log('ReportScopeInterceptor - User:', {
        id: userId,
        role: user.role,
        appliedFilters: {
          sellerId: request.query.sellerId,
          collectorId: request.query.collectorId,
        },
      });

      return next.handle();
    } catch (error) {
      console.error('Error in ReportScopeInterceptor:', error);
      return next.handle();
    }
  }
}