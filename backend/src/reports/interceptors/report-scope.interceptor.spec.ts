import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler } from '@nestjs/common';
import { ReportScopeInterceptor } from './report-scope.interceptor';
import { Role } from '@prisma/client';
import { of } from 'rxjs';

describe('ReportScopeInterceptor', () => {
  let interceptor: ReportScopeInterceptor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ReportScopeInterceptor],
    }).compile();

    interceptor = module.get<ReportScopeInterceptor>(ReportScopeInterceptor);
  });

  const createMockExecutionContext = (user: any, query: any = {}): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          user,
          query,
        }),
      }),
    } as ExecutionContext;
  };

  const createMockCallHandler = (): CallHandler => ({
    handle: () => of({}),
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('Vendedor role', () => {
    it('should force sellerId to user id and remove collectorId', (done) => {
      const user = { id: 'vendedor-123', role: Role.VENDEDOR };
      const query = { 
        sellerId: 'another-seller-id', 
        collectorId: 'some-collector-id',
        startDate: '2024-01-01',
      };
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('vendedor-123');
        expect(request.query.collectorId).toBeUndefined();
        expect(request.query.startDate).toBe('2024-01-01');
        done();
      });
    });

    it('should handle userId field in user object', (done) => {
      const user = { userId: 'vendedor-456', role: Role.VENDEDOR };
      const query = {};
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('vendedor-456');
        done();
      });
    });
  });

  describe('Cobrador role', () => {
    it('should force collectorId to user id and remove sellerId', (done) => {
      const user = { id: 'cobrador-123', role: Role.COBRADOR };
      const query = { 
        sellerId: 'some-seller-id', 
        collectorId: 'another-collector-id',
        status: 'ENTREGUE',
      };
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.collectorId).toBe('cobrador-123');
        expect(request.query.sellerId).toBeUndefined();
        expect(request.query.status).toBe('ENTREGUE');
        done();
      });
    });
  });

  describe('Admin role', () => {
    it('should not modify any filters', (done) => {
      const user = { id: 'admin-123', role: Role.ADMIN };
      const query = { 
        sellerId: 'seller-id', 
        collectorId: 'collector-id',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      };
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('seller-id');
        expect(request.query.collectorId).toBe('collector-id');
        expect(request.query.startDate).toBe('2024-01-01');
        expect(request.query.endDate).toBe('2024-12-31');
        done();
      });
    });
  });

  describe('Supervisor role', () => {
    it('should not modify any filters', (done) => {
      const user = { id: 'supervisor-123', role: Role.SUPERVISOR };
      const query = { 
        sellerId: 'seller-id', 
        collectorId: 'collector-id',
      };
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('seller-id');
        expect(request.query.collectorId).toBe('collector-id');
        done();
      });
    });
  });

  describe('Unknown role', () => {
    it('should set no-access for unknown roles', (done) => {
      const user = { id: 'unknown-123', role: 'UNKNOWN_ROLE' };
      const query = {};
      
      const context = createMockExecutionContext(user, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('no-access');
        expect(request.query.collectorId).toBe('no-access');
        done();
      });
    });
  });

  describe('No user', () => {
    it('should not modify filters when no user is present', (done) => {
      const query = { sellerId: 'seller-id' };
      
      const context = createMockExecutionContext(null, query);
      const next = createMockCallHandler();

      interceptor.intercept(context, next).subscribe(() => {
        const request = context.switchToHttp().getRequest();
        expect(request.query.sellerId).toBe('seller-id');
        done();
      });
    });
  });
});