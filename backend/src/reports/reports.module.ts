import { Module } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ReportScopeInterceptor } from './interceptors/report-scope.interceptor';

@Module({
  imports: [PrismaModule],
  controllers: [ReportsController],
  providers: [
    ReportsService,
    ReportScopeInterceptor,
  ],
  exports: [ReportsService],
})
export class ReportsModule {}