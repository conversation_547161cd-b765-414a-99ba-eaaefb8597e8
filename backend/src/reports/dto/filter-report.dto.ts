import { IsDateString, IsOptional, IsUUID, IsEnum, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { OrderStatus } from '@prisma/client';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class FilterReportDto {
  @ApiPropertyOptional({ 
    description: 'Data inicial do período (paymentReceivedDate)',
    example: '2024-01-01T00:00:00Z' 
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({ 
    description: 'Data final do período (paymentReceivedDate)',
    example: '2024-12-31T23:59:59Z' 
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({ 
    description: 'ID do vendedor. Para vendedores logados, este campo é ignorado e sempre usa o ID do usuário logado.',
    example: 'f412c8f5-4f82-49db-99ab-88dcb8a2fac7' 
  })
  @IsUUID()
  @IsOptional()
  sellerId?: string;

  @ApiPropertyOptional({ 
    description: 'ID do cobrador. Para cobradores logados, este campo é ignorado e sempre usa o ID do usuário logado.',
    example: '6d4525e0-0b75-4a42-a0c7-113e70e5809d' 
  })
  @IsUUID()
  @IsOptional()
  collectorId?: string;

  @ApiPropertyOptional({ 
    description: 'Status do pedido',
    enum: OrderStatus 
  })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @ApiPropertyOptional({ 
    description: 'Filtrar apenas pedidos com alertas',
    type: Boolean 
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  hasAlert?: boolean;

  @ApiPropertyOptional({ 
    description: 'ID do produto',
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsUUID()
  @IsOptional()
  productId?: string;

  @ApiPropertyOptional({ 
    description: 'ID do cliente',
    example: '123e4567-e89b-12d3-a456-************' 
  })
  @IsUUID()
  @IsOptional()
  customerId?: string;
}