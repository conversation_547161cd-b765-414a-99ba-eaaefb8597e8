import { Injectable, Logger, NotFoundException, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import * as crypto from 'crypto';

@Injectable()
export class ConfigurationService implements OnModuleInit {
  private readonly logger = new Logger(ConfigurationService.name);
  private configCache: Map<string, any> = new Map();
  private readonly algorithm = 'aes-256-gcm';
  private readonly secretKey: string;
  
  // Lista de chaves que devem ser criptografadas
  private readonly encryptedKeys = [
    'whatsapp_api_key',
    'correios_credentials',
    'stripe_secret_key',
    'stripe_webhook_secret',
    'api_keys',
    'smtp_password',
    'database_backup_password',
    'jwt_secret',
    'refresh_token_secret',
  ];

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    // Obter chave de criptografia do ambiente
    this.secretKey = this.configService.get<string>('ENCRYPTION_KEY') || 
      crypto.randomBytes(32).toString('hex');
    
    if (!this.configService.get<string>('ENCRYPTION_KEY')) {
      this.logger.warn('ENCRYPTION_KEY não encontrada no .env, usando chave temporária');
    }
  }

  async onModuleInit() {
    await this.loadToMemory();
  }

  /**
   * Criptografa um valor
   */
  private encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const key = Buffer.from(this.secretKey.substring(0, 32), 'utf8');
    const cipher = crypto.createCipheriv(this.algorithm, key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return JSON.stringify({
      encrypted,
      authTag: authTag.toString('hex'),
      iv: iv.toString('hex'),
    });
  }

  /**
   * Descriptografa um valor
   */
  private decrypt(encryptedData: string): string {
    try {
      const { encrypted, authTag, iv } = JSON.parse(encryptedData);
      const key = Buffer.from(this.secretKey.substring(0, 32), 'utf8');
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        key,
        Buffer.from(iv, 'hex'),
      );
      
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      this.logger.error('Erro ao descriptografar:', error);
      throw new Error('Falha ao descriptografar configuração');
    }
  }

  /**
   * Verifica se uma chave deve ser criptografada
   */
  private shouldEncrypt(key: string): boolean {
    return this.encryptedKeys.some(encKey => 
      key.toLowerCase().includes(encKey.toLowerCase())
    );
  }

  /**
   * Serializa e criptografa se necessário
   */
  private serializeValue(key: string, value: any): string {
    const serialized = JSON.stringify(value);
    
    if (this.shouldEncrypt(key)) {
      return this.encrypt(serialized);
    }
    
    return serialized;
  }

  /**
   * Deserializa e descriptografa se necessário
   */
  private deserializeValue(key: string, value: string): any {
    try {
      // Tenta descriptografar primeiro se for uma chave sensível
      if (this.shouldEncrypt(key)) {
        const decrypted = this.decrypt(value);
        return JSON.parse(decrypted);
      }
      
      // Se não for sensível, apenas faz o parse
      return JSON.parse(value);
    } catch (error) {
      // Se falhar, pode ser um valor não criptografado antigo
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
  }

  /**
   * Define uma configuração
   */
  async set(key: string, value: any, tenantId: string): Promise<void> {
    const serializedValue = this.serializeValue(key, value);
    
    await this.prisma.configuration.upsert({
      where: { 
        key_tenantId: {
          key,
          tenantId,
        },
      },
      create: {
        key,
        value: serializedValue as any,
        tenantId,
      },
      update: {
        value: serializedValue as any,
      },
    });
    
    // Atualiza o cache
    this.configCache.set(`${tenantId}:${key}`, value);
    
    this.logger.log(`Configuração '${key}' atualizada`);
  }

  /**
   * Obtém uma configuração
   */
  async get(key: string, tenantId: string): Promise<any> {
    // Verifica o cache primeiro
    const cacheKey = `${tenantId}:${key}`;
    if (this.configCache.has(cacheKey)) {
      return this.configCache.get(cacheKey);
    }
    
    const config = await this.prisma.configuration.findUnique({
      where: { 
        key_tenantId: {
          key,
          tenantId,
        },
      },
    });
    
    if (!config) {
      return null;
    }
    
    const value = this.deserializeValue(key, config.value as string);
    
    // Atualiza o cache
    this.configCache.set(`${tenantId}:${key}`, value);
    
    return value;
  }

  /**
   * Obtém todas as configurações
   */
  async getAll(tenantId: string): Promise<Record<string, any>> {
    const configs = await this.prisma.configuration.findMany({ where: { tenantId } });
    const result: Record<string, any> = {};
    
    for (const config of configs) {
      const value = this.deserializeValue(config.key, config.value as string);
      result[config.key] = value;
      this.configCache.set(config.key, value);
    }
    
    return result;
  }

  /**
   * Remove uma configuração
   */
  async delete(key: string, tenantId: string): Promise<void> {
    const config = await this.prisma.configuration.findUnique({
      where: { 
        key_tenantId: {
          key,
          tenantId,
        },
      },
    });
    
    if (!config) {
      throw new NotFoundException(`Configuração '${key}' não encontrada`);
    }
    
    await this.prisma.configuration.delete({
      where: { 
        key_tenantId: {
          key,
          tenantId,
        },
      },
    });
    
    // Remove do cache
    this.configCache.delete(`${tenantId}:${key}`);
    
    this.logger.log(`Configuração '${key}' removida`);
  }

  /**
   * Carrega todas as configurações para memória
   */
  async loadToMemory(): Promise<void> {
    // Skip loading configurations on module init since we need tenantId
    // Configurations will be loaded on demand per tenant
    this.logger.log('Configuration service initialized. Configurations will be loaded per tenant on demand.');
  }

  /**
   * Obtém configuração do cache (método rápido)
   */
  getCached(key: string, tenantId: string): any {
    return this.configCache.get(`${tenantId}:${key}`);
  }

  /**
   * Verifica se uma configuração existe
   */
  async exists(key: string, tenantId: string): Promise<boolean> {
    const cacheKey = `${tenantId}:${key}`;
    if (this.configCache.has(cacheKey)) {
      return true;
    }
    
    const config = await this.prisma.configuration.count({
      where: { 
        key,
        tenantId,
      },
    });
    
    return config > 0;
  }

  /**
   * Obtém múltiplas configurações de uma vez
   */
  async getMultiple(keys: string[], tenantId: string): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    
    for (const key of keys) {
      result[key] = await this.get(key, tenantId);
    }
    
    return result;
  }

  /**
   * Define múltiplas configurações de uma vez
   */
  async setMultiple(configs: Record<string, any>, tenantId: string): Promise<void> {
    for (const [key, value] of Object.entries(configs)) {
      await this.set(key, value, tenantId);
    }
  }

  /**
   * Limpa o cache (útil para testes ou reload)
   */
  clearCache(): void {
    this.configCache.clear();
    this.logger.log('Cache de configurações limpo');
  }
}