import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
  ForbiddenException,
  Request,
  Logger,
} from '@nestjs/common';
import { ConfigurationService } from './configuration.service';
import { SetConfigDto } from './dto/set-config.dto';
import { GetConfigDto } from './dto/get-config.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('config')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ConfigurationController {
  private readonly logger = new Logger(ConfigurationController.name);
  
  // Configurações que SUPERVISOR pode ler
  private readonly supervisorReadableKeys = [
    'whatsapp_api_key',
    'default_notification_template',
    'tracking_alert_thresholds',
    'system_preferences',
  ];

  constructor(private readonly configurationService: ConfigurationService) {}

  /**
   * Criar ou atualizar configuração
   */
  @Post()
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async setConfig(
    @Body() setConfigDto: SetConfigDto,
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { sub: userId, email } = req.user;
    
    await this.configurationService.set(setConfigDto.key, setConfigDto.value, tenantId);
    
    this.logger.log(
      `Configuração '${setConfigDto.key}' atualizada por ${email} (${userId}) para tenant ${tenantId}`,
    );
    
    return {
      message: 'Configuração salva com sucesso',
      key: setConfigDto.key,
    };
  }

  /**
   * Buscar configuração por chave
   */
  @Get(':key')
  @Roles('ADMIN', 'SUPERVISOR')
  async getConfig(
    @Param('key') key: string,
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { role } = req.user;
    
    // Verifica permissões do SUPERVISOR
    if (role === Role.SUPERVISOR && !this.canSupervisorRead(key)) {
      throw new ForbiddenException(
        'Você não tem permissão para acessar esta configuração',
      );
    }
    
    const value = await this.configurationService.get(key, tenantId);
    
    if (value === null) {
      return {
        key,
        value: null,
        exists: false,
      };
    }
    
    return {
      key,
      value,
      exists: true,
    };
  }

  /**
   * Listar todas as configurações
   */
  @Get()
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  async getAllConfigs(
    @Query() query: GetConfigDto,
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { role } = req.user;
    
    const allConfigs = await this.configurationService.getAll(tenantId);
    
    // Se for SUPERVISOR, filtra apenas as permitidas
    if (role === Role.SUPERVISOR) {
      const filteredConfigs: Record<string, any> = {};
      
      for (const key of Object.keys(allConfigs)) {
        if (this.canSupervisorRead(key)) {
          filteredConfigs[key] = allConfigs[key];
        }
      }
      
      return {
        configs: filteredConfigs,
        total: Object.keys(filteredConfigs).length,
      };
    }
    
    // ADMIN vê tudo
    return {
      configs: allConfigs,
      total: Object.keys(allConfigs).length,
    };
  }

  /**
   * Remover configuração
   */
  @Delete(':key')
  @Roles('ADMIN')
  async deleteConfig(
    @Param('key') key: string,
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { sub: userId, email } = req.user;
    
    await this.configurationService.delete(key, tenantId);
    
    this.logger.log(
      `Configuração '${key}' removida por ${email} (${userId})`,
    );
    
    return {
      message: 'Configuração removida com sucesso',
      key,
    };
  }

  /**
   * Endpoint para recarregar configurações do banco
   */
  @Post('reload')
  @Roles('ADMIN')
  async reloadConfigs(
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { email } = req.user;
    
    // Clear cache for the specific tenant
    this.configurationService.clearCache();
    // Note: Configurations are now loaded on demand per tenant
    
    this.logger.log(`Cache de configurações limpo por ${email} para tenant ${tenantId}`);
    
    return {
      message: 'Cache de configurações limpo com sucesso',
    };
  }

  /**
   * Endpoint para definir múltiplas configurações
   */
  @Post('bulk')
  @Roles('ADMIN')
  async setBulkConfigs(
    @Body() configs: Record<string, any>,
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { sub: userId, email } = req.user;
    
    await this.configurationService.setMultiple(configs, tenantId);
    
    const keys = Object.keys(configs);
    this.logger.log(
      `${keys.length} configurações atualizadas em lote por ${email} (${userId})`,
    );
    
    return {
      message: 'Configurações salvas com sucesso',
      count: keys.length,
      keys,
    };
  }

  /**
   * Endpoint para buscar múltiplas configurações
   */
  @Post('bulk/get')
  @Roles('ADMIN', 'SUPERVISOR')
  async getBulkConfigs(
    @Body() keys: string[],
    @Request() req: any,
    @TenantId() tenantId: string,
  ) {
    const { role } = req.user;
    
    // Filtra chaves permitidas para SUPERVISOR
    if (role === Role.SUPERVISOR) {
      keys = keys.filter(key => this.canSupervisorRead(key));
    }
    
    const configs = await this.configurationService.getMultiple(keys, tenantId);
    
    return {
      configs,
      requested: keys.length,
      found: Object.keys(configs).filter(k => configs[k] !== null).length,
    };
  }

  /**
   * Verifica se SUPERVISOR pode ler uma configuração
   */
  private canSupervisorRead(key: string): boolean {
    return this.supervisorReadableKeys.some(allowedKey =>
      key.toLowerCase().includes(allowedKey.toLowerCase()),
    );
  }

  /**
   * Endpoint de health check para verificar se o serviço está funcionando
   */
  @Get('health/check')
  @Roles('ADMIN')
  async healthCheck(@TenantId() tenantId: string) {
    const configCount = Object.keys(await this.configurationService.getAll(tenantId)).length;
    
    return {
      status: 'ok',
      service: 'ConfigurationService',
      configsLoaded: configCount,
      cacheEnabled: true,
      encryptionEnabled: true,
    };
  }
}