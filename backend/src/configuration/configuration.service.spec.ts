import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService } from './configuration.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigModule } from '@nestjs/config';

describe('ConfigurationService', () => {
  let service: ConfigurationService;
  let prisma: PrismaService;

  const mockPrismaService = {
    configuration: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        ConfigurationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ConfigurationService>(ConfigurationService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('set', () => {
    it('should create a new configuration', async () => {
      const key = 'test-key';
      const value = { test: 'value' };
      const tenantId = 'tenant-1';

      mockPrismaService.configuration.upsert.mockResolvedValue({
        id: 'config-1',
        key,
        value,
        tenantId,
      });

      await service.set(key, value, tenantId);

      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledWith({
        where: {
          tenantId_key: { tenantId, key },
        },
        update: { value },
        create: { tenantId, key, value },
      });
    });
  });

  describe('get', () => {
    it('should return configuration value', async () => {
      const key = 'test-key';
      const value = { test: 'value' };
      const tenantId = 'tenant-1';

      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: 'config-1',
        key,
        value,
        tenantId,
      });

      const result = await service.get(key, tenantId);

      expect(result).toEqual(value);
      expect(mockPrismaService.configuration.findUnique).toHaveBeenCalledWith({
        where: {
          tenantId_key: { tenantId, key },
        },
      });
    });

    it('should return null when configuration not found', async () => {
      mockPrismaService.configuration.findUnique.mockResolvedValue(null);

      const result = await service.get('non-existent', 'tenant-1');

      expect(result).toBeNull();
    });
  });

  describe('getAll', () => {
    it('should return all configurations for tenant', async () => {
      const tenantId = 'tenant-1';
      const configs = [
        { key: 'key1', value: 'value1' },
        { key: 'key2', value: { nested: 'value' } },
      ];

      mockPrismaService.configuration.findMany.mockResolvedValue(configs);

      const result = await service.getAll(tenantId);

      expect(result).toEqual({
        key1: 'value1',
        key2: { nested: 'value' },
      });
      expect(mockPrismaService.configuration.findMany).toHaveBeenCalledWith({
        where: { tenantId },
      });
    });
  });

  describe('delete', () => {
    it('should delete configuration', async () => {
      const key = 'test-key';
      const tenantId = 'tenant-1';

      await service.delete(key, tenantId);

      expect(mockPrismaService.configuration.delete).toHaveBeenCalledWith({
        where: {
          tenantId_key: { tenantId, key },
        },
      });
    });
  });

  describe('exists', () => {
    it('should return true when configuration exists', async () => {
      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: 'config-1',
        key: 'test-key',
        value: 'test-value',
      });

      const result = await service.exists('test-key', 'tenant-1');

      expect(result).toBe(true);
    });

    it('should return false when configuration does not exist', async () => {
      mockPrismaService.configuration.findUnique.mockResolvedValue(null);

      const result = await service.exists('test-key', 'tenant-1');

      expect(result).toBe(false);
    });
  });

  describe('getMultiple', () => {
    it('should return multiple configurations', async () => {
      const keys = ['key1', 'key2'];
      const tenantId = 'tenant-1';
      const configs = [
        { key: 'key1', value: 'value1' },
        { key: 'key2', value: 'value2' },
      ];

      mockPrismaService.configuration.findMany.mockResolvedValue(configs);

      const result = await service.getMultiple(keys, tenantId);

      expect(result).toEqual({
        key1: 'value1',
        key2: 'value2',
      });
      expect(mockPrismaService.configuration.findMany).toHaveBeenCalledWith({
        where: {
          tenantId,
          key: { in: keys },
        },
      });
    });
  });

  describe('setMultiple', () => {
    it('should set multiple configurations', async () => {
      const configs = {
        key1: 'value1',
        key2: { nested: 'value' },
      };
      const tenantId = 'tenant-1';

      await service.setMultiple(configs, tenantId);

      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledTimes(2);
      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledWith({
        where: {
          tenantId_key: { tenantId, key: 'key1' },
        },
        update: { value: 'value1' },
        create: { tenantId, key: 'key1', value: 'value1' },
      });
      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledWith({
        where: {
          tenantId_key: { tenantId, key: 'key2' },
        },
        update: { value: { nested: 'value' } },
        create: { tenantId, key: 'key2', value: { nested: 'value' } },
      });
    });
  });

  describe('clearCache', () => {
    it('should clear the cache', () => {
      // Just verify the method doesn't throw
      expect(() => service.clearCache()).not.toThrow();
    });
  });
});