import { PrismaClient } from '@prisma/client';

export async function checkDatabaseConnection(): Promise<void> {
  const prisma = new PrismaClient();
  const maxRetries = 10;
  let retries = 0;

  console.log('🔍 Checking database connection...');

  while (retries < maxRetries) {
    try {
      await prisma.$connect();
      console.log('✅ Database connected successfully!');
      await prisma.$disconnect();
      return;
    } catch (error) {
      retries++;
      console.error(`❌ Database connection attempt ${retries}/${maxRetries} failed:`, error.message);
      
      if (retries === maxRetries) {
        throw new Error('Failed to connect to database after maximum retries');
      }
      
      // Wait before retrying (exponential backoff)
      const waitTime = Math.min(1000 * Math.pow(2, retries), 10000);
      console.log(`⏳ Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}