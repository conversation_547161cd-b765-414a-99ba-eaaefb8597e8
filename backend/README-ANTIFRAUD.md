# Anti-Fraud System Implementation Guide

## Phase 0 Progress - Foundation & Infrastructure ✅

### Completed Items:

#### 0.1 Multi-Tenant Architecture Setup ✅
- [x] **Prisma Schema Updated**: Added `tenantId` to User, Order, Product, Customer, Kit, Configuration models
- [x] **Tenant Middleware**: Created `/src/common/middleware/tenant.middleware.ts`
- [x] **Tenant Decorators**: Created `/src/common/decorators/tenant.decorator.ts`
- [x] **Tenant Scope Interceptor**: Created `/src/common/interceptors/tenant-scope.interceptor.ts`
- [x] **Tenant Auth Guard**: Created `/src/common/guards/tenant-auth.guard.ts`
- [x] **Tenant Module**: Created `/src/common/module/tenant.module.ts`
- [x] **App Module Updated**: Registered tenant middleware globally

#### 0.2 Environment & Deployment Infrastructure ✅
- [x] **Environment Files**: Created comprehensive `.env.example` files for both frontend and backend
- [x] **Feature Flags**: Implemented feature flag system in `/src/utils/featureFlags.ts`
- [x] **Feature Flag Context**: Created React context in `/src/contexts/FeatureFlagContext.tsx`

#### 0.3 Core Infrastructure Components (Partial)
- [x] **Tenant Exception Filter**: Created `/src/common/filters/tenant-exception.filter.ts`
- [ ] Sentry integration with tenant context (pending)
- [ ] OpenTelemetry setup (pending)

#### 0.4 Security Foundation ✅
- [x] **Encryption Utilities**: Created `/src/common/utils/encryption.util.ts` with:
  - AES-256-GCM encryption for sensitive data
  - CPF-specific encryption with format preservation
  - Hash functions for searchable fields
  - Key generation utilities

### Enhanced PrismaService Features:
- **Automatic Tenant Filtering**: All queries are automatically scoped to the current tenant
- **Async Context Support**: Uses AsyncLocalStorage for proper tenant isolation
- **Middleware for All Operations**: Covers findMany, create, update, delete, etc.
- **Tenant Context Runner**: `runInTenantContext()` method for background jobs

## Phase 1 Progress - Database Design & Core Services ✅

### Completed Items:

1. **Anti-Fraud Database Schema** ✅
   - Added `DuplicateStatus`, `ReviewDecision`, and `AuditAction` enums
   - Extended `Order` model with anti-fraud fields:
     - `customerCPF` (encrypted)
     - `customerCPFHash` (for searching)
     - `fullAddress`
     - `isDuplicate`, `duplicateStatus`, `duplicateMatchScore`
     - Review tracking fields
   - Created `OrderAddressComponents` model for parsed addresses
   - Created `OrderAuditLog` model with cryptographic signatures

2. **Prisma Migration Applied** ✅
   - Database schema synced with `npx prisma db push`
   - All models and indexes created successfully

## Phase 2 Progress - Anti-Fraud Services ✅

### Completed Services:

1. **Brazilian Address Parser** (`brazilian-address.parser.ts`) ✅
   - Parses Brazilian addresses into structured components
   - Handles common abbreviations and formats
   - Normalizes addresses for matching
   - Extracts: street, number, complement, neighborhood, city, state, CEP

2. **Phonetic Encoder Service** (`phonetic-encoder.service.ts`) ✅
   - Soundex algorithm adapted for Portuguese
   - Metaphone algorithm for Portuguese pronunciation
   - Double Metaphone for alternate encodings
   - Phonetic similarity scoring

3. **Fuzzy Matching Service** (`fuzzy-matching.service.ts`) ✅
   - Multi-algorithm address comparison
   - Component-wise matching with weights
   - Levenshtein distance for string similarity
   - Handles abbreviations and common variations
   - Returns detailed match scores and confidence levels

4. **Duplicate Detection Service** (`duplicate-detection.service.ts`) ✅
   - Fast CPF + address matching
   - Configurable timeout (2s default)
   - Returns matched orders with scores
   - Saves parsed address components for future searches

5. **Anti-Fraud Service** (`antifraud.service.ts`) ✅
   - Main orchestration service
   - Processes orders for duplicate detection
   - Manages review queue
   - Creates cryptographically signed audit logs
   - Handles duplicate review decisions

## Phase 3 Progress - Integration ✅

### Completed Integrations:

1. **Order Creation Integration** ✅
   - Updated `CreateOrderDto` to include `customerCPF` and `fullAddress`
   - Modified `OrdersService.create()` to trigger anti-fraud check
   - Anti-fraud check runs asynchronously to not block order creation
   - Automatic tenant context propagation

2. **Anti-Fraud Module** ✅
   - Created `AntifraudModule` with all services
   - Added to `AppModule` imports
   - Integrated with `OrdersModule`

3. **API Endpoints** ✅
   - `GET /antifraud/duplicates/review-queue` - Get pending duplicates
   - `POST /antifraud/duplicates/:orderId/review` - Review a duplicate
   - `GET /antifraud/orders/:orderId/audit-trail` - Get audit history
   - All endpoints properly secured with role-based access

### Next Steps:

1. **Test the Anti-Fraud System**:
   ```bash
   # Create test orders with duplicate CPF/address
   # Check review queue
   # Test review workflow
   ```

2. **Phase 4 - Frontend Implementation**:
   - Create duplicate review interface
   - Add real-time WebSocket updates
   - Build audit trail viewer

## Important Notes:

### Tenant Middleware Behavior:
- Requires `x-tenant-id` header on all requests (except /health endpoints)
- Validates UUID format
- Automatically attaches to request context

### Feature Flags Available:
- `DUPLICATE_CHECK_ENABLED`
- `FUZZY_MATCHING_ENABLED`
- `AUDIT_TRAIL_ENABLED`
- `REAL_TIME_UPDATES_ENABLED`
- `BULK_REVIEW_ENABLED`
- `ML_SCORING_ENABLED`

### Security Considerations:
- Encryption keys must be set in environment variables
- CPF data is encrypted at rest and hashed for searching
- Audit logs will be cryptographically signed

## Testing the Anti-Fraud System:

1. **Create Test Orders**:
   ```bash
   # Order 1 - Original
   curl -X POST http://localhost:3001/orders \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "x-tenant-id: YOUR_TENANT_ID" \
     -H "Content-Type: application/json" \
     -d '{
       "customerName": "João Silva",
       "customerPhone": "11999999999",
       "customerCPF": "12345678901",
       "fullAddress": "Rua das Flores, 123, Apt 45, Centro, São Paulo - SP, 01000-000",
       "items": [{"productId": "xxx", "productName": "Test", "quantity": 1, "unitPrice": 100}]
     }'
   
   # Order 2 - Duplicate (same CPF, similar address)
   curl -X POST http://localhost:3001/orders \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "x-tenant-id: YOUR_TENANT_ID" \
     -H "Content-Type: application/json" \
     -d '{
       "customerName": "João Silva",
       "customerPhone": "11999999999",
       "customerCPF": "12345678901",
       "fullAddress": "R. das Flores, 123, Apto 45, Centro, São Paulo - SP",
       "items": [{"productId": "xxx", "productName": "Test", "quantity": 1, "unitPrice": 100}]
     }'
   ```

2. **Check Review Queue**:
   ```bash
   curl http://localhost:3001/antifraud/duplicates/review-queue \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "x-tenant-id: YOUR_TENANT_ID"
   ```

3. **Review a Duplicate**:
   ```bash
   curl -X POST http://localhost:3001/antifraud/duplicates/ORDER_ID/review \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "x-tenant-id: YOUR_TENANT_ID" \
     -H "Content-Type: application/json" \
     -d '{
       "decision": "APPROVE_ORDER",
       "notes": "Customer confirmed different orders"
     }'
   ```