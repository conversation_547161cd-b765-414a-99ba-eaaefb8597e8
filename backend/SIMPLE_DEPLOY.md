# Simple Railway Deployment (No Health Checks)

## 1. In Railway Dashboard:

### Delete the Failed Service (if exists)
- Click on the failed backend service
- Go to Settings → Delete Service

### Create New Service
1. Click "+ New" → "GitHub Repo"
2. Select your repository: `gilsonljr/zencash`
3. Set root directory to: `/backend`

### Add Environment Variables
Go to Variables tab and add these (use Raw Editor):

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=zencash-super-secret-jwt-key-2024
API_PREFIX=api/v1
NODE_ENV=production
CORS_ORIGIN=https://zencash-sand.vercel.app
```

**DO NOT ADD PORT** - Railway provides it automatically

### Deploy
Click Deploy or wait for automatic deployment

## 2. Check Logs

Once deployed, go to Logs tab and look for:
```
Starting NestJS application...
Environment: production
Port: [some number]
🚀 NestJS Application Started Successfully!
```

## 3. Test Your API

Once you see your Railway URL (e.g., `https://backend-production-xxxx.up.railway.app`):

### Test root endpoint:
```bash
curl https://[your-url]/
```

Should return:
```json
{
  "message": "ZenCash Backend API is running!",
  "timestamp": "...",
  "environment": "production"
}
```

### Test ping:
```bash
curl https://[your-url]/ping
```
Should return: `pong`

### Test API endpoints:
```bash
curl https://[your-url]/api/v1/health
```

## 4. Update Vercel

Once backend is running:
1. Go to Vercel dashboard
2. Add environment variables:
   ```
   REACT_APP_API_URL=https://[your-railway-url]/api/v1
   REACT_APP_TENANT_ID=************************************
   ```
3. Redeploy frontend

## 5. If Still Failing

Check Railway logs for:
- Database connection errors
- Missing environment variables
- Port binding issues

Common fixes:
- Make sure DATABASE_URL uses `postgres.railway.internal`
- Ensure backend and database are in same Railway project
- Don't set PORT manually