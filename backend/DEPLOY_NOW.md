# Deploy Backend to Railway - Complete Steps

Since you have project ID: `afe56c34-d890-4fde-9fe6-b18f21095611`

## Step 1: Login to Railway
```bash
railway login
```

## Step 2: Link to Your Project
```bash
# Run this command and select your project from the list
railway link
```
When prompted:
1. Select "gilsonljr's Projects" (or your workspace)
2. Select the project that contains your PostgreSQL database
3. Select "production" environment

## Step 3: Verify Link
```bash
railway status
```

## Step 4: Set All Environment Variables
```bash
# Copy and paste all these commands one by one:

railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"

railway variables set JWT_SECRET="zencash-jwt-secret-super-secure-2024-$(openssl rand -hex 16)"

railway variables set API_PREFIX="api/v1"

railway variables set NODE_ENV="production"

railway variables set PORT="3000"

railway variables set CORS_ORIGIN="https://zencash-sand.vercel.app"
```

## Step 5: Deploy
```bash
railway up
```

## Step 6: Get Your Backend URL
```bash
railway open
```

Your URL will be something like: `https://[service-name]-production.up.railway.app`

## Step 7: Test Your Backend

### Health Check:
```bash
# Replace [your-url] with your actual Railway URL
curl https://[your-url]/api/v1/health
```

### Check Logs:
```bash
railway logs
```

## Step 8: Update Vercel Frontend

Go to Vercel Dashboard and add these environment variables:
```
REACT_APP_API_URL=https://[your-railway-url]/api/v1
REACT_APP_TENANT_ID=************************************
```

Then redeploy your frontend on Vercel.

## Alternative: Using Railway Dashboard

If the CLI is giving issues:

1. Go to https://railway.app/dashboard
2. Open your PostgreSQL project
3. Click "+ New" → "Empty Service" 
4. Go to Settings → Connect GitHub repo (if you want automatic deploys)
5. Or use "Deploy from CLI" option
6. Add all environment variables in the Variables tab
7. Deploy!

## Quick Test After Deployment

```bash
# Test health endpoint
curl https://[your-url]/api/v1/health

# Test login
curl -X POST https://[your-url]/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: ************************************" \
  -d '{"email":"<EMAIL>","password":"Gilson123$"}'
```