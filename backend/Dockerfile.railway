# Railway-optimized Dockerfile for ZenCash backend
FROM node:20-alpine

# Install dependencies for building native modules
RUN apk add --no-cache python3 make g++ dumb-init

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci && \
    npx prisma generate

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Set environment
ENV NODE_ENV=production

# Railway provides PORT dynamically, don't hardcode it
# The healthcheck endpoint will be available at /health

# Create a simple startup script inline
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'echo "Starting ZenCash Backend on port ${PORT:-5000}..."' >> /app/start.sh && \
    echo 'npx prisma generate' >> /app/start.sh && \
    echo 'echo "Attempting database migration..."' >> /app/start.sh && \
    echo 'npx prisma migrate deploy || echo "Migration failed - continuing anyway"' >> /app/start.sh && \
    echo 'echo "Starting application..."' >> /app/start.sh && \
    echo 'exec node dist/main' >> /app/start.sh && \
    chmod +x /app/start.sh

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["/app/start.sh"]