#!/bin/bash

# Railway Deployment Script for ZenCash Backend

echo "🚀 Starting Railway deployment for ZenCash Backend..."

# Check if railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    npm install -g @railway/cli
fi

# Check if we're in the correct directory
if [ ! -f "package.json" ] || [ ! -f "nest-cli.json" ]; then
    echo "❌ Error: Must run from the backend directory"
    exit 1
fi

# Login to Railway
echo "📝 Logging into Railway..."
railway login

# Check if project is already linked
if [ ! -f ".railway/config.json" ]; then
    echo "🔗 No Railway project linked. Please follow these steps:"
    echo "1. Go to your Railway dashboard"
    echo "2. Find your existing project with PostgreSQL"
    echo "3. Copy the project ID"
    echo "4. Run: railway link [PROJECT_ID]"
    exit 1
fi

# Set environment variables
echo "⚙️  Setting environment variables..."

# Database URL (using the one you provided)
railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"

# Other required variables
railway variables set JWT_SECRET="zencash-jwt-secret-$(openssl rand -hex 32)"
railway variables set API_PREFIX="api/v1"
railway variables set NODE_ENV="production"
railway variables set CORS_ORIGIN="https://zencash-sand.vercel.app"
railway variables set PORT="3000"

echo "✅ Environment variables configured"

# Deploy
echo "🚂 Deploying to Railway..."
railway up

echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'railway open' to get your backend URL"
echo "2. Update Vercel environment variables:"
echo "   REACT_APP_API_URL=https://[your-backend-url]/api/v1"
echo "   REACT_APP_TENANT_ID=28a833c0-c2a1-4498-85ca-b028f982ffb2"
echo "3. Test the health endpoint: https://[your-backend-url]/api/v1/health"
echo "4. Monitor logs with: railway logs"