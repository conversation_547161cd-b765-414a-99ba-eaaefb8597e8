import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkEnumValues() {
  try {
    // Get current enum values from database
    const enumValues = await prisma.$queryRaw`
      SELECT unnest(enum_range(NULL::"OrderStatus"))::text as value
    `;
    
    console.log('Current OrderStatus enum values in database:');
    console.log(enumValues);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkEnumValues();