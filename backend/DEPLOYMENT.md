# ZenCash Backend Deployment Guide

## Overview
This guide documents the automated deployment process for the ZenCash NestJS + Prisma backend on Railway.

## Automated Deployment Pipeline

### 1. Local Development
```bash
# Install dependencies
npm install

# Setup local database
npm run setup:postgres

# Run migrations
npm run db:migrate

# Seed database
npm run db:seed

# Start development server
npm run start:dev
```

### 2. Pre-Deployment Checklist
- [ ] All tests passing: `npm test`
- [ ] Prisma schema valid: `npx prisma validate`
- [ ] Migrations generated: `npx prisma migrate dev`
- [ ] Environment variables set in Railway

### 3. Deployment Process

#### Automatic Deployment (Recommended)
Simply push to the main branch:
```bash
git add .
git commit -m "feat: your changes"
git push origin main
```

The following happens automatically:
1. GitHub Actions runs tests
2. Railway detects the push
3. Railway runs the build command
4. Deployment script handles migrations and seeding
5. Application starts with health checks

#### Manual Deployment
```bash
# Login to Railway
railway login

# Link to project
railway link

# Deploy
railway up

# Check logs
railway logs
```

### 4. Post-Deployment Verification

#### Health Check
```bash
curl https://your-api.railway.app/api/v1/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-06-30T00:00:00.000Z",
  "database": "connected",
  "version": "1.0.0"
}
```

#### Database Verification
```bash
# Check migrations
railway run npx prisma migrate status

# Open Prisma Studio
railway run npx prisma studio
```

## Environment Variables

Required environment variables in Railway:

```env
# Database
DATABASE_URL=postgresql://user:password@host:port/database

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=30d

# App
PORT=3000
NODE_ENV=production

# Admin User (for seeding)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
```

## Troubleshooting

### Database Connection Issues
```bash
# Test connection
railway run npx prisma db execute --sql "SELECT 1"

# Check DATABASE_URL
railway variables
```

### Migration Issues
```bash
# Check migration status
railway run npx prisma migrate status

# Reset migrations (CAUTION: Data loss)
railway run npx prisma migrate reset --force
```

### Deployment Failures
```bash
# Check build logs
railway logs --build

# Check runtime logs
railway logs

# Restart service
railway restart
```

## Rollback Procedure

1. **Immediate Rollback**
   ```bash
   # Revert to previous deployment
   railway rollback
   ```

2. **Database Rollback**
   ```bash
   # Only if migrations were applied
   railway run npx prisma migrate resolve --rolled-back
   ```

## Monitoring

### Logs
- Build logs: `railway logs --build`
- Runtime logs: `railway logs`
- Real-time logs: `railway logs -f`

### Metrics
- Memory usage: Railway Dashboard → Metrics
- CPU usage: Railway Dashboard → Metrics
- Response times: Railway Dashboard → Metrics

### Alerts
Set up alerts in Railway Dashboard for:
- Health check failures
- High memory usage
- Deployment failures

## Security Best Practices

1. **Never commit sensitive data**
   - Use environment variables
   - Keep .env files in .gitignore

2. **Rotate secrets regularly**
   - JWT_SECRET
   - Database passwords
   - API keys

3. **Keep dependencies updated**
   ```bash
   npm audit
   npm update
   ```

4. **Use Railway's private networking**
   - Database should not be publicly accessible
   - Use Railway's internal URLs

## Backup and Recovery

### Database Backup
```bash
# Manual backup
railway run pg_dump $DATABASE_URL > backup.sql

# Restore from backup
railway run psql $DATABASE_URL < backup.sql
```

### Automated Backups
Configure in Railway Dashboard:
- Daily backups
- 7-day retention
- Point-in-time recovery

## Performance Optimization

1. **Enable connection pooling**
   ```prisma
   datasource db {
     provider = "postgresql"
     url      = env("DATABASE_URL")
     directUrl = env("DIRECT_URL")
   }
   ```

2. **Set appropriate resource limits**
   - Memory: 512MB minimum
   - CPU: 0.5 vCPU minimum

3. **Enable caching**
   - Redis for session storage
   - CDN for static assets

## Support

For deployment issues:
1. Check Railway status page
2. Review deployment logs
3. Consult Railway documentation
4. Open support ticket if needed