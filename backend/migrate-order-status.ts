import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateOrderStatus() {
  console.log('🔄 Starting order status migration...\n');

  try {
    // Map old status to new status
    const statusMap = {
      'PENDENTE': 'ANALISE',
      'EM_SEPARACAO': 'SEPARACAO',
      'ENVIADO': 'TRANSITO',
      'FALHA': 'ENTREGA_FALHA',
      'PAGAMENTO_PARCIAL': 'PARCIAL',
    };

    // Get count of orders for each old status
    for (const [oldStatus, newStatus] of Object.entries(statusMap)) {
      const count = await prisma.$executeRawUnsafe(
        `SELECT COUNT(*) as count FROM "Order" WHERE status = '${oldStatus}'::text::"OrderStatus"`
      );
      console.log(`Found ${count[0]?.count || 0} orders with status ${oldStatus}`);
    }

    console.log('\n📝 Updating order statuses...');

    // Update each status
    for (const [oldStatus, newStatus] of Object.entries(statusMap)) {
      const result = await prisma.$executeRawUnsafe(
        `UPDATE "Order" SET status = '${newStatus}'::text::"OrderStatus" WHERE status = '${oldStatus}'::text::"OrderStatus"`
      );
      console.log(`Updated ${oldStatus} → ${newStatus}`);
    }

    // Update status history
    console.log('\n📜 Updating status history...');
    for (const [oldStatus, newStatus] of Object.entries(statusMap)) {
      await prisma.$executeRawUnsafe(
        `UPDATE "StatusHistory" SET "previousStatus" = '${newStatus}'::text::"OrderStatus" WHERE "previousStatus" = '${oldStatus}'::text::"OrderStatus"`
      );
      await prisma.$executeRawUnsafe(
        `UPDATE "StatusHistory" SET "newStatus" = '${newStatus}'::text::"OrderStatus" WHERE "newStatus" = '${oldStatus}'::text::"OrderStatus"`
      );
    }

    console.log('\n✅ Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

migrateOrderStatus()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });