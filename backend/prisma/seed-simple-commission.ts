import { PrismaClient, Role, OrderStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database with simple commission data...');

  // Create users
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('Admin@123', 10),
      role: Role.ADMIN,
    },
  });

  const supervisor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Supervisor',
      email: '<EMAIL>',
      password: await bcrypt.hash('Supervisor@123', 10),
      role: Role.SUPERVISOR,
    },
  });

  const seller = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Vendedor 1',
      email: '<EMAIL>',
      password: await bcrypt.hash('Vendedor1@123', 10),
      role: Role.VENDEDOR,
    },
  });

  const collector = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Cobrador 1',
      email: '<EMAIL>',
      password: await bcrypt.hash('Cobrador1@123', 10),
      role: Role.COBRADOR,
    },
  });

  // Create commission settings
  await prisma.commissionSetting.upsert({
    where: { userId: seller.id },
    update: { percentage: 5.0 },
    create: {
      userId: seller.id,
      percentage: 5.0,
      role: Role.VENDEDOR,
    },
  });

  await prisma.commissionSetting.upsert({
    where: { userId: collector.id },
    update: { percentage: 3.0 },
    create: {
      userId: collector.id,
      percentage: 3.0,
      role: Role.COBRADOR,
    },
  });

  // Create some orders with different statuses
  const order1 = await prisma.order.create({
    data: {
      customerName: 'Cliente Teste 1',
      customerPhone: '11999999999',
      total: 1500.00,
      status: OrderStatus.COMPLETO,
      sellerId: seller.id,
      collectorId: collector.id,
      commissionApprovalStatus: 'NONE',
    },
  });

  const order2 = await prisma.order.create({
    data: {
      customerName: 'Cliente Teste 2',
      customerPhone: '11888888888',
      total: 2000.00,
      status: OrderStatus.PAGAMENTO_PARCIAL,
      sellerId: seller.id,
      collectorId: collector.id,
      commissionApprovalStatus: 'NONE',
    },
  });

  console.log('Seed completed successfully!');
  console.log(`Created users: ${admin.email}, ${supervisor.email}, ${seller.email}, ${collector.email}`);
  console.log(`Created orders: ${order1.id} (COMPLETO), ${order2.id} (PAGAMENTO_PARCIAL)`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });