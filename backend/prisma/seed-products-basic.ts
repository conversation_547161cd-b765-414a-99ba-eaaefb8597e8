import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Criando produtos básicos...');

  const products = [
    {
      name: 'Camiseta Básica',
      description: 'Camiseta 100% algodão',
      variations: [
        { variation: 'P', price: new Decimal(29.90), sku: 'CAM-BAS-P' },
        { variation: 'M', price: new Decimal(29.90), sku: 'CAM-BAS-M' },
        { variation: 'G', price: new Decimal(29.90), sku: 'CAM-BAS-G' },
        { variation: 'GG', price: new Decimal(29.90), sku: 'CAM-BAS-GG' },
      ],
    },
    {
      name: '<PERSON><PERSON> Jeans',
      description: 'Calça jeans tradicional',
      variations: [
        { variation: '38', price: new Decimal(89.90), sku: 'CAL-JNS-38' },
        { variation: '40', price: new Decimal(89.90), sku: 'CAL-JNS-40' },
        { variation: '42', price: new Decimal(89.90), sku: 'CAL-JNS-42' },
        { variation: '44', price: new Decimal(89.90), sku: 'CAL-JNS-44' },
      ],
    },
    {
      name: 'Tênis Esportivo',
      description: 'Tênis para prática esportiva',
      variations: [
        { variation: '38', price: new Decimal(149.90), sku: 'TEN-ESP-38' },
        { variation: '39', price: new Decimal(149.90), sku: 'TEN-ESP-39' },
        { variation: '40', price: new Decimal(149.90), sku: 'TEN-ESP-40' },
        { variation: '41', price: new Decimal(149.90), sku: 'TEN-ESP-41' },
        { variation: '42', price: new Decimal(149.90), sku: 'TEN-ESP-42' },
      ],
    },
    {
      name: 'Jaqueta Corta Vento',
      description: 'Jaqueta impermeável',
      variations: [
        { variation: 'P', price: new Decimal(199.90), sku: 'JAQ-CRT-P' },
        { variation: 'M', price: new Decimal(199.90), sku: 'JAQ-CRT-M' },
        { variation: 'G', price: new Decimal(199.90), sku: 'JAQ-CRT-G' },
        { variation: 'GG', price: new Decimal(199.90), sku: 'JAQ-CRT-GG' },
      ],
    },
    {
      name: 'Boné Ajustável',
      description: 'Boné com ajuste traseiro',
      variations: [
        { variation: 'Único', price: new Decimal(39.90), sku: 'BON-AJU-UN' },
      ],
    },
  ];

  for (const productData of products) {
    const { variations, ...product } = productData;
    
    const createdProduct = await prisma.product.create({
      data: {
        ...product,
        active: true,
        variations: {
          create: variations.map(v => ({
            ...v,
            active: true,
          })),
        },
      },
    });

    console.log(`✅ Produto criado: ${createdProduct.name}`);
  }

  // Criar também alguns clientes básicos
  const customers = [
    { name: 'João Silva', email: '<EMAIL>', phone: '11999999999', cpf: '12345678901' },
    { name: 'Maria Santos', email: '<EMAIL>', phone: '11888888888', cpf: '23456789012' },
    { name: 'Pedro Oliveira', email: '<EMAIL>', phone: '11777777777', cpf: '34567890123' },
    { name: 'Ana Costa', email: '<EMAIL>', phone: '11666666666', cpf: '45678901234' },
    { name: 'Carlos Souza', email: '<EMAIL>', phone: '11555555555', cpf: '56789012345' },
  ];

  for (const customer of customers) {
    const existingCustomer = await prisma.customer.findUnique({
      where: { cpf: customer.cpf },
    });

    if (!existingCustomer) {
      await prisma.customer.create({ data: customer });
      console.log(`✅ Cliente criado: ${customer.name}`);
    }
  }

  console.log('\n✅ Seed de produtos básicos concluído!');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });