// This is the new product schema structure

model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  imageUrl    String?
  isActive    Boolean     @default(true)
  tenantId    String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  tenant      Tenant      @relation(fields: [tenantId], references: [id])
  variations  ProductVariation[]
  kits        Kit[]
  
  @@index([tenantId])
}

model ProductVariation {
  id          String      @id @default(cuid())
  productId   String
  type        VariationType
  customName  String?     // Used when type is CUSTOM
  costPrice   Float       @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  kitItems    KitItem[]
  
  @@unique([productId, type, customName])
  @@index([productId])
}

model Kit {
  id          String      @id @default(cuid())
  productId   String
  sku         String      @unique // Auto-generated SKU
  name        String
  description String?
  price       Float
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  items       KitItem[]
  orders      Order[]
  
  @@index([productId])
  @@index([sku])
}

model KitItem {
  id          String      @id @default(cuid())
  kitId       String
  variationId String
  quantity    Int
  
  kit         Kit         @relation(fields: [kitId], references: [id], onDelete: Cascade)
  variation   ProductVariation @relation(fields: [variationId], references: [id])
  
  @@unique([kitId, variationId])
  @@index([kitId])
  @@index([variationId])
}

enum VariationType {
  CAPSULAS
  GOTAS
  GEL
  SPRAY
  CREME
  CUSTOM
}

// Update Order model to reference Kit instead of Offer
model Order {
  id                        String      @id @default(cuid())
  idVenda                   String      @unique
  cliente                   String
  vendedor                  String
  dataVenda                 DateTime
  telefone                  String
  documentoCliente          String?
  kitId                     String?     // Changed from offerId
  valorVenda                Float
  estadoDestinatario        String
  cidadeDestinatario        String
  ruaDestinatario           String
  cepDestinatario           String
  complementoDestinatario   String?
  bairroDestinatario        String
  numeroEnderecoDestinatario String
  operador                  String?
  situacaoVenda             OrderStatus @default(PENDING)
  rastreio                  String?
  statusCorreios            String?
  tenantId                  String
  zapId                     String?
  createdAt                 DateTime    @default(now())
  updatedAt                 DateTime    @updatedAt
  
  tenant                    Tenant      @relation(fields: [tenantId], references: [id])
  kit                       Kit?        @relation(fields: [kitId], references: [id])
  zap                       Zap?        @relation(fields: [zapId], references: [id])
  trackingHistory           TrackingEvent[]
  
  @@index([idVenda])
  @@index([telefone])
  @@index([documentoCliente])
  @@index([dataVenda])
  @@index([tenantId])
  @@index([vendedor])
  @@index([kitId])
}