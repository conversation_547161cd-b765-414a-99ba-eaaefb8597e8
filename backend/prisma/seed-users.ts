import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Fixed tenant ID for all users (acme-corp)
  const tenantId = 'b5c6e7d8-9a0b-1c2d-3e4f-567890abcdef';
  
  // Hash das senhas
  const hashedPassword = await bcrypt.hash('senha123', 10);

  // Criar ADMIN
  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON>stra<PERSON>',
      password: hashedPassword,
      role: 'ADMIN',
      active: true,
      tenantId: tenantId,
    },
  });

  // Criar SUPERVISOR
  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Supervisor Teste',
      password: hashedPassword,
      role: 'SUPERVISOR',
      active: true,
      tenantId: tenantId,
    },
  });

  // Criar VENDEDOR
  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Vendedor Teste',
      password: hashedPassword,
      role: 'VENDEDOR',
      active: true,
      tenantId: tenantId,
    },
  });

  // Criar COBRADOR
  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Cobrador Teste',
      password: hashedPassword,
      role: 'COBRADOR',
      active: true,
      tenantId: tenantId,
    },
  });

  // Criar usuário inativo
  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Usuário Inativo',
      password: hashedPassword,
      role: 'VENDEDOR',
      active: false,
      tenantId: tenantId,
    },
  });

  console.log('✅ Usuários de teste criados:');
  console.log('<EMAIL> / senha123 (ADMIN)');
  console.log('<EMAIL> / senha123 (SUPERVISOR)');
  console.log('<EMAIL> / senha123 (VENDEDOR)');
  console.log('<EMAIL> / senha123 (COBRADOR)');
  console.log('<EMAIL> / senha123 (VENDEDOR - Inativo)');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });