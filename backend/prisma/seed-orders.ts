import { PrismaClient, OrderStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function main() {
  // Fixed tenant ID for all users (acme-corp)
  const tenantId = 'b5c6e7d8-9a0b-1c2d-3e4f-567890abcdef';
  
  // Primeiro, criar usuários necessários
  const hashedPassword = await bcrypt.hash('senha123', 10);

  // Criar vendedor
  const vendedor = await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Vendedor Teste',
      password: hashedPassword,
      role: 'VENDEDOR',
      active: true,
      tenantId: tenantId,
    },
  });

  // Criar cobrador
  const cobrador = await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Cobrador Teste',
      password: hashedPassword,
      role: 'COBRADOR',
      active: true,
      tenantId: tenantId,
    },
  });

  console.log('✅ Usuários criados');

  // Criar pedido 1 - PENDENTE
  const order1 = await prisma.order.create({
    data: {
      customerName: 'João Silva',
      customerPhone: '11999999999',
      total: new Decimal(250.00),
      status: OrderStatus.Analise,
      sellerId: vendedor.id,
      tenantId: tenantId,
      items: {
        create: [
          {
            productId: 'prod-001',
            productName: 'Produto A',
            quantity: 2,
            unitPrice: new Decimal(50.00),
          },
          {
            productId: 'prod-002',
            productName: 'Produto B',
            quantity: 1,
            unitPrice: new Decimal(150.00),
          },
        ],
      },
      statusHistory: {
        create: {
          previousStatus: OrderStatus.Analise,
          newStatus: OrderStatus.Analise,
          changedById: vendedor.id,
        },
      },
    },
  });

  // Criar pedido 2 - EM SEPARAÇÃO (com cobrador)
  const order2 = await prisma.order.create({
    data: {
      customerName: 'Maria Santos',
      customerPhone: '11888888888',
      total: new Decimal(500.00),
      status: OrderStatus.Separacao,
      sellerId: vendedor.id,
      collectorId: cobrador.id,
      tenantId: tenantId,
      items: {
        create: [
          {
            productId: 'prod-003',
            productName: 'Produto C',
            quantity: 5,
            unitPrice: new Decimal(100.00),
          },
        ],
      },
      statusHistory: {
        create: [
          {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Analise,
            changedById: vendedor.id,
            changedAt: new Date(Date.now() - 86400000), // 1 dia atrás
          },
          {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Separacao,
            changedById: cobrador.id,
            changedAt: new Date(),
          },
        ],
      },
    },
  });

  // Criar pedido 3 - ENTREGUE
  const order3 = await prisma.order.create({
    data: {
      customerName: 'Pedro Oliveira',
      customerPhone: '11777777777',
      total: new Decimal(1200.00),
      status: OrderStatus.Completo,
      sellerId: vendedor.id,
      collectorId: cobrador.id,
      tenantId: tenantId,
      items: {
        create: [
          {
            productId: 'prod-001',
            productName: 'Produto A',
            quantity: 10,
            unitPrice: new Decimal(50.00),
          },
          {
            productId: 'prod-004',
            productName: 'Produto D',
            quantity: 2,
            unitPrice: new Decimal(350.00),
          },
        ],
      },
      statusHistory: {
        create: [
          {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Analise,
            changedById: vendedor.id,
            changedAt: new Date(Date.now() - 172800000), // 2 dias atrás
          },
          {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Separacao,
            changedById: cobrador.id,
            changedAt: new Date(Date.now() - 86400000), // 1 dia atrás
          },
          {
            previousStatus: OrderStatus.Separacao,
            newStatus: OrderStatus.Transito,
            changedById: cobrador.id,
            changedAt: new Date(Date.now() - 43200000), // 12 horas atrás
          },
          {
            previousStatus: OrderStatus.Transito,
            newStatus: OrderStatus.Completo,
            changedById: cobrador.id,
            changedAt: new Date(Date.now() - 3600000), // 1 hora atrás
          },
        ],
      },
    },
  });

  console.log('✅ Pedidos de exemplo criados:');
  console.log(`- Pedido ${order1.id}: ${order1.customerName} - Status: PENDENTE`);
  console.log(`- Pedido ${order2.id}: ${order2.customerName} - Status: SEPARACAO`);
  console.log(`- Pedido ${order3.id}: ${order3.customerName} - Status: ENTREGUE`);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });