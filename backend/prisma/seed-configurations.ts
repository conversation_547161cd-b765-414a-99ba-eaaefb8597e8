import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

// Chave de criptografia para o seed (em produção usar do .env)
const ENCRYPTION_KEY = 'zencash-encryption-key-32-chars!';
const algorithm = 'aes-256-gcm';

/**
 * Criptografa valores sensíveis
 */
function encrypt(text: string): string {
  const iv = crypto.randomBytes(16);
  const key = Buffer.from(ENCRYPTION_KEY.substring(0, 32), 'utf8');
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return JSON.stringify({
    encrypted,
    authTag: authTag.toString('hex'),
    iv: iv.toString('hex'),
  });
}

async function main() {
  console.log('🌱 Iniciando seed de configurações...');

  const configurations = [
    // Configurações do WhatsApp
    {
      key: 'whatsapp_api_key',
      value: encrypt(JSON.stringify({
        apiKey: 'wa_test_key_123456789',
        baseUrl: 'https://api.whatsapp.com/v1',
        phoneNumberId: '1234567890',
      })),
      encrypted: true,
    },
    
    // Credenciais dos Correios
    {
      key: 'correios_credentials',
      value: encrypt(JSON.stringify({
        username: 'correios_user',
        password: 'correios_pass',
        contractNumber: 'CTR123456',
        administrativeCode: 'ADM789',
      })),
      encrypted: true,
    },
    
    // Configurações do Stripe (exemplo)
    {
      key: 'stripe_secret_key',
      value: encrypt(JSON.stringify('sk_test_51234567890abcdefghijklmnop')),
      encrypted: true,
    },
    
    // Limites de alerta de rastreamento
    {
      key: 'tracking_alert_thresholds',
      value: JSON.stringify({
        daysWithoutUpdate: 3,
        maxDeliveryAttempts: 3,
        criticalStatuses: ['EXTRAVIADO', 'DEVOLVIDO', 'CANCELADO'],
        warningStatuses: ['DESTINATÁRIO AUSENTE', 'ENDEREÇO INCORRETO'],
      }),
      encrypted: false,
    },
    
    // Preferências do sistema
    {
      key: 'system_preferences',
      value: JSON.stringify({
        defaultLanguage: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        currency: 'BRL',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        paginationLimit: 20,
        maxFileUploadSize: 10485760, // 10MB
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
      }),
      encrypted: false,
    },
    
    // Template padrão de notificação
    {
      key: 'default_notification_template',
      value: JSON.stringify({
        orderCreated: {
          whatsapp: 'Olá {{customerName}}! Seu pedido #{{orderId}} foi criado com sucesso. Total: R$ {{total}}',
          email: {
            subject: 'Pedido #{{orderId}} confirmado',
            body: 'Prezado(a) {{customerName}},\\n\\nSeu pedido foi confirmado!\\n\\nDetalhes:\\n{{orderDetails}}\\n\\nObrigado pela preferência!',
          },
        },
        statusChanged: {
          whatsapp: 'Olá {{customerName}}! Seu pedido #{{orderId}} mudou para: {{newStatus}}',
          email: {
            subject: 'Atualização do pedido #{{orderId}}',
            body: 'Prezado(a) {{customerName}},\\n\\nSeu pedido teve uma atualização de status.\\n\\nNovo status: {{newStatus}}\\n\\nAcompanhe pelo nosso site.',
          },
        },
        trackingAlert: {
          whatsapp: 'Atenção! Seu pedido #{{orderId}} requer ação: {{alertReason}}',
          email: {
            subject: 'Alerta sobre seu pedido #{{orderId}}',
            body: 'Prezado(a) {{customerName}},\\n\\nIdentificamos uma situação que requer sua atenção:\\n\\n{{alertReason}}\\n\\nEntre em contato conosco.',
          },
        },
      }),
      encrypted: false,
    },
    
    // URLs de webhooks
    {
      key: 'webhook_urls',
      value: JSON.stringify({
        orderStatusChanged: 'https://example.com/webhooks/order-status',
        paymentReceived: 'https://example.com/webhooks/payment',
        trackingUpdated: 'https://example.com/webhooks/tracking',
        lowStock: 'https://example.com/webhooks/inventory',
      }),
      encrypted: false,
    },
    
    // E-mails administrativos
    {
      key: 'admin_emails',
      value: JSON.stringify({
        systemAlerts: ['<EMAIL>', '<EMAIL>'],
        financialReports: ['<EMAIL>', '<EMAIL>'],
        operationalReports: ['<EMAIL>', '<EMAIL>'],
        securityAlerts: ['<EMAIL>', '<EMAIL>'],
      }),
      encrypted: false,
    },
    
    // Configurações de integração com transportadoras
    {
      key: 'shipping_integrations',
      value: JSON.stringify({
        correios: {
          enabled: true,
          apiUrl: 'https://api.correios.com.br/v1',
          timeout: 30000,
        },
        mercadoEnvios: {
          enabled: false,
          apiUrl: 'https://api.mercadolibre.com/shipments',
          timeout: 30000,
        },
      }),
      encrypted: false,
    },
    
    // Configurações de cache
    {
      key: 'cache_settings',
      value: JSON.stringify({
        ttl: {
          products: 3600, // 1 hora
          customers: 1800, // 30 minutos
          orders: 300, // 5 minutos
          reports: 600, // 10 minutos
        },
        maxSize: {
          memory: 104857600, // 100MB
          redis: 1073741824, // 1GB
        },
      }),
      encrypted: false,
    },
    
    // Configurações de segurança
    {
      key: 'security_settings',
      value: JSON.stringify({
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
          expirationDays: 90,
        },
        session: {
          timeout: 3600, // 1 hora
          maxConcurrent: 3,
        },
        rateLimit: {
          windowMs: 900000, // 15 minutos
          maxRequests: 100,
        },
        twoFactorAuth: {
          enabled: false,
          providers: ['google', 'sms'],
        },
      }),
      encrypted: false,
    },
  ];

  let created = 0;
  let updated = 0;

  for (const config of configurations) {
    try {
      const existing = await prisma.configuration.findUnique({
        where: { key: config.key },
      });

      if (existing) {
        await prisma.configuration.update({
          where: { key: config.key },
          data: { value: config.value as any },
        });
        updated++;
        console.log(`✅ Configuração '${config.key}' atualizada`);
      } else {
        await prisma.configuration.create({
          data: {
            key: config.key,
            value: config.value as any,
          },
        });
        created++;
        console.log(`✅ Configuração '${config.key}' criada`);
      }
    } catch (error) {
      console.error(`❌ Erro ao processar '${config.key}':`, error);
    }
  }

  console.log(`\n✅ Seed de configurações concluído!`);
  console.log(`- ${created} configurações criadas`);
  console.log(`- ${updated} configurações atualizadas`);
  console.log(`- Total: ${configurations.length} configurações processadas`);
  
  console.log('\n📋 Configurações disponíveis:');
  configurations.forEach(config => {
    const status = config.encrypted ? '[CRIPTOGRAFADA]' : '[TEXTO PLANO]';
    console.log(`  • ${config.key} ${status}`);
  });
  
  console.log('\n🔐 IMPORTANTE: Em produção, defina ENCRYPTION_KEY no arquivo .env');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });