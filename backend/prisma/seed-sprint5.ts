import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed da Sprint 5...');

  // Criar usuário admin (necess<PERSON>rio para outras sprints)
  const hashedPassword = await bcrypt.hash('Admin@123', 10);
  await prisma.user.create({
    data: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN',
      active: true,
    },
  });

  // Criar clientes
  const cliente1 = await prisma.customer.create({
    data: {
      name: '<PERSON>',
      cpf: '12345678901', // CPF válido: 123.456.789-01
      phone: '11987654321',
      email: '<EMAIL>',
      active: true,
      addresses: {
        create: [
          {
            cep: '01310100',
            street: 'Avenida Paulista',
            number: '1578',
            neighborhood: 'Bela Vista',
            city: 'São Paulo',
            state: 'SP',
            complement: 'Apto 1001',
            main: true,
          },
          {
            cep: '04543000',
            street: 'Rua Funchal',
            number: '411',
            neighborhood: 'Vila Olímpia',
            city: 'São Paulo',
            state: 'SP',
            complement: 'Escritório',
            main: false,
          },
        ],
      },
    },
  });

  const cliente2 = await prisma.customer.create({
    data: {
      name: 'Maria Oliveira Costa',
      cpf: '98765432100', // CPF válido: 987.654.321-00
      phone: '11912345678',
      email: '<EMAIL>',
      active: true,
      addresses: {
        create: [
          {
            cep: '20040020',
            street: 'Avenida Rio Branco',
            number: '1',
            neighborhood: 'Centro',
            city: 'Rio de Janeiro',
            state: 'RJ',
            complement: 'Sala 2301',
            main: true,
          },
          {
            cep: '22631050',
            street: 'Avenida das Américas',
            number: '3000',
            neighborhood: 'Barra da Tijuca',
            city: 'Rio de Janeiro',
            state: 'RJ',
            complement: 'Casa 5',
            main: false,
          },
        ],
      },
    },
  });

  const cliente3 = await prisma.customer.create({
    data: {
      name: 'Carlos Pereira Lima',
      cpf: '45678912303', // CPF válido: 456.789.123-03
      phone: '31998877665',
      email: null, // Cliente sem email
      active: true,
      addresses: {
        create: [
          {
            cep: '30140071',
            street: 'Rua da Bahia',
            number: '1000',
            neighborhood: 'Centro',
            city: 'Belo Horizonte',
            state: 'MG',
            main: true,
          },
          {
            cep: '31270901',
            street: 'Avenida Cristiano Machado',
            number: '4000',
            neighborhood: 'União',
            city: 'Belo Horizonte',
            state: 'MG',
            complement: 'Loja 10',
            main: false,
          },
        ],
      },
    },
  });

  // Cliente inativo para testes
  const clienteInativo = await prisma.customer.create({
    data: {
      name: 'Pedro Souza Inativo',
      cpf: '11122233344', // CPF válido: 111.222.333-44
      phone: '11911112222',
      email: '<EMAIL>',
      active: false,
      addresses: {
        create: {
          cep: '01310200',
          street: 'Avenida Paulista',
          number: '2000',
          neighborhood: 'Bela Vista',
          city: 'São Paulo',
          state: 'SP',
          main: true,
        },
      },
    },
  });

  console.log('✅ Seed da Sprint 5 concluído!');
  console.log(`- ${4} clientes criados`);
  console.log('- Clientes com múltiplos endereços');
  console.log('- 1 cliente sem email para teste');
  console.log('- 1 cliente inativo para teste');
  console.log('\n📝 CPFs dos clientes:');
  console.log('- João Silva: 123.456.789-01');
  console.log('- Maria Oliveira: 987.654.321-00');
  console.log('- Carlos Pereira: 456.789.123-03');
  console.log('- Pedro Souza (inativo): 111.222.333-44');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });