import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed da Sprint 4...');

  // Criar produtos
  const camiseta = await prisma.product.create({
    data: {
      name: 'Camiseta Básica',
      description: 'Camiseta 100% algodão, confortável e durável',
      active: true,
    },
  });

  const calca = await prisma.product.create({
    data: {
      name: '<PERSON><PERSON> Jean<PERSON>',
      description: 'Calça jeans tradicional, corte reto',
      active: true,
    },
  });

  const tenis = await prisma.product.create({
    data: {
      name: '<PERSON><PERSON><PERSON>sportivo',
      description: 'Tênis para corrida e caminhada',
      active: true,
    },
  });

  const bone = await prisma.product.create({
    data: {
      name: '<PERSON><PERSON>',
      description: '<PERSON><PERSON> ajustável com aba reta',
      active: true,
    },
  });

  // Criar variações com estoque
  const camisetaP = await prisma.productVariation.create({
    data: {
      productId: camiseta.id,
      variation: 'P - Branca',
      sku: 'CAM-BRA-P',
      price: new Decimal(29.90),
      active: true,
      inventory: {
        create: {
          quantity: 50,
          minAlert: 10,
        },
      },
    },
  });

  const camisetaM = await prisma.productVariation.create({
    data: {
      productId: camiseta.id,
      variation: 'M - Branca',
      sku: 'CAM-BRA-M',
      price: new Decimal(29.90),
      active: true,
      inventory: {
        create: {
          quantity: 100,
          minAlert: 20,
        },
      },
    },
  });

  const camisetaG = await prisma.productVariation.create({
    data: {
      productId: camiseta.id,
      variation: 'G - Preta',
      sku: 'CAM-PRE-G',
      price: new Decimal(29.90),
      active: true,
      inventory: {
        create: {
          quantity: 8, // Baixo estoque
          minAlert: 15,
        },
      },
    },
  });

  const calcaM = await prisma.productVariation.create({
    data: {
      productId: calca.id,
      variation: '38 - Azul',
      sku: 'CAL-AZU-38',
      price: new Decimal(89.90),
      active: true,
      inventory: {
        create: {
          quantity: 30,
          minAlert: 5,
        },
      },
    },
  });

  const calcaG = await prisma.productVariation.create({
    data: {
      productId: calca.id,
      variation: '40 - Azul',
      sku: 'CAL-AZU-40',
      price: new Decimal(89.90),
      active: true,
      inventory: {
        create: {
          quantity: 25,
          minAlert: 5,
        },
      },
    },
  });

  const tenis40 = await prisma.productVariation.create({
    data: {
      productId: tenis.id,
      variation: '40 - Preto/Branco',
      sku: 'TEN-PB-40',
      price: new Decimal(159.90),
      active: true,
      inventory: {
        create: {
          quantity: 15,
          minAlert: 3,
        },
      },
    },
  });

  const tenis42 = await prisma.productVariation.create({
    data: {
      productId: tenis.id,
      variation: '42 - Preto/Branco',
      sku: 'TEN-PB-42',
      price: new Decimal(159.90),
      active: true,
      inventory: {
        create: {
          quantity: 2, // Baixo estoque
          minAlert: 5,
        },
      },
    },
  });

  const boneUnico = await prisma.productVariation.create({
    data: {
      productId: bone.id,
      variation: 'Único - Preto',
      sku: 'BON-PRE-U',
      price: new Decimal(49.90),
      active: true,
      inventory: {
        create: {
          quantity: 40,
          minAlert: 10,
        },
      },
    },
  });

  // Criar kits
  const kitEsportivo = await prisma.kit.create({
    data: {
      name: 'Kit Esportivo Completo',
      description: 'Kit com camiseta, calça e tênis para prática esportiva',
      active: true,
      items: {
        create: [
          {
            productVariationId: camisetaM.id,
            quantity: 1,
          },
          {
            productVariationId: calcaM.id,
            quantity: 1,
          },
          {
            productVariationId: tenis40.id,
            quantity: 1,
          },
        ],
      },
    },
  });

  const kitCasual = await prisma.kit.create({
    data: {
      name: 'Kit Casual',
      description: 'Kit com camiseta e boné para o dia a dia',
      active: true,
      items: {
        create: [
          {
            productVariationId: camisetaP.id,
            quantity: 2, // 2 camisetas
          },
          {
            productVariationId: boneUnico.id,
            quantity: 1,
          },
        ],
      },
    },
  });

  const kitPromocional = await prisma.kit.create({
    data: {
      name: 'Kit Promocional 3 Camisetas',
      description: 'Promoção: Leve 3 camisetas de tamanhos diferentes',
      active: true,
      items: {
        create: [
          {
            productVariationId: camisetaP.id,
            quantity: 1,
          },
          {
            productVariationId: camisetaM.id,
            quantity: 1,
          },
          {
            productVariationId: camisetaG.id,
            quantity: 1,
          },
        ],
      },
    },
  });

  // Produto inativo para testes
  const produtoDescontinuado = await prisma.product.create({
    data: {
      name: 'Jaqueta de Couro',
      description: 'Produto descontinuado',
      active: false,
      variations: {
        create: {
          variation: 'M - Marrom',
          sku: 'JAQ-MAR-M',
          price: new Decimal(299.90),
          active: false,
          inventory: {
            create: {
              quantity: 0,
              minAlert: 5,
            },
          },
        },
      },
    },
  });

  console.log('✅ Seed da Sprint 4 concluído!');
  console.log(`- ${4} produtos criados`);
  console.log(`- ${8} variações criadas`);
  console.log(`- ${3} kits criados`);
  console.log('- Alguns produtos com baixo estoque para teste');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });