import { PrismaClient, OrderStatus, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Criando dados de teste para Dashboard Inteligente...\n');

  // 1. Criar usuários de teste
  const hashedPassword = await bcrypt.hash('senha123', 10);

  const supervisor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Supervisor Teste',
      password: hashedPassword,
      role: Role.SUPERVISOR,
      active: true,
    },
  });

  const vendedor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>endedor Dashboard',
      password: hashedPassword,
      role: Role.VENDEDOR,
      active: true,
    },
  });

  const cobrador = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Cobrador Dashboard',
      password: hashedPassword,
      role: Role.COBRADOR,
      active: true,
    },
  });

  console.log('✓ Usuários criados');

  // 2. Criar clientes
  const clientes = [];
  for (let i = 1; i <= 5; i++) {
    const cliente = await prisma.customer.create({
      data: {
        name: `Cliente Dash ${i}`,
        cpf: `0000000000${i}`,
        phone: `119999999${i}`,
        email: `cliente.dash${i}@email.com`,
        addresses: {
          create: {
            cep: '01310-100',
            street: 'Av. Paulista',
            number: `100${i}`,
            neighborhood: 'Bela Vista',
            city: 'São Paulo',
            state: 'SP',
            main: true,
          },
        },
      },
    });
    clientes.push(cliente);
  }

  console.log('✓ Clientes criados');

  // 3. Criar produto de teste
  const produto = await prisma.product.create({
    data: {
      name: 'Produto Dashboard Teste',
      description: 'Produto para testes do dashboard',
      variations: {
        create: {
          variation: 'Padrão',
          price: new Decimal(100),
          sku: 'DASH-001',
        },
      },
    },
    include: {
      variations: true,
    },
  });

  const variation = produto.variations[0];

  console.log('✓ Produto criado');

  // 4. Criar pedidos para "Receber Hoje"
  const today = new Date();
  today.setHours(12, 0, 0, 0); // Meio-dia de hoje

  console.log('\n📋 Criando pedidos para "Receber Hoje"...');
  for (let i = 0; i < 3; i++) {
    const lastContact = new Date();
    lastContact.setDate(lastContact.getDate() - (i + 1)); // Contato há 1, 2, 3 dias

    await prisma.order.create({
      data: {
        customerName: clientes[i].name,
        customerPhone: clientes[i].phone,
        customerId: clientes[i].id,
        total: new Decimal(100 * (i + 1)),
        status: OrderStatus.PAGAMENTO_PENDENTE,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        nextPaymentDate: today,
        lastContactDate: lastContact,
        items: {
          create: {
            productId: produto.id,
            productVariationId: variation.id,
            productName: produto.name,
            quantity: i + 1,
            unitPrice: variation.price,
          },
        },
      },
    });
  }

  // 5. Criar pedidos em Risco
  console.log('\n⚠️ Criando pedidos em Risco...');
  const riskStatuses = [
    OrderStatus.CONFIRMAR_ENTREGA,
    OrderStatus.ENTREGA_FALHA,
    OrderStatus.RETIRAR_CORREIOS,
  ];

  for (let i = 0; i < 3; i++) {
    const lastContact = new Date();
    lastContact.setDate(lastContact.getDate() - (i + 5)); // Contato há 5, 6, 7 dias

    const order = await prisma.order.create({
      data: {
        customerName: clientes[i].name,
        customerPhone: clientes[i].phone,
        customerId: clientes[i].id,
        total: new Decimal(200 * (i + 1)),
        status: riskStatuses[i],
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        lastContactDate: lastContact,
        items: {
          create: {
            productId: produto.id,
            productVariationId: variation.id,
            productName: produto.name,
            quantity: (i + 1) * 2,
            unitPrice: variation.price,
          },
        },
      },
    });

    // Criar tracking para pedidos em risco
    if (i < 2) {
      await prisma.tracking.create({
        data: {
          orderId: order.id,
          code: `BR${Date.now()}${i}BR`,
          status: i === 0 ? 'Entregue' : 'Destinatário ausente',
          lastUpdate: new Date(),
          isDelivered: i === 0,
          hasAlert: i === 1,
          alertReason: i === 1 ? 'DESTINATÁRIO AUSENTE' : null,
        },
      });
    }
  }

  // 6. Criar pedidos em Separação
  console.log('\n📦 Criando pedidos em Separação...');
  for (let i = 0; i < 4; i++) {
    await prisma.order.create({
      data: {
        customerName: clientes[i].name,
        customerPhone: clientes[i].phone,
        customerId: clientes[i].id,
        total: new Decimal(150 * (i + 1)),
        status: OrderStatus.SEPARACAO,
        sellerId: vendedor.id,
        items: {
          create: [
            {
              productId: produto.id,
              productVariationId: variation.id,
              productName: `${produto.name} - Item 1`,
              quantity: i + 1,
              unitPrice: variation.price,
            },
            {
              productId: produto.id,
              productVariationId: variation.id,
              productName: `${produto.name} - Item 2`,
              quantity: 1,
              unitPrice: new Decimal(50),
            },
          ],
        },
        statusHistory: {
          create: {
            previousStatus: OrderStatus.ANALISE,
            newStatus: OrderStatus.SEPARACAO,
            changedById: supervisor.id,
          },
        },
      },
    });
  }

  // 7. Criar pedidos em Análise
  console.log('\n🔍 Criando pedidos em Análise...');
  for (let i = 0; i < 5; i++) {
    const createdAt = new Date();
    createdAt.setDate(createdAt.getDate() - i); // Criados há 0 a 4 dias

    await prisma.order.create({
      data: {
        customerName: clientes[i % clientes.length].name,
        customerPhone: clientes[i % clientes.length].phone,
        customerId: clientes[i % clientes.length].id,
        total: new Decimal(300 * (i + 1)),
        status: OrderStatus.ANALISE,
        sellerId: vendedor.id,
        createdAt,
        items: {
          create: {
            productId: produto.id,
            productVariationId: variation.id,
            productName: produto.name,
            quantity: (i + 1) * 3,
            unitPrice: variation.price,
          },
        },
      },
    });
  }

  console.log('\n✅ Seed do Dashboard Inteligente concluído!');
  console.log('\nResumo dos dados criados:');
  console.log('- 3 pedidos para Receber Hoje');
  console.log('- 3 pedidos em Risco (1 Confirmar Entrega, 1 Entrega Falha, 1 Retirar Correios)');
  console.log('- 4 pedidos em Separação');
  console.log('- 5 pedidos em Análise');
  console.log('\nUsuários de teste:');
  console.log('- <EMAIL> (senha123)');
  console.log('- <EMAIL> (senha123)');
  console.log('- <EMAIL> (senha123)');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });