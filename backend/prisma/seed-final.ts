import { PrismaClient, Role, OrderStatus } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

// Chave de criptografia para configurações
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'zencash-encryption-key-32-chars!';
const algorithm = 'aes-256-gcm';

function encrypt(text: string): string {
  const iv = crypto.randomBytes(16);
  const key = Buffer.from(ENCRYPTION_KEY.substring(0, 32), 'utf8');
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return JSON.stringify({
    encrypted,
    authTag: authTag.toString('hex'),
    iv: iv.toString('hex'),
  });
}

async function main() {
  console.log('🌱 Iniciando seed final do ZenCash...\n');

  // Fixed tenant ID for all users (acme-corp)
  const tenantId = 'b5c6e7d8-9a0b-1c2d-3e4f-567890abcdef';

  // 1. Criar Administrador
  console.log('👤 Criando administrador...');
  const adminPassword = await bcrypt.hash('Admin@123', 10);
  const admin = await prisma.user.create({
    data: {
      name: 'Administrador ZenCash',
      email: '<EMAIL>',
      password: adminPassword,
      role: Role.ADMIN,
      active: true,
      tenantId: tenantId,
    },
  });
  console.log('✅ Admin criado:', admin.email);

  // 2. Criar Vendedores e Cobradores
  console.log('\n👥 Criando usuários...');
  const users = [];
  
  // Vendedores
  for (let i = 1; i <= 3; i++) {
    const password = await bcrypt.hash(`Vendedor${i}@123`, 10);
    const user = await prisma.user.create({
      data: {
        name: `Vendedor ${i}`,
        email: `vendedor${i}@zencash.com`,
        password,
        role: Role.VENDEDOR,
        active: true,
        tenantId: tenantId,
      },
    });
    users.push(user);
    console.log(`✅ Vendedor criado: ${user.email}`);
  }

  // Cobradores
  for (let i = 1; i <= 2; i++) {
    const password = await bcrypt.hash(`Cobrador${i}@123`, 10);
    const user = await prisma.user.create({
      data: {
        name: `Cobrador ${i}`,
        email: `cobrador${i}@zencash.com`,
        password,
        role: Role.COBRADOR,
        active: true,
        tenantId: tenantId,
      },
    });
    users.push(user);
    console.log(`✅ Cobrador criado: ${user.email}`);
  }

  // Supervisor
  const supervisorPassword = await bcrypt.hash('Supervisor@123', 10);
  const supervisor = await prisma.user.create({
    data: {
      name: 'Supervisor ZenCash',
      email: '<EMAIL>',
      password: supervisorPassword,
      role: Role.SUPERVISOR,
      active: true,
      tenantId: tenantId,
    },
  });
  console.log('✅ Supervisor criado:', supervisor.email);

  // 3. Criar Produtos
  console.log('\n📦 Criando produtos...');
  const products = [
    {
      name: 'Smartphone Galaxy A54',
      description: 'Smartphone Samsung Galaxy A54 5G 128GB',
      variations: [
        { variation: 'Preto', price: 1899.90, sku: 'GALAXY-A54-BLACK' },
        { variation: 'Branco', price: 1899.90, sku: 'GALAXY-A54-WHITE' },
        { variation: 'Azul', price: 1899.90, sku: 'GALAXY-A54-BLUE' },
      ],
    },
    {
      name: 'Notebook Dell Inspiron',
      description: 'Notebook Dell Inspiron 15 i5 8GB 256GB SSD',
      variations: [
        { variation: 'i5/8GB/256GB', price: 3299.90, sku: 'DELL-INSP-I5-8-256' },
        { variation: 'i7/16GB/512GB', price: 4599.90, sku: 'DELL-INSP-I7-16-512' },
      ],
    },
    {
      name: 'Fone Bluetooth JBL',
      description: 'Fone de Ouvido Bluetooth JBL Tune 510BT',
      variations: [
        { variation: 'Preto', price: 199.90, sku: 'JBL-510BT-BLACK' },
        { variation: 'Branco', price: 199.90, sku: 'JBL-510BT-WHITE' },
        { variation: 'Azul', price: 199.90, sku: 'JBL-510BT-BLUE' },
        { variation: 'Rosa', price: 199.90, sku: 'JBL-510BT-PINK' },
      ],
    },
    {
      name: 'Smart TV LG 50"',
      description: 'Smart TV LG 50" 4K UHD LED',
      variations: [
        { variation: '50" 4K', price: 2199.90, sku: 'LG-TV-50-4K' },
      ],
    },
    {
      name: 'Console PlayStation 5',
      description: 'Console PlayStation 5 com controle DualSense',
      variations: [
        { variation: 'Standard', price: 3999.90, sku: 'PS5-STANDARD' },
        { variation: 'Digital', price: 3499.90, sku: 'PS5-DIGITAL' },
      ],
    },
  ];

  const createdProducts = [];
  for (const productData of products) {
    const { variations, ...productInfo } = productData;
    const product = await prisma.product.create({
      data: {
        ...productInfo,
        active: true,
        tenantId: tenantId,
        variations: {
          create: variations.map(v => ({
            ...v,
            price: new Decimal(v.price),
            active: true,
          })),
        },
      },
      include: { variations: true },
    });
    createdProducts.push(product);
    console.log(`✅ Produto criado: ${product.name} (${product.variations.length} variações)`);
  }

  // 4. Criar Kits
  console.log('\n🎁 Criando kits...');
  const kit1 = await prisma.kit.create({
    data: {
      name: 'Kit Home Office',
      description: 'Kit completo para trabalho em casa',
      active: true,
      tenantId: tenantId,
      items: {
        create: [
          {
            productVariationId: createdProducts[1].variations[0].id, // Notebook
            quantity: 1,
          },
          {
            productVariationId: createdProducts[2].variations[0].id, // Fone JBL
            quantity: 1,
          },
        ],
      },
    },
  });
  console.log('✅ Kit criado:', kit1.name);

  const kit2 = await prisma.kit.create({
    data: {
      name: 'Kit Gamer',
      description: 'Kit completo para gamers',
      active: true,
      tenantId: tenantId,
      items: {
        create: [
          {
            productVariationId: createdProducts[4].variations[0].id, // PS5
            quantity: 1,
          },
          {
            productVariationId: createdProducts[3].variations[0].id, // TV LG
            quantity: 1,
          },
        ],
      },
    },
  });
  console.log('✅ Kit criado:', kit2.name);

  // 5. Criar estoque (Inventory)
  console.log('\n📊 Criando estoque inicial...');
  for (const product of createdProducts) {
    for (const variation of product.variations) {
      await prisma.inventory.create({
        data: {
          productVariationId: variation.id,
          quantity: Math.floor(Math.random() * 50) + 10, // 10-60 unidades
          minAlert: 5,
        },
      });
    }
  }
  console.log('✅ Estoque inicial criado para todos os produtos');

  // 6. Criar Clientes
  console.log('\n👥 Criando clientes...');
  const customers = [
    {
      name: 'João Silva',
      cpf: '12345678901',
      phone: '11999999999',
      email: '<EMAIL>',
      addresses: [{
        cep: '01310-100',
        street: 'Av. Paulista',
        number: '1000',
        neighborhood: 'Bela Vista',
        city: 'São Paulo',
        state: 'SP',
        main: true,
      }],
    },
    {
      name: 'Maria Oliveira',
      cpf: '23456789012',
      phone: '11888888888',
      email: '<EMAIL>',
      addresses: [{
        cep: '20040-020',
        street: 'Av. Rio Branco',
        number: '100',
        neighborhood: 'Centro',
        city: 'Rio de Janeiro',
        state: 'RJ',
        main: true,
      }],
    },
    {
      name: 'Pedro Santos',
      cpf: '34567890123',
      phone: '11777777777',
      email: '<EMAIL>',
      addresses: [{
        cep: '30130-100',
        street: 'Av. Afonso Pena',
        number: '500',
        neighborhood: 'Centro',
        city: 'Belo Horizonte',
        state: 'MG',
        main: true,
      }],
    },
    {
      name: 'Ana Costa',
      cpf: '45678901234',
      phone: '11666666666',
      email: '<EMAIL>',
      addresses: [{
        cep: '80010-000',
        street: 'Rua XV de Novembro',
        number: '200',
        neighborhood: 'Centro',
        city: 'Curitiba',
        state: 'PR',
        main: true,
      }],
    },
    {
      name: 'Carlos Pereira',
      cpf: '56789012345',
      phone: '11555555555',
      email: '<EMAIL>',
      addresses: [{
        cep: '90010-000',
        street: 'Rua dos Andradas',
        number: '300',
        neighborhood: 'Centro',
        city: 'Porto Alegre',
        state: 'RS',
        main: true,
      }],
    },
  ];

  const createdCustomers = [];
  for (const customerData of customers) {
    const { addresses, ...customerInfo } = customerData;
    const customer = await prisma.customer.create({
      data: {
        ...customerInfo,
        active: true,
        tenantId: tenantId,
        addresses: {
          create: addresses,
        },
      },
      include: { addresses: true },
    });
    createdCustomers.push(customer);
    console.log(`✅ Cliente criado: ${customer.name}`);
  }

  // 7. Criar Pedidos com diferentes status
  console.log('\n📋 Criando pedidos...');
  const vendedores = users.filter(u => u.role === Role.VENDEDOR);
  const cobradores = users.filter(u => u.role === Role.COBRADOR);
  const statuses = [
    OrderStatus.Analise,
    OrderStatus.Separacao,
    OrderStatus.Transito,
    OrderStatus.Completo,
    OrderStatus.Frustrado,
    OrderStatus.Recuperacao,
    OrderStatus.Negociacao,
    OrderStatus.Parcial,
  ];

  const orders = [];
  let orderCount = 0;

  for (let i = 0; i < 20; i++) {
    const customer = createdCustomers[Math.floor(Math.random() * createdCustomers.length)];
    const vendedor = vendedores[Math.floor(Math.random() * vendedores.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const needsCollector = status !== OrderStatus.Analise;
    const cobrador = needsCollector ? cobradores[Math.floor(Math.random() * cobradores.length)] : null;

    // Selecionar produtos aleatórios
    const numItems = Math.floor(Math.random() * 3) + 1;
    const items = [];
    let total = new Decimal(0);

    for (let j = 0; j < numItems; j++) {
      const product = createdProducts[Math.floor(Math.random() * createdProducts.length)];
      const variation = product.variations[Math.floor(Math.random() * product.variations.length)];
      const quantity = Math.floor(Math.random() * 3) + 1;
      
      items.push({
        productVariationId: variation.id,
        productId: product.id,
        productName: `${product.name} - ${variation.variation}`,
        quantity,
        unitPrice: variation.price,
      });
      
      total = total.add(variation.price.mul(quantity));
    }

    const order = await prisma.order.create({
      data: {
        customerId: customer.id,
        customerName: customer.name,
        customerPhone: customer.phone,
        total,
        status,
        sellerId: vendedor.id,
        collectorId: cobrador?.id,
        tenantId: tenantId,
        items: {
          create: items,
        },
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Analise,
            newStatus: status,
            changedById: vendedor.id,
          },
        },
      },
    });

    orders.push(order);
    orderCount++;
  }
  console.log(`✅ ${orderCount} pedidos criados com diferentes status`);

  // 8. Criar rastreamento para pedidos enviados
  console.log('\n📍 Criando rastreamentos...');
  const shippedOrders = await prisma.order.findMany({
    where: {
      status: {
        in: [OrderStatus.Transito, OrderStatus.Completo, OrderStatus.Frustrado],
      },
    },
  });

  for (const order of shippedOrders) {
    const trackingCode = `BR${Date.now()}${Math.random().toString(36).substring(2, 5).toUpperCase()}BR`;
    
    let trackingStatus = 'Em trânsito';
    let isDelivered = false;
    let hasAlert = false;
    let alertReason = null;

    if (order.status === OrderStatus.Completo) {
      trackingStatus = 'Objeto entregue ao destinatário';
      isDelivered = true;
    } else if (order.status === OrderStatus.Frustrado) {
      trackingStatus = 'Destinatário ausente';
      hasAlert = true;
      alertReason = 'DESTINATÁRIO AUSENTE - 3 tentativas';
    }

    await prisma.tracking.create({
      data: {
        orderId: order.id,
        code: trackingCode,
        status: trackingStatus,
        lastUpdate: new Date(),
        events: [
          {
            timestamp: new Date(Date.now() - 86400000), // 1 dia atrás
            status: 'Objeto postado',
            location: 'São Paulo - SP',
            description: 'Objeto postado',
          },
          {
            timestamp: new Date(),
            status: trackingStatus,
            location: 'Destino - UF',
            description: trackingStatus,
          },
        ],
        isDelivered,
        hasAlert,
        alertReason,
      },
    });
  }
  console.log(`✅ Rastreamentos criados para ${shippedOrders.length} pedidos`);

  // 9. Inserir configurações iniciais
  console.log('\n⚙️ Inserindo configurações iniciais...');
  const configurations = [
    {
      key: 'whatsapp_api_key',
      value: encrypt(JSON.stringify({
        apiKey: 'wa_prod_key_' + Math.random().toString(36).substring(2),
        baseUrl: 'https://api.whatsapp.com/v1',
        phoneNumberId: '5511999999999',
      })),
    },
    {
      key: 'correios_credentials',
      value: encrypt(JSON.stringify({
        username: 'zencash_correios',
        password: 'correios_' + Math.random().toString(36).substring(2),
        contractNumber: 'CTR-' + Math.floor(Math.random() * 999999),
        administrativeCode: 'ADM-' + Math.floor(Math.random() * 9999),
      })),
    },
    {
      key: 'system_preferences',
      value: JSON.stringify({
        defaultLanguage: 'pt-BR',
        timezone: 'America/Sao_Paulo',
        currency: 'BRL',
        dateFormat: 'DD/MM/YYYY',
        paginationLimit: 20,
        orderStatusUpdateNotification: true,
        lowStockAlert: true,
        trackingAlertEnabled: true,
      }),
    },
    {
      key: 'tracking_alert_thresholds',
      value: JSON.stringify({
        daysWithoutUpdate: 3,
        maxDeliveryAttempts: 3,
        criticalStatuses: ['EXTRAVIADO', 'DEVOLVIDO', 'CANCELADO'],
        warningStatuses: ['DESTINATÁRIO AUSENTE', 'ENDEREÇO INCORRETO'],
      }),
    },
    {
      key: 'default_notification_template',
      value: JSON.stringify({
        orderCreated: {
          whatsapp: 'Olá {{customerName}}! 🎉\n\nSeu pedido #{{orderId}} foi criado com sucesso!\n\nTotal: R$ {{total}}\n\nAgradecemos pela preferência!',
          email: {
            subject: 'Pedido #{{orderId}} confirmado - ZenCash',
            body: 'Prezado(a) {{customerName}},\n\nConfirmamos o recebimento do seu pedido!\n\nDetalhes do pedido:\n{{orderDetails}}\n\nTotal: R$ {{total}}\n\nVocê receberá atualizações sobre o status do seu pedido.\n\nAtenciosamente,\nEquipe ZenCash',
          },
        },
        statusChanged: {
          whatsapp: 'Olá {{customerName}}! 📦\n\nSeu pedido #{{orderId}} foi atualizado:\n\nNovo status: {{newStatus}}\n\nAcompanhe seu pedido em nosso site.',
          email: {
            subject: 'Atualização do pedido #{{orderId}}',
            body: 'Prezado(a) {{customerName}},\n\nInformamos que seu pedido teve uma atualização.\n\nStatus anterior: {{previousStatus}}\nNovo status: {{newStatus}}\n\nContinue acompanhando seu pedido.\n\nAtenciosamente,\nEquipe ZenCash',
          },
        },
      }),
    },
    {
      key: 'admin_emails',
      value: JSON.stringify({
        systemAlerts: ['<EMAIL>', '<EMAIL>'],
        financialReports: ['<EMAIL>', '<EMAIL>'],
        operationalReports: ['<EMAIL>', '<EMAIL>'],
      }),
    },
  ];

  for (const config of configurations) {
    await prisma.configuration.upsert({
      where: { 
        key_tenantId: {
          key: config.key,
          tenantId: tenantId
        }
      },
      create: {
        key: config.key,
        value: config.value as any,
        tenantId: tenantId,
      },
      update: {
        value: config.value as any,
      },
    });
    console.log(`✅ Configuração inserida: ${config.key}`);
  }

  // 10. Resumo final
  console.log('\n📊 Resumo do seed:');
  console.log('==================');
  
  const userCount = await prisma.user.count();
  const productCount = await prisma.product.count();
  const kitCount = await prisma.kit.count();
  const customerCount = await prisma.customer.count();
  const orderCount2 = await prisma.order.count();
  const trackingCount = await prisma.tracking.count();
  const configCount = await prisma.configuration.count();

  console.log(`👥 Usuários: ${userCount}`);
  console.log(`📦 Produtos: ${productCount}`);
  console.log(`🎁 Kits: ${kitCount}`);
  console.log(`👥 Clientes: ${customerCount}`);
  console.log(`📋 Pedidos: ${orderCount2}`);
  console.log(`📍 Rastreamentos: ${trackingCount}`);
  console.log(`⚙️ Configurações: ${configCount}`);

  console.log('\n🔐 Credenciais de acesso:');
  console.log('========================');
  console.log('Admin: <EMAIL> / Admin@123');
  console.log('Supervisor: <EMAIL> / Supervisor@123');
  console.log('Vendedor 1: <EMAIL> / Vendedor1@123');
  console.log('Vendedor 2: <EMAIL> / Vendedor2@123');
  console.log('Vendedor 3: <EMAIL> / Vendedor3@123');
  console.log('Cobrador 1: <EMAIL> / Cobrador1@123');
  console.log('Cobrador 2: <EMAIL> / Cobrador2@123');

  console.log('\n✅ Seed finalizado com sucesso!');
  console.log('🚀 Sistema ZenCash pronto para uso!');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });