import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Hash da senha "admin123"
  const hashedPassword = await bcrypt.hash('admin123', 10);

  // Fixed tenant ID for all users (acme-corp)
  const tenantId = 'b5c6e7d8-9a0b-1c2d-3e4f-567890abcdef';

  // Criar usuário admin
  const admin = await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrador',
      password: hashedPassword,
      role: 'ADMIN',
      active: true,
      tenantId: tenantId,
    },
  });

  console.log('✅ Usuário admin criado:', admin.email);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });