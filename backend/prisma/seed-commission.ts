import { PrismaClient, Role } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Creating commission settings...');

  try {
    // Get all users
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: [Role.VENDEDOR, Role.COBRADOR],
        },
      },
    });

    // Create commission settings
    for (const user of users) {
      const percentage = user.role === Role.VENDEDOR ? 5.0 : 3.0; // 5% for sellers, 3% for collectors

      const existing = await prisma.commissionSetting.findUnique({
        where: { userId: user.id },
      });

      if (!existing) {
        await prisma.commissionSetting.create({
          data: {
            userId: user.id,
            percentage,
            role: user.role,
          },
        });
        console.log(`Created commission setting for ${user.name} (${user.role}): ${percentage}%`);
      }
    }

    // Create some test orders with payment status
    const seller = await prisma.user.findFirst({
      where: { role: Role.VENDEDOR },
    });

    const collector = await prisma.user.findFirst({
      where: { role: Role.COBRADOR },
    });

    const customer = await prisma.customer.findFirst();

    if (seller && collector && customer) {
      // Create a completed order
      const order1 = await prisma.order.create({
        data: {
          customerName: customer.name,
          customerPhone: customer.phone,
          customerId: customer.id,
          total: 1500.00,
          status: 'COMPLETO',
          sellerId: seller.id,
          collectorId: collector.id,
          commissionApprovalStatus: 'NONE',
          items: {
            create: [
              {
                productId: 'prod1',
                productName: 'Produto Teste',
                quantity: 2,
                unitPrice: 750.00,
              },
            ],
          },
          statusHistory: {
            create: [
              {
                previousStatus: 'PENDENTE',
                newStatus: 'SEPARACAO',
                changedById: seller.id,
              },
              {
                previousStatus: 'SEPARACAO',
                newStatus: 'ENVIADO',
                changedById: collector.id,
              },
              {
                previousStatus: 'ENVIADO',
                newStatus: 'ENTREGUE',
                changedById: collector.id,
              },
              {
                previousStatus: 'ENTREGUE',
                newStatus: 'COMPLETO',
                changedById: collector.id,
              },
            ],
          },
        },
      });

      console.log(`Created order ${order1.id} with COMPLETO status`);

      // Create a partial payment order
      const order2 = await prisma.order.create({
        data: {
          customerName: customer.name,
          customerPhone: customer.phone,
          customerId: customer.id,
          total: 2000.00,
          status: 'PAGAMENTO_PARCIAL',
          sellerId: seller.id,
          collectorId: collector.id,
          commissionApprovalStatus: 'NONE',
          items: {
            create: [
              {
                productId: 'prod2',
                productName: 'Produto Premium',
                quantity: 1,
                unitPrice: 2000.00,
              },
            ],
          },
          statusHistory: {
            create: [
              {
                previousStatus: 'PENDENTE',
                newStatus: 'SEPARACAO',
                changedById: seller.id,
              },
              {
                previousStatus: 'SEPARACAO',
                newStatus: 'ENVIADO',
                changedById: collector.id,
              },
              {
                previousStatus: 'ENVIADO',
                newStatus: 'ENTREGUE',
                changedById: collector.id,
              },
              {
                previousStatus: 'ENTREGUE',
                newStatus: 'PAGAMENTO_PARCIAL',
                changedById: collector.id,
              },
            ],
          },
        },
      });

      console.log(`Created order ${order2.id} with PAGAMENTO_PARCIAL status`);
    }

    console.log('Commission settings created successfully!');
  } catch (error) {
    console.error('Error creating commission settings:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });