import { PrismaClient, OrderStatus } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed da Sprint 7...');

  // Busca ou cria usuários
  let vendedor = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  if (!vendedor) {
    const hashedPassword = await bcrypt.hash('Vendedor@123', 10);
    vendedor = await prisma.user.create({
      data: {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'VENDEDOR',
        active: true,
      },
    });
  }

  // Busca clientes existentes
  const customers = await prisma.customer.findMany({
    take: 3,
  });

  if (customers.length === 0) {
    console.log('⚠️ Nenhum cliente encontrado. Execute os seeds anteriores primeiro.');
    return;
  }

  // Criar pedidos com diferentes status para rastreamento
  const orders: any[] = [];

  // 1. Pedido entregue (código terminando em 1)
  const order1 = await prisma.order.create({
    data: {
      customerId: customers[0].id,
      customerName: customers[0].name,
      customerPhone: customers[0].phone,
      total: new Decimal(299.90),
      status: OrderStatus.ENVIADO,
      sellerId: vendedor.id,
      items: {
        create: [
          {
            productId: 'prod-1',
            productName: 'Kit Esportivo Completo',
            quantity: 1,
            unitPrice: new Decimal(299.90),
          },
        ],
      },
      statusHistory: {
        create: [
          {
            previousStatus: OrderStatus.PENDENTE,
            newStatus: OrderStatus.SEPARACAO,
            changedById: vendedor.id,
          },
          {
            previousStatus: OrderStatus.SEPARACAO,
            newStatus: OrderStatus.ENVIADO,
            changedById: vendedor.id,
          },
        ],
      },
    },
  });
  orders.push(order1);

  // 2. Pedido em trânsito (código terminando em 2)
  const order2 = await prisma.order.create({
    data: {
      customerId: customers[1].id,
      customerName: customers[1].name,
      customerPhone: customers[1].phone,
      total: new Decimal(189.80),
      status: OrderStatus.ENVIADO,
      sellerId: vendedor.id,
      items: {
        create: [
          {
            productId: 'prod-2',
            productName: 'Camiseta Básica - P',
            quantity: 2,
            unitPrice: new Decimal(29.90),
          },
          {
            productId: 'prod-3',
            productName: 'Calça Jeans - 38',
            quantity: 1,
            unitPrice: new Decimal(129.90),
          },
        ],
      },
    },
  });
  orders.push(order2);

  // 3. Pedido com problema (código terminando em 3)
  const order3 = await prisma.order.create({
    data: {
      customerId: customers[2].id,
      customerName: customers[2].name,
      customerPhone: customers[2].phone,
      total: new Decimal(159.90),
      status: OrderStatus.ENVIADO,
      sellerId: vendedor.id,
      items: {
        create: [
          {
            productId: 'prod-4',
            productName: 'Tênis Esportivo - 42',
            quantity: 1,
            unitPrice: new Decimal(159.90),
          },
        ],
      },
    },
  });
  orders.push(order3);

  // 4. Pedido com falha crítica (código terminando em 4)
  const order4 = await prisma.order.create({
    data: {
      customerName: 'Pedro Teste',
      customerPhone: '11999998888',
      total: new Decimal(99.90),
      status: OrderStatus.ENVIADO,
      sellerId: vendedor.id,
      items: {
        create: [
          {
            productId: 'prod-5',
            productName: 'Boné Snapback',
            quantity: 2,
            unitPrice: new Decimal(49.95),
          },
        ],
      },
    },
  });
  orders.push(order4);

  // Criar rastreamentos para os pedidos
  const trackingCodes = [
    'AB123456781BR', // Será entregue
    'CD987654322BR', // Em trânsito
    'EF111222333BR', // Destinatário ausente
    'GH444555664BR', // Fluxo cancelado
  ];

  console.log('\n📦 Criando rastreamentos...');

  for (let i = 0; i < orders.length; i++) {
    const order = orders[i];
    const code = trackingCodes[i];

    // Simula dados de rastreamento baseado no código
    let status = 'OBJETO POSTADO';
    let isDelivered = false;
    let hasAlert = false;
    let alertReason: string | null = null;
    let events: any[] = [];
    const now = new Date();

    if (code.endsWith('1BR')) {
      status = 'OBJETO ENTREGUE AO DESTINATÁRIO';
      isDelivered = true;
      events = [
        {
          date: new Date(now.getTime() - 1 * 60 * 60 * 1000),
          location: 'São Paulo/SP',
          status: 'OBJETO ENTREGUE AO DESTINATÁRIO',
          description: 'Objeto entregue ao destinatário',
        },
      ];
    } else if (code.endsWith('2BR')) {
      status = 'OBJETO EM TRANSFERÊNCIA';
      events = [
        {
          date: new Date(now.getTime() - 3 * 60 * 60 * 1000),
          location: 'Rio de Janeiro/RJ',
          status: 'OBJETO EM TRANSFERÊNCIA',
          description: 'Objeto em transferência',
        },
      ];
    } else if (code.endsWith('3BR')) {
      status = 'DESTINATÁRIO AUSENTE';
      hasAlert = true;
      alertReason = 'DESTINATÁRIO AUSENTE';
      events = [
        {
          date: new Date(now.getTime() - 2 * 60 * 60 * 1000),
          location: 'Belo Horizonte/MG',
          status: 'DESTINATÁRIO AUSENTE',
          description: 'Não foi possível entregar - destinatário ausente',
        },
      ];
    } else if (code.endsWith('4BR')) {
      status = 'FC - FLUXO CANCELADO';
      hasAlert = true;
      alertReason = 'FC - FLUXO CANCELADO';
      events = [
        {
          date: new Date(now.getTime() - 1 * 60 * 60 * 1000),
          location: 'Salvador/BA',
          status: 'FC - FLUXO CANCELADO',
          description: 'Fluxo de postagem cancelado',
        },
      ];
    }

    await prisma.tracking.create({
      data: {
        orderId: order.id,
        code,
        status,
        lastUpdate: events[0]?.date || now,
        events,
        isDelivered,
        hasAlert,
        alertReason,
        lastSync: now,
      },
    });

    console.log(`✅ Rastreamento ${code} criado para pedido ${order.id}`);
  }

  // Estatísticas
  const stats = await prisma.tracking.groupBy({
    by: ['hasAlert', 'isDelivered'],
    _count: true,
  });

  console.log('\n✅ Seed da Sprint 7 concluído!');
  console.log(`- ${orders.length} pedidos com rastreamento criados`);
  console.log('\n📊 Status dos rastreamentos:');
  console.log('- 1 entregue (AB123456781BR)');
  console.log('- 1 em trânsito (CD987654322BR)');
  console.log('- 1 destinatário ausente (EF111222333BR)');
  console.log('- 1 fluxo cancelado (GH444555664BR)');
  console.log('\n🚨 Alertas críticos: 2');
  console.log('\n💡 Execute npm run start:dev para testar:');
  console.log('- GET /tracking - Lista todos os rastreamentos');
  console.log('- GET /tracking/AB123456781BR - Busca por código');
  console.log('- GET /tracking/alerts - Lista alertas críticos');
  console.log('- POST /tracking/sync - Força sincronização');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });