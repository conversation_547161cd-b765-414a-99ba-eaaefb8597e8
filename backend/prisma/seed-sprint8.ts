import { PrismaClient, OrderStatus, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed da Sprint 8 - Dados para Relatórios...');

  // Criar usuários variados se não existirem
  const users: any[] = [];
  
  // Vendedores
  for (let i = 1; i <= 3; i++) {
    const email = `vendedor${i}@zencash.com`;
    let user = await prisma.user.findUnique({ where: { email } });
    
    if (!user) {
      const hashedPassword = await bcrypt.hash('Vendedor@123', 10);
      user = await prisma.user.create({
        data: {
          name: `Vendedor ${i}`,
          email,
          password: hashedPassword,
          role: Role.VENDEDOR,
          active: true,
        },
      });
    }
    users.push({ ...user, type: 'vendedor' });
  }

  // Cobradores
  for (let i = 1; i <= 2; i++) {
    const email = `cobrador${i}@zencash.com`;
    let user = await prisma.user.findUnique({ where: { email } });
    
    if (!user) {
      const hashedPassword = await bcrypt.hash('Cobrador@123', 10);
      user = await prisma.user.create({
        data: {
          name: `Cobrador ${i}`,
          email,
          password: hashedPassword,
          role: Role.COBRADOR,
          active: true,
        },
      });
    }
    users.push({ ...user, type: 'cobrador' });
  }

  // Buscar clientes
  const customers = await prisma.customer.findMany({ take: 10 });
  if (customers.length < 3) {
    console.log('⚠️ Poucos clientes encontrados. Execute seeds anteriores.');
    return;
  }

  // Buscar produtos
  const products = await prisma.product.findMany({
    include: { variations: true },
  });
  if (products.length === 0) {
    console.log('⚠️ Nenhum produto encontrado. Execute seeds anteriores.');
    return;
  }

  // Criar pedidos variados para análise
  const orderStatuses = [
    OrderStatus.PENDENTE,
    OrderStatus.SEPARACAO,
    OrderStatus.ENVIADO,
    OrderStatus.ENTREGUE,
    OrderStatus.FALHA,
    OrderStatus.CANCELADO,
    OrderStatus.RECUPERACAO,
    OrderStatus.NEGOCIACAO,
  ];

  const orderDates = [
    new Date('2024-01-01'),
    new Date('2024-01-15'),
    new Date('2024-02-01'),
    new Date('2024-02-15'),
    new Date('2024-03-01'),
  ];

  let ordersCreated = 0;
  const vendedores = users.filter(u => u.type === 'vendedor');
  const cobradores = users.filter(u => u.type === 'cobrador');

  // Criar pedidos distribuídos
  for (const date of orderDates) {
    for (let i = 0; i < 10; i++) {
      const vendedor = vendedores[Math.floor(Math.random() * vendedores.length)];
      const cobrador = cobradores[Math.floor(Math.random() * cobradores.length)];
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
      
      // Selecionar produtos aleatórios
      const numItems = Math.floor(Math.random() * 3) + 1;
      const items: any[] = [];
      let total = new Decimal(0);

      for (let j = 0; j < numItems; j++) {
        const product = products[Math.floor(Math.random() * products.length)];
        const variation = product.variations[Math.floor(Math.random() * product.variations.length)];
        
        if (variation) {
          const quantity = Math.floor(Math.random() * 5) + 1;
          const unitPrice = variation.price;
          const itemTotal = unitPrice.mul(quantity);
          
          items.push({
            productId: product.id,
            productVariationId: variation.id,
            productName: `${product.name} - ${variation.variation}`,
            quantity,
            unitPrice,
          });
          
          total = total.add(itemTotal);
        }
      }

      if (items.length > 0) {
        const order = await prisma.order.create({
          data: {
            customerId: customer.id,
            customerName: customer.name,
            customerPhone: customer.phone,
            total,
            status,
            sellerId: vendedor.id,
            collectorId: status !== OrderStatus.PENDENTE ? cobrador.id : null,
            createdAt: date,
            updatedAt: new Date(date.getTime() + (Math.random() * 30 * 24 * 60 * 60 * 1000)),
            items: {
              create: items,
            },
            statusHistory: {
              create: {
                previousStatus: OrderStatus.PENDENTE,
                newStatus: status,
                changedById: vendedor.id,
                changedAt: date,
              },
            },
          },
        });

        // Adicionar rastreamento para pedidos enviados/entregues
        if (status === OrderStatus.ENVIADO || status === OrderStatus.ENTREGUE || status === OrderStatus.FALHA) {
          const trackingCodes = [
            `BR${Date.now()}${i}A`,
            `BR${Date.now()}${i}B`,
            `BR${Date.now()}${i}C`,
            `BR${Date.now()}${i}D`,
          ];
          
          const code = trackingCodes[Math.floor(Math.random() * trackingCodes.length)];
          const hasAlert = status === OrderStatus.FALHA;
          
          await prisma.tracking.create({
            data: {
              orderId: order.id,
              code,
              status: status === OrderStatus.ENTREGUE 
                ? 'OBJETO ENTREGUE' 
                : status === OrderStatus.FALHA 
                  ? 'DESTINATÁRIO AUSENTE'
                  : 'EM TRANSPORTE',
              lastUpdate: new Date(date.getTime() + (Math.random() * 10 * 24 * 60 * 60 * 1000)),
              events: [],
              isDelivered: status === OrderStatus.ENTREGUE,
              hasAlert,
              alertReason: hasAlert ? 'DESTINATÁRIO AUSENTE' : null,
            },
          });
        }

        ordersCreated++;
      }
    }
  }

  // Criar alguns pedidos específicos para testar filtros
  // Pedido grande
  const bigOrder = await prisma.order.create({
    data: {
      customerId: customers[0].id,
      customerName: customers[0].name,
      customerPhone: customers[0].phone,
      total: new Decimal(2500),
      status: OrderStatus.ENTREGUE,
      sellerId: vendedores[0].id,
      collectorId: cobradores[0].id,
      items: {
        create: products.slice(0, 5).map(p => ({
          productId: p.id,
          productVariationId: p.variations[0]?.id,
          productName: p.name,
          quantity: 10,
          unitPrice: new Decimal(50),
        })),
      },
    },
  });

  ordersCreated++;

  // Estatísticas
  const stats = await Promise.all([
    prisma.order.count(),
    prisma.order.groupBy({
      by: ['status'],
      _count: { _all: true },
    }),
    prisma.order.groupBy({
      by: ['sellerId'],
      _count: { _all: true },
      _sum: { total: true },
    }),
  ]);

  console.log('\n✅ Seed da Sprint 8 concluído!');
  console.log(`- ${ordersCreated} pedidos criados`);
  console.log(`- ${vendedores.length} vendedores`);
  console.log(`- ${cobradores.length} cobradores`);
  console.log('\n📊 Distribuição por status:');
  stats[1].forEach(s => {
    console.log(`  • ${s.status}: ${s._count._all} pedidos`);
  });
  console.log('\n💰 Top vendedores:');
  stats[2]
    .sort((a, b) => {
      const bTotal = b._sum.total ? Number(b._sum.total.toString()) : 0;
      const aTotal = a._sum.total ? Number(a._sum.total.toString()) : 0;
      return bTotal - aTotal;
    })
    .slice(0, 3)
    .forEach(async (s) => {
      const user = await prisma.user.findUnique({ where: { id: s.sellerId } });
      console.log(`  • ${user?.name}: R$ ${s._sum.total || 0} (${s._count._all} pedidos)`);
    });

  console.log('\n📈 Endpoints de relatórios disponíveis:');
  console.log('- GET /reports/summary - Resumo geral');
  console.log('- GET /reports/collector - Por cobrador');
  console.log('- GET /reports/seller - Por vendedor');
  console.log('- GET /reports/status - Por status');
  console.log('- GET /reports/product - Por produto');
  console.log('- GET /reports/alerts - Alertas de rastreamento');
  console.log('- GET /reports/history - Histórico completo');
  console.log('- GET /reports/performance - Métricas de performance');
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });