generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String               @id @default(uuid())
  name               String
  email              String
  password           String
  role               Role
  active             Boolean              @default(true)
  tenantId           String
  commissionRate     Decimal?             @default(0) @db.Decimal(5, 2)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  commissionPayments CommissionPayment[]
  commissionSetting  CommissionSetting?
  ordersAsCollector  Order[]              @relation("CollectorOrders")
  ordersAsSeller     Order[]              @relation("SellerOrders")
  orderStatusChanges OrderStatusHistory[]
  paymentApprovals   PaymentApproval[]
  payments           Payment[]
  paymentConfirmedOrders Order[]          @relation("PaymentConfirmedOrders")

  @@unique([email, tenantId])
  @@index([tenantId])
}

model Order {
  id                       String                    @id @default(uuid())
  orderNumber              String?
  customerName             String
  customerPhone            String
  status                   OrderStatus               @default(Analise)
  total                    Decimal                   @db.Decimal(10, 2)
  sellerId                 String
  tenantId                 String
  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @updatedAt
  commissionApprovalStatus CommissionApprovalStatus? @default(NONE)
  paymentReceivedAmount    Decimal?                  @db.Decimal(10, 2)
  paymentReceivedDate      DateTime?
  commissionApproved       Boolean                   @default(false)
  customerId               String?
  collectorId              String?
  nextPaymentDate          DateTime?
  lastContactDate          DateTime?
  zapId                    String?
  zapSourceId              String?
  customerCPF              String?                   @db.VarChar(64)
  customerCPFHash          String?
  duplicateCheckVersion    String?
  duplicateMatchScore      Int?
  duplicateStatus          DuplicateStatus?
  fullAddress              String?
  isDuplicate              Boolean                   @default(false)
  originalOrderIds         String[]
  reviewDecision           ReviewDecision?
  reviewDuration           Int?
  reviewedAt               DateTime?
  reviewedBy               String?
  reviewedByName           String?
  reviewedByRole           String?
  observation              String?
  fraudCheckCompletedAt    DateTime?
  requiresReview           Boolean                   @default(false)
  riskFactors              String[]
  riskLevel                RiskLevel?
  riskScore                Int?
  deletedAt                DateTime?
  deletedBy                String?
  trackingCode             String?
  trackingLastUpdate       DateTime?
  trackingStatus           String?
  statusCorreios           String?
  lastWebhookEvent         String?
  paymentConfirmationStatus String?
  paymentConfirmedBy       String?
  paymentConfirmationDate  DateTime?
  paymentDenialReason      String?
  commissionPayments       CommissionPayment[]
  collector                User?                     @relation("CollectorOrders", fields: [collectorId], references: [id])
  customer                 Customer?                 @relation(fields: [customerId], references: [id])
  seller                   User                      @relation("SellerOrders", fields: [sellerId], references: [id])
  paymentConfirmedByUser   User?                     @relation("PaymentConfirmedOrders", fields: [paymentConfirmedBy], references: [id])
  zapSource                Zap?                      @relation(fields: [zapSourceId], references: [id])
  addressComponents        OrderAddressComponents?
  auditLogs                OrderAuditLog[]
  items                    OrderItem[]
  statusHistory            OrderStatusHistory[]
  paymentApprovals         PaymentApproval[]
  tracking                 Tracking?
  TrackingHistory          TrackingHistory[]
  payments                 Payment[]

  @@unique([orderNumber, tenantId])
  @@index([orderNumber])
  @@index([tenantId])
  @@index([tenantId, isDuplicate, duplicateStatus])
  @@index([tenantId, customerCPFHash])
  @@index([tenantId, createdAt])
  @@index([reviewedBy, reviewedAt])
  @@index([zapSourceId])
}

model OrderItem {
  id                 String            @id @default(uuid())
  orderId            String
  productVariationId String?
  productId          String
  productName        String
  quantity           Int
  unitPrice          Decimal           @db.Decimal(10, 2)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  order              Order             @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productVariation   ProductVariation? @relation(fields: [productVariationId], references: [id])
}

model OrderStatusHistory {
  id             String      @id @default(uuid())
  orderId        String
  previousStatus OrderStatus
  newStatus      OrderStatus
  changedAt      DateTime    @default(now())
  changedById    String
  changedBy      User        @relation(fields: [changedById], references: [id])
  order          Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Product {
  id          String             @id @default(uuid())
  name        String
  description String?
  imageUrl    String?
  price       Decimal            @default(0) @db.Decimal(10, 2)
  active      Boolean            @default(true)
  tenantId    String
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  variations  ProductVariation[]

  @@index([tenantId])
}

model ProductVariation {
  id         String      @id @default(uuid())
  productId  String
  variation  String
  price      Decimal     @db.Decimal(10, 2)
  sku        String      @unique
  active     Boolean     @default(true)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  inventory  Inventory?
  kitItems   KitItem[]
  orderItems OrderItem[]
  product    Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model Inventory {
  id                 String           @id @default(uuid())
  productVariationId String           @unique
  quantity           Int              @default(0)
  minAlert           Int              @default(10)
  updatedAt          DateTime         @updatedAt
  productVariation   ProductVariation @relation(fields: [productVariationId], references: [id], onDelete: Cascade)
}

model Kit {
  id          String    @id @default(uuid())
  name        String
  description String?
  price       Decimal   @default(0) @db.Decimal(10, 2)
  active      Boolean   @default(true)
  tenantId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  items       KitItem[]

  @@index([tenantId])
}

model KitItem {
  id                 String           @id @default(uuid())
  kitId              String
  productVariationId String
  quantity           Int
  kit                Kit              @relation(fields: [kitId], references: [id], onDelete: Cascade)
  productVariation   ProductVariation @relation(fields: [productVariationId], references: [id])

  @@unique([kitId, productVariationId])
}

model Customer {
  id        String    @id @default(uuid())
  name      String
  cpf       String
  phone     String
  email     String?
  active    Boolean   @default(true)
  tenantId  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  addresses Address[]
  orders    Order[]

  @@unique([cpf, tenantId])
  @@index([tenantId])
}

model Address {
  id           String   @id @default(uuid())
  customerId   String
  cep          String
  street       String
  number       String
  neighborhood String
  city         String
  state        String   @db.VarChar(2)
  complement   String?
  main         Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  customer     Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
}

model NotificationJob {
  id        String              @id @default(uuid())
  type      NotificationType
  payload   Json
  channel   NotificationChannel @default(WHATSAPP)
  status    NotificationStatus  @default(PENDING)
  retries   Int                 @default(0)
  error     String?
  sentAt    DateTime?
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
}

model Tracking {
  id          String    @id @default(uuid())
  code        String    @unique
  status      String
  lastUpdate  DateTime
  events      Json      @default("[]")
  orderId     String    @unique
  lastSync    DateTime?
  isDelivered Boolean   @default(false)
  hasAlert    Boolean   @default(false)
  alertReason String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  order       Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([hasAlert])
}

model Configuration {
  id        String   @id @default(uuid())
  key       String
  value     Json
  tenantId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([key, tenantId])
  @@index([tenantId])
}

model CommissionSetting {
  id         String   @id @default(uuid())
  userId     String   @unique
  percentage Decimal  @db.Decimal(5, 2)
  role       Role
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id])
}

model PaymentApproval {
  id              String                   @id @default(uuid())
  orderId         String
  approvedById    String
  previousStatus  OrderStatus
  newStatus       OrderStatus
  paymentAmount   Decimal                  @db.Decimal(10, 2)
  paymentType     String
  approvalStatus  CommissionApprovalStatus
  rejectionReason String?
  createdAt       DateTime                 @default(now())
  updatedAt       DateTime                 @updatedAt
  approvedBy      User                     @relation(fields: [approvedById], references: [id])
  order           Order                    @relation(fields: [orderId], references: [id])
}

model CommissionPayment {
  id               String   @id @default(uuid())
  orderId          String
  userId           String
  userRole         Role
  baseAmount       Decimal  @db.Decimal(10, 2)
  percentage       Decimal  @db.Decimal(5, 2)
  commissionAmount Decimal  @db.Decimal(10, 2)
  paymentDate      DateTime
  createdAt        DateTime @default(now())
  order            Order    @relation(fields: [orderId], references: [id])
  user             User     @relation(fields: [userId], references: [id])

  @@unique([orderId, userId, paymentDate])
}

model OrderAddressComponents {
  id                  String   @id @default(uuid())
  orderId             String   @unique
  street              String
  streetNumber        String
  complement          String?
  neighborhood        String
  city                String
  state               String   @db.VarChar(2)
  zipCode             String   @db.VarChar(8)
  streetNormalized    String
  streetSoundex       String
  streetMetaphone     String
  neighborhoodNorm    String
  neighborhoodSoundex String
  cityNormalized      String
  latitude            Float?
  longitude           Float?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  order               Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([streetSoundex, cityNormalized])
  @@index([zipCode])
  @@index([streetNormalized, streetNumber])
}

model OrderAuditLog {
  id                 String      @id @default(uuid())
  orderId            String
  tenantId           String
  action             AuditAction
  performedBy        String
  performedByName    String
  performedByRole    String
  performedAt        DateTime    @default(now())
  previousData       Json?
  newData            Json?
  metadata           Json?
  signature          String
  signatureAlgorithm String      @default("SHA256")
  order              Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId, performedAt])
  @@index([tenantId, action, performedAt])
  @@index([performedBy, performedAt])
}

model WebhookLog {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.VarChar(255)
  endpoint         String?   @db.VarChar(255)
  method           String?   @db.VarChar(255)
  headers          Json?
  payload          Json?
  status           String?   @db.VarChar(255)
  error            String?
  identifierField  String?   @db.VarChar(255)
  identifierValue  String?   @db.VarChar(255)
  orderId          String?   @db.VarChar(255)
  isNewOrder       Boolean?
  mappingsUsed     Int?
  mappedData       Json?
  receivedAt       DateTime? @default(now()) @db.Timestamp(6)
  processedAt      DateTime? @db.Timestamp(6)
  processingTimeMs Int?
  responseStatus   Int?
  responseData     Json?

  @@index([identifierValue], map: "idx_webhook_log_identifier")
  @@index([orderId], map: "idx_webhook_log_order_id")
  @@index([receivedAt], map: "idx_webhook_log_received_at")
  @@index([status, receivedAt], map: "idx_webhook_log_status")
}

model EventStatusMapping {
  id                  String   @id @default(uuid())
  event               String
  correiosDescription String?
  internalStatus      String
  priority            Int      @default(0)
  isActive            Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@index([event, correiosDescription])
  @@index([isActive])
}

model UnmappedEventStatus {
  id                  String   @id @default(uuid())
  event               String
  correiosDescription String?
  firstSeenAt         DateTime @default(now())
  lastSeenAt          DateTime @default(now())
  occurrenceCount     Int      @default(1)
  samplePayload       Json?

  @@unique([event, correiosDescription])
  @@index([lastSeenAt])
}

model TrackingHistory {
  id           String   @id
  orderId      String
  trackingCode String   @db.VarChar(20)
  status       String   @db.VarChar(50)
  description  String?
  location     String?
  eventDate    DateTime
  rawData      Json?
  createdAt    DateTime @default(now())
  Order        Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([eventDate])
  @@index([orderId])
  @@index([trackingCode])
}

model webhook_mappings {
  id           String   @id(map: "PK_c20a6a765ba00af894c25a59385") @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  payloadKey   String   @db.VarChar(255)
  entityColumn String?  @db.VarChar(255)
  entityType   String   @default("Sale") @db.VarChar(50)
  dataType     String?  @db.VarChar(50)
  sampleValue  String?
  isActive     Boolean  @default(true)
  tenantId     String?  @db.VarChar(100)
  description  String?
  createdAt    DateTime @default(now()) @db.Timestamp(6)
  updatedAt    DateTime @default(now()) @db.Timestamp(6)

  @@unique([payloadKey, tenantId], map: "IDX_97c303f5a7435007235bb646f9")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model webhooklog {
  id           String?   @db.Uuid
  payloadKey   String?   @db.VarChar(255)
  entityColumn String?   @db.VarChar(255)
  entityType   String?   @db.VarChar(50)
  dataType     String?   @db.VarChar(50)
  sampleValue  String?
  isActive     Boolean?
  tenantId     String?   @db.VarChar(100)
  description  String?
  createdAt    DateTime? @db.Timestamp(6)
  updatedAt    DateTime? @db.Timestamp(6)

  @@ignore
}

enum Role {
  ADMIN
  SUPERVISOR
  COBRADOR
  VENDEDOR
}

enum OrderStatus {
  PagamentoPendente
  Completo
  Parcial
  Cancelado
  Transito
  Analise
  Separacao
  Frustrado
  Recuperacao
  Negociacao
  Promessa
  RetirarCorreios
  EntregaFalha
  ConfirmarEntrega
  DevolvidoCorreios
  ConfirmarPagamento
}

enum NotificationType {
  ORDER_CREATED
  STATUS_CHANGED
  LOW_STOCK
  PAYMENT_REMINDER
  CUSTOM
}

enum NotificationChannel {
  WHATSAPP
  EMAIL
  SMS
}

enum NotificationStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
}

enum CommissionApprovalStatus {
  NONE
  PENDING
  APPROVED
  REJECTED
}

enum DuplicateStatus {
  PENDING_REVIEW
  APPROVED
  DENIED
  AUTO_APPROVED
}

enum ReviewDecision {
  APPROVE_ORDER
  DENY_ORDER
  MERGE_ORDERS
  INVESTIGATE_FURTHER
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AuditAction {
  ORDER_CREATED
  ORDER_UPDATED
  DUPLICATE_DETECTED
  DUPLICATE_REVIEWED
  DUPLICATE_APPROVED
  DUPLICATE_DENIED
  ADDRESS_PARSED
  ADDRESS_GEOCODED
  MANUAL_OVERRIDE
  SYSTEM_OVERRIDE
  RISK_ASSESSED
  ORDER_REVIEWED
  ORDER_DELETED
}

model PaymentMethod {
  id        String    @id @default(uuid())
  name      String
  isActive  Boolean   @default(true)
  tenantId  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  payments  Payment[]

  @@unique([name, tenantId])
  @@index([tenantId])
}

model Payment {
  id              String        @id @default(uuid())
  orderId         String
  amount          Decimal       @db.Decimal(10, 2)
  paymentMethodId String
  collectorId     String
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  collector       User          @relation(fields: [collectorId], references: [id])

  @@index([orderId])
  @@index([collectorId])
  @@index([paymentMethodId])
  @@index([createdAt])
}

enum ZapStatus {
  Ativo
  Aquecendo
  Pronto
  Bloqueado
  EmAnalise
  Recuperado
  StandBy
}

model Zap {
  id          String      @id @default(uuid())
  name        String
  phoneNumber String?
  tenantId    String
  status      ZapStatus   @default(Ativo)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  orders      Order[]

  @@unique([name, tenantId])
  @@index([tenantId])
  @@index([status])
}
