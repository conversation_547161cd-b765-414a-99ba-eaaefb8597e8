generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        String   @id @default(cuid())
  name      String
  domain    String   @unique
  status    TenantStatus @default(ACTIVE)
  plan      TenantPlan   @default(FREE)
  
  // Subscription & Billing
  billingEmail        String?
  stripeCustomerId    String?   @unique
  stripeSubscriptionId String?
  trialEndsAt         DateTime?
  suspendedAt         DateTime?
  
  // Limits based on plan
  maxUsers      Int      @default(5)
  maxOrders     Int      @default(1000)
  maxProducts   Int      @default(100)
  storageQuotaMB Int     @default(1024)
  
  // Customization
  customDomain  String?
  logoUrl       String?
  primaryColor  String   @default("#1976d2")
  metadata      Json     @default("{}")
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  users               User[]
  orders              Order[]
  settings            Settings?
  products            Product[]
  inventoryItems      InventoryItem[]
  inventoryTransactions InventoryTransaction[]
  usageRecords        TenantUsage[]
  auditLogs           AuditLog[]
  
  @@index([domain])
  @@index([status])
  @@index([stripeCustomerId])
}

model User {
  id               String            @id @default(cuid())
  email            String
  hashedPassword   String
  fullName         String
  role             UserRole
  isActive         Boolean           @default(true)
  tenantId         String
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  
  tenant                  Tenant                  @relation(fields: [tenantId], references: [id])
  trackingHistories       TrackingHistory[]
  ordersAsSeller          Order[]                 @relation("SellerOrders")
  ordersAsCollector       Order[]                 @relation("CollectorOrders")
  billingHistories        BillingHistory[]
  inventoryTransactions   InventoryTransaction[]
  auditLogs               AuditLog[]

  @@unique([email, tenantId])
  @@index([email, tenantId])
  @@index([tenantId])
}

model Product {
  id          String    @id @default(cuid())
  name        String
  description String?
  imageUrl    String?
  isActive    Boolean   @default(true)
  tenantId    String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  tenant      Tenant    @relation(fields: [tenantId], references: [id])
  offers      Offer[]
  variations  ProductVariation[]
  kits        Kit[]
  
  @@unique([name, tenantId])
  @@index([tenantId])
}

model Offer {
  id                String    @id @default(cuid())
  productId         String
  name              String
  price             Float
  gelQuantity       Int       @default(0)
  capsulesQuantity  Int       @default(0)
  isKit             Boolean   @default(false)
  isActive          Boolean   @default(true)
  displayOrder      Int       @default(0)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  product           Product   @relation(fields: [productId], references: [id])
  orders            Order[]
  
  @@index([productId])
  @@index([isActive])
}

model Order {
  id              String           @id @default(cuid())
  orderNumber     String
  customerName    String
  customerPhone   String
  customerAddress String
  totalAmount     Float
  paidAmount      Float            @default(0)
  status          OrderStatus      @default(PENDING)
  trackingCode    String?
  isDuplicate     Boolean          @default(false)
  tenantId        String
  sellerId        String
  collectorId     String?
  offerId         String?
  kitId           String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  deletedAt       DateTime?        // Soft delete
  
  tenant          Tenant           @relation(fields: [tenantId], references: [id])
  seller          User             @relation("SellerOrders", fields: [sellerId], references: [id])
  collector       User?            @relation("CollectorOrders", fields: [collectorId], references: [id])
  offer           Offer?           @relation(fields: [offerId], references: [id])
  kit             Kit?             @relation(fields: [kitId], references: [id])
  billingHistory  BillingHistory[]

  @@unique([orderNumber, tenantId])
  @@index([tenantId])
  @@index([sellerId])
  @@index([collectorId])
  @@index([status])
  @@index([createdAt])
  @@index([deletedAt])
  @@index([offerId])
  @@index([kitId])
}

model InventoryItem {
  id                String                  @id @default(cuid())
  productType       ProductType
  currentStock      Int                     @default(0)
  minimumStock      Int                     @default(0)
  unitCost          Float                   @default(0)
  tenantId          String
  createdAt         DateTime                @default(now())
  updatedAt         DateTime                @updatedAt
  
  tenant            Tenant                  @relation(fields: [tenantId], references: [id])
  transactions      InventoryTransaction[]
  
  @@unique([productType, tenantId])
  @@index([tenantId])
}

model InventoryTransaction {
  id                String              @id @default(cuid())
  inventoryItemId   String
  type              TransactionType
  quantity          Int
  unitCost          Float?
  totalCost         Float?
  reference         String?             // Order ID or other reference
  notes             String?
  performedById     String
  tenantId          String
  createdAt         DateTime            @default(now())
  
  inventoryItem     InventoryItem       @relation(fields: [inventoryItemId], references: [id])
  performedBy       User                @relation(fields: [performedById], references: [id])
  tenant            Tenant              @relation(fields: [tenantId], references: [id])
  
  @@index([inventoryItemId])
  @@index([type])
  @@index([createdAt])
  @@index([tenantId])
}

model BillingHistory {
  id          String   @id @default(cuid())
  orderId     String
  amount      Float
  notes       String?
  createdById String
  createdAt   DateTime @default(now())
  
  order       Order    @relation(fields: [orderId], references: [id])
  createdBy   User     @relation(fields: [createdById], references: [id])
  
  @@index([orderId])
  @@index([createdById])
}

model Settings {
  id            String   @id @default(cuid())
  tenantId      String   @unique
  theme         String   @default("light")
  notifyByEmail Boolean  @default(true)
  notifyBySMS   Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  tenant        Tenant   @relation(fields: [tenantId], references: [id])
}

model TrackingHistory {
  id           String   @id @default(cuid())
  trackingCode String
  timestamp    DateTime @default(now())
  status       String
  success      Boolean  @default(true)
  userId       String?
  details      String?
  
  user         User?    @relation(fields: [userId], references: [id])
  
  @@index([trackingCode])
  @@index([userId])
  @@index([timestamp])
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  SUPERVISOR
  COLLECTOR
  SELLER
}

enum OrderStatus {
  PENDING
  IN_PROGRESS
  PAID
  PARTIALLY_PAID
  NEGOTIATING
  CANCELLED
  DELIVERED
}

enum ProductType {
  GEL
  CAPSULES
}

enum TransactionType {
  PURCHASE
  SALE
  ADJUSTMENT
  RETURN
}

model ProductVariation {
  id          String      @id @default(cuid())
  productId   String
  type        VariationType
  customName  String?     // Used when type is CUSTOM
  costPrice   Float       @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  kitItems    KitItem[]
  
  @@unique([productId, type, customName])
  @@index([productId])
}

model Kit {
  id          String      @id @default(cuid())
  productId   String
  sku         String      @unique // Auto-generated SKU
  name        String
  description String?
  price       Float
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  items       KitItem[]
  orders      Order[]
  
  @@index([productId])
  @@index([sku])
}

model KitItem {
  id          String      @id @default(cuid())
  kitId       String
  variationId String
  quantity    Int
  
  kit         Kit         @relation(fields: [kitId], references: [id], onDelete: Cascade)
  variation   ProductVariation @relation(fields: [variationId], references: [id])
  
  @@unique([kitId, variationId])
  @@index([kitId])
  @@index([variationId])
}

enum VariationType {
  CAPSULAS
  GOTAS
  GEL
  SPRAY
  CREME
  CUSTOM
}

enum TenantStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TRIAL
}

enum TenantPlan {
  FREE
  BASIC
  PRO
  ENTERPRISE
}

model TenantUsage {
  id            String   @id @default(cuid())
  tenantId      String
  date          DateTime @db.Date
  apiCalls      Int      @default(0)
  storageUsedMB Float    @default(0)
  activeUsers   Int      @default(0)
  ordersCreated Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  tenant        Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@unique([tenantId, date])
  @@index([tenantId])
  @@index([date])
}

model AuditLog {
  id        String   @id @default(cuid())
  tenantId  String
  userId    String?
  action    String
  entity    String
  entityId  String?
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([tenantId])
  @@index([userId])
  @@index([entity, entityId])
  @@index([createdAt])
}