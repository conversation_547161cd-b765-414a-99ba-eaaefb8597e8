import { PrismaClient, NotificationType, NotificationChannel, NotificationStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Iniciando seed da Sprint 6...');

  // Busca usuários existentes ou cria novos
  let admin = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  if (!admin) {
    const hashedPassword = await bcrypt.hash('Admin@123', 10);
    admin = await prisma.user.create({
      data: {
        name: 'Administrador',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        active: true,
      },
    });
  }

  let vendedor = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  if (!vendedor) {
    const hashedPassword = await bcrypt.hash('<PERSON><PERSON><PERSON>@123', 10);
    vendedor = await prisma.user.create({
      data: {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'VENDEDOR',
        active: true,
      },
    });
  }

  // Busca clientes existentes
  const customers = await prisma.customer.findMany({
    take: 3,
  });

  if (customers.length === 0) {
    console.log('⚠️ Nenhum cliente encontrado. Execute o seed da Sprint 5 primeiro.');
    return;
  }

  // Criar notificações de exemplo
  const notifications: any[] = [];

  // 1. Notificação de pedido criado (enviada)
  notifications.push({
    type: NotificationType.ORDER_CREATED,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.SENT,
    payload: {
      order: {
        id: '123456',
        total: '150.00',
        items: [
          { productName: 'Camiseta Básica', quantity: 2, unitPrice: '29.90' },
          { productName: 'Calça Jeans', quantity: 1, unitPrice: '89.90' },
        ],
      },
      customer: {
        name: customers[0].name,
        phone: customers[0].phone,
      },
      seller: {
        name: vendedor.name,
      },
    },
    sentAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
    retries: 0,
  });

  // 2. Notificação de mudança de status (enviada)
  notifications.push({
    type: NotificationType.STATUS_CHANGED,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.SENT,
    payload: {
      order: {
        id: '123456',
      },
      customer: {
        name: customers[0].name,
        phone: customers[0].phone,
      },
      previousStatus: 'Pendente',
      newStatus: 'Enviado',
    },
    sentAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hora atrás
    retries: 0,
  });

  // 3. Notificação de estoque baixo (pendente)
  notifications.push({
    type: NotificationType.LOW_STOCK,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.PENDING,
    payload: {
      product: { name: 'Tênis Esportivo' },
      variation: { name: '42 - Preto/Branco' },
      inventory: {
        quantity: 2,
        minAlert: 5,
      },
    },
    retries: 0,
  });

  // 4. Notificação de lembrete de pagamento (falhou)
  notifications.push({
    type: NotificationType.PAYMENT_REMINDER,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.FAILED,
    error: 'Número de telefone inválido',
    payload: {
      order: {
        id: '789012',
        total: '299.90',
      },
      customer: {
        name: customers[1].name,
        phone: '11000000000', // Número inválido propositalmente
      },
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
      collector: {
        name: 'Carlos Cobrador',
      },
    },
    retries: 3,
  });

  // 5. Notificação customizada (processando)
  notifications.push({
    type: NotificationType.CUSTOM,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.PROCESSING,
    payload: {
      subject: 'Promoção Especial',
      message: 'Olá! Temos uma promoção especial para você. Aproveite 20% de desconto em todos os produtos até domingo!',
    },
    retries: 0,
  });

  // 6. Notificação de pedido entregue
  notifications.push({
    type: NotificationType.STATUS_CHANGED,
    channel: NotificationChannel.WHATSAPP,
    status: NotificationStatus.SENT,
    payload: {
      order: {
        id: '345678',
      },
      customer: {
        name: customers[2].name,
        phone: customers[2].phone,
      },
      previousStatus: 'Enviado',
      newStatus: 'Entregue',
    },
    sentAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutos atrás
    retries: 0,
  });

  // Criar notificações no banco
  for (const notification of notifications) {
    await prisma.notificationJob.create({
      data: notification,
    });
  }

  console.log('✅ Seed da Sprint 6 concluído!');
  console.log(`- ${notifications.length} notificações criadas`);
  console.log('- Status das notificações:');
  console.log('  • 3 enviadas (SENT)');
  console.log('  • 1 pendente (PENDING)');
  console.log('  • 1 processando (PROCESSING)');
  console.log('  • 1 falhou (FAILED)');
  console.log('\n📱 Exemplos de payloads:');
  console.log('\n1. Pedido Criado:');
  console.log(JSON.stringify(notifications[0].payload, null, 2));
  console.log('\n2. Mudança de Status:');
  console.log(JSON.stringify(notifications[1].payload, null, 2));
  console.log('\n3. Estoque Baixo:');
  console.log(JSON.stringify(notifications[2].payload, null, 2));
}

main()
  .catch((e) => {
    console.error('❌ Erro ao executar seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });