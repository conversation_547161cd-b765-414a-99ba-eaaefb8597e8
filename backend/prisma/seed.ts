import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const password = 'admin123';
  const hashedPassword = await bcrypt.hash(password, 10);
  
  // The tenant ID from your environment variables
  const tenantId = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
  
  // First ensure the tenant exists
  await prisma.tenant.upsert({
    where: { id: tenantId },
    update: {
      name: 'ZenCash',
      active: true,
    },
    create: {
      id: tenantId,
      name: 'ZenCash',
      slug: 'zencash',
      domain: 'zencash-production-1ccd.up.railway.app',
      active: true,
      settings: {},
    }
  });
  
  console.log('✅ Tenant created or updated successfully');

  await prisma.user.upsert({
    where: { 
      email_tenantId: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    },
    update: {
      name: '<PERSON><PERSON>',
      password: hashedPassword,
      role: Role.ADMIN,
      tenantId: tenantId, // Add tenant ID
    },
    create: {
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      password: hashedPassword,
      role: Role.ADMIN,
      tenantId: tenantId, // Add tenant ID
      active: true,
    },
  });

  console.log('✅ Admin user created or updated successfully');
  console.log('📧 Email: <EMAIL>');
  console.log('🔑 Password: admin123');
}

main()
  .catch((e) => {
    console.error('❌ Error while seeding admin user:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
