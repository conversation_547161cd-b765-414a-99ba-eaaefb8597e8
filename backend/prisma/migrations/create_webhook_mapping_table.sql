-- CreateTable for webhook_mappings
CREATE TABLE IF NOT EXISTS webhook_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    payload_key VARCHAR(255) NOT NULL,
    entity_column VARCHAR(255),
    entity_type VARCHAR(50) DEFAULT 'Sale' NOT NULL,
    data_type VARCHAR(50),
    sample_value TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    tenant_id VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create unique index on payload_key and tenant_id
CREATE UNIQUE INDEX idx_webhook_mappings_key_tenant ON webhook_mappings(payload_key, tenant_id);

-- Create index for tenant queries
CREATE INDEX idx_webhook_mappings_tenant ON webhook_mappings(tenant_id);

-- Create index for active mappings with entity columns
CREATE INDEX idx_webhook_mappings_active ON webhook_mappings(is_active, entity_column) 
WHERE is_active = true AND entity_column IS NOT NULL;

-- Add update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_webhook_mappings_updated_at 
BEFORE UPDATE ON webhook_mappings 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column();