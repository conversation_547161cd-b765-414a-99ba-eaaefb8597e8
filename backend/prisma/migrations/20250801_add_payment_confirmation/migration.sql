-- Add ConfirmarPagamento status to OrderStatus enum
ALTER TYPE "OrderStatus" ADD VALUE 'ConfirmarPagamento';

-- Add payment confirmation fields to Order table
ALTER TABLE "Order" 
ADD COLUMN "paymentConfirmationStatus" TEXT,
ADD COLUMN "paymentConfirmedBy" TEXT,
ADD COLUMN "paymentConfirmationDate" TIMESTAMP(3),
ADD COLUMN "paymentDenialReason" TEXT;

-- Add foreign key constraint for paymentConfirmedBy
ALTER TABLE "Order"
ADD CONSTRAINT "Order_paymentConfirmedBy_fkey" 
FOREIGN KEY ("paymentConfirmedBy") 
REFERENCES "User"("id") 
ON DELETE SET NULL 
ON UPDATE CASCADE;