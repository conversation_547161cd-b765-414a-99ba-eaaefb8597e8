-- Create ZapStatus enum
CREATE TYPE "ZapStatus" AS ENUM ('Ativo', 'Aquecendo', 'Pronto', 'Bloqueado', 'EmAnalise', 'Recuperado', 'StandBy');

-- Create Zap table
CREATE TABLE "Zap" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "status" "ZapStatus" NOT NULL DEFAULT 'Ativo',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Zap_pkey" PRIMARY KEY ("id")
);

-- Add zapSourceId to Order table
ALTER TABLE "Order" ADD COLUMN "zapSourceId" TEXT;

-- Create indexes
CREATE UNIQUE INDEX "Zap_name_tenantId_key" ON "Zap"("name", "tenantId");
CREATE INDEX "Zap_tenantId_idx" ON "Zap"("tenantId");
CREATE INDEX "Zap_status_idx" ON "Zap"("status");
CREATE INDEX "Order_zapSourceId_idx" ON "Order"("zapSourceId");

-- Add foreign key constraint
ALTER TABLE "Order" ADD CONSTRAINT "Order_zapSourceId_fkey" FOREIGN KEY ("zapSourceId") REFERENCES "Zap"("id") ON DELETE SET NULL ON UPDATE CASCADE;