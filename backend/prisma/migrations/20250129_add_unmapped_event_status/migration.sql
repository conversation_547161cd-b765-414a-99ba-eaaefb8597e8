-- CreateTable
CREATE TABLE "UnmappedEventStatus" (
    "id" TEXT NOT NULL,
    "event" TEXT NOT NULL,
    "correiosDescription" TEXT,
    "firstSeenAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastSeenAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "occurrenceCount" INTEGER NOT NULL DEFAULT 1,
    "samplePayload" JSONB,

    CONSTRAINT "UnmappedEventStatus_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UnmappedEventStatus_lastSeenAt_idx" ON "UnmappedEventStatus"("lastSeenAt");

-- CreateIndex
CREATE UNIQUE INDEX "UnmappedEventStatus_event_correiosDescription_key" ON "UnmappedEventStatus"("event", "correiosDescription");