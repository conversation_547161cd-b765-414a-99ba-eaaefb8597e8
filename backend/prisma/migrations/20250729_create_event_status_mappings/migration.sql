-- CreateTable
CREATE TABLE "EventStatusMapping" (
    "id" TEXT NOT NULL,
    "event" TEXT NOT NULL,
    "correiosDescription" TEXT,
    "internalStatus" TEXT NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EventStatusMapping_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "EventStatusMapping_event_correiosDescription_idx" ON "EventStatusMapping"("event", "correiosDescription");

-- CreateIndex
CREATE INDEX "EventStatusMapping_isActive_idx" ON "EventStatusMapping"("isActive");