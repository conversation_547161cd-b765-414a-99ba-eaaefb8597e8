#!/bin/bash

# Test production webhook endpoint
echo "Testing production webhook endpoint..."

curl -X POST https://zencash-production.up.railway.app/api/v1/webhooks/shipments \
  -H "Content-Type: application/json" \
  -d '{
    "sale": {
      "id": "test-123",
      "code": "sal00001",
      "tracking_code": "BR123456789BR",
      "status": "SHIPPED"
    },
    "customer": {
      "name": "Test Customer",
      "email": "<EMAIL>",
      "phone": "11999999999"
    }
  }' \
  -w "\n\nHTTP Status: %{http_code}\n"