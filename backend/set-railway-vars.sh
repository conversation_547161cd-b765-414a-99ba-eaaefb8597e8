#!/bin/bash

# Set all Railway environment variables at once

echo "Setting Railway environment variables..."

# Database URL from your PostgreSQL instance
railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"

# Generate a secure JWT secret
JWT_SECRET="zencash-jwt-secret-$(date +%s)-$(openssl rand -hex 32)"
railway variables set JWT_SECRET="$JWT_SECRET"

# API Configuration
railway variables set API_PREFIX="api/v1"
railway variables set NODE_ENV="production"
railway variables set PORT="3000"

# CORS for your Vercel frontend
railway variables set CORS_ORIGIN="https://zencash-sand.vercel.app"

echo "✅ All environment variables set!"
echo ""
echo "JWT_SECRET generated: $JWT_SECRET"
echo ""
echo "Next steps:"
echo "1. Run: railway up"
echo "2. Run: railway open (to get your URL)"
echo "3. Update Vercel with your Railway URL"