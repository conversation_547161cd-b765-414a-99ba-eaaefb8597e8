import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function updatePasswords() {
  try {
    const hashedPassword = await bcrypt.hash('senha123', 10);
    
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of emails) {
      const updated = await prisma.user.update({
        where: { email },
        data: { password: hashedPassword }
      });
      console.log(`✓ Updated password for: ${updated.email}`);
    }

    console.log('\n✅ All passwords updated to: senha123');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updatePasswords();