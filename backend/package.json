{"name": "zencash-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=20.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:railway": "./scripts/start-prod.sh", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "pre-commit": "lint-staged", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:migrate": "npx prisma migrate dev", "db:add-promessa": "node scripts/add-promessa-status.js", "db:generate-mock": "ts-node scripts/generate-mock-sales.ts", "db:mock-sales": "ts-node scripts/generate-mock-sales-simple.ts", "db:studio": "npx prisma studio", "db:seed": "npx prisma db seed", "db:seed-simple": "ts-node prisma/seed-simple.ts", "db:update-admin": "ts-node scripts/update-admin-user.ts", "setup:postgres": "./scripts/setup-postgres.sh", "seed": "ts-node prisma/seed-final.ts", "cleanup:users": "ts-node scripts/cleanup-users.ts", "antifraud:all": "npx ts-node src/scripts/process-antifraud-direct.ts", "antifraud:check": "npx ts-node src/scripts/check-antifraud-queue.ts", "fix:tenant-ids": "ts-node scripts/fix-tenant-ids.ts", "check:users": "ts-node scripts/check-users.ts", "assess:orders": "ts-node scripts/assess-existing-orders.ts", "delete:all-orders": "ts-node scripts/delete-all-orders.ts", "check:orders": "ts-node src/scripts/check-recent-orders.ts", "check:webhooks": "ts-node src/scripts/webhook-order-status.ts", "postinstall": "prisma generate", "prepare": "node -e \"try { if (process.env.NODE_ENV !== 'production' && require.resolve('husky')) { require('husky').install() } } catch (e) {}\" || true"}, "dependencies": {"@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@nestjs/axios": "4.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "11.0.0", "@nestjs/throttler": "6.4.0", "@nestjs/typeorm": "11.0.0", "@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.60.1", "@opentelemetry/exporter-jaeger": "2.0.1", "@opentelemetry/exporter-trace-otlp-http": "0.202.0", "@opentelemetry/instrumentation-express": "0.51.0", "@opentelemetry/instrumentation-http": "0.202.0", "@opentelemetry/instrumentation-nestjs-core": "0.48.0", "@opentelemetry/resources": "2.0.1", "@opentelemetry/sdk-node": "0.202.0", "@opentelemetry/sdk-trace-base": "2.0.1", "@opentelemetry/semantic-conventions": "1.34.0", "@prisma/client": "^6.10.1", "@sentry/node": "9.34.0", "@sentry/profiling-node": "9.34.0", "@types/bcryptjs": "2.4.6", "@types/cookie-parser": "^1.4.9", "bcrypt": "6.0.0", "bcryptjs": "3.0.2", "bull-board": "^1.7.2", "bullmq": "^5.56.0", "cache-manager": "^7.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "date-fns": "4.1.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "2.0.2", "nest-winston": "^1.10.2", "opossum": "9.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "8.16.3", "prom-client": "15.1.3", "rastreio-correios": "^1.1.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "soap": "1.1.12", "stripe": "^18.2.1", "swagger": "^0.7.5", "typeorm": "0.3.25", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "9.9.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "2.0.0", "@types/node": "^22.10.7", "@types/papaparse": "5.3.16", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/soap": "0.18.0", "@types/supertest": "^6.0.2", "axios": "1.10.0", "csv-parse": "5.6.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.0", "papaparse": "5.5.3", "prettier": "^3.4.2", "prisma": "^6.10.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}