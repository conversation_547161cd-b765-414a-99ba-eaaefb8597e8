#!/bin/sh
echo "=== Debug Container Start ==="
echo "Current directory: $(pwd)"
echo "Files in dist/:"
ls -la dist/ || echo "dist/ directory not found"
echo ""
echo "Environment variables:"
echo "PORT: $PORT"
echo "DATABASE_URL: ${DATABASE_URL:0:30}..."
echo "NODE_ENV: $NODE_ENV"
echo ""
echo "Attempting to start application..."
echo "Running: node dist/main"
node dist/main || echo "Failed to start application"