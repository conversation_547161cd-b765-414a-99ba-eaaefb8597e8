{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(antifraud_duplicate_detection_total{tenant_id=\"$tenant\"}[5m])", "legendFormat": "{{result}} - {{match_level}}", "refId": "A"}], "title": "Duplicate Detection Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.0", "targets": [{"expr": "antifraud_review_queue_size{tenant_id=\"$tenant\"}", "refId": "A"}], "title": "Review Queue Size", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 3, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(antifraud_review_decisions_total{tenant_id=\"$tenant\"}) by (decision)", "legendFormat": "{{decision}}", "refId": "A"}], "title": "Review Decisions Distribution", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 2000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(antifraud_duplicate_detection_duration_seconds_bucket{tenant_id=\"$tenant\"}[5m])) by (le)) * 1000", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(antifraud_duplicate_detection_duration_seconds_bucket{tenant_id=\"$tenant\"}[5m])) by (le)) * 1000", "legendFormat": "99th percentile", "refId": "B"}], "title": "Duplicate Detection Latency", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "increase(antifraud_errors_total{tenant_id=\"$tenant\"}[1h])", "legendFormat": "{{error_type}} - {{operation}}", "refId": "A"}], "title": "Errors by Type (Last Hour)", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 16}, "id": 6, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.0", "targets": [{"expr": "avg(antifraud_fuzzy_matching_accuracy{tenant_id=\"$tenant\"})", "refId": "A"}], "title": "Fuzzy Matching Accuracy", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 18, "x": 6, "y": 16}, "id": 7, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(antifraud_review_processing_duration_seconds_bucket{tenant_id=\"$tenant\"}[5m])) by (le, decision))", "legendFormat": "{{decision}}", "refId": "A"}], "title": "Review Processing Time by Decision (95th percentile)", "type": "timeseries"}], "schemaVersion": 27, "style": "dark", "tags": ["antifraud", "monitoring"], "templating": {"list": [{"current": {"selected": false, "text": "default-tenant", "value": "default-tenant"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Tenant", "multi": false, "name": "tenant", "options": [], "query": "label_values(antifraud_duplicate_detection_total, tenant_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Anti-fraud System Overview", "uid": "antifraud-overview", "version": 1}