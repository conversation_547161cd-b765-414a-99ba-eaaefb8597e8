# Multi-Tenant Migration Guide

## Quick Fixes for Compilation Errors

### 1. Configuration Service
```typescript
// src/configuration/configuration.service.ts

async set(key: string, value: any, tenantId: string): Promise<Configuration> {
  // Add tenantId to the data object
  return this.prisma.configuration.create({
    data: {
      key,
      value: JSON.stringify(value),
      tenantId, // Add this
    },
  });
}

async get(key: string, tenantId: string): Promise<any> {
  const config = await this.prisma.configuration.findUnique({
    where: {
      key_tenantId: { // Use compound unique key
        key,
        tenantId,
      },
    },
  });
  return config ? JSON.parse(config.value) : null;
}
```

### 2. Kits Service
```typescript
// src/kits/kits.service.ts

async create(createKitDto: CreateKitDto, tenantId: string): Promise<Kit> {
  const { items, ...kitData } = createKitDto;
  
  return this.prisma.kit.create({
    data: {
      ...kitData,
      tenantId, // Add this
      items: {
        create: items.map(item => ({
          productVariationId: item.productVariationId,
          quantity: item.quantity,
        })),
      },
    },
  });
}
```

### 3. Products Service
```typescript
// src/products/products.service.ts

async create(createProductDto: CreateProductDto, tenantId: string): Promise<Product> {
  return this.prisma.product.create({
    data: {
      ...createProductDto,
      tenantId, // Add this
    },
  });
}
```

### 4. Users Service
```typescript
// src/users/users.service.ts

async create(createUserDto: CreateUserDto, tenantId: string): Promise<User> {
  const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
  
  return this.prisma.user.create({
    data: {
      ...createUserDto,
      password: hashedPassword,
      tenantId, // Add this
    },
  });
}

async findByEmail(email: string, tenantId: string): Promise<User | null> {
  return this.prisma.user.findUnique({
    where: {
      email_tenantId: { // Use compound unique key
        email,
        tenantId,
      },
    },
  });
}
```

### 5. Controller Updates
Add `@Tenant()` decorator to all controllers:

```typescript
// Example for products.controller.ts
import { Tenant } from '../common/decorators/tenant.decorator';

@Post()
async create(
  @Body() createProductDto: CreateProductDto,
  @Tenant() tenantId: string, // Add this
) {
  return this.productsService.create(createProductDto, tenantId);
}
```

## Testing Multi-Tenancy

### 1. Create Test Tenant
```sql
-- Insert a test tenant
INSERT INTO "User" (id, name, email, password, role, "tenantId", active, "createdAt", "updatedAt")
VALUES (
  'test-user-id',
  'Test Admin',
  '<EMAIL>',
  '$2b$10$...',  -- bcrypt hash of password
  'ADMIN',
  'tenant-123',  -- This is your tenant ID
  true,
  NOW(),
  NOW()
);
```

### 2. Test with cURL
```bash
# Test product creation with tenant
curl -X POST http://localhost:3001/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-tenant-id: tenant-123" \
  -d '{
    "name": "Test Product",
    "price": 100
  }'
```

### 3. Verify Tenant Isolation
```bash
# Try accessing with different tenant ID - should return empty
curl http://localhost:3001/products \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-tenant-id: different-tenant"
```

## Migration Script for Existing Data

```typescript
// scripts/migrate-to-multi-tenant.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const DEFAULT_TENANT_ID = 'default-tenant-id';

async function migrate() {
  // Update all existing records with default tenant
  await prisma.$transaction([
    prisma.user.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
    prisma.order.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
    prisma.product.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
    prisma.kit.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
    prisma.customer.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
    prisma.configuration.updateMany({
      where: { tenantId: null },
      data: { tenantId: DEFAULT_TENANT_ID },
    }),
  ]);
}

migrate().catch(console.error).finally(() => prisma.$disconnect());
```

## Common Issues & Solutions

### Issue: "Tenant ID is required"
**Solution**: Ensure `x-tenant-id` header is sent with all requests

### Issue: "Unique constraint failed"
**Solution**: Use compound unique constraints (e.g., `email_tenantId`)

### Issue: "Cannot find records"
**Solution**: Check if PrismaService is properly filtering by tenant

### Issue: "Order creation fails"
**Solution**: Ensure seller's tenantId is used for the order