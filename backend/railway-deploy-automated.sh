#!/bin/bash

echo "🚀 Starting Railway Deployment for ZenCash Backend"
echo "================================================"

# Check if we're in the backend directory
if [ ! -f "nest-cli.json" ]; then
    echo "❌ Error: Must run from the backend directory"
    exit 1
fi

# Check Railway CLI
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

echo "📝 Please make sure you're logged in to Railway"
echo "If not logged in, run: railway login"
echo ""
echo "Press Enter to continue..."
read

echo "🔗 Linking to Railway project..."
echo "When prompted:"
echo "1. Select your workspace"
echo "2. Select the project with ID: afe56c34-d890-4fde-9fe6-b18f21095611"
echo "3. Select 'production' environment"
echo ""
echo "Press Enter to start linking..."
read

railway link

echo ""
echo "✅ Checking link status..."
railway status

echo ""
echo "⚙️  Setting environment variables..."
./set-railway-vars.sh

echo ""
echo "🚂 Deploying to Railway..."
echo "This may take a few minutes..."
railway up

echo ""
echo "✅ Deployment complete!"
echo ""
echo "📋 Final steps:"
echo "1. Get your backend URL: railway open"
echo "2. Test health endpoint: curl https://[your-url]/api/v1/health"
echo "3. Update Vercel environment variables:"
echo "   REACT_APP_API_URL=https://[your-url]/api/v1"
echo "   REACT_APP_TENANT_ID=28a833c0-c2a1-4498-85ca-b028f982ffb2"
echo ""
echo "📊 View logs: railway logs"