# Production Safeguards Guide

## Overview

This document outlines critical safeguards to prevent accidental data loss in production environments.

## Script Safety Checklist

Before running ANY script in production, verify:

- [ ] <PERSON><PERSON><PERSON> has environment checks preventing production execution
- [ ] <PERSON><PERSON><PERSON> has confirmation prompts for destructive operations
- [ ] A backup has been created before running the script
- [ ] Script operations are wrapped in transactions where possible
- [ ] <PERSON><PERSON><PERSON> has been tested in development/staging first

## Dangerous Commands - NEVER USE IN PRODUCTION

### 1. Prisma Commands
```bash
# NEVER USE:
npx prisma db push --force-reset
npx prisma migrate reset
npx prisma migrate reset --force

# SAFE ALTERNATIVES:
npx prisma db push --skip-generate
npx prisma migrate deploy
```

### 2. SQL Commands
```sql
-- NEVER USE:
DROP DATABASE database_name;
DROP SCHEMA public CASCADE;
DROP TABLE table_name;
TRUNCATE TABLE table_name;
DELETE FROM table_name;  -- without WHERE clause

-- SAFE ALTERNATIVES:
-- Use soft deletes with status fields
UPDATE orders SET deleted_at = NOW() WHERE id = ?;
-- Or archive data instead of deleting
INSERT INTO orders_archive SELECT * FROM orders WHER<PERSON> created_at < ?;
```

### 3. Script Patterns
```typescript
// NEVER USE:
await prisma.order.deleteMany({});  // Deletes ALL records

// SAFE ALTERNATIVE:
// Add environment check
if (process.env.NODE_ENV === 'production') {
  throw new Error('This operation is not allowed in production');
}

// Add specific conditions
await prisma.order.deleteMany({
  where: {
    id: specificId,
    tenantId: tenantId
  }
});
```

## Required Safeguards for Destructive Scripts

### 1. Environment Protection
```typescript
// Add at the beginning of any destructive script
if (process.env.NODE_ENV === 'production' || 
    process.env.RAILWAY_ENVIRONMENT === 'production') {
  console.error('❌ This script cannot run in production!');
  process.exit(1);
}
```

### 2. Confirmation Prompts
```typescript
const readline = require('readline');

async function confirmAction(message: string): Promise<boolean> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(`${message} (yes/no): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes');
    });
  });
}

// Usage
if (!await confirmAction('Are you sure you want to proceed?')) {
  console.log('Operation cancelled');
  process.exit(0);
}
```

### 3. Backup Before Operations
```bash
#!/bin/bash
# Always backup before destructive operations
./scripts/backup-database.sh

if [ $? -ne 0 ]; then
  echo "Backup failed, aborting operation"
  exit 1
fi

# Proceed with operation...
```

### 4. Transaction Wrapping
```typescript
// Wrap destructive operations in transactions
const result = await prisma.$transaction(async (tx) => {
  // All operations here can be rolled back if any fail
  await tx.orderItem.deleteMany({ where: { orderId } });
  await tx.order.delete({ where: { id: orderId } });
  return { success: true };
});
```

## Safe Script Naming Convention

1. **Dangerous Scripts**: Prefix with `DANGEROUS-` and add `.disabled` extension
   - Example: `DANGEROUS-delete-all-orders.ts.disabled`

2. **Safe Scripts**: Use descriptive names indicating safety
   - Example: `safe-delete-single-order.ts`

3. **Test Scripts**: Prefix with `test-` and include environment checks
   - Example: `test-order-deletion.ts`

## Emergency Recovery Procedures

If data loss occurs:

1. **Stop all operations immediately**
2. **Check available backups**:
   ```bash
   ls -la ./backups/
   # Check Railway backups in dashboard
   ```

3. **Restore from backup**:
   ```bash
   # Local backup
   ./scripts/restore-database.sh backup_file.sql.gz
   
   # Railway backup
   ./scripts/restore-railway-backup.sh
   ```

4. **Verify restoration**:
   ```bash
   node scripts/check-db-state.js
   ```

## Monitoring and Alerts

Set up monitoring for:
- Mass deletion operations (> 100 records)
- Schema changes
- Failed transactions
- Unusual query patterns

## Script Audit Requirements

All production scripts must:
1. Be reviewed by another developer
2. Include comprehensive logging
3. Have rollback procedures documented
4. Be tested in staging environment first

## Regular Safety Checks

Run monthly:
```bash
# Find potentially dangerous scripts
grep -r "deleteMany\s*(\s*{\s*}\s*)" --include="*.ts" --include="*.js" .
grep -r "force-reset" --include="*.sh" --include="*.ts" .
grep -r "DROP\s\+TABLE" --include="*.sql" --include="*.ts" .
```

## Contact for Emergencies

If you encounter a production data issue:
1. Document exactly what happened
2. Take screenshots of any errors
3. Note the exact time of the incident
4. Contact the team lead immediately

Remember: It's always better to ask before running a script than to risk data loss!