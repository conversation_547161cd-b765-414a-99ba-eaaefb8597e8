#!/bin/bash

# Test production webhook with complete data
echo "Testing production webhook with complete sale data..."

curl -X POST https://zencash-production.up.railway.app/api/v1/webhooks/shipments \
  -H "Content-Type: application/json" \
  -d '{
    "sale": {
      "id": "test-sale-123",
      "code": "sal00001",
      "value": 15000,
      "created_at": "2025-01-23T12:00:00Z"
    },
    "customer": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "11999998888",
      "document": "12345678901",
      "address": {
        "street": "Rua das Flores",
        "number": "123",
        "complement": "Apto 101",
        "district": "Centro",
        "city": "São Paulo",
        "state": "SP",
        "zip": "01234567"
      }
    }
  }' \
  -w "\n\nHTTP Status: %{http_code}\n" \
  -v