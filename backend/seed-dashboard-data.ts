import { PrismaClient, OrderStatus } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function seedDashboardData() {
  console.log('🌱 Criando dados de teste para Dashboard Inteligente...\n');

  try {
    // Get test users
    const vendedor = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    const cobrador = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!vendedor || !cobrador) {
      console.error('❌ Usuários de teste não encontrados!');
      return;
    }

    // Get or create customer
    let customer = await prisma.customer.findFirst({
      include: { addresses: true }
    });

    if (!customer) {
      customer = await prisma.customer.create({
        data: {
          name: 'Cliente Dashboard Test',
          cpf: '99999999999',
          phone: '11999999999',
          email: '<EMAIL>',
          addresses: {
            create: {
              cep: '01310-100',
              street: 'Av. Paulista',
              number: '1000',
              neighborhood: 'Bela Vista',
              city: 'São Paulo',
              state: 'SP',
              main: true,
            },
          },
        },
        include: { addresses: true }
      });
    }

    // Get or create product
    let product = await prisma.product.findFirst({
      include: { variations: true }
    });

    if (!product) {
      product = await prisma.product.create({
        data: {
          name: 'Produto Dashboard Test',
          description: 'Produto para testes',
          variations: {
            create: {
              variation: 'Padrão',
              price: new Decimal(100),
              sku: 'DASH-TEST-001',
            },
          },
        },
        include: { variations: true },
      });
    }

    const variation = product.variations[0];

    // 1. Create orders for "Receber Hoje" (payment due today)
    const today = new Date();
    today.setHours(12, 0, 0, 0);

    console.log('📋 Criando pedidos para "Receber Hoje"...');
    
    const order1 = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(500),
        status: OrderStatus.PagamentoPendente,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        nextPaymentDate: today,
        lastContactDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 5,
            unitPrice: variation.price,
          },
        },
      },
    });
    console.log('✓ Pedido criado para Receber Hoje:', order1.id);

    // 2. Create orders for "Riscos" (at risk)
    console.log('\n⚠️ Criando pedidos em Risco...');
    
    const riskOrder1 = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(800),
        status: OrderStatus.ConfirmarEntrega,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        lastContactDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 8,
            unitPrice: variation.price,
          },
        },
      },
    });
    console.log('✓ Pedido criado em risco (Confirmar Entrega):', riskOrder1.id);

    const riskOrder2 = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(300),
        status: OrderStatus.EntregaFalha,
        sellerId: vendedor.id,
        collectorId: cobrador.id,
        lastContactDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 3,
            unitPrice: variation.price,
          },
        },
      },
    });
    console.log('✓ Pedido criado em risco (Entrega Falha):', riskOrder2.id);

    // 3. Create orders for "Em Separação"
    console.log('\n📦 Criando pedidos em Separação...');
    
    const sepOrder = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(1200),
        status: OrderStatus.Separacao,
        sellerId: vendedor.id,
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 12,
            unitPrice: variation.price,
          },
        },
      },
    });
    console.log('✓ Pedido criado em Separação:', sepOrder.id);

    // 4. Create orders for "Análise"
    console.log('\n🔍 Criando pedidos em Análise...');
    
    const analiseOrder = await prisma.order.create({
      data: {
        customerName: customer.name,
        customerPhone: customer.phone,
        customerId: customer.id,
        total: new Decimal(2000),
        status: OrderStatus.Analise,
        sellerId: vendedor.id,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        items: {
          create: {
            productId: product.id,
            productVariationId: variation.id,
            productName: product.name,
            quantity: 20,
            unitPrice: variation.price,
          },
        },
      },
    });
    console.log('✓ Pedido criado em Análise:', analiseOrder.id);

    console.log('\n✅ Dados de teste criados com sucesso!');
    console.log('\nResumo:');
    console.log('- 1 pedido para Receber Hoje');
    console.log('- 2 pedidos em Risco');
    console.log('- 1 pedido em Separação');
    console.log('- 1 pedido em Análise');

  } catch (error) {
    console.error('❌ Erro ao criar dados:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedDashboardData();