# Sprint 3 - <PERSON><PERSON><PERSON><PERSON> (Orders)

## ✅ Implementações Concluídas

### 📊 Entidades Criadas

1. **Order**
   - `id`: UUID
   - `customerName`: Nome do cliente
   - `customerPhone`: Telefone do cliente
   - `total`: Valor total (Decimal)
   - `status`: Enum de status
   - `sellerId`: Vendedor responsável
   - `collectorId`: <PERSON><PERSON> responsável (opcional)
   - `createdAt/updatedAt`: Timestamps
   - **Relações**: `seller`, `collector`, `items`, `statusHistory`

2. **OrderItem**
   - `id`: UUID
   - `orderId`: Referência ao pedido
   - `productId`: ID do produto (mock por enquanto)
   - `productName`: Nome do produto
   - `quantity`: Quantidade
   - `unitPrice`: Preço unitário (Decimal)
   - **Relação**: `order` (cascade delete)

3. **OrderStatusHistory**
   - `id`: UUID
   - `orderId`: Referência ao pedido
   - `previousStatus`: Status anterior
   - `newStatus`: Novo status
   - `changedAt`: Data/hora da mudança
   - `changedById`: Usuário que fez a mudança
   - **Relações**: `order`, `changedBy`

### 📦 Arquivos Criados

1. **DTOs**
   - `/src/orders/dto/create-order.dto.ts`
   - `/src/orders/dto/update-status.dto.ts`
   - `/src/orders/dto/order-filter.dto.ts`

2. **Service & Controller**
   - `/src/orders/orders.service.ts`
   - `/src/orders/orders.controller.ts`
   - `/src/orders/orders.module.ts`

3. **Testes & Seeds**
   - `/src/orders/orders.service.spec.ts`
   - `/prisma/seed-orders.ts`

## 🔐 Rotas e Permissões

### 1. **POST /orders** - Criar pedido
- **Permissão**: `VENDEDOR`, `ADMIN`, `SUPERVISOR`
- **Body**:
```json
{
  "customerName": "Nome do Cliente",
  "customerPhone": "11999999999",
  "items": [
    {
      "productId": "prod-001",
      "productName": "Produto A",
      "quantity": 2,
      "unitPrice": 50.00
    }
  ],
  "collectorId": "uuid-cobrador" // opcional
}
```

### 2. **GET /orders** - Listar pedidos
- **Permissão**: `VENDEDOR`, `COBRADOR`, `ADMIN`, `SUPERVISOR`
- **Query Params**:
  - `status`: Filtrar por status
  - `sellerId`: Filtrar por vendedor
  - `collectorId`: Filtrar por cobrador
  - `startDate`: Data inicial
  - `endDate`: Data final
- **Restrições**:
  - `VENDEDOR`: vê apenas seus próprios pedidos
  - `COBRADOR`: vê apenas pedidos atribuídos a ele
  - `ADMIN/SUPERVISOR`: veem todos

### 3. **GET /orders/:id** - Buscar pedido específico
- **Permissão**: `VENDEDOR`, `COBRADOR`, `ADMIN`, `SUPERVISOR`
- **Retorna**: Pedido completo com items e histórico de status
- **Restrições**: Mesmas do listar

### 4. **PATCH /orders/:id/status** - Atualizar status
- **Permissão**: `COBRADOR`, `ADMIN`, `SUPERVISOR`
- **Body**:
```json
{
  "status": "SEPARACAO"
}
```
- **Validações**:
  - Transições de status válidas
  - `COBRADOR` só pode alterar pedidos atribuídos a ele
  - Registra no histórico automaticamente

### 5. **DELETE /orders/:id** - Cancelar pedido
- **Permissão**: `VENDEDOR`, `ADMIN`, `SUPERVISOR`
- **Comportamento**: Muda status para CANCELADO
- **Restrições**:
  - `VENDEDOR`: apenas seus pedidos PENDENTES
  - `SUPERVISOR`: apenas pedidos PENDENTES
  - `ADMIN`: qualquer pedido

## 🔄 Fluxo de Status

### Transições Válidas:
- `PENDENTE` → `SEPARACAO`, `CANCELADO`, `FALHA`
- `SEPARACAO` → `ENVIADO`, `CANCELADO`, `FALHA`
- `ENVIADO` → `ENTREGUE`, `FALHA`, `RECUPERACAO`
- `FALHA` → `RECUPERACAO`, `NEGOCIACAO`, `CANCELADO`
- `RECUPERACAO` → `ENVIADO`, `NEGOCIACAO`, `CANCELADO`
- `NEGOCIACAO` → `RECUPERACAO`, `CANCELADO`
- `ENTREGUE` e `CANCELADO` são estados finais

## 🧪 Dados de Teste

Execute `npx ts-node prisma/seed-orders.ts` para criar:

1. **Pedido PENDENTE**
   - Cliente: João Silva
   - Total: R$ 250,00
   - 2 items

2. **Pedido SEPARACAO**
   - Cliente: Maria Santos
   - Total: R$ 500,00
   - Com cobrador atribuído

3. **Pedido ENTREGUE**
   - Cliente: Pedro Oliveira
   - Total: R$ 1.200,00
   - Histórico completo de status

## 📝 Exemplos de Uso

### Criar Pedido (como Vendedor)
```bash
POST http://localhost:3003/orders
Authorization: Bearer {token}
{
  "customerName": "Novo Cliente",
  "customerPhone": "11999999999",
  "items": [
    {
      "productId": "prod-001",
      "productName": "Produto Teste",
      "quantity": 3,
      "unitPrice": 99.90
    }
  ]
}
```

### Listar Pedidos Pendentes
```bash
GET http://localhost:3003/orders?status=PENDENTE
Authorization: Bearer {token}
```

### Atualizar Status (como Cobrador)
```bash
PATCH http://localhost:3003/orders/{orderId}/status
Authorization: Bearer {token}
{
  "status": "SEPARACAO"
}
```

### Ver Histórico de Status
```bash
GET http://localhost:3003/orders/{orderId}
Authorization: Bearer {token}
# Retorna pedido com array statusHistory
```

## ⚠️ Observações Importantes

1. **Total calculado automaticamente** baseado nos items
2. **Histórico de status** registrado automaticamente
3. **Produtos são mockados** (apenas ID e nome por enquanto)
4. **Validação de transições** impede mudanças inválidas
5. **Cascade delete** nos items quando pedido é deletado
6. Todas as rotas exigem autenticação JWT