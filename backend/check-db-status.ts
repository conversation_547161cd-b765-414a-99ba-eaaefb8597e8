import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDatabase() {
  try {
    // Check current orders
    const orderCount = await prisma.order.count();
    console.log(`Total orders in database: ${orderCount}`);

    if (orderCount > 0) {
      // Get status distribution
      const statusCounts = await prisma.$queryRaw`
        SELECT status, COUNT(*) as count 
        FROM "Order" 
        GROUP BY status
      `;
      console.log('\nCurrent order status distribution:');
      console.log(statusCounts);
    }

    // Check if new fields exist
    const orderFields = await prisma.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Order' 
      AND column_name IN ('nextPaymentDate', 'lastContactDate')
    `;
    console.log('\nNew fields present:', orderFields);

  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();