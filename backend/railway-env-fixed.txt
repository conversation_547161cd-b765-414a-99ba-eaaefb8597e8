# Fixed Railway Environment Variables

# All your existing variables (keep as-is):
DATABASE_URL="postgresql://postgres:<EMAIL>:48459/railway"
REDIS_URL="redis://default:<EMAIL>:30709"
JWT_SECRET="PCozzDTSytXrAgmmMS/k50c9z49MCTFA"
API_PREFIX="api/v1"
NODE_ENV="production"
CORS_ORIGIN="https://zencash-sand.vercel.app"
ENCRYPTION_KEY="xxp+fcareyO5LGTdzf3efLofc2b7dLON"
SIGNING_KEY="209YsyerSuj93/KqlaOvuXpB+/OZ+rzZK72njOLmbtXA65Cd2+riEBLZaPDOMHYp"
DUPLICATE_CHECK_TIMEOUT_MS="2000"
JWT_EXPIRES_IN="7d"

# Fixed JSON variables (use single quotes outside, double quotes inside):
FUZZY_MATCH_CONFIG='{"threshold":0.7,"algorithms":["levenshtein","soundex","metaphone"],"weights":{"street":0.3,"number":0.2,"neighborhood":0.2,"city":0.2,"zipCode":0.1}}'
FEATURE_FLAGS='{"antifraud":{"enabled":true,"shadowMode":false,"enableBulkOperations":true,"maxBulkSize":100}}'