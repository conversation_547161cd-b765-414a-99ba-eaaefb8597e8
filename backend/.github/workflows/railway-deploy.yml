name: Deploy to Railway

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Generate Prisma Client
        run: npx prisma generate
      
      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: postgresql://test:test@localhost:5432/testdb
      
      - name: Validate Prisma schema
        run: npx prisma validate

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install Railway CLI
        run: npm install -g @railway/cli
      
      - name: Deploy to Railway
        run: railway up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      
      - name: Run post-deployment checks
        run: |
          echo "Waiting for deployment to stabilize..."
          sleep 30
          
          # Get the deployment URL from Railway
          DEPLOYMENT_URL=$(railway variables get RAILWAY_PUBLIC_DOMAIN)
          
          # Check health endpoint
          echo "Checking health endpoint..."
          curl -f https://${DEPLOYMENT_URL}/api/v1/health || exit 1
          
          echo "Deployment successful!"
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}