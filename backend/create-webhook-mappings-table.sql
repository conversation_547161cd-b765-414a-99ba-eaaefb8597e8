-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create webhook_mappings table
CREATE TABLE IF NOT EXISTS webhook_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "payloadKey" VARCHAR(255) NOT NULL,
    "entityColumn" VARCHAR(255),
    "entityType" VARCHAR(50) DEFAULT 'Sale',
    "dataType" VARCHAR(50),
    "sampleValue" TEXT,
    "isActive" BOOLEAN DEFAULT true,
    "tenantId" VARCHAR(100),
    description TEXT,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create unique index
CREATE UNIQUE INDEX IF NOT EXISTS idx_webhook_mappings_payload_tenant 
ON webhook_mappings ("payloadKey", "tenantId");

-- Create update trigger for updatedAt
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_webhook_mappings_updated_at 
BEFORE UPDATE ON webhook_mappings 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column();