# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files first
COPY package*.json ./

# Copy prisma directory to ensure schema is available
COPY prisma ./prisma/

# Install ALL dependencies (including dev deps needed for build)
RUN npm ci

# Generate Prisma client after dependencies are installed
RUN npx prisma generate

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine AS production

# Install postgresql-client for database operations
RUN apk add --no-cache postgresql-client

WORKDIR /app

# Copy package files
COPY package*.json ./

# IMPORTANT: Copy prisma directory BEFORE npm install for postinstall script
COPY prisma ./prisma/

# Install production dependencies
# Note: This will also run postinstall script which generates Prisma client
RUN npm ci --omit=dev

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy generated Prisma client from builder stage
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma

# Copy scripts
COPY ./scripts ./scripts
RUN chmod +x ./scripts/*.sh

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/v1/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1); })"

# Start the application
CMD ["./scripts/start-with-migration.sh"]