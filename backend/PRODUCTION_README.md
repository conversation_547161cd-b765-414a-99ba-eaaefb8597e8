# Sistema de Cobrança - Backend Production Guide

## Overview

This is the production-ready backend for Sistema de Cobrança, built with NestJS, PostgreSQL, and Prisma ORM.

## Key Features

- Multi-tenant architecture
- JWT-based authentication
- Role-based access control (RBAC)
- Winston logging infrastructure
- Request throttling
- Comprehensive error handling
- Health checks
- API documentation with Swagger

## Prerequisites

- Node.js 18+ 
- PostgreSQL 14+
- Redis (optional, for caching)

## Environment Setup

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Update the following environment variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/sistema_cobranca"

# Server
PORT=3000
NODE_ENV=production

# JWT
JWT_SECRET=your-production-secret-key-change-this
JWT_EXPIRES_IN=24h

# Security
BCRYPT_SALT_ROUNDS=12

# CORS
CORS_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# API
API_PREFIX=api/v1

# Logging
LOG_LEVEL=info

# External Services
CORREIOS_API_KEY=your-correios-api-key
```

## Database Setup

1. Create the database:
```bash
createdb sistema_cobranca
```

2. Run migrations:
```bash
npx prisma migrate deploy
```

3. Seed initial data (optional):
```bash
npx prisma db seed
```

## Running in Production

1. Build the application:
```bash
npm run build
```

2. Start the server:
```bash
npm run start:prod
```

## Default Users

After seeding, the following users are available:

| Email | Password | Role | Tenant ID |
|-------|----------|------|-----------|
| <EMAIL> | admin123 | ADMIN | cmc4a2wg50000uvkzj76t0mpt |
| <EMAIL> | supervisor123 | SUPERVISOR | cmc4a2wg50000uvkzj76t0mpt |
| <EMAIL> | vendedor123 | SELLER | cmc4a2wg50000uvkzj76t0mpt |
| <EMAIL> | cobrador123 | COLLECTOR | cmc4a2wg50000uvkzj76t0mpt |

## API Documentation

Swagger documentation is available at: `http://localhost:3000/docs`

## Key API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Login
- `GET /api/v1/auth/me` - Get current user

### Orders
- `GET /api/v1/orders` - List orders
- `POST /api/v1/orders` - Create order
- `GET /api/v1/orders/:id` - Get order
- `PATCH /api/v1/orders/:id` - Update order
- `DELETE /api/v1/orders/:id` - Delete order

### Products
- `GET /api/v1/products` - List products
- `POST /api/v1/products` - Create product
- `GET /api/v1/products/:id` - Get product
- `PATCH /api/v1/products/:id` - Update product

### Users
- `GET /api/v1/users` - List users
- `POST /api/v1/users` - Create user
- `GET /api/v1/users/:id` - Get user
- `PATCH /api/v1/users/:id` - Update user

## Authentication

All API requests (except login) require:

1. **Authorization Header**: `Bearer <jwt-token>`
2. **Tenant Header**: `x-tenant-id: <tenant-id>`

Example:
```bash
curl -X GET http://localhost:3000/api/v1/orders \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "x-tenant-id: cmc4a2wg50000uvkzj76t0mpt"
```

## Health Checks

- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/ready` - Readiness probe
- `GET /api/v1/health/live` - Liveness probe

## Logging

Logs are written to:
- Console (all environments)
- `logs/combined-YYYY-MM-DD.log` - All logs
- `logs/error-YYYY-MM-DD.log` - Error logs only

Log files are automatically rotated daily and compressed.

## Security Features

1. **Helmet**: Security headers
2. **Rate Limiting**: Request throttling
3. **Input Validation**: DTOs with class-validator
4. **SQL Injection Protection**: Prisma ORM
5. **Password Hashing**: bcrypt with configurable rounds
6. **JWT Tokens**: Stateless authentication

## Performance Optimization

1. **Database Indexes**: On frequently queried fields
2. **Connection Pooling**: PostgreSQL connection pool
3. **Request/Response Compression**: gzip enabled
4. **Efficient Queries**: Prisma query optimization

## Monitoring

### Application Metrics
- Request/response times logged
- Error rates tracked
- Database query performance logged

### Recommended Monitoring Stack
- **APM**: New Relic, DataDog, or AppDynamics
- **Logs**: ELK Stack or CloudWatch
- **Uptime**: Pingdom or UptimeRobot

## Deployment

### Docker

```dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
COPY prisma ./prisma/
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
COPY prisma ./prisma/
RUN npm ci --only=production
COPY --from=builder /app/dist ./dist
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### PM2

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'sistema-cobranca-backend',
    script: './dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

## Backup Strategy

1. **Database Backups**: Daily automated PostgreSQL backups
2. **Log Archival**: Weekly compression and archival
3. **Configuration Backup**: Version control for all configs

## Troubleshooting

### Common Issues

1. **"Invalid tenant" error**
   - Ensure x-tenant-id header is correct
   - Verify tenant exists in database

2. **"Unauthorized" error**
   - Check JWT token expiration
   - Verify Authorization header format

3. **Database connection errors**
   - Check DATABASE_URL format
   - Verify PostgreSQL is running
   - Check connection pool settings

### Debug Mode

Enable detailed logging:
```bash
LOG_LEVEL=debug npm run start:prod
```

## Maintenance

### Database Migrations

```bash
# Create new migration
npx prisma migrate dev --name migration_name

# Deploy migrations
npx prisma migrate deploy

# Reset database (CAUTION!)
npx prisma migrate reset
```

### Dependency Updates

```bash
# Check for updates
npm outdated

# Update dependencies
npm update

# Update Prisma
npm install @prisma/client@latest prisma@latest
```

## Support

For issues and questions:
- Check logs in `logs/` directory
- Review Swagger documentation
- Check health endpoints
- Enable debug logging for detailed information