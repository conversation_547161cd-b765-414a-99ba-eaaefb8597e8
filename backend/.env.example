# ========================================
# RAILWAY ENVIRONMENT VARIABLES
# ========================================

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/database_name

# Server Configuration
# NOTE: Do NOT set PORT when deploying to Railway - Railway provides it automatically
PORT=3000
NODE_ENV=production
API_PREFIX=api/v1

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Security
BCRYPT_SALT_ROUNDS=12

# CORS Configuration
# For multiple origins, separate with commas
CORS_ORIGINS=https://yourfrontend.com,https://app.yourdomain.com
# Production domain (used to allow all subdomains in production)
PRODUCTION_DOMAIN=yourdomain.com
# Note: Vercel preview URLs (*.vercel.app) and Railway apps (*.railway.app) are automatically allowed

# Swagger Configuration
SWAGGER_ENABLED=true

# ========================================
# PHASE 0.2 - CORREIOS API CONFIGURATION
# ========================================
CORREIOS_API_URL=https://api.correios.com.br/v1
CORREIOS_API_KEY=your-correios-api-key-here
CORREIOS_USE_MOCK=false

# SIGEP/SRO Configuration
CORREIOS_CONTRACT=9912405232
CORREIOS_ADMIN_CODE=csmoreira
CORREIOS_CNPJ=SEU_CNPJ_AQUI
CORREIOS_PASSWORD_TEMP=cws-ch1_IF1GKYETFQf7OwRUEo6Y3Ntb3JlaXJhOjk5MTI0MDUyMzI_MjpEaTA6kv0kjEPUEbbN1oR
CORREIOS_WSDL_LABELS=https://webservice.correios.com.br/service/rastro/Rastro.wsdl
CORREIOS_WSDL_TRACKING=https://webservice.correios.com.br/service/rastro/Rastro.wsdl

# Legacy fields (keep for backward compatibility)
CORREIOS_USER=your-username
CORREIOS_PASSWORD=your-password
CORREIOS_CLIENT_ID=your-client-id

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# Session Configuration
SESSION_SECRET=your-session-secret-change-this-in-production

# Redis Configuration (optional - for caching/queues)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Configuration (optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>
NOTIFICATION_FROM_NAME=ZenCash
NOTIFICATION_FROM_EMAIL=<EMAIL>

# Sentry Configuration (optional - for error tracking)
SENTRY_DSN=https://<EMAIL>/project-id

# AWS S3 Configuration (optional - for file uploads)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_REGION=us-east-1

# Payment Gateway Configuration (optional)
PAYMENT_GATEWAY_API_KEY=your-payment-gateway-api-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret

# Webhook Configuration
# Generate with: openssl rand -hex 32
WEBHOOK_SECRET=98405ec1ac97c083ff144b1d15f09d42a0fe2c1f8e98ebec73c26b648ce3e899
WEBHOOK_ORDER_UPDATE_URL=
# Set to true to require webhook signature validation
WEBHOOK_REQUIRE_SIGNATURE=false

# Application URLs
APP_URL=https://api.yourdomain.com
FRONTEND_URL=https://yourdomain.com

# Seeds and Test Environment
SEED_ADMIN_PASSWORD=senha123
SEED_SUPERVISOR_EMAIL=<EMAIL>
SEED_COBRADOR_EMAIL=<EMAIL>
SEED_VENDEDOR_EMAIL=<EMAIL>
