import { Controller, Post, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';
import { CorreiosTasks } from './correios.tasks';
import { CorreiosService } from './correios.service';

@ApiTags('Correios')
@Controller('correios')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class CorreiosController {
  constructor(
    private readonly correiosTasks: CorreiosTasks,
    private readonly correiosService: CorreiosService,
  ) {}

  @Post('poll-all')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Manually trigger tracking update for all orders' })
  @ApiResponse({ status: 200, description: 'Polling started successfully' })
  async triggerPollAll() {
    // Run the polling task manually
    await this.correiosTasks.pollAll();
    
    return {
      success: true,
      message: 'Tracking update started for all orders',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('debug/:code')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Debug tracking information by code' })
  @ApiResponse({ status: 200, description: 'Debug tracking information' })
  async debugTrackPackage(@Param('code') code: string) {
    try {
      // Get raw events
      const rawEvents = await this.correiosService.buscaEventosPorEtiquetaRaw(code);
      
      // Get processed events
      const processedEvents = await this.correiosService.buscaEventosPorEtiqueta(code);
      
      // Sort events
      const sortedEvents = this.correiosService.sortEventsByTimestamp(rawEvents);
      
      // Get latest event
      const latestEvent = sortedEvents[0];
      
      // Map status
      const mappedStatus = latestEvent ? this.correiosService.mapCorreiosStatus(
        latestEvent.descricaoFrontEnd || latestEvent.descricao || ''
      ) : 'No events';

      return {
        trackingCode: code,
        debug: {
          rawEventsCount: rawEvents.length,
          processedEventsCount: processedEvents.length,
          rawEvents: rawEvents.slice(0, 5), // First 5 raw events
          processedEvents: processedEvents.slice(0, 5), // First 5 processed events
          sortedEvents: sortedEvents.slice(0, 5), // First 5 sorted events
          latestEvent: latestEvent || null,
          latestEventDescription: latestEvent?.descricaoFrontEnd || latestEvent?.descricao || 'N/A',
          mappedStatus: mappedStatus,
          statusMapping: {
            input: latestEvent?.descricaoFrontEnd || latestEvent?.descricao || '',
            output: mappedStatus,
            trimmedInput: (latestEvent?.descricaoFrontEnd || latestEvent?.descricao || '').trim(),
          },
          eventOrder: {
            firstRaw: rawEvents[0],
            lastRaw: rawEvents[rawEvents.length - 1],
            firstSorted: sortedEvents[0],
            lastSorted: sortedEvents[sortedEvents.length - 1],
          }
        }
      };
    } catch (error) {
      return {
        trackingCode: code,
        error: error.message,
        stack: error.stack,
      };
    }
  }
}