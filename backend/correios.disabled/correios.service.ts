import {
  Injectable,
  HttpException,
  HttpStatus,
  Logger,
  OnModuleInit,
} from '@nestjs/common';
import * as soap from 'soap';
import { PrismaService } from '../prisma/prisma.service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import * as CircuitBreaker from 'opossum';
import { mapCorreiosToInternalStatus, isFinalStatus } from './status-map';

export interface SolicitaEtiquetasArgs {
  tipoDestinatario: string;
  identificador: string;
  idServico: string;
  qtdEtiquetas: number;
  usuario: string;
  senha: string;
}

export interface CadastroObjetoDto {
  etiqueta: string;
  cliente: {
    nome: string;
    documento: string;
    telefone: string;
    email?: string;
  };
  endereco: {
    cep: string;
    logradouro: string;
    numero: string;
    complemento?: string;
    bairro: string;
    cidade: string;
    uf: string;
  };
  servico: {
    codigo: string;
    valor: number;
    peso: number;
    dimensoes: {
      altura: number;
      largura: number;
      comprimento: number;
    };
  };
}

export interface EventoRastreamento {
  tipo: string;
  status: number;
  data: string;
  hora: string;
  descricao: string;
  descricaoFrontEnd?: string;
  internalStatus?: string;
  local?: string;
  codigo?: string;
  cidade?: string;
  uf?: string;
  dtHrCriado?: string;
}

@Injectable()
export class CorreiosService implements OnModuleInit {
  private readonly logger = new Logger(CorreiosService.name);
  private soapClient: soap.Client;
  private breaker: any;
  
  // Rate limiting for API calls
  private lastApiCallTime = 0;
  private apiCallInterval = 2000; // Start with 2 seconds between calls
  private rateLimitQueue: Array<{ resolve: Function; reject: Function; fn: Function }> = [];
  private isProcessingQueue = false;
  private consecutiveErrors = 0;
  private lastErrorTime = 0;
  
  // Simple in-memory cache for tracking results
  private trackingCache = new Map<string, { data: EventoRastreamento[]; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes cache

  constructor(
    private prisma: PrismaService,
    private httpService: HttpService,
    private configService: ConfigService,
  ) {
    // Configure circuit breaker
    this.breaker = new CircuitBreaker(this.soapCall.bind(this), {
      timeout: 30000, // 30 seconds
      errorThresholdPercentage: 50,
      resetTimeout: 30000,
    });

    this.breaker.on('open', () => {
      this.logger.warn('Circuit breaker is open - Correios service unavailable');
    });

    this.breaker.on('halfOpen', () => {
      this.logger.log('Circuit breaker is half-open - Testing Correios service');
    });

    this.breaker.on('close', () => {
      this.logger.log('Circuit breaker is closed - Correios service recovered');
    });
  }

  async onModuleInit() {
    try {
      await this.initializeSoapClient();
      // Start the rate limit queue processor
      this.startQueueProcessor();
    } catch (error) {
      this.logger.error('Failed to initialize SOAP client:', error);
    }
  }

  /**
   * Process queued API calls with rate limiting
   */
  private async startQueueProcessor() {
    setInterval(async () => {
      if (this.rateLimitQueue.length === 0 || this.isProcessingQueue) {
        return;
      }

      this.isProcessingQueue = true;
      const item = this.rateLimitQueue.shift();
      
      if (item) {
        try {
          const result = await item.fn();
          item.resolve(result);
          this.consecutiveErrors = 0; // Reset error count on success
        } catch (error) {
          item.reject(error);
          this.consecutiveErrors++;
          this.lastErrorTime = Date.now();
          
          // Increase interval on errors
          if (this.consecutiveErrors > 3) {
            this.apiCallInterval = Math.min(this.apiCallInterval * 2, 30000);
            this.logger.warn(`Increasing rate limit interval to ${this.apiCallInterval}ms due to errors`);
          }
        }
      }
      
      this.isProcessingQueue = false;
    }, this.apiCallInterval);
  }

  /**
   * Add a function to the rate limit queue
   */
  private queueApiCall<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.rateLimitQueue.push({ resolve, reject, fn });
    });
  }

  private async initializeSoapClient() {
    const wsdlUrl = this.configService.get<string>('CORREIOS_WSDL_LABELS');
    const username = this.configService.get<string>('CORREIOS_ADMIN_CODE');
    const password = this.configService.get<string>('CORREIOS_PASSWORD_TEMP');

    if (!wsdlUrl || !username || !password) {
      throw new Error('Missing Correios configuration');
    }

    try {
      // Create SOAP client with proper headers
      this.soapClient = await soap.createClientAsync(wsdlUrl, {
        wsdl_headers: {
          'User-Agent': 'Mozilla/5.0',
          'Accept': 'text/xml',
          'Accept-Encoding': 'gzip, deflate',
        },
        disableCache: true,
        escapeXML: false,
        // Some Correios endpoints need specific options
        endpoint: wsdlUrl.replace('?wsdl', ''),
      });

      // The authentication might be needed in the SOAP headers instead
      // Let's log available methods first
      if (this.soapClient) {
        const describe = this.soapClient.describe();
        this.logger.log('Available SOAP services:', JSON.stringify(Object.keys(describe)));
      }

      this.logger.log('Correios SOAP client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to create SOAP client:', error);
      throw error;
    }
  }

  /**
   * Solicita etiquetas aos Correios
   */
  async solicitaEtiquetas(
    quantidade: number,
    tipoServico: string,
    retryCount = 0,
  ): Promise<string[]> {
    const maxRetries = 3;
    const backoffDelay = (attempt: number) => Math.pow(2, attempt) * 1000;

    try {
      if (!this.soapClient) {
        throw new HttpException(
          'Correios service not initialized',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      const args: SolicitaEtiquetasArgs = {
        tipoDestinatario: 'C',
        identificador: this.configService.get<string>('CORREIOS_CONTRACT') || '',
        idServico: tipoServico,
        qtdEtiquetas: quantidade,
        usuario: this.configService.get<string>('CORREIOS_ADMIN_CODE') || '',
        senha: this.configService.get<string>('CORREIOS_PASSWORD_TEMP') || '',
      };

      this.logger.debug(
        `Solicitando ${quantidade} etiquetas do tipo ${tipoServico}`,
      );

      const result = await this.breaker.fire('solicitaEtiquetas', args);
      
      if (!result || !result[0]) {
        throw new Error('Invalid response from Correios');
      }

      const etiquetas = this.parseEtiquetas(result[0]);
      this.logger.log(`${etiquetas.length} etiquetas solicitadas com sucesso`);
      
      return etiquetas;
    } catch (error) {
      this.logger.error(`Erro ao solicitar etiquetas: ${error.message}`);

      // Retry logic
      if (retryCount < maxRetries) {
        const delay = backoffDelay(retryCount);
        this.logger.warn(
          `Tentativa ${retryCount + 1}/${maxRetries} - Aguardando ${delay}ms`,
        );
        await this.sleep(delay);
        return this.solicitaEtiquetas(quantidade, tipoServico, retryCount + 1);
      }

      throw new HttpException(
        'Falha ao solicitar etiquetas após múltiplas tentativas',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * Cadastra objeto para postagem
   */
  async cadastraObjeto(
    payload: CadastroObjetoDto,
    retryCount = 0,
  ): Promise<void> {
    const maxRetries = 3;
    const backoffDelay = (attempt: number) => Math.pow(2, attempt) * 1000;

    try {
      if (!this.soapClient) {
        throw new HttpException(
          'Correios service not initialized',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      const args = {
        xml: this.buildXmlPlp(payload),
        idPlpCliente: Date.now(),
        cartaoPostagem: this.configService.get<string>('CORREIOS_CONTRACT'),
        faixaEtiquetas: payload.etiqueta,
        usuario: this.configService.get<string>('CORREIOS_ADMIN_CODE'),
        senha: this.configService.get<string>('CORREIOS_PASSWORD_TEMP'),
      };

      this.logger.debug(`Cadastrando objeto: ${payload.etiqueta}`);

      const result = await this.breaker.fire('fechaPlpVariosServicos', args);
      
      if (!result || !result[0]) {
        throw new Error('Invalid response from Correios');
      }

      this.logger.log(`Objeto ${payload.etiqueta} cadastrado com sucesso`);
    } catch (error) {
      this.logger.error(`Erro ao cadastrar objeto: ${error.message}`);

      // Retry logic
      if (retryCount < maxRetries) {
        const delay = backoffDelay(retryCount);
        this.logger.warn(
          `Tentativa ${retryCount + 1}/${maxRetries} - Aguardando ${delay}ms`,
        );
        await this.sleep(delay);
        return this.cadastraObjeto(payload, retryCount + 1);
      }

      throw new HttpException(
        'Falha ao cadastrar objeto após múltiplas tentativas',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  /**
   * Busca eventos de rastreamento por etiqueta
   */
  async buscaEventosPorEtiqueta(
    etiqueta: string,
    retryCount = 0,
  ): Promise<EventoRastreamento[]> {
    // Clean the tracking code first
    const cleanCode = this.cleanTrackingCode(etiqueta);
    if (!cleanCode) {
      this.logger.error(`Invalid tracking code: ${etiqueta}`);
      throw new HttpException(
        'Código de rastreamento inválido',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check cache first
    const cached = this.trackingCache.get(cleanCode);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      this.logger.debug(`Returning cached tracking for ${this.maskTrackingCode(cleanCode)}`);
      return cached.data;
    }

    // Check if we're rate limited
    const timeSinceLastError = Date.now() - this.lastErrorTime;
    if (this.consecutiveErrors > 5 && timeSinceLastError < 60000) {
      throw new HttpException(
        'Serviço temporariamente indisponível devido a muitos erros. Tente novamente em alguns minutos.',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    try {
      // Queue the API call for rate limiting
      const result = await this.queueApiCall(async () => {
        // First check if SOAP client is available
        if (this.soapClient) {
          try {
            // Try SOAP first as it's more reliable
            const soapResult = await this.buscaViaSOAP(cleanCode);
            if (soapResult) {
              return soapResult;
            }
          } catch (soapError) {
            this.logger.warn(`SOAP tracking failed for ${this.maskTrackingCode(cleanCode)}, trying public API`);
          }
        }
        
        // Try Wonca API as second option
        const woncaResult = await this.buscaViaWonca(cleanCode);
        if (woncaResult) {
          return woncaResult;
        }
        
        // Fallback to public API
        const publicApiResult = await this.buscaViaApiPublica(cleanCode);
        if (publicApiResult) {
          return publicApiResult;
        }

        // If all failed, return empty array instead of throwing
        this.logger.warn(`No tracking data found for ${this.maskTrackingCode(cleanCode)}`);
        return [];
      });
      
      // Sort events by timestamp to ensure consistent ordering
      if (result && result.length > 0) {
        const sortedResult = this.sortEventsByTimestamp(result);
        this.trackingCache.set(cleanCode, { data: sortedResult, timestamp: Date.now() });
        return sortedResult;
      }
      
      return result;
    } catch (error) {
      // Handle specific error cases
      if (error.status === 429) {
        this.apiCallInterval = Math.min(this.apiCallInterval * 2, 30000);
        throw new HttpException(
          'Limite de requisições excedido. Tente novamente em alguns segundos.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
      
      if (error.status === 400) {
        throw new HttpException(
          'Código de rastreamento inválido ou formato incorreto.',
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.error(`Error tracking ${this.maskTrackingCode(cleanCode)}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Busca eventos via SOAP
   */
  private async buscaViaSOAP(
    etiqueta: string,
  ): Promise<EventoRastreamento[] | null> {
    try {
      const args = {
        usuario: this.configService.get<string>('CORREIOS_ADMIN_CODE'),
        senha: this.configService.get<string>('CORREIOS_PASSWORD_TEMP'),
        tipo: 'L',
        resultado: 'T',
        lingua: '101',
        objetos: etiqueta,
      };

      this.logger.debug(`SOAP tracking for: ${this.maskTrackingCode(etiqueta)}`);

      const result = await this.breaker.fire('buscaEventos', args);
      
      if (!result || !result[0]) {
        return null;
      }

      const eventos = this.parseEventos(result[0]);
      this.logger.log(
        `${eventos.length} eventos encontrados via SOAP para ${this.maskTrackingCode(etiqueta)}`,
      );
      
      return eventos;
    } catch (error) {
      this.logger.error(`SOAP tracking error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Busca eventos via API pública como fallback
   */
  private async buscaViaApiPublica(
    etiqueta: string,
  ): Promise<EventoRastreamento[] | null> {
    try {
      // Don't clean again, it's already cleaned in the main method
      const cleanCode = etiqueta;

      // Try different API endpoints
      const endpoints = [
        `https://proxyapp.correios.com.br/v1/sro-rastro/${cleanCode}`,
        `https://api.correios.com.br/srorastro/v1/objetos/${cleanCode}`,
      ];

      for (const url of endpoints) {
        try {
          this.logger.debug(`Trying Correios API: ${url}`);
          
          const response = await this.httpService.axiosRef.get(url, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
              'Accept-Encoding': 'gzip, deflate, br',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache',
              'Origin': 'https://www.correios.com.br',
              'Referer': 'https://www.correios.com.br/',
              'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
              'Sec-Ch-Ua-Mobile': '?0',
              'Sec-Ch-Ua-Platform': '"Windows"',
              'Sec-Fetch-Dest': 'empty',
              'Sec-Fetch-Mode': 'cors',
              'Sec-Fetch-Site': 'same-site',
            },
            timeout: 15000,
            validateStatus: (status) => status < 500, // Don't throw on 4xx errors
          });

          // Log full response for debugging
          this.logger.debug(`Response status: ${response.status}`);
          this.logger.debug(`Response headers:`, response.headers);
          
          // Check for error responses
          if (response.status === 400) {
            this.logger.error(`Bad request (400) for tracking code: ${cleanCode}`);
            this.logger.error(`Response data:`, JSON.stringify(response.data));
            this.logger.error(`Request URL: ${url}`);
            continue; // Try next endpoint
          }
          
          if (response.status === 404) {
            this.logger.debug(`Tracking not found (404) for: ${cleanCode}`);
            continue; // Try next endpoint
          }

          if (response.status === 200) {
            // Reset interval on success
            this.apiCallInterval = 1000;

            // Handle different response formats
            let eventos: any[] | null = null;
            
            // Format 1: { objetos: [{ eventos: [...] }] }
            if (response.data.objetos?.[0]?.eventos) {
              eventos = response.data.objetos[0].eventos;
            }
            // Format 2: { eventos: [...] }
            else if (response.data.eventos) {
              eventos = response.data.eventos;
            }
            // Format 3: Direct array
            else if (Array.isArray(response.data)) {
              eventos = response.data;
            }

            if (!eventos || eventos.length === 0) {
              this.logger.debug(`No tracking events found for: ${cleanCode}`);
              continue;
            }

            this.logger.log(`Found ${eventos.length} events via public API`);

            return eventos.map((evento: any) => ({
              tipo: evento.codigo || evento.tipo || 'UNKNOWN',
              status: this.mapStatusCode(evento.codigo || evento.tipo),
              data: this.parseDate(evento.dtHrCriado || evento.data),
              hora: this.parseTime(evento.dtHrCriado || evento.hora),
              descricao: evento.descricao || evento.detalhe || '',
              // Check for descricaoWeb or other front-end specific field
              descricaoFrontEnd: evento.descricaoWeb || evento.descricaoFrontEnd || evento.descricao || evento.detalhe || '',
              // Map internal status based on the best available description
              internalStatus: this.mapCorreiosStatus(
                evento.descricaoWeb || evento.descricaoFrontEnd || evento.descricao || evento.detalhe || ''
              ),
              local: evento.unidade?.nome || evento.unidadeDestino?.nome || evento.local || '',
              cidade: evento.unidade?.endereco?.cidade || evento.unidadeDestino?.endereco?.cidade || evento.cidade || '',
              uf: evento.unidade?.endereco?.uf || evento.unidadeDestino?.endereco?.uf || evento.uf || '',
              dtHrCriado: evento.dtHrCriado || `${evento.data} ${evento.hora}` || new Date().toISOString(),
            }));
          }
        } catch (endpointError) {
          this.logger.debug(`Endpoint ${url} failed:`, endpointError.message);
          continue;
        }
      }

      // All endpoints failed
      return null;
    } catch (error) {
      if (error.response?.status === 429) {
        // Increase wait time exponentially on rate limit
        this.apiCallInterval = Math.min(this.apiCallInterval * 2, 30000); // Max 30 seconds
        this.logger.warn(`Rate limit hit (429). Increasing interval to ${this.apiCallInterval}ms`);
        throw new HttpException(
          'Limite de requisições excedido. Tente novamente em alguns segundos.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
      this.logger.error('Public API failed:', error.message);
      return null;
    }
  }

  /**
   * Generic SOAP call wrapper for circuit breaker
   */
  private async soapCall(method: string, args: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.soapClient[method](args, (err: any, result: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * Parse etiquetas from SOAP response
   */
  private parseEtiquetas(response: any): string[] {
    // Implementation depends on actual SOAP response structure
    if (typeof response === 'string') {
      return response.split(',').map(e => e.trim());
    }
    return [];
  }

  /**
   * Parse eventos from SOAP response
   */
  private parseEventos(response: any): EventoRastreamento[] {
    // Implementation depends on actual SOAP response structure
    const eventos: EventoRastreamento[] = [];
    
    if (response.objeto && response.objeto.evento) {
      const eventList = Array.isArray(response.objeto.evento)
        ? response.objeto.evento
        : [response.objeto.evento];

      for (const evento of eventList) {
        eventos.push({
          tipo: evento.tipo,
          status: parseInt(evento.status),
          data: evento.data,
          hora: evento.hora,
          descricao: evento.descricao,
          descricaoFrontEnd: evento.descricao,
          internalStatus: this.mapCorreiosStatus(evento.descricao),
          local: evento.local,
          codigo: evento.codigo,
          cidade: evento.cidade,
          uf: evento.uf,
          dtHrCriado: `${evento.data} ${evento.hora}`,
        });
      }
    }

    return eventos;
  }

  /**
   * Build XML for PLP
   */
  private buildXmlPlp(payload: CadastroObjetoDto): string {
    // Simplified XML structure - adjust according to Correios specifications
    return `<?xml version="1.0" encoding="UTF-8"?>
    <correioslog>
      <tipo_arquivo>Postagem</tipo_arquivo>
      <versao_arquivo>2.3</versao_arquivo>
      <plp>
        <id_plp>${Date.now()}</id_plp>
        <valor_global>0</valor_global>
        <mcu_unidade_postagem></mcu_unidade_postagem>
        <nome_unidade_postagem></nome_unidade_postagem>
        <cartao_postagem>${this.configService.get('CORREIOS_CONTRACT')}</cartao_postagem>
      </plp>
      <remetente>
        <numero_contrato>${this.configService.get('CORREIOS_CONTRACT')}</numero_contrato>
        <numero_diretoria>10</numero_diretoria>
        <codigo_administrativo>${this.configService.get('CORREIOS_ADMIN_CODE')}</codigo_administrativo>
      </remetente>
      <forma_pagamento/>
      <objeto_postal>
        <numero_etiqueta>${payload.etiqueta}</numero_etiqueta>
        <codigo_servico_postagem>${payload.servico.codigo}</codigo_servico_postagem>
        <peso>${payload.servico.peso}</peso>
        <destinatario>
          <nome_destinatario>${payload.cliente.nome}</nome_destinatario>
          <telefone_destinatario>${payload.cliente.telefone}</telefone_destinatario>
          <email_destinatario>${payload.cliente.email || ''}</email_destinatario>
          <logradouro_destinatario>${payload.endereco.logradouro}</logradouro_destinatario>
          <numero_end_destinatario>${payload.endereco.numero}</numero_end_destinatario>
          <complemento_destinatario>${payload.endereco.complemento || ''}</complemento_destinatario>
          <bairro_destinatario>${payload.endereco.bairro}</bairro_destinatario>
          <cidade_destinatario>${payload.endereco.cidade}</cidade_destinatario>
          <uf_destinatario>${payload.endereco.uf}</uf_destinatario>
          <cep_destinatario>${payload.endereco.cep}</cep_destinatario>
        </destinatario>
        <dimensao_objeto>
          <tipo_objeto>002</tipo_objeto>
          <dimensao_altura>${payload.servico.dimensoes.altura}</dimensao_altura>
          <dimensao_largura>${payload.servico.dimensoes.largura}</dimensao_largura>
          <dimensao_comprimento>${payload.servico.dimensoes.comprimento}</dimensao_comprimento>
        </dimensao_objeto>
        <valor_declarado>${payload.servico.valor}</valor_declarado>
      </objeto_postal>
    </correioslog>`;
  }

  /**
   * Map status code to internal status
   */
  private mapStatusCode(code: string): number {
    const statusMap: Record<string, number> = {
      'BDE': 1, // Entregue
      'BDI': 1, // Entregue
      'BDR': 1, // Entregue
      'OEC': 2, // Saiu para entrega
      'LDI': 3, // Em trânsito
      'RO': 4,  // Postado
      'PO': 4,  // Postado
      'DO': 3,  // Em trânsito
      'FC': 9,  // Falha na entrega
    };
    return statusMap[code] || 0;
  }

  /**
   * Mask tracking code for logs
   */
  private maskTrackingCode(code: string): string {
    if (!code || code.length < 8) return code;
    return `${code.substring(0, 2)}****${code.substring(code.length - 4)}`;
  }

  /**
   * Clean and validate tracking code
   */
  private cleanTrackingCode(code: string): string | null {
    if (!code) return null;
    
    // Remove spaces and convert to uppercase
    let cleaned = code.trim().toUpperCase().replace(/\s/g, '');
    
    // Remove any non-alphanumeric characters except for tracking code format
    cleaned = cleaned.replace(/[^A-Z0-9]/g, '');
    
    // Validate Brazilian tracking code format
    // Format: AA123456789BR or similar
    if (cleaned.length < 9 || cleaned.length > 13) {
      this.logger.error(`Invalid tracking code length: ${cleaned.length} for ${cleaned}`);
      return null;
    }
    
    // Log the cleaned code for debugging
    this.logger.debug(`Cleaned tracking code: ${code} -> ${cleaned}`);
    
    return cleaned;
  }

  /**
   * Parse date from various formats
   */
  private parseDate(dateInput: any): string {
    if (!dateInput) return '';
    
    try {
      const date = new Date(dateInput);
      if (isNaN(date.getTime())) return dateInput.toString();
      return date.toLocaleDateString('pt-BR');
    } catch {
      return dateInput.toString();
    }
  }

  /**
   * Parse time from various formats
   */
  private parseTime(timeInput: any): string {
    if (!timeInput) return '';
    
    try {
      const date = new Date(timeInput);
      if (isNaN(date.getTime())) return timeInput.toString();
      return date.toLocaleTimeString('pt-BR');
    } catch {
      return timeInput.toString();
    }
  }

  /**
   * Sleep helper for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get critical status codes
   */
  getCriticalStatusCodes(): string[] {
    return ['FC', 'BLQ', 'LDE', 'PAR', 'CD', 'AC', 'CA'];
  }

  /**
   * Check if status is critical
   */
  isCriticalStatus(code: string): boolean {
    return this.getCriticalStatusCodes().includes(code);
  }

  /**
   * Check if status indicates delivery
   */
  isDeliveredStatus(code: string): boolean {
    return ['BDE', 'BDI', 'BDR'].includes(code);
  }

  /**
   * Get raw events without any processing
   */
  async buscaEventosPorEtiquetaRaw(etiqueta: string): Promise<any[]> {
    const cleanCode = this.cleanTrackingCode(etiqueta);
    if (!cleanCode) {
      throw new HttpException('Código de rastreamento inválido', HttpStatus.BAD_REQUEST);
    }

    // Try all sources and return raw events
    if (this.soapClient) {
      try {
        const soapResult = await this.buscaViaSOAP(cleanCode);
        if (soapResult) return soapResult;
      } catch (error) {
        this.logger.debug(`SOAP failed: ${error.message}`);
      }
    }

    const woncaResult = await this.buscaViaWonca(cleanCode);
    if (woncaResult) return woncaResult;

    const publicApiResult = await this.buscaViaApiPublica(cleanCode);
    if (publicApiResult) return publicApiResult;

    return [];
  }

  /**
   * Sort events by timestamp (newest first)
   */
  sortEventsByTimestamp(events: EventoRastreamento[]): EventoRastreamento[] {
    return events.sort((a, b) => {
      const dateA = new Date(a.dtHrCriado || `${a.data} ${a.hora}`).getTime();
      const dateB = new Date(b.dtHrCriado || `${b.data} ${b.hora}`).getTime();
      return dateB - dateA; // Newest first
    });
  }

  /**
   * Map Correios status to internal status
   * Now uses the centralized status-map module
   */
  mapCorreiosStatus(description: string): string {
    return mapCorreiosToInternalStatus(description);
  }
  
  /**
   * Check if status is final
   */
  isStatusFinal(status: string): boolean {
    return isFinalStatus(status);
  }

  /**
   * Try to get tracking from Wonca API
   */
  private async buscaViaWonca(etiqueta: string): Promise<EventoRastreamento[] | null> {
    try {
      const response = await this.httpService.axiosRef.post(
        'https://api-labs.wonca.com.br/wonca.labs.v1.LabsService/Track',
        { code: etiqueta },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Apikey oxlkx7NJiZ-gptGUkE1gAkxPxNgLQdRKbMYcW1t6sE8',
          },
          timeout: 10000,
        }
      );

      if (response.data?.json) {
        const payload = JSON.parse(response.data.json);
        
        // Log the structure for debugging
        this.logger.debug(`Wonca API response structure for ${this.maskTrackingCode(etiqueta)}:`, {
          hasObjetos: !!payload.objetos,
          objetosLength: payload.objetos?.length,
          firstObjeto: payload.objetos?.[0] ? Object.keys(payload.objetos[0]) : null,
          hasEventos: !!payload.objetos?.[0]?.eventos,
          eventosLength: payload.objetos?.[0]?.eventos?.length,
          firstEvento: payload.objetos?.[0]?.eventos?.[0] ? Object.keys(payload.objetos[0].eventos[0]) : null,
          sampleEvento: payload.objetos?.[0]?.eventos?.[0],
        });
        
        // Check if we have tracking data
        if (payload.objetos?.[0]?.eventos) {
          const eventos = payload.objetos[0].eventos;
          
          return eventos.map((evento: any) => ({
            tipo: evento.codigo || evento.tipo || 'UNKNOWN',
            status: this.mapStatusCode(evento.codigo || evento.tipo),
            data: this.parseDate(evento.data || evento.dtHrCriado),
            hora: this.parseTime(evento.hora || evento.dtHrCriado),
            descricao: evento.descricao || evento.detalhe || '',
            // Check for descricaoWeb or other front-end specific field
            descricaoFrontEnd: evento.descricaoWeb || evento.descricaoFrontEnd || evento.descricao || evento.detalhe || '',
            // Map internal status based on the best available description
            internalStatus: this.mapCorreiosStatus(
              evento.descricaoWeb || evento.descricaoFrontEnd || evento.descricao || evento.detalhe || ''
            ),
            local: evento.unidade?.nome || evento.unidadeDestino?.nome || evento.local || '',
            cidade: evento.unidade?.endereco?.cidade || evento.unidadeDestino?.endereco?.cidade || evento.cidade || '',
            uf: evento.unidade?.endereco?.uf || evento.unidadeDestino?.endereco?.uf || evento.uf || '',
            dtHrCriado: evento.dtHrCriado || `${evento.data} ${evento.hora}` || new Date().toISOString(),
          }));
        }
      }

      return null;
    } catch (error) {
      this.logger.debug(`Wonca API failed for ${this.maskTrackingCode(etiqueta)}: ${error.message}`);
      return null;
    }
  }

  /**
   * Test SOAP connection
   */
  async testConnection(): Promise<boolean> {
    try {
      // Test with tracking WSDL since that's what we have
      const trackingWsdl = this.configService.get<string>('CORREIOS_WSDL_TRACKING');
      const username = this.configService.get<string>('CORREIOS_ADMIN_CODE');
      const password = this.configService.get<string>('CORREIOS_PASSWORD_TEMP');

      if (!trackingWsdl || !username || !password) {
        throw new Error('Missing Correios configuration');
      }

      // Try a simple tracking query first
      try {
        // Use a known test tracking code or try the public API
        const testCode = 'AA123456789BR';
        const publicApiResult = await this.buscaViaApiPublica(testCode);
        
        // If public API works, connection is good
        if (publicApiResult !== null || this.soapClient) {
          this.logger.log('Successfully connected to Correios services');
          return true;
        }
      } catch (testError) {
        this.logger.debug('API test failed, trying SOAP:', testError.message);
      }

      // Create a test client for tracking
      const testClient = await soap.createClientAsync(trackingWsdl, {
        wsdl_headers: {
          'User-Agent': 'Mozilla/5.0',
          'Accept': 'text/xml',
          'Accept-Encoding': 'gzip, deflate',
        },
        disableCache: true,
        escapeXML: false,
      });

      // The tracking service doesn't use basic auth in the same way
      // Let's just check if we can create the client successfully
      if (testClient) {
        this.logger.log('Successfully connected to Correios tracking WSDL');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error('Connection test failed:', error);
      // Check if it's a network/WSDL error
      if (error.message?.includes('ENOTFOUND') || error.message?.includes('ECONNREFUSED')) {
        throw new Error('Não foi possível conectar ao servidor dos Correios. Verifique a URL do WSDL.');
      } else if (error.message?.includes('Non-whitespace before first tag')) {
        throw new Error('Resposta inválida do servidor. Verifique as credenciais.');
      }
      throw error;
    }
  }
}