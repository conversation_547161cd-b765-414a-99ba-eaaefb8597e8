import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus,
  Logger,
  Req,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';
import { Throttle } from '@nestjs/throttler';
import { CorreiosService } from './correios.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import * as XLSX from 'xlsx';
import * as csv from 'csv-parse';
import { Express } from 'express';
import { SoapExceptionFilter } from '../common/filters/soap-exception.filter';
import { UseFilters } from '@nestjs/common';

interface FileUpload {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
}

interface ImportedShipment {
  orderNumber: string;
  customerName: string;
  customerDocument: string;
  customerPhone: string;
  customerEmail?: string;
  cep: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  serviceCode: string;
  weight: number;
  height: number;
  width: number;
  length: number;
  declaredValue?: number;
}

@ApiTags('Shipments')
@Controller('shipments')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseFilters(SoapExceptionFilter)
@ApiBearerAuth()
export class ShipmentsController {
  private readonly logger = new Logger(ShipmentsController.name);

  constructor(
    private readonly correiosService: CorreiosService,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  @Post('import')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Import shipments from XLSX/CSV file' })
  @ApiResponse({ status: 200, description: 'Shipments imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file format' })
  @Throttle({ default: { ttl: 60000, limit: 5 } })
  async importShipments(
    @UploadedFile() file: FileUpload,
    @Req() req: any,
  ) {
    if (!file) {
      throw new HttpException('No file provided', HttpStatus.BAD_REQUEST);
    }

    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    
    if (!['xlsx', 'xls', 'csv'].includes(fileExtension || '')) {
      throw new HttpException(
        'Invalid file format. Only XLSX and CSV files are allowed',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      let shipments: ImportedShipment[] = [];

      if (fileExtension === 'csv') {
        shipments = await this.parseCsvFile(file.buffer);
      } else {
        shipments = await this.parseExcelFile(file.buffer);
      }

      this.logger.log(`Processing ${shipments.length} shipments from ${file.originalname}`);

      const results = {
        successful: 0,
        failed: 0,
        errors: [] as any[],
      };

      // Process each shipment
      for (const shipment of shipments) {
        try {
          await this.processShipment(shipment, req.user.tenantId);
          results.successful++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            orderNumber: shipment.orderNumber,
            error: error.message,
          });
          this.logger.error(`Failed to process shipment ${shipment.orderNumber}:`, error);
        }
      }

      return {
        message: 'Import completed',
        totalProcessed: shipments.length,
        successful: results.successful,
        failed: results.failed,
        errors: results.errors,
      };
    } catch (error) {
      this.logger.error('Error importing shipments:', error);
      throw new HttpException(
        'Error processing file: ' + error.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('generate/:orderId')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Generate shipping label for an order' })
  @ApiResponse({ status: 200, description: 'Label generated successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  @Throttle({ default: { ttl: 60000, limit: 10 } })
  async generateLabel(
    @Param('orderId') orderId: string,
    @Body() body: {
      serviceCode: string;
      weight: number;
      dimensions: {
        height: number;
        width: number;
        length: number;
      };
      declaredValue?: number;
    },
    @Req() req: any,
  ) {
    // Validate order exists and belongs to tenant
    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        tenantId: req.user.tenantId,
      },
      include: {
        customer: {
          include: {
            addresses: {
              where: { main: true },
            },
          },
        },
        shipmentLabel: true,
      },
    });

    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }

    if (order.shipmentLabel) {
      throw new HttpException(
        'Shipping label already exists for this order',
        HttpStatus.CONFLICT,
      );
    }

    if (!order.customer || order.customer.addresses.length === 0) {
      throw new HttpException(
        'Customer address not found',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      // Request label from Correios
      const labels = await this.correiosService.solicitaEtiquetas(1, body.serviceCode);
      
      if (!labels || labels.length === 0) {
        throw new Error('No labels returned from Correios');
      }

      const trackingCode = labels[0];
      const address = order.customer.addresses[0];

      // Register object with Correios
      await this.correiosService.cadastraObjeto({
        etiqueta: trackingCode,
        cliente: {
          nome: order.customer.name,
          documento: order.customer.cpf,
          telefone: order.customer.phone,
          email: order.customer.email || undefined,
        },
        endereco: {
          cep: address.cep,
          logradouro: address.street,
          numero: address.number,
          complemento: address.complement || undefined,
          bairro: address.neighborhood,
          cidade: address.city,
          uf: address.state,
        },
        servico: {
          codigo: body.serviceCode,
          valor: body.declaredValue || Number(order.total),
          peso: body.weight,
          dimensoes: {
            altura: body.dimensions.height,
            largura: body.dimensions.width,
            comprimento: body.dimensions.length,
          },
        },
      });

      // Save label to database
      const shipmentLabel = await this.prisma.shipmentLabel.create({
        data: {
          trackingCode,
          labelNumber: trackingCode,
          serviceType: body.serviceCode,
          weight: body.weight,
          dimensions: body.dimensions,
          declaredValue: body.declaredValue || Number(order.total),
          orderId: order.id,
        },
      });

      // Create tracking record
      await this.prisma.tracking.create({
        data: {
          code: trackingCode,
          status: 'Postado',
          lastUpdate: new Date(),
          orderId: order.id,
        },
      });

      // Update order status
      await this.prisma.order.update({
        where: { id: order.id },
        data: { status: 'Transito' },
      });

      this.logger.log(`Label generated successfully for order ${order.orderNumber}`);

      return {
        success: true,
        trackingCode,
        label: shipmentLabel,
      };
    } catch (error) {
      this.logger.error(`Error generating label for order ${orderId}:`, error);
      throw new HttpException(
        'Error generating shipping label: ' + error.message,
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  @Get(':saleId/debug')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Debug tracking information for an order' })
  @ApiResponse({ status: 200, description: 'Debug information retrieved successfully' })
  @Throttle({ default: { ttl: 60000, limit: 10 } })
  async debugTrackingDetails(
    @Param('saleId') saleId: string,
    @Req() req: any,
  ) {
    // Find order with tracking
    const order = await this.prisma.order.findFirst({
      where: {
        id: saleId,
        tenantId: req.user.tenantId,
      },
      include: {
        tracking: true,
      },
    });

    if (!order || !order.tracking?.code) {
      throw new HttpException('Order or tracking not found', HttpStatus.NOT_FOUND);
    }

    try {
      // Get raw events
      const rawEvents = await this.correiosService.buscaEventosPorEtiquetaRaw(order.tracking.code);
      
      // Sort events
      const sortedEvents = this.correiosService.sortEventsByTimestamp(rawEvents);
      
      // Get latest event
      const latestEvent = sortedEvents[0];
      
      // Map status
      const mappedStatus = latestEvent ? this.correiosService.mapCorreiosStatus(
        latestEvent.descricaoFrontEnd || latestEvent.descricao || ''
      ) : 'No events';

      return {
        orderId: order.id,
        trackingCode: order.tracking.code,
        currentStoredStatus: order.tracking.status,
        debug: {
          rawEventsCount: rawEvents.length,
          rawEvents: rawEvents.slice(0, 5), // First 5 raw events
          sortedEvents: sortedEvents.slice(0, 5), // First 5 sorted events
          latestEvent: latestEvent || null,
          latestEventDescription: latestEvent?.descricaoFrontEnd || latestEvent?.descricao || 'N/A',
          mappedStatus: mappedStatus,
          statusMapping: {
            input: latestEvent?.descricaoFrontEnd || latestEvent?.descricao || '',
            output: mappedStatus,
            trimmedInput: (latestEvent?.descricaoFrontEnd || latestEvent?.descricao || '').trim(),
          }
        }
      };
    } catch (error) {
      return {
        orderId: order.id,
        trackingCode: order.tracking.code,
        error: error.message,
        stack: error.stack,
      };
    }
  }

  @Get(':saleId')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.COBRADOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Get tracking details for an order' })
  @ApiResponse({ status: 200, description: 'Tracking details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Order or tracking not found' })
  @Throttle({ default: { ttl: 60000, limit: 30 } })
  async getTrackingDetails(
    @Param('saleId') saleId: string,
    @Req() req: any,
  ) {
    // Find order with tracking
    const order = await this.prisma.order.findFirst({
      where: {
        id: saleId,
        tenantId: req.user.tenantId,
      },
      include: {
        tracking: true,
      },
    });

    if (!order) {
      throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
    }

    if (!order.tracking?.code) {
      throw new HttpException('No tracking code for this order', HttpStatus.NOT_FOUND);
    }

    // Check if we have stored tracking data first
    if (order.tracking.events && Array.isArray(order.tracking.events) && order.tracking.events.length > 0) {
      // Return stored data from database
      const lastStatus = order.tracking.status || 'Aguardando atualização';
      
      return {
        saleId: order.id,
        orderNumber: order.orderNumber,
        etiqueta: order.tracking.code,
        lastStatus,
        lastUpdate: order.tracking.lastUpdate,
        lastSync: order.tracking.lastSync,
        isDelivered: order.tracking.isDelivered,
        hasAlert: order.tracking.hasAlert,
        alertReason: order.tracking.alertReason,
        events: (order.tracking.events as any[]).map(event => ({
          descricao: event.descricao,
          descricaoFrontEnd: event.descricaoFrontEnd || event.descricao,
          internalStatus: event.internalStatus || this.correiosService.mapCorreiosStatus(event.descricao),
          dtHrCriado: event.dtHrCriado || (() => {
            try {
              // Try to parse Brazilian date format (DD/MM/YYYY HH:mm:ss)
              const [date, time] = [event.data || '', event.hora || ''];
              if (date && time) {
                const [day, month, year] = date.split('/');
                return new Date(`${year}-${month}-${day} ${time}`).toISOString();
              }
              return new Date().toISOString();
            } catch {
              return new Date().toISOString();
            }
          })(),
          tipo: event.tipo,
          local: event.local,
          cidade: event.cidade,
          uf: event.uf,
        })),
      };
    }

    // If no stored events, return empty data (webhook system will handle updates)
    this.logger.log(`No stored tracking events for order ${order.orderNumber}, waiting for webhook updates`);
    
    return {
      saleId: order.id,
      orderNumber: order.orderNumber,
      etiqueta: order.tracking.code,
      lastStatus: 'Aguardando atualização via webhook',
      lastUpdate: order.tracking.lastUpdate,
      lastSync: order.tracking.lastSync,
      isDelivered: false,
      hasAlert: false,
      alertReason: null,
      events: [],
    };
  }

  @Get(':orderId/refresh')
  @Roles(Role.ADMIN, Role.SUPERVISOR, Role.COBRADOR, Role.VENDEDOR)
  @ApiOperation({ summary: 'Manually refresh tracking status for an order (user-initiated action)' })
  @ApiResponse({ status: 200, description: 'Tracking refreshed successfully' })
  @ApiResponse({ status: 404, description: 'Tracking not found' })
  @Throttle({ default: { ttl: 60000, limit: 20 } })
  async refreshTracking(
    @Param('orderId') orderId: string,
    @Req() req: any,
  ) {
    // This is a manual refresh action initiated by the user
    // Webhook system handles automatic updates
    // Find tracking for order
    const tracking = await this.prisma.tracking.findFirst({
      where: {
        orderId,
        order: {
          tenantId: req.user.tenantId,
        },
      },
    });

    if (!tracking) {
      throw new HttpException('Tracking not found for this order', HttpStatus.NOT_FOUND);
    }

    try {
      // Log tracking code for debugging
      this.logger.log(`Refreshing tracking for order ${orderId}, tracking code: ${tracking.code}`);
      
      // Validate tracking code format
      if (!tracking.code || tracking.code.length < 9) {
        throw new HttpException(
          'Código de rastreamento inválido',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      // Get latest events from Correios
      const events = await this.correiosService.buscaEventosPorEtiqueta(tracking.code);

      if (events && events.length > 0) {
        const latestEvent = events[0];
        const isDelivered = this.correiosService.isDeliveredStatus(latestEvent.tipo);
        const hasAlert = this.correiosService.isCriticalStatus(latestEvent.tipo);

        // Update tracking
        await this.prisma.tracking.update({
          where: { id: tracking.id },
          data: {
            status: latestEvent.descricao,
            lastUpdate: new Date(),
            events: JSON.parse(JSON.stringify(events)),
            lastSync: new Date(),
            isDelivered,
            hasAlert,
            alertReason: hasAlert ? latestEvent.descricao : null,
          },
        });

        // Log sync attempt
        await this.prisma.trackingSyncAttempt.create({
          data: {
            trackingId: tracking.id,
            status: 'SUCCESS',
          },
        });

        // Update order status if delivered
        if (isDelivered) {
          await this.prisma.order.update({
            where: { id: orderId },
            data: { status: 'Completo' },
          });
        }

        this.logger.log(`Tracking refreshed for ${this.maskTrackingCode(tracking.code)}`);

        return {
          success: true,
          tracking: {
            ...tracking,
            events,
          },
        };
      }

      return {
        success: true,
        tracking,
        message: 'No new events found',
      };
    } catch (error) {
      // Log failed sync attempt
      await this.prisma.trackingSyncAttempt.create({
        data: {
          trackingId: tracking.id,
          status: 'FAILED',
          errorMessage: error.message,
        },
      });

      // Handle specific error cases
      if (error.status === 429 || error.response?.status === 429) {
        throw new HttpException(
          'Limite de requisições excedido. Por favor, aguarde alguns segundos antes de tentar novamente.',
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
      
      throw new HttpException(
        'Error refreshing tracking: ' + error.message,
        error.status || HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  @Post('bulk-generate')
  @Roles(Role.ADMIN, Role.SUPERVISOR)
  @ApiOperation({ summary: 'Generate labels for multiple orders' })
  @ApiResponse({ status: 200, description: 'Labels generated successfully' })
  @Throttle({ default: { ttl: 60000, limit: 5 } })
  async bulkGenerateLabels(
    @Body() body: {
      orders: Array<{
        orderId: string;
        serviceCode: string;
        weight: number;
        dimensions: {
          height: number;
          width: number;
          length: number;
        };
        declaredValue?: number;
      }>;
    },
    @Req() req: any,
  ) {
    if (!body.orders || body.orders.length === 0) {
      throw new HttpException('No orders provided', HttpStatus.BAD_REQUEST);
    }

    if (body.orders.length > 50) {
      throw new HttpException(
        'Maximum 50 orders allowed per request',
        HttpStatus.BAD_REQUEST,
      );
    }

    const results = {
      successful: [] as any[],
      failed: [] as any[],
    };

    for (const orderData of body.orders) {
      try {
        const result = await this.generateLabel(
          orderData.orderId,
          {
            serviceCode: orderData.serviceCode,
            weight: orderData.weight,
            dimensions: orderData.dimensions,
            declaredValue: orderData.declaredValue,
          },
          req,
        );

        results.successful.push({
          orderId: orderData.orderId,
          trackingCode: result.trackingCode,
        });
      } catch (error) {
        results.failed.push({
          orderId: orderData.orderId,
          error: error.message,
        });
      }
    }

    return {
      message: 'Bulk generation completed',
      totalProcessed: body.orders.length,
      successful: results.successful.length,
      failed: results.failed.length,
      results,
    };
  }

  // Helper methods

  private async parseExcelFile(buffer: Buffer): Promise<ImportedShipment[]> {
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    return data.map((row: any) => ({
      orderNumber: String(row['Pedido'] || row['Order Number'] || ''),
      customerName: String(row['Cliente'] || row['Customer Name'] || ''),
      customerDocument: String(row['CPF'] || row['Document'] || '').replace(/\D/g, ''),
      customerPhone: String(row['Telefone'] || row['Phone'] || '').replace(/\D/g, ''),
      customerEmail: row['Email'] || row['E-mail'] || undefined,
      cep: String(row['CEP'] || row['Zip Code'] || '').replace(/\D/g, ''),
      street: String(row['Rua'] || row['Street'] || ''),
      number: String(row['Número'] || row['Number'] || ''),
      complement: row['Complemento'] || row['Complement'] || undefined,
      neighborhood: String(row['Bairro'] || row['Neighborhood'] || ''),
      city: String(row['Cidade'] || row['City'] || ''),
      state: String(row['Estado'] || row['State'] || '').toUpperCase(),
      serviceCode: String(row['Serviço'] || row['Service'] || '04162'),
      weight: parseFloat(row['Peso'] || row['Weight'] || '0.5'),
      height: parseFloat(row['Altura'] || row['Height'] || '10'),
      width: parseFloat(row['Largura'] || row['Width'] || '15'),
      length: parseFloat(row['Comprimento'] || row['Length'] || '20'),
      declaredValue: row['Valor Declarado'] || row['Declared Value'] 
        ? parseFloat(row['Valor Declarado'] || row['Declared Value']) 
        : undefined,
    }));
  }

  private async parseCsvFile(buffer: Buffer): Promise<ImportedShipment[]> {
    return new Promise((resolve, reject) => {
      const results: ImportedShipment[] = [];
      
      const parser = csv.parse({
        columns: true,
        delimiter: ',',
        skip_empty_lines: true,
      });

      parser.on('readable', function() {
        let record;
        while ((record = parser.read()) !== null) {
          results.push({
            orderNumber: String(record['Pedido'] || record['Order Number'] || ''),
            customerName: String(record['Cliente'] || record['Customer Name'] || ''),
            customerDocument: String(record['CPF'] || record['Document'] || '').replace(/\D/g, ''),
            customerPhone: String(record['Telefone'] || record['Phone'] || '').replace(/\D/g, ''),
            customerEmail: record['Email'] || record['E-mail'] || undefined,
            cep: String(record['CEP'] || record['Zip Code'] || '').replace(/\D/g, ''),
            street: String(record['Rua'] || record['Street'] || ''),
            number: String(record['Número'] || record['Number'] || ''),
            complement: record['Complemento'] || record['Complement'] || undefined,
            neighborhood: String(record['Bairro'] || record['Neighborhood'] || ''),
            city: String(record['Cidade'] || record['City'] || ''),
            state: String(record['Estado'] || record['State'] || '').toUpperCase(),
            serviceCode: String(record['Serviço'] || record['Service'] || '04162'),
            weight: parseFloat(record['Peso'] || record['Weight'] || '0.5'),
            height: parseFloat(record['Altura'] || record['Height'] || '10'),
            width: parseFloat(record['Largura'] || record['Width'] || '15'),
            length: parseFloat(record['Comprimento'] || record['Length'] || '20'),
            declaredValue: record['Valor Declarado'] || record['Declared Value'] 
              ? parseFloat(record['Valor Declarado'] || record['Declared Value']) 
              : undefined,
          });
        }
      });

      parser.on('error', reject);
      parser.on('end', () => resolve(results));

      parser.write(buffer);
      parser.end();
    });
  }

  private async processShipment(shipment: ImportedShipment, tenantId: string) {
    // Validate required fields
    if (!shipment.orderNumber || !shipment.customerName || !shipment.cep) {
      throw new Error('Missing required fields');
    }

    // Find or create order
    let order = await this.prisma.order.findFirst({
      where: {
        orderNumber: shipment.orderNumber,
        tenantId,
      },
      include: {
        customer: true,
        shipmentLabel: true,
      },
    });

    if (!order) {
      // Create customer if needed
      let customer = await this.prisma.customer.findFirst({
        where: {
          cpf: shipment.customerDocument,
          tenantId,
        },
      });

      if (!customer) {
        customer = await this.prisma.customer.create({
          data: {
            name: shipment.customerName,
            cpf: shipment.customerDocument,
            phone: shipment.customerPhone,
            email: shipment.customerEmail,
            tenantId,
          },
        });

        // Create address
        await this.prisma.address.create({
          data: {
            customerId: customer.id,
            cep: shipment.cep,
            street: shipment.street,
            number: shipment.number,
            complement: shipment.complement,
            neighborhood: shipment.neighborhood,
            city: shipment.city,
            state: shipment.state,
            main: true,
          },
        });
      }

      throw new Error(`Order ${shipment.orderNumber} not found`);
    }

    if (order.shipmentLabel) {
      throw new Error(`Order ${shipment.orderNumber} already has a shipping label`);
    }

    // Generate label
    const labels = await this.correiosService.solicitaEtiquetas(1, shipment.serviceCode);
    const trackingCode = labels[0];

    // Register with Correios
    await this.correiosService.cadastraObjeto({
      etiqueta: trackingCode,
      cliente: {
        nome: shipment.customerName,
        documento: shipment.customerDocument,
        telefone: shipment.customerPhone,
        email: shipment.customerEmail,
      },
      endereco: {
        cep: shipment.cep,
        logradouro: shipment.street,
        numero: shipment.number,
        complemento: shipment.complement,
        bairro: shipment.neighborhood,
        cidade: shipment.city,
        uf: shipment.state,
      },
      servico: {
        codigo: shipment.serviceCode,
        valor: shipment.declaredValue || Number(order.total),
        peso: shipment.weight,
        dimensoes: {
          altura: shipment.height,
          largura: shipment.width,
          comprimento: shipment.length,
        },
      },
    });

    // Save label and tracking
    await this.prisma.shipmentLabel.create({
      data: {
        trackingCode,
        labelNumber: trackingCode,
        serviceType: shipment.serviceCode,
        weight: shipment.weight,
        dimensions: {
          height: shipment.height,
          width: shipment.width,
          length: shipment.length,
        },
        declaredValue: shipment.declaredValue || Number(order.total),
        orderId: order.id,
      },
    });

    await this.prisma.tracking.create({
      data: {
        code: trackingCode,
        status: 'Postado',
        lastUpdate: new Date(),
        orderId: order.id,
      },
    });

    // Update order status
    await this.prisma.order.update({
      where: { id: order.id },
      data: { status: 'Transito' },
    });
  }

  private maskTrackingCode(code: string): string {
    if (!code || code.length < 8) return code;
    return `${code.substring(0, 2)}****${code.substring(code.length - 4)}`;
  }

  @Get('config')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Get Correios configuration status' })
  @ApiResponse({ status: 200, description: 'Configuration status' })
  async getConfig() {
    const isConfigured = !!(
      this.configService.get('CORREIOS_WSDL_LABELS') &&
      this.configService.get('CORREIOS_WSDL_TRACKING') &&
      this.configService.get('CORREIOS_ADMIN_CODE') &&
      this.configService.get('CORREIOS_PASSWORD_TEMP') &&
      this.configService.get('CORREIOS_CONTRACT') &&
      this.configService.get('CORREIOS_CNPJ')
    );

    return {
      isConfigured,
      CORREIOS_WSDL_LABELS: this.configService.get('CORREIOS_WSDL_LABELS') || '',
      CORREIOS_WSDL_TRACKING: this.configService.get('CORREIOS_WSDL_TRACKING') || '',
      CORREIOS_ADMIN_CODE: this.configService.get('CORREIOS_ADMIN_CODE') || '',
      CORREIOS_PASSWORD_TEMP: isConfigured ? '********' : '',
      CORREIOS_CONTRACT: this.configService.get('CORREIOS_CONTRACT') || '',
      CORREIOS_CNPJ: this.configService.get('CORREIOS_CNPJ') || '',
      configuredAt: isConfigured ? new Date().toISOString() : null,
    };
  }

  @Post('config')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Save Correios configuration (for display only)' })
  @ApiResponse({ status: 200, description: 'Configuration saved' })
  async saveConfig(@Body() config: any) {
    // This endpoint doesn't actually save to env vars (that's not possible at runtime)
    // It's just for UI feedback. The actual config must be set in environment variables
    return {
      message: 'Configuração recebida. Por favor, atualize as variáveis de ambiente no servidor e reinicie.',
      config: {
        ...config,
        CORREIOS_PASSWORD_TEMP: '********',
      },
    };
  }

  @Get('test-connection')
  @Roles(Role.ADMIN)
  @ApiOperation({ summary: 'Test Correios SOAP connection' })
  @ApiResponse({ status: 200, description: 'Connection test result' })
  async testConnection() {
    try {
      // Check all required configuration
      const config = {
        wsdlLabels: this.configService.get<string>('CORREIOS_WSDL_LABELS'),
        wsdlTracking: this.configService.get<string>('CORREIOS_WSDL_TRACKING'),
        username: this.configService.get<string>('CORREIOS_ADMIN_CODE'),
        password: this.configService.get<string>('CORREIOS_PASSWORD_TEMP'),
        contract: this.configService.get<string>('CORREIOS_CONTRACT'),
        cnpj: this.configService.get<string>('CORREIOS_CNPJ'),
      };

      // Log what we have (without password)
      this.logger.log('Testing connection with config:', {
        ...config,
        password: config.password ? '***' : undefined,
      });

      const missingFields: string[] = [];
      if (!config.wsdlLabels) missingFields.push('CORREIOS_WSDL_LABELS');
      if (!config.wsdlTracking) missingFields.push('CORREIOS_WSDL_TRACKING');
      if (!config.username) missingFields.push('CORREIOS_ADMIN_CODE');
      if (!config.password) missingFields.push('CORREIOS_PASSWORD_TEMP');
      if (!config.contract) missingFields.push('CORREIOS_CONTRACT');
      if (!config.cnpj) missingFields.push('CORREIOS_CNPJ');

      if (missingFields.length > 0) {
        return {
          success: false,
          message: `Configuração incompleta. Faltando: ${missingFields.join(', ')}`,
          missingFields,
        };
      }

      // Test if we can reach the WSDL
      await this.correiosService.testConnection();

      return {
        success: true,
        message: 'Conexão com Correios estabelecida com sucesso!',
      };
    } catch (error) {
      this.logger.error('Connection test failed:', error);
      
      // Provide more specific error messages
      let message = error.message;
      if (error.message?.includes('ENOTFOUND')) {
        message = 'Não foi possível conectar ao servidor dos Correios. Verifique a URL do WSDL.';
      } else if (error.message?.includes('ECONNREFUSED')) {
        message = 'Conexão recusada pelo servidor dos Correios.';
      } else if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
        message = 'Credenciais inválidas. Verifique usuário e senha.';
      }
      
      return {
        success: false,
        message: `Falha na conexão: ${message}`,
        error: error.message,
      };
    }
  }
}