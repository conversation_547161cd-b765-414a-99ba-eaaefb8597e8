/**
 * Mapeamento de status dos Correios para status internos do sistema
 */

export interface StatusMapping {
  [key: string]: string;
}

/**
 * Mapa de conversão de descricaoFrontEnd dos Correios para status interno
 */
export const correiosToInternalStatusMap: StatusMapping = {
  // Separação
  'Etiqueta emitida: Aguardando postagem pelo remetente': 'Separação',
  'Aguardando postagem': 'Separação',
  'Etiqueta gerada': 'Separação',
  
  // Trânsito
  'Postado': 'Transito',
  'Objeto expedido': 'Transito',
  'Postado depois do horário': 'Transito',
  'Objeto postado': 'Transito',
  'Objeto recebido pelos Correios': 'Transito',
  'Objeto em trânsito': 'Transito',
  'Objeto encaminhado': 'Transito',
  'Encaminhado': 'Transito',
  'Em trânsito': 'Transito',
  'Recebido pelos Correios': 'Transito',
  'Postado pelos Correios': 'Transito',
  'Objeto despachado': 'Transito',
  
  // Confirmar Entrega  
  'Saiu para entrega ao destinatário': 'Confirmar Entrega',
  'Saiu para entrega': 'Confirmar Entrega',
  'Em rota de entrega': 'Confirmar Entrega',
  'ENTREGUE': 'Confirmar Entrega',
  'Entregue': 'Confirmar Entrega',
  'Objeto entregue ao destinatário': 'Confirmar Entrega',
  'Objeto entregue': 'Confirmar Entrega',
  'Entrega efetuada': 'Confirmar Entrega',
  
  // Entrega Falha
  'Destinatário ausente': 'Entrega Falha',
  'Objeto mal encaminhado': 'Entrega Falha',
  'Carteiro não atendido': 'Entrega Falha',
  'Não foi possível entregar': 'Entrega Falha',
  'Endereço incorreto': 'Entrega Falha',
  'Tentativa de entrega': 'Entrega Falha',
  'Tentativa de entrega não efetuada': 'Entrega Falha',
  'Não entregue': 'Entrega Falha',
  'Entrega não realizada': 'Entrega Falha',
  
  // Retirar Correios
  'Aguardando retirada - Área sem entrega': 'Retirar Correios',
  'Objeto aguardando retirada': 'Retirar Correios',
  'Aguardando retirada': 'Retirar Correios',
  'Disponível para retirada': 'Retirar Correios',
  'Aguardando retirada no local': 'Retirar Correios',
  
  // Devolvido
  'Objeto devolvido ao remetente': 'Devolvido Correios',
  'Devolvido ao remetente': 'Devolvido Correios',
  'Devolução': 'Devolvido Correios',
  'Em devolução': 'Devolvido Correios',
  'Devolvido': 'Devolvido Correios',
  
  // Outros status problemáticos
  'Objeto extraviado': 'Entrega Falha',
  'Objeto roubado': 'Entrega Falha',
  'Objeto perdido': 'Entrega Falha',
  'Objeto danificado': 'Entrega Falha',
};

/**
 * Status finais que não devem ser atualizados pelo cron
 */
export const finalStatuses: string[] = [
  'Confirmar Entrega',
  'Entrega Falha',
  'Retirar Correios',
  'Devolvido Correios',
  'Separação',
  'Cancelado',
  'Completo'
];

/**
 * Converte descrição dos Correios para status interno
 * @param descricao - Descrição do evento dos Correios
 * @returns Status interno correspondente ou a própria descrição se não houver mapeamento
 */
export function mapCorreiosToInternalStatus(descricao: string | undefined | null): string {
  if (!descricao) return 'Aguardando atualização';
  
  // Trim whitespace
  const trimmedDescricao = descricao.trim();
  
  // Primeiro tenta match exato
  if (correiosToInternalStatusMap[trimmedDescricao]) {
    return correiosToInternalStatusMap[trimmedDescricao];
  }
  
  // Depois tenta match parcial (case insensitive)
  const descricaoLower = trimmedDescricao.toLowerCase();
  for (const [key, value] of Object.entries(correiosToInternalStatusMap)) {
    if (descricaoLower.includes(key.toLowerCase())) {
      return value;
    }
  }
  
  // Log unmapped status for debugging
  console.log(`Unmapped status: "${trimmedDescricao}" (original: "${descricao}")`);
  
  // Se não encontrar mapeamento, retorna a descrição original
  return trimmedDescricao;
}

/**
 * Verifica se um status é final (não deve ser atualizado)
 */
export function isFinalStatus(status: string | undefined | null): boolean {
  if (!status) return false;
  return finalStatuses.includes(status);
}