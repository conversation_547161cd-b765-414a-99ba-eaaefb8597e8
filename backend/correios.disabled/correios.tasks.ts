import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CorreiosService } from './correios.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationsService } from '../notifications/notifications.service';
import { OrderStatus } from '@prisma/client';
import { finalStatuses } from './status-map';

@Injectable()
export class CorreiosTasks {
  private readonly logger = new Logger(CorreiosTasks.name);
  private isPolling = false;

  constructor(
    private readonly correiosService: CorreiosService,
    private readonly prisma: PrismaService,
    private readonly notificationsService: NotificationsService,
  ) {}

  /**
   * Poll all tracking updates every 30 minutes
   */
  // DISABLED: Using webhook system instead
  // To re-enable, uncomment the @Cron decorator below
  // @Cron('0 */30 * * * *') // Every 30 minutes
  async pollAll() {
    if (this.isPolling) {
      this.logger.warn('Previous polling job still running, skipping...');
      return;
    }

    this.isPolling = true;
    this.logger.log('Starting tracking update polling job');

    try {
      // Get all trackings that are NOT in final states
      const trackings = await this.prisma.tracking.findMany({
        where: {
          code: {
            not: '',
          },
          // Exclude orders with final statuses using centralized list
          status: {
            notIn: finalStatuses,
          },
        },
        include: {
          order: true,
        },
        take: 100, // Process max 100 at a time to avoid overload
      });

      this.logger.log(`Found ${trackings.length} trackings to update`);

      let successCount = 0;
      let failureCount = 0;
      const criticalAlerts: any[] = [];

      // Process each tracking
      for (let index = 0; index < trackings.length; index++) {
        const tracking = trackings[index];
        if (!tracking.code) continue;
        
        try {
          await this.sleep(500); // Rate limiting - 500ms between requests

          const events = await this.correiosService.buscaEventosPorEtiqueta(tracking.code);
          
          if (events && events.length > 0) {
            const latestEvent = events[0];
            const previousStatus = tracking.status;
            
            // Use the internalStatus that's already mapped
            const mappedStatus = latestEvent.internalStatus || 
                               this.correiosService.mapCorreiosStatus(
                                 latestEvent.descricaoFrontEnd || latestEvent.descricao || latestEvent.tipo,
                               );
            
            // Debug logging for the first 5 orders in each batch
            if (index < 5) {
              this.logger.log(
                `DEBUG ${this.maskTrackingCode(tracking.code)}: ` +
                `raw="${latestEvent.descricaoFrontEnd || latestEvent.descricao}" -> ` +
                `mapped="${mappedStatus}" (previous="${previousStatus}")`,
              );
            }
            
            const isDelivered = ['Confirmar Entrega', 'Entregue', 'Devolvido Correios'].includes(mappedStatus);
            const hasAlert = this.correiosService.isCriticalStatus(latestEvent.tipo);

            // Update tracking with mapped status
            await this.prisma.tracking.update({
              where: { id: tracking.id },
              data: {
                status: mappedStatus, // Use mapped status instead of raw description
                lastUpdate: new Date(),
                events: JSON.parse(JSON.stringify(events)),
                lastSync: new Date(),
                isDelivered,
                hasAlert,
                alertReason: hasAlert ? latestEvent.descricao : null,
              },
            });

            // Log successful sync
            await this.prisma.trackingSyncAttempt.create({
              data: {
                trackingId: tracking.id,
                status: 'SUCCESS',
              },
            });

            // Handle status changes
            if (previousStatus !== mappedStatus) {
              this.logger.log(
                `Status changed for ${this.maskTrackingCode(tracking.code)}: ${previousStatus} -> ${mappedStatus}`
              );

              // Update order status if delivered
              if (isDelivered && tracking.order) {
                await this.prisma.order.update({
                  where: { id: tracking.orderId },
                  data: { status: OrderStatus.Completo },
                });

                // Send delivery notification
                if (tracking.order) {
                  await this.notificationsService.sendNotification({
                    type: 'STATUS_CHANGED',
                    recipient: tracking.order.customerPhone || '',
                    data: {
                      orderId: tracking.orderId,
                      orderNumber: tracking.order.orderNumber,
                      newStatus: mappedStatus,
                      trackingCode: tracking.code,
                    },
                  });
                }
              }

              // Handle critical alerts
              if (hasAlert) {
                criticalAlerts.push({
                  tracking: tracking,
                  event: latestEvent,
                  order: tracking.order,
                });
              }
            }

            successCount++;
          }
        } catch (error) {
          failureCount++;
          this.logger.error(
            `Failed to update tracking ${this.maskTrackingCode(tracking.code)}:`,
            error.message
          );

          // Log failed sync
          await this.prisma.trackingSyncAttempt.create({
            data: {
              trackingId: tracking.id,
              status: 'FAILED',
              errorMessage: error.message,
            },
          });
        }
      }

      // Handle critical alerts batch notification
      if (criticalAlerts.length > 0) {
        await this.handleCriticalAlerts(criticalAlerts);
      }

      this.logger.log(
        `Tracking update completed: ${successCount} successful, ${failureCount} failed, ${criticalAlerts.length} alerts`
      );
    } catch (error) {
      this.logger.error('Error in tracking polling job:', error);
    } finally {
      this.isPolling = false;
    }
  }

  /**
   * Check for stale trackings daily at 9 AM
   */
  // DISABLED: Using webhook system instead
  // @Cron('0 9 * * *') // Daily at 9 AM
  async checkStaleTrackings() {
    this.logger.log('Starting stale tracking check');

    try {
      // Find trackings not updated in 7 days
      const staleDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      const staleTrackings = await this.prisma.tracking.findMany({
        where: {
          isDelivered: false,
          lastUpdate: {
            lt: staleDate,
          },
        },
        include: {
          order: {
            include: {
              seller: true,
            },
          },
        },
      });

      if (staleTrackings.length > 0) {
        this.logger.warn(`Found ${staleTrackings.length} stale trackings`);

        // Group by seller for notification
        const trackingsBySeller = new Map<string, any[]>();
        
        for (const tracking of staleTrackings) {
          if (tracking.order?.seller) {
            const sellerId = tracking.order.seller.id;
            if (!trackingsBySeller.has(sellerId)) {
              trackingsBySeller.set(sellerId, []);
            }
            trackingsBySeller.get(sellerId)!.push(tracking);
          }
        }

        // Send notifications to sellers
        for (const [sellerId, trackings] of trackingsBySeller) {
          const seller = trackings[0].order.seller;
          await this.notificationsService.sendNotification({
            type: 'CUSTOM',
            recipient: seller.email,
            channel: 'EMAIL',
            data: {
              subject: 'Rastreamentos sem atualização',
              message: `Você tem ${trackings.length} pedidos com rastreamento sem atualização há mais de 7 dias.`,
              trackings: trackings.map((t: any) => ({
                orderNumber: t.order.orderNumber,
                trackingCode: this.maskTrackingCode(t.code),
                lastUpdate: t.lastUpdate,
              })),
            },
          });
        }
      }

      this.logger.log(`Stale tracking check completed: ${staleTrackings.length} found`);
    } catch (error) {
      this.logger.error('Error checking stale trackings:', error);
    }
  }

  /**
   * Clean up old sync attempts weekly
   */
  // DISABLED: Using webhook system instead
  // @Cron(CronExpression.EVERY_WEEK) // Every Sunday at midnight
  async cleanupOldSyncAttempts() {
    this.logger.log('Starting cleanup of old sync attempts');

    try {
      // Delete sync attempts older than 30 days
      const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const result = await this.prisma.trackingSyncAttempt.deleteMany({
        where: {
          attemptedAt: {
            lt: cutoffDate,
          },
        },
      });

      this.logger.log(`Cleaned up ${result.count} old sync attempts`);
    } catch (error) {
      this.logger.error('Error cleaning up sync attempts:', error);
    }
  }

  /**
   * Check for orders in transit without tracking
   */
  // DISABLED: Using webhook system instead
  // @Cron('0 10 * * *') // Daily at 10 AM
  async checkOrdersWithoutTracking() {
    this.logger.log('Checking for orders in transit without tracking');

    try {
      const ordersWithoutTracking = await this.prisma.order.findMany({
        where: {
          status: OrderStatus.Transito,
          tracking: null,
          createdAt: {
            gt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        include: {
          seller: true,
        },
        take: 50,
      });

      if (ordersWithoutTracking.length > 0) {
        this.logger.warn(`Found ${ordersWithoutTracking.length} orders without tracking`);

        // Group by seller
        const ordersBySeller = new Map<string, any[]>();
        
        for (const order of ordersWithoutTracking) {
          const sellerId = order.seller.id;
          if (!ordersBySeller.has(sellerId)) {
            ordersBySeller.set(sellerId, []);
          }
          ordersBySeller.get(sellerId)!.push(order);
        }

        // Notify sellers
        for (const [sellerId, orders] of ordersBySeller) {
          const seller = orders[0].seller;
          await this.notificationsService.sendNotification({
            type: 'CUSTOM',
            recipient: seller.email,
            channel: 'EMAIL',
            data: {
              subject: 'Pedidos sem código de rastreamento',
              message: `Você tem ${orders.length} pedidos em trânsito sem código de rastreamento.`,
              orders: orders.map((o: any) => ({
                orderNumber: o.orderNumber,
                customerName: o.customerName,
                createdAt: o.createdAt,
              })),
            },
          });
        }
      }

      this.logger.log(`Check completed: ${ordersWithoutTracking.length} orders without tracking`);
    } catch (error) {
      this.logger.error('Error checking orders without tracking:', error);
    }
  }

  /**
   * Process high priority trackings more frequently
   */
  // DISABLED: Using webhook system instead
  // @Cron('0 */10 8-18 * * 1-5') // Every 10 minutes during business hours
  async pollHighPriorityTrackings() {
    this.logger.log('Starting high priority tracking poll');

    try {
      // Find trackings with critical status or recent alerts
      const criticalTrackings = await this.prisma.tracking.findMany({
        where: {
          isDelivered: false,
          hasAlert: true,
          lastSync: {
            lt: new Date(Date.now() - 10 * 60 * 1000), // Not synced in last 10 minutes
          },
        },
        include: {
          order: true,
        },
        take: 20, // Process fewer items but more frequently
      });

      this.logger.log(`Found ${criticalTrackings.length} critical trackings to update`);

      for (const tracking of criticalTrackings) {
        try {
          await this.sleep(300); // Shorter delay for priority items

          const events = await this.correiosService.buscaEventosPorEtiqueta(tracking.code);
          
          if (events && events.length > 0) {
            const latestEvent = events[0];
            const stillCritical = this.correiosService.isCriticalStatus(latestEvent.tipo);

            await this.prisma.tracking.update({
              where: { id: tracking.id },
              data: {
                status: latestEvent.descricao,
                lastUpdate: new Date(),
                events: JSON.parse(JSON.stringify(events)),
                lastSync: new Date(),
                hasAlert: stillCritical,
                alertReason: stillCritical ? latestEvent.descricao : null,
              },
            });

            // If status improved, notify
            if (!stillCritical && tracking.hasAlert) {
              this.logger.log(
                `Alert resolved for ${this.maskTrackingCode(tracking.code)}`
              );
            }
          }
        } catch (error) {
          this.logger.error(
            `Failed to update critical tracking ${this.maskTrackingCode(tracking.code)}:`,
            error.message
          );
        }
      }
    } catch (error) {
      this.logger.error('Error in high priority polling:', error);
    }
  }

  // Helper methods

  private async handleCriticalAlerts(alerts: any[]) {
    try {
      // Group alerts by type
      const alertsByType = new Map<string, any[]>();
      
      for (const alert of alerts) {
        const eventType = alert.event.tipo;
        if (!alertsByType.has(eventType)) {
          alertsByType.set(eventType, []);
        }
        alertsByType.get(eventType)!.push(alert);
      }

      // Send summary notification to admins
      const adminUsers = await this.prisma.user.findMany({
        where: {
          role: 'ADMIN',
          active: true,
        },
      });

      for (const admin of adminUsers) {
        await this.notificationsService.sendNotification({
          type: 'CUSTOM',
          recipient: admin.email,
          channel: 'EMAIL',
          data: {
            subject: `${alerts.length} alertas críticos de rastreamento`,
            message: 'Foram detectados os seguintes alertas críticos:',
            alerts: Array.from(alertsByType.entries()).map(([type, items]) => ({
              type,
              count: items.length,
              examples: items.slice(0, 5).map((a: any) => ({
                orderNumber: a.order?.orderNumber,
                trackingCode: this.maskTrackingCode(a.tracking.code),
                description: a.event.descricao,
              })),
            })),
          },
        });
      }
    } catch (error) {
      this.logger.error('Error handling critical alerts:', error);
    }
  }

  private maskTrackingCode(code: string): string {
    if (!code || code.length < 8) return code;
    return `${code.substring(0, 2)}****${code.substring(code.length - 4)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}