import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { HttpModule } from '@nestjs/axios';
import { ThrottlerModule } from '@nestjs/throttler';
import { CorreiosService } from './correios.service';
import { CorreiosTasks } from './correios.tasks';
import { CorreiosController } from './correios.controller';
import { ShipmentsController } from './shipments.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    PrismaModule,
    NotificationsModule,
    HttpModule,
    ScheduleModule.forRoot(),
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 10,
    }]),
  ],
  controllers: [ShipmentsController, CorreiosController],
  providers: [CorreiosService, CorreiosTasks],
  exports: [CorreiosService],
})
export class CorreiosModule {}