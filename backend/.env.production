# Production environment variables for Railway deployment

# Database
DATABASE_URL=************************************/dbname?sslmode=require

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
SIGNING_KEY=your-signing-key-here
JWT_SECRET=your-jwt-secret-here

# Anti-fraud Configuration
DUPLICATE_CHECK_TIMEOUT_MS=2000
FUZZY_MATCH_CONFIG={"threshold":0.7}

# Feature Flags
FEATURE_FLAGS={"antifraud":{"enabled":true,"shadowMode":false}}

# Monitoring (Optional - can be disabled initially)
SENTRY_DSN=
OTLP_ENABLED=false
PROMETHEUS_ENABLED=false

# Node
NODE_ENV=production
PORT=3000