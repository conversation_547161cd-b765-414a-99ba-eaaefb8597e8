#!/bin/bash
# Quick script to switch from Dockerfile to Nixpacks

echo "Switching to Nixpacks builder..."

# Backup current railway.toml
cp railway.toml railway.toml.dockerfile

# Create new railway.toml for nixpacks
cat > railway.toml << 'EOF'
[build]
builder = "nixpacks"

[deploy]
startCommand = "./scripts/start-with-migration.sh"
healthcheckPath = "/api/v1/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3
port = 3000
EOF

echo "✅ Switched to nixpacks builder"
echo "Run: git add railway.toml && git commit -m 'Switch to nixpacks' && git push"