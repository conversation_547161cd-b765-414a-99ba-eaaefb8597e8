# Railway Deployment Troubleshooting

## Current Issue: Health Check Failing

The build is successful but the health check is failing. This usually means:

1. **App isn't starting** - Check logs for startup errors
2. **Wrong PORT** - Make sure PORT environment variable is set
3. **Database connection failing** - Check DATABASE_URL
4. **Missing environment variables** - Verify all are set

## Quick Fixes to Try:

### 1. Check Railway Logs
In your Railway dashboard:
- Click on your service
- Go to "Logs" tab
- Look for error messages during startup

### 2. Verify Environment Variables
Make sure ALL these are set in Railway:

```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/railway
JWT_SECRET=your-secret-key-here
API_PREFIX=api/v1
NODE_ENV=production
PORT=${{PORT}}  # Railway provides this
CORS_ORIGIN=https://zencash-sand.vercel.app
```

**IMPORTANT**: For PORT, use `${{PORT}}` (Railway's variable reference)

### 3. Try Manual Migration
If migrations are failing:

1. Go to Railway dashboard
2. Click on your backend service
3. Go to "Settings" tab
4. Under "Deploy", temporarily change start command to:
   ```
   npm run start:prod
   ```
5. Deploy and let it start
6. Then run migrations manually via Railway CLI:
   ```
   railway run npx prisma migrate deploy
   ```

### 4. Disable Health Check (Temporary)
To see if the app starts without health check:

1. In Railway dashboard → Service Settings
2. Remove the health check path
3. Deploy and check logs

### 5. Test Locally with Production Build
```bash
# In backend directory
npm run build
NODE_ENV=production npm run start:prod
```

## Common Error Messages and Solutions:

### "Cannot find module"
- Make sure root directory is set to `/backend`
- Check that build completed successfully

### "ECONNREFUSED" or Database errors
- Verify DATABASE_URL is using `postgres.railway.internal`
- Make sure backend and database are in same project

### "Port already in use"
- Use `${{PORT}}` in Railway environment variables
- Don't hardcode port 3000

### "Prisma error: The table does not exist"
- Migrations haven't run
- Try manual migration (see above)

## Alternative: Simple Node.js Deploy

If nothing works, try this minimal approach:

1. Delete the current service
2. Create new service
3. Set these in Railway:
   - Root Directory: `/backend`
   - Build Command: `npm install && npx prisma generate && npm run build`
   - Start Command: `node dist/main`
4. Add all environment variables
5. Deploy

## Get Help

If still failing:
1. Check Railway Discord/Community
2. Share the error logs from Railway dashboard
3. Make sure the app runs locally with: `npm run build && npm run start:prod`