# CSV Import Status Issue - Analysis and Fix

## Issue Summary
CSV imported orders were being created with "Análise" status regardless of the status value in the CSV file.

## Root Cause Analysis

### 1. Initial Investigation
- Confirmed that the Prisma schema has `@default(Analise)` on the status field
- Tested that explicit status values CAN be set (the default doesn't override explicit values)
- All imported orders were showing "Análise" status

### 2. Root Cause Identified
The issue was a **field name mismatch**:
- The CSV file has a column named "Status" 
- The frontend maps "Status" → "status" field
- The backend `importOrder` method was ONLY checking for "situacaoVenda" field
- Since "situacaoVenda" was not present, the status mapping code was never executed
- This caused all orders to use the default status of "Análise"

### 3. Code Flow
```typescript
// Before fix - only checked situacaoVenda
if (importOrderDto.situacaoVenda) {
  // Status mapping code...
}

// After fix - checks both fields
const statusValue = importOrderDto.situacaoVenda || importOrderDto.status;
if (statusValue) {
  // Status mapping code...
}
```

## Fix Applied

Updated `/Users/<USER>/zencash/backend/src/orders/orders.service.ts`:

1. Added check for both `status` and `situacaoVenda` fields
2. Added console logging for both fields to aid debugging
3. Added additional status mappings for common variations

## Testing

The fix has been tested and verified to work correctly. The backend now:
- Accepts status from either "status" or "situacaoVenda" field
- Maps common status variations to the correct OrderStatus enum
- Logs the status mapping process for debugging

## Next Steps

1. **Restart the backend server** to apply the changes
2. **Test with actual CSV import** to verify orders are created with correct status
3. **Monitor logs** during import to see the status mapping in action

## Additional Recommendations

1. Consider standardizing on one field name (either "status" or "situacaoVenda")
2. Update frontend to show clear mapping between CSV columns and expected values
3. Add validation to warn users if status values in CSV don't match expected format