# Railway Deployment Guide for ZenCash Backend

## Prerequisites
- Railway CLI installed (`npm install -g @railway/cli`)
- Git repository initialized
- Railway account created

## Step-by-Step Deployment

### 1. Login to Railway CLI
```bash
railway login
```

### 2. Create a New Railway Project
```bash
# In the backend directory
cd /Users/<USER>/zencash/backend

# Create new project
railway init
```
When prompted, select "Empty Project"

### 3. Link to Existing Database
Since you already have a PostgreSQL database on Railway:
1. Go to your Railway dashboard
2. Open your existing project with the PostgreSQL database
3. Click on the project settings
4. Copy the project ID
5. Link your backend to the same project:
```bash
railway link [PROJECT_ID]
```

### 4. Configure Environment Variables
Create all required environment variables in Railway dashboard or via CLI:

```bash
# Set environment variables
railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"
railway variables set JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-random"
railway variables set API_PREFIX="api/v1"
railway variables set NODE_ENV="production"
railway variables set CORS_ORIGIN="https://zencash-sand.vercel.app"
railway variables set PORT="3000"
```

### 5. Deploy the Backend
```bash
# Deploy to Railway
railway up
```

This will:
- Build your NestJS application
- Run Prisma migrations
- Start the production server

### 6. Get Your Backend URL
```bash
# Get the deployment URL
railway open
```

Your backend URL will be something like: `https://zencash-backend-production.up.railway.app`

### 7. Update Frontend Environment Variables on Vercel

Go to your Vercel project settings and add/update:
```
REACT_APP_API_URL=https://zencash-backend-production.up.railway.app/api/v1
REACT_APP_TENANT_ID=************************************
```

### 8. Test the Deployment

#### Health Check:
```bash
curl https://your-backend-url.railway.app/api/v1/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-06-28T...",
  "database": "connected",
  "version": "1.0.0",
  "node": "v18.x.x"
}
```

#### Test Login:
```bash
curl -X POST https://your-backend-url.railway.app/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: ************************************" \
  -d '{"email":"<EMAIL>","password":"Gilson123$"}'
```

### 9. Monitor Logs
```bash
# View live logs
railway logs
```

## Troubleshooting

### Database Connection Issues
- Ensure DATABASE_URL is using the internal Railway URL format
- Check if migrations ran successfully in the logs

### CORS Issues
- Verify CORS_ORIGIN matches your Vercel URL exactly
- Check browser console for specific CORS error messages

### 500 Errors
- Check Railway logs for detailed error messages
- Ensure all environment variables are set correctly
- Verify JWT_SECRET is set

### Build Failures
- Ensure all dependencies are in package.json
- Check if Prisma schema is valid
- Verify TypeScript compilation succeeds locally

## Important Notes
1. The `railway.json` file configures automatic migrations on deploy
2. Health checks are configured at `/api/v1/health`
3. The backend binds to `0.0.0.0` to work with Railway's proxy
4. Logs are configured based on NODE_ENV (production = less verbose)