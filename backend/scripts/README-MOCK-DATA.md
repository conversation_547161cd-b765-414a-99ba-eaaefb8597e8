# Mock Data Generation

## Overview
Two scripts are available to generate mock sales data for testing and development:

1. **generate-mock-sales.ts** - Full mock data generation including users and products
2. **generate-mock-sales-simple.ts** - Generates only orders using existing users

## Usage

### Simple Mock Sales (Recommended)
Generates 30 diverse orders with realistic scenarios:

```bash
# Local development
npm run db:mock-sales

# With Railway
railway run npm run db:mock-sales
```

### Full Mock Generation
Creates users, products, and orders:

```bash
# Local development
npm run db:generate-mock

# With Railway
railway run npm run db:generate-mock
```

## Order Scenarios

The mock data includes 30 orders covering all status types:

### Financial Distribution
- **6 Completed Orders** - Fully paid, with tracking
- **4 Partial Payments** - 30-70% paid
- **3 In Negotiation** - Customer negotiating terms
- **3 Payment Promises** - Scheduled future payments
- **3 Payment Pending** - Delivered, awaiting payment
- **2 In Transit** - Currently being delivered
- **2 Confirm Delivery** - Delivered, awaiting confirmation
- **4 Failed/Cancelled** - Unable to collect or cancelled
- **2 Recovery** - Attempting to recover old debts
- **1 Analysis** - New high-value order

### Data Variety
- **Customer Names**: 30 different Brazilian names
- **Addresses**: 10 different Brazilian addresses
- **Phone Numbers**: Randomly generated Brazilian mobile numbers
- **Order Values**: R$ 199.90 to R$ 1,299.90
- **Payment Methods**: PIX, Credit Card, Boleto
- **Time Range**: Orders from today to 15 days ago

## Reporting Features Tested

The mock data tests all reporting scenarios:

1. **Revenue Reports**
   - Total sales by period
   - Received vs pending amounts
   - Collection rate percentage

2. **Status Distribution**
   - Orders by status
   - Conversion funnel
   - Recovery success rate

3. **User Performance**
   - Sales by seller
   - Collection by collector
   - Average ticket size

4. **Time-based Analysis**
   - Daily/weekly trends
   - Payment delays
   - Delivery times

## Example Output

```
📊 Order Summary:
  Completo: 6 orders - Total: R$ 2,549.40 - Received: R$ 2,549.40
  Parcial: 4 orders - Total: R$ 1,899.60 - Received: R$ 900.00
  Negociacao: 3 orders - Total: R$ 1,899.70 - Received: R$ 50.00
  ...

💰 Financial Summary:
  Total Order Value: R$ 15,897.00
  Total Received: R$ 5,799.40
  Pending: R$ 10,097.60
  Collection Rate: 36.5%
```

## Notes

- The script can be run multiple times
- Each run generates unique order numbers
- Existing orders are preserved (unless explicitly cleared)
- All orders respect the business rules and status transitions
- Tracking codes follow Brazilian postal format (BR + 9 digits + BR)