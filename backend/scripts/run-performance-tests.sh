#!/bin/bash

# Performance Testing Script for Anti-fraud System

set -e

echo "🚀 Starting Anti-fraud Performance Tests"
echo "======================================="

# Check if k6 is installed
if ! command -v k6 &> /dev/null; then
    echo "❌ k6 is not installed. Please install k6 first:"
    echo "   brew install k6  (macOS)"
    echo "   or visit: https://k6.io/docs/getting-started/installation/"
    exit 1
fi

# Configuration
BASE_URL=${BASE_URL:-"http://localhost:5000"}
TENANT_ID=${TENANT_ID:-"perf-test-tenant"}
OUTPUT_DIR="k6-results"

# Create output directory
mkdir -p $OUTPUT_DIR

echo "📋 Configuration:"
echo "   Base URL: $BASE_URL"
echo "   Tenant ID: $TENANT_ID"
echo "   Output Directory: $OUTPUT_DIR"
echo ""

# Function to run a test
run_test() {
    local test_name=$1
    local test_file=$2
    local extra_args=$3
    
    echo "🔄 Running $test_name..."
    
    k6 run \
        --out json=$OUTPUT_DIR/${test_name}-results.json \
        --summary-export=$OUTPUT_DIR/${test_name}-summary.json \
        -e BASE_URL=$BASE_URL \
        -e TENANT_ID=$TENANT_ID \
        $extra_args \
        $test_file
    
    if [ $? -eq 0 ]; then
        echo "✅ $test_name completed successfully"
    else
        echo "❌ $test_name failed"
        return 1
    fi
    echo ""
}

# Run tests sequentially
echo "🏃 Starting test suite..."
echo ""

# Test 1: General Anti-fraud Performance
run_test "antifraud-performance" "k6-tests/antifraud-performance.js" ""

# Test 2: Duplicate Detection Load Test
run_test "duplicate-detection-baseline" "k6-tests/duplicate-detection-load.js" "--scenario=baseline"

# Test 3: Peak Load Test
run_test "duplicate-detection-peak" "k6-tests/duplicate-detection-load.js" "--scenario=peak"

# Test 4: Stress Test (optional)
read -p "Run stress test? This will generate very high load. (y/N) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    run_test "duplicate-detection-stress" "k6-tests/duplicate-detection-load.js" "--scenario=stress"
fi

# Generate consolidated report
echo "📊 Generating consolidated report..."

cat > $OUTPUT_DIR/performance-report.md << EOF
# Anti-fraud System Performance Test Report

Generated on: $(date)

## Test Environment
- Base URL: $BASE_URL
- Tenant ID: $TENANT_ID

## Test Results Summary

### 1. General Anti-fraud Performance
- See: antifraud-performance-summary.json

### 2. Duplicate Detection - Baseline (10K orders/hour)
- See: duplicate-detection-baseline-summary.json

### 3. Duplicate Detection - Peak (50K orders/hour)
- See: duplicate-detection-peak-summary.json

EOF

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "### 4. Duplicate Detection - Stress Test" >> $OUTPUT_DIR/performance-report.md
    echo "- See: duplicate-detection-stress-summary.json" >> $OUTPUT_DIR/performance-report.md
fi

echo "" >> $OUTPUT_DIR/performance-report.md
echo "## Recommendations" >> $OUTPUT_DIR/performance-report.md
echo "" >> $OUTPUT_DIR/performance-report.md

# Analyze results and add recommendations
if [ -f "$OUTPUT_DIR/antifraud-performance-summary.json" ]; then
    error_rate=$(jq -r '.metrics.http_req_failed.values.rate // 0' $OUTPUT_DIR/antifraud-performance-summary.json)
    if (( $(echo "$error_rate > 0.05" | bc -l) )); then
        echo "⚠️  High error rate detected: $error_rate" >> $OUTPUT_DIR/performance-report.md
        echo "   - Consider increasing connection pool size" >> $OUTPUT_DIR/performance-report.md
        echo "   - Review database query optimization" >> $OUTPUT_DIR/performance-report.md
    fi
fi

echo "✅ All tests completed!"
echo "📁 Results saved to: $OUTPUT_DIR/"
echo ""
echo "📈 To view detailed results:"
echo "   - JSON results: $OUTPUT_DIR/*-results.json"
echo "   - Summary reports: $OUTPUT_DIR/*-summary.json"
echo "   - Consolidated report: $OUTPUT_DIR/performance-report.md"

# Optional: Start k6 web dashboard
read -p "Start k6 web dashboard to view results? (y/N) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Starting k6 web dashboard on http://localhost:5665"
    k6 inspect $OUTPUT_DIR/antifraud-performance-results.json
fi