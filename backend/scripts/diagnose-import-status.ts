import { PrismaClient, OrderStatus } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function diagnoseImportStatus() {
  console.log('=== Diagnosing Import Status Issue ===\n');

  try {
    // 1. Check recent imported orders
    console.log('1. Checking recent imported orders...');
    const recentOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        createdAt: true,
        observation: true,
        statusHistory: {
          orderBy: { changedAt: 'asc' },
          select: {
            previousStatus: true,
            newStatus: true,
            changedAt: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    console.log(`Found ${recentOrders.length} recent orders:\n`);
    recentOrders.forEach(order => {
      console.log(`Order ${order.orderNumber}:`);
      console.log(`  - Current Status: ${order.status}`);
      console.log(`  - Created At: ${order.createdAt}`);
      console.log(`  - Observation: ${order.observation || 'None'}`);
      console.log(`  - Status History:`);
      order.statusHistory.forEach((history, index) => {
        console.log(`    ${index + 1}. ${history.previousStatus} → ${history.newStatus} at ${history.changedAt}`);
      });
      console.log('');
    });

    // 2. Check if there are orders with non-Analise status
    console.log('\n2. Checking distribution of order statuses...');
    const statusCounts = await prisma.order.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
    });

    console.log('Status distribution:');
    statusCounts.forEach(({ status, _count }) => {
      console.log(`  - ${status}: ${_count.id} orders`);
    });

    // 3. Test creating an order with specific status
    console.log('\n3. Testing order creation with specific status...');
    const testOrderNumber = `TEST-${Date.now()}`;
    
    try {
      const testOrder = await prisma.order.create({
        data: {
          orderNumber: testOrderNumber,
          customerName: 'Test Import Status',
          customerPhone: '11999999999',
          status: OrderStatus.Completo, // Explicitly set to Completo
          total: 100,
          sellerId: 'test-seller',
          tenantId: 'test-tenant',
          items: {
            create: {
              productId: 'test-product',
              productName: 'Test Product',
              quantity: 1,
              unitPrice: 100,
            },
          },
          statusHistory: {
            create: {
              previousStatus: OrderStatus.Completo,
              newStatus: OrderStatus.Completo,
              changedById: 'test-seller',
            },
          },
        },
        select: {
          id: true,
          orderNumber: true,
          status: true,
        },
      });

      console.log(`Created test order ${testOrder.orderNumber} with status: ${testOrder.status}`);
      
      // Verify the status was saved correctly
      const verifyOrder = await prisma.order.findUnique({
        where: { id: testOrder.id },
        select: { status: true },
      });
      
      console.log(`Verified status after creation: ${verifyOrder?.status}`);
      
      // Clean up test order
      await prisma.order.delete({ where: { id: testOrder.id } });
      console.log('Test order cleaned up.');
      
    } catch (error) {
      console.error('Error creating test order:', error);
    }

    // 4. Check for any middleware or hooks
    console.log('\n4. Checking for potential issues...');
    
    // Check if there are any orders with status mismatch in history
    const mismatchedOrders = await prisma.order.findMany({
      where: {
        statusHistory: {
          some: {
            newStatus: {
              not: OrderStatus.Analise,
            },
          },
        },
        status: OrderStatus.Analise,
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        statusHistory: {
          orderBy: { changedAt: 'desc' },
          take: 1,
          select: {
            newStatus: true,
            changedAt: true,
          },
        },
      },
      take: 5,
    });

    if (mismatchedOrders.length > 0) {
      console.log(`\nFound ${mismatchedOrders.length} orders with status mismatch:`);
      mismatchedOrders.forEach(order => {
        const latestHistory = order.statusHistory[0];
        console.log(`  - Order ${order.orderNumber}: Current=${order.status}, Latest History=${latestHistory?.newStatus}`);
      });
    }

    // 5. Provide recommendations
    console.log('\n=== Recommendations ===');
    console.log('1. The issue appears to be the @default(Analise) in the Prisma schema');
    console.log('2. When importing orders, ensure the status field is explicitly set');
    console.log('3. Check if the frontend is sending the correct status field name');
    console.log('4. Verify that the status mapping in importOrder method is working correctly');
    console.log('5. Consider removing the @default(Analise) from the schema if not needed');

  } catch (error) {
    console.error('Error during diagnosis:', error);
  } finally {
    await prisma.$disconnect();
  }
}

diagnoseImportStatus().catch(console.error);