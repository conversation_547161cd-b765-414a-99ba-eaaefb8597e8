import { PrismaClient, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function testAntifraudAnaliseOrders() {
  try {
    console.log('🔍 Verificando pedidos em status ANÁLISE...\n');

    // Buscar pedidos em análise
    const analiseOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        status: true,
        requiresReview: true,
        riskScore: true,
        riskLevel: true,
        isDuplicate: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    console.log(`📊 Total de pedidos em ANÁLISE: ${analiseOrders.length}\n`);

    if (analiseOrders.length === 0) {
      console.log('⚠️  Nenhum pedido em status ANÁLISE encontrado.');
      return;
    }

    // Analisar cada pedido
    console.log('📋 Detalhes dos pedidos em ANÁLISE:');
    console.log('═'.repeat(80));
    
    analiseOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. Pedido ${order.orderNumber || order.id}`);
      console.log(`   Cliente: ${order.customerName}`);
      console.log(`   Status: ${order.status}`);
      console.log(`   Requer Revisão: ${order.requiresReview ? '✅ SIM' : '❌ NÃO'}`);
      console.log(`   Score de Risco: ${order.riskScore || 'Não calculado'}`);
      console.log(`   Nível de Risco: ${order.riskLevel || 'Não definido'}`);
      console.log(`   É Duplicata: ${order.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
      console.log(`   Criado em: ${order.createdAt.toLocaleString('pt-BR')}`);
    });

    console.log('\n' + '═'.repeat(80));

    // Contar quantos pedidos em análise têm requiresReview = false
    const ordersNotRequiringReview = analiseOrders.filter(o => !o.requiresReview);
    
    if (ordersNotRequiringReview.length > 0) {
      console.log(`\n⚠️  PROBLEMA: ${ordersNotRequiringReview.length} pedidos em ANÁLISE não estão marcados para revisão!`);
      console.log('   Estes pedidos NÃO aparecerão no dashboard Anti-Fraud.');
      console.log('\n   IDs dos pedidos problemáticos:');
      ordersNotRequiringReview.forEach(order => {
        console.log(`   - ${order.orderNumber || order.id} (Score: ${order.riskScore || 'N/A'})`);
      });
    } else {
      console.log('\n✅ Todos os pedidos em ANÁLISE estão marcados para revisão!');
    }

    // Verificar pedidos que precisam ter o risco avaliado
    const ordersWithoutRiskScore = analiseOrders.filter(o => o.riskScore === null || o.riskScore === undefined);
    
    if (ordersWithoutRiskScore.length > 0) {
      console.log(`\n⚠️  ${ordersWithoutRiskScore.length} pedidos ainda não tiveram o risco avaliado.`);
      console.log('   Execute o processo de avaliação de risco para estes pedidos.');
    }

  } catch (error) {
    console.error('❌ Erro ao verificar pedidos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o teste
testAntifraudAnaliseOrders();