import * as fs from 'fs';
import * as path from 'path';
import * as <PERSON> from 'papa<PERSON><PERSON>';
import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3003/api/v1';
const TENANT_ID = '28a833c0-c2a1-4498-85ca-b028f982ffb2';

// Same mappings as frontend
const DEFAULT_MAPPINGS: { [key: string]: string } = {
  'Data Venda': 'dataVenda',
  'ID Venda': 'idVenda',
  'Cliente': 'cliente',
  'Telefone': 'telefone',
  'Oferta': 'oferta',
  'Valor Venda': 'valorVenda',
  'Status': 'status',
  'Situação Venda': 'situacaoVenda',
  'Valor Recebido': 'valorRecebido',
  'Historico': 'historico',
  'Ultima Atualização': 'ultimaAtualizacao',
  'Código de Rastreio': 'codigoRastreio',
  'Status Correios': 'statusCorreios',
  'Vendedor': 'vendedor',
  'Operador': 'operador',
  'Zap': 'zap',
  'ESTADO DO DESTINATÁRIO': 'estadoDestinatario',
  'CIDADE DO DESTINATÁRIO': 'cidadeDestinatario',
  'RUA DO DESTINATÁRIO': 'ruaDestinatario',
  'CEP DO DESTINATÁRIO': 'cepDestinatario',
  'COMPLEMENTO DO DESTINATÁRIO': 'complementoDestinatario',
  'BAIRRO DO DESTINATÁRIO': 'bairroDestinatario',
  'NÚMERO DO ENDEREÇO DO DESTINATÁRIO': 'numeroEnderecoDestinatario',
  'DATA ESTIMADA DE CHEGADA': 'dataEstimadaChegada',
  'CÓDIGO DO AFILIADO': 'codigoAfiliado',
  'NOME DO AFILIADO': 'nomeAfiliado',
  'E-MAIL DO AFILIADO': 'emailAfiliado',
  'DOCUMENTO DO AFILIADO': 'documentoAfiliado',
  'DATA DE RECEBIMENTO': 'dataRecebimento',
  'Data_Negociacao': 'dataNegociacao',
  'FormaPagamento': 'formaPagamento',
  'DOCUMENTO CLIENTE': 'documentoCliente',
  'Parcial': 'parcial',
  'Pagamento_Parcial': 'pagamentoParcial',
  'FormaPagamentoParcial': 'formaPagamentoParcial',
  'DataPagamentoParcial': 'dataPagamentoParcial',
};

async function testSingleRow() {
  const targetId = process.argv[2] || 'VENDA008';
  console.log(`\n=== TESTING SINGLE ROW (${targetId}) ===\n`);
  
  // Read the CSV file
  const csvPath = path.join(__dirname, '../test-import.csv');
  const csvContent = fs.readFileSync(csvPath, 'utf-8');
  
  // Parse CSV
  const parseResult = Papa.parse(csvContent, {
    header: true,
    skipEmptyLines: true,
  });
  
  // Get the row specified in command line or default to VENDA008
  const rows = parseResult.data as any[];
  const targetRow = rows.find((row: any) => row['ID Venda'] === targetId);
  
  if (!targetRow) {
    console.error(`Could not find ${targetId} in CSV`);
    return;
  }
  
  console.log('=== ROW DATA ===');
  console.log('ID Venda:', targetRow['ID Venda']);
  console.log('Situação Venda:', targetRow['Situação Venda']);
  console.log('Valor Venda:', targetRow['Valor Venda']);
  
  // Map the row
  const mappedOrder = mapRowToOrder(targetRow);
  console.log('\n=== MAPPED ORDER ===');
  console.log('idVenda:', mappedOrder.idVenda);
  console.log('situacaoVenda:', mappedOrder.situacaoVenda);
  console.log('valorVenda:', mappedOrder.valorVenda);
  
  // Test import
  await testImport(mappedOrder);
}

function mapRowToOrder(row: any): any {
  const order: any = {};
  
  Object.keys(DEFAULT_MAPPINGS).forEach(csvField => {
    const systemField = DEFAULT_MAPPINGS[csvField];
    const value = row[csvField];
    
    if (value && value.trim() !== '') {
      // Handle monetary values
      if (['valorVenda', 'valorRecebido', 'pagamentoParcial'].includes(systemField)) {
        // Handle both formats: "350.00" and "1.350,00"
        let cleanValue = value.replace('R$', '').trim();
        
        // If value contains comma, it's Brazilian format (1.350,00)
        if (cleanValue.includes(',')) {
          cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
        }
        // Otherwise it's already in decimal format (350.00)
        
        const numValue = parseFloat(cleanValue);
        order[systemField] = numValue;
      }
      // Handle boolean values
      else if (systemField === 'parcial') {
        order[systemField] = value.toLowerCase() === 'sim' || value === '1' || value === 'true';
      }
      // Handle dates
      else if (systemField.includes('data') || systemField.includes('Data')) {
        order[systemField] = value;
      }
      // Default to string
      else {
        order[systemField] = value;
      }
    }
  });
  
  return order;
}

async function testImport(orderData: any) {
  console.log('\n=== TESTING IMPORT ===');
  
  try {
    // Login first
    console.log('Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
    }, {
      headers: {
        'x-tenant-id': TENANT_ID
      }
    });
    
    const token = loginResponse.data.access_token;
    console.log('Login successful');
    
    // Test import
    console.log('\nImporting order...');
    const importResponse = await axios.post(`${API_URL}/orders/import`, orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': TENANT_ID,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n=== IMPORT RESPONSE ===');
    console.log('Status:', importResponse.status);
    console.log('Order Number:', importResponse.data.orderNumber);
    console.log('Order Status:', importResponse.data.status);
    console.log('Customer:', importResponse.data.customerName);
    console.log('Total:', importResponse.data.total);
    
    // Check if status was mapped correctly
    const expectedStatusMap: { [key: string]: string } = {
      'Aguardando Pagamento': 'PagamentoPendente',
      'Paga': 'Completo',
      'Cancelada': 'Cancelado',
    };
    
    const expectedStatus = expectedStatusMap[orderData.situacaoVenda];
    if (expectedStatus && importResponse.data.status === expectedStatus) {
      console.log('\n✅ STATUS MAPPING WORKING CORRECTLY!');
      console.log(`   "${orderData.situacaoVenda}" -> "${importResponse.data.status}"`);
    } else if (expectedStatus && importResponse.data.status !== expectedStatus) {
      console.log('\n❌ STATUS MAPPING FAILED!');
      console.log(`   Expected "${expectedStatus}" but got "${importResponse.data.status}"`);
    }
    
  } catch (error: any) {
    console.error('\n=== IMPORT ERROR ===');
    console.error('Status:', error.response?.status);
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
testSingleRow().catch(console.error);