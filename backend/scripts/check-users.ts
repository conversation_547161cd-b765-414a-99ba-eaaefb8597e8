import axios from 'axios';

async function checkUsers() {
  try {
    // First login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post('https://zencash-production.up.railway.app/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      headers: {
        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
      }
    });

    const token = loginResponse.data.access_token;
    console.log('Login successful');

    // Get users list
    console.log('\n2. Fetching users...');
    try {
      const usersResponse = await axios.get('https://zencash-production.up.railway.app/api/v1/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
        }
      });

      console.log('Users found:', usersResponse.data);
      
      if (Array.isArray(usersResponse.data)) {
        console.log(`\nTotal users: ${usersResponse.data.length}`);
        usersResponse.data.forEach((user: any) => {
          console.log(`- ${user.email} (${user.role}) - Active: ${user.active}`);
        });
      }
    } catch (error: any) {
      console.error('Error fetching users:', error.response?.data || error.message);
    }

    // Check anti-fraud access
    console.log('\n3. Testing anti-fraud access...');
    try {
      const antifraudResponse = await axios.get('https://zencash-production.up.railway.app/api/v1/antifraud/duplicates/review-queue', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
        }
      });

      console.log('Anti-fraud access successful');
    } catch (error: any) {
      console.error('Anti-fraud access error:', error.response?.data || error.message);
    }

  } catch (error: any) {
    console.error('Error:', error.message);
  }
}

checkUsers();