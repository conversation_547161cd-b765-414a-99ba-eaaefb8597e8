const { PrismaClient } = require('@prisma/client');

async function checkDatabaseState() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking database state...\n');
    
    // Check tables
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('📋 Tables in database:');
    tables.forEach(t => console.log(`  - ${t.table_name}`));
    
    // Check if Order table exists and has data
    try {
      const orderCount = await prisma.order.count();
      console.log(`\n📦 Orders in database: ${orderCount}`);
    } catch (e) {
      console.log('\n❌ Order table does not exist or is inaccessible');
    }
    
    // Check if WebhookLog table exists and has data
    try {
      const webhookCount = await prisma.webhookLog.count();
      console.log(`🔗 Webhook logs in database: ${webhookCount}`);
    } catch (e) {
      console.log('❌ WebhookLog table does not exist or is inaccessible');
    }
    
    // Check if User table exists and has data
    try {
      const userCount = await prisma.user.count();
      console.log(`👤 Users in database: ${userCount}`);
    } catch (e) {
      console.log('❌ User table does not exist or is inaccessible');
    }
    
    // Check if Zap table exists and has data
    try {
      const zapCount = await prisma.zap.count();
      console.log(`💬 Zaps in database: ${zapCount}`);
    } catch (e) {
      console.log('❌ Zap table does not exist or is inaccessible');
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseState();