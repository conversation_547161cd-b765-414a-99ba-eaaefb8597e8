import { PrismaClient, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import axios from 'axios';

const prisma = new PrismaClient();

async function testAntifraud() {
  try {
    console.log('🧪 Testando sistema Anti-Fraud via API...\n');

    // Get test user
    const testUser = await prisma.user.findFirst({
      where: {
        role: Role.VENDEDOR,
        active: true
      }
    });

    if (!testUser) {
      console.error('❌ Nenhum vendedor ativo encontrado');
      return;
    }

    // Login to get token
    const apiUrl = process.env.API_URL || 'http://localhost:3000/api/v1';
    console.log('🔐 Fazendo login...');
    const loginResponse = await axios.post(`${apiUrl}/auth/login`, {
      email: testUser.email,
      password: 'senha123' // Assumindo senha padrão para teste
    });

    const token = loginResponse.data.access_token;
    console.log('✅ Login realizado com sucesso\n');

    // Create test orders
    const testData = {
      customerName: 'Pedro Teste Duplicata',
      customerPhone: '11777666555',
      customerCPF: '11122233344',
      items: [{
        productId: 'test-product-id',
        productName: 'Produto Teste',
        quantity: 1,
        unitPrice: 150
      }],
      address: {
        cep: '01310100',
        street: 'Avenida Paulista',
        number: '1000',
        neighborhood: 'Bela Vista',
        city: 'São Paulo',
        state: 'SP'
      }
    };

    console.log('📝 Criando primeiro pedido...');
    const order1Response = await axios.post(`${apiUrl}/orders`, testData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': testUser.tenantId
      }
    });

    const order1 = order1Response.data;
    console.log(`✅ Pedido 1 criado: ${order1.orderNumber}`);
    
    // Check the order details from database
    const dbOrder1 = await prisma.order.findUnique({
      where: { id: order1.id }
    });
    
    console.log(`   - CPF salvo? ${dbOrder1?.customerCPF ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - É duplicata? ${dbOrder1?.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - Score de risco: ${dbOrder1?.riskScore || 'N/A'}`);
    console.log(`   - Nível de risco: ${dbOrder1?.riskLevel || 'N/A'}\n`);

    // Wait before creating duplicate
    console.log('⏳ Aguardando 3 segundos...\n');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('📝 Criando segundo pedido com mesmos dados...');
    const order2Response = await axios.post(`${apiUrl}/orders`, testData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': testUser.tenantId
      }
    });

    const order2 = order2Response.data;
    console.log(`✅ Pedido 2 criado: ${order2.orderNumber}`);
    
    // Check the order details from database
    const dbOrder2 = await prisma.order.findUnique({
      where: { id: order2.id }
    });
    
    console.log(`   - CPF salvo? ${dbOrder2?.customerCPF ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - É duplicata? ${dbOrder2?.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - Score duplicata: ${dbOrder2?.duplicateMatchScore || 'N/A'}`);
    console.log(`   - Score de risco: ${dbOrder2?.riskScore || 'N/A'}`);
    console.log(`   - Nível de risco: ${dbOrder2?.riskLevel || 'N/A'}`);
    console.log(`   - Requer revisão? ${dbOrder2?.requiresReview ? '✅ SIM' : '❌ NÃO'}`);

    console.log('\n✅ Teste concluído!');

  } catch (error: any) {
    console.error('❌ Erro durante o teste:', error.response?.data || error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testAntifraud();