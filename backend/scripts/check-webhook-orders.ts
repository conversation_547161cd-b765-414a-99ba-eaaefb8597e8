import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkWebhookOrders() {
  console.log('🔍 Checking recent webhook orders...\n');
  
  try {
    // Get the most recent orders
    const recentOrders = await prisma.order.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        status: true,
        createdAt: true,
        tenantId: true,
      }
    });

    console.log(`📦 Found ${recentOrders.length} recent orders:\n`);
    recentOrders.forEach((order, index) => {
      console.log(`${index + 1}. Order ${order.orderNumber || order.id}`);
      console.log(`   Customer: ${order.customerName}`);
      console.log(`   Status: ${order.status}`);
      console.log(`   Created: ${order.createdAt.toLocaleString()}`);
      console.log(`   Tenant: ${order.tenantId || 'default'}`);
      console.log('');
    });

    // Get recent webhook logs
    console.log('\n📋 Recent webhook logs:\n');
    const webhookLogs = await prisma.webhookLog.findMany({
      take: 10,
      orderBy: { receivedAt: 'desc' },
      select: {
        id: true,
        status: true,
        orderId: true,
        isNewOrder: true,
        receivedAt: true,
        error: true,
        identifierField: true,
        identifierValue: true,
      }
    });

    webhookLogs.forEach((log, index) => {
      console.log(`${index + 1}. Webhook ${log.id.slice(0, 8)}...`);
      console.log(`   Status: ${log.status}`);
      console.log(`   Order ID: ${log.orderId || 'None'}`);
      console.log(`   New Order: ${log.isNewOrder}`);
      console.log(`   Identifier: ${log.identifierField} = ${log.identifierValue}`);
      if (log.error) {
        console.log(`   Error: ${log.error}`);
      }
      console.log(`   Received: ${log.receivedAt.toLocaleString()}`);
      console.log('');
    });

    // Check if webhook orders exist
    console.log('\n🔗 Checking webhook order links:\n');
    const webhookOrderIds = webhookLogs
      .filter(log => log.orderId)
      .map(log => log.orderId as string);

    if (webhookOrderIds.length > 0) {
      const existingOrders = await prisma.order.findMany({
        where: { id: { in: webhookOrderIds } },
        select: { id: true, orderNumber: true }
      });

      const existingOrderIds = new Set(existingOrders.map(o => o.id));
      
      webhookOrderIds.forEach(orderId => {
        if (existingOrderIds.has(orderId)) {
          console.log(`✅ Order ${orderId} exists in database`);
        } else {
          console.log(`❌ Order ${orderId} NOT FOUND in database`);
        }
      });
    }

  } catch (error) {
    console.error('Error checking orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkWebhookOrders().catch(console.error);