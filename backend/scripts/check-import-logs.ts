import { PrismaClient, OrderStatus } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

async function checkImportLogs() {
  console.log('=== Checking Import Logs ===\n');

  try {
    // Find orders that look like they were imported (have specific patterns)
    const importedOrders = await prisma.order.findMany({
      where: {
        OR: [
          { orderNumber: { startsWith: 'sal' } }, // Common prefix in imports
          { observation: { not: null } }, // Imported orders often have historico -> observation
          { 
            createdAt: {
              equals: new Date('2025-12-01T21:00:00.000Z') // Common import date from CSV
            }
          }
        ]
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        observation: true,
        createdAt: true,
        statusHistory: {
          orderBy: { changedAt: 'asc' },
          select: {
            previousStatus: true,
            newStatus: true,
            changedAt: true,
          },
        },
      },
      take: 5,
    });

    console.log(`Found ${importedOrders.length} potentially imported orders:\n`);

    importedOrders.forEach(order => {
      console.log(`Order ${order.orderNumber}:`);
      console.log(`  - Current Status: ${order.status}`);
      console.log(`  - Created At: ${order.createdAt}`);
      console.log(`  - Observation: ${order.observation || 'None'}`);
      
      if (order.statusHistory.length > 0) {
        const firstHistory = order.statusHistory[0];
        console.log(`  - Initial Status History: ${firstHistory.previousStatus} → ${firstHistory.newStatus}`);
        
        // Check if status was ever different from Analise
        const nonAnaliseHistory = order.statusHistory.find(h => 
          h.newStatus !== OrderStatus.Analise || h.previousStatus !== OrderStatus.Analise
        );
        
        if (nonAnaliseHistory) {
          console.log(`  ⚠️  Found non-Analise status in history: ${nonAnaliseHistory.previousStatus} → ${nonAnaliseHistory.newStatus}`);
        }
      }
      console.log('');
    });

    // Check if there's a pattern in the observation field
    console.log('\n=== Checking Observation Patterns ===');
    const ordersWithObservation = await prisma.order.findMany({
      where: {
        observation: { not: null },
        status: OrderStatus.Analise,
      },
      select: {
        orderNumber: true,
        observation: true,
      },
      take: 5,
    });

    console.log('Orders with observations (historico from import):');
    ordersWithObservation.forEach(order => {
      console.log(`  - ${order.orderNumber}: "${order.observation?.substring(0, 50)}..."`);
    });

    // Look for any order that was successfully imported with a different status
    console.log('\n=== Looking for Successfully Imported Orders with Non-Analise Status ===');
    const nonAnaliseOrders = await prisma.order.findMany({
      where: {
        status: { not: OrderStatus.Analise },
        orderNumber: { startsWith: 'sal' }, // Common import prefix
      },
      select: {
        orderNumber: true,
        status: true,
        createdAt: true,
      },
    });

    if (nonAnaliseOrders.length > 0) {
      console.log(`Found ${nonAnaliseOrders.length} imported orders with non-Analise status:`);
      nonAnaliseOrders.forEach(order => {
        console.log(`  - ${order.orderNumber}: ${order.status} (created ${order.createdAt})`);
      });
    } else {
      console.log('❌ No imported orders found with non-Analise status!');
      console.log('This confirms the status mapping is not working during import.');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkImportLogs().catch(console.error);