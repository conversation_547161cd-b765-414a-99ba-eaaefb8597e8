import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function fixOrderIds() {
  console.log('=== Advanced Order ID Fix ===\n');

  try {
    // Find the order with the invalid ID
    const invalidIdOrder = await prisma.$queryRaw`
      SELECT * FROM "Order" WHERE id = 'sal47952'
    ` as any[];

    if (invalidIdOrder.length === 0) {
      console.log('No order found with ID "sal47952"');
      return;
    }

    const order = invalidIdOrder[0];
    console.log('Found order with invalid ID:');
    console.log(`  Current ID: ${order.id}`);
    console.log(`  Order Number: ${order.orderNumber}`);
    console.log(`  Customer: ${order.customerName}`);
    console.log(`  Status: ${order.status}`);
    console.log(`  Created: ${order.createdAt}`);

    // Generate a new UUID for this order
    const newId = randomUUID();
    console.log(`\nNew UUID to be assigned: ${newId}`);

    // Alternative approach: Create a new order with the correct ID and copy all data
    console.log('\nCreating new order with valid UUID...');

    await prisma.$transaction(async (tx) => {
      // First, let's check what related data exists
      const orderItems = await tx.$queryRaw`
        SELECT COUNT(*) as count FROM "OrderItem" WHERE "orderId" = 'sal47952'
      ` as any[];
      console.log(`Found ${orderItems[0].count} order items`);

      // Create the new order by copying all fields
      await tx.$executeRaw`
        INSERT INTO "Order" (
          id, "orderNumber", "customerName", "customerPhone", status, total,
          "sellerId", "tenantId", "createdAt", "updatedAt", "commissionApprovalStatus",
          "paymentReceivedAmount", "paymentReceivedDate", "commissionApproved",
          "customerId", "collectorId", "nextPaymentDate", "lastContactDate", "zapId",
          "customerCPF", "customerCPFHash", "fullAddress", "isDuplicate", "duplicateStatus",
          "duplicateMatchScore", "duplicateCheckVersion", "originalOrderIds", "reviewedBy",
          "reviewedByName", "reviewedByRole", "reviewedAt", "reviewDecision", "reviewDuration",
          "observation", "riskScore", "riskLevel", "riskFactors", "requiresReview",
          "fraudCheckCompletedAt", "deletedAt", "deletedBy"
        )
        SELECT 
          ${newId}, "orderNumber", "customerName", "customerPhone", status, total,
          "sellerId", "tenantId", "createdAt", "updatedAt", "commissionApprovalStatus",
          "paymentReceivedAmount", "paymentReceivedDate", "commissionApproved",
          "customerId", "collectorId", "nextPaymentDate", "lastContactDate", "zapId",
          "customerCPF", "customerCPFHash", "fullAddress", "isDuplicate", "duplicateStatus",
          "duplicateMatchScore", "duplicateCheckVersion", "originalOrderIds", "reviewedBy",
          "reviewedByName", "reviewedByRole", "reviewedAt", "reviewDecision", "reviewDuration",
          "observation", "riskScore", "riskLevel", "riskFactors", "requiresReview",
          "fraudCheckCompletedAt", "deletedAt", "deletedBy"
        FROM "Order"
        WHERE id = 'sal47952'
      `;
      console.log('Created new order with valid UUID');

      // Copy OrderItems
      const itemsCopied = await tx.$executeRaw`
        INSERT INTO "OrderItem" (id, "orderId", "productVariationId", "productId", "productName", quantity, "unitPrice", "createdAt", "updatedAt")
        SELECT gen_random_uuid(), ${newId}, "productVariationId", "productId", "productName", quantity, "unitPrice", "createdAt", "updatedAt"
        FROM "OrderItem"
        WHERE "orderId" = 'sal47952'
      `;
      console.log(`Copied ${itemsCopied} order items`);

      // Copy OrderStatusHistory
      const historyCount = await tx.$executeRaw`
        INSERT INTO "OrderStatusHistory" (id, "orderId", "previousStatus", "newStatus", "changedAt", "changedById")
        SELECT gen_random_uuid(), ${newId}, "previousStatus", "newStatus", "changedAt", "changedById"
        FROM "OrderStatusHistory"
        WHERE "orderId" = 'sal47952'
      `;
      console.log(`Copied ${historyCount} status history records`);

      // Update WebhookLog to point to new order
      const webhookLogUpdated = await tx.$executeRaw`
        UPDATE "WebhookLog" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${webhookLogUpdated} webhook log records`);

      // Delete the old order (cascade will handle related records)
      await tx.$executeRaw`
        DELETE FROM "Order" WHERE id = 'sal47952'
      `;
      console.log('Deleted old order with invalid ID');
    });

    console.log('\n✅ Successfully fixed order ID!');

    // Verify the fix
    const fixedOrder = await prisma.order.findUnique({
      where: { id: newId },
      include: {
        items: true,
        statusHistory: true,
        seller: {
          select: { name: true, email: true }
        }
      }
    });

    if (fixedOrder) {
      console.log('\nVerification - Order now has valid UUID:');
      console.log(`  ID: ${fixedOrder.id}`);
      console.log(`  Order Number: ${fixedOrder.orderNumber}`);
      console.log(`  Customer: ${fixedOrder.customerName}`);
      console.log(`  Items: ${fixedOrder.items.length}`);
      console.log(`  Status History: ${fixedOrder.statusHistory.length}`);
      console.log(`  Seller: ${fixedOrder.seller.name}`);
      console.log('\n✅ Order should now appear in the UI!');
    }

  } catch (error) {
    console.error('Error fixing order IDs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixOrderIds().catch(console.error);