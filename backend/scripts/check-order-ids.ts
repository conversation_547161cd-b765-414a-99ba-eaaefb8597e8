import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// UUID v4 regex pattern
const UUID_V4_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

async function checkOrderIds() {
  console.log('=== Order ID Integrity Check ===\n');

  try {
    // 1. Check for the specific order
    const specificOrderId = '846dced7-bcec-4b6c-9979-2b51d4edae15';
    console.log(`1. Checking for specific order ID: ${specificOrderId}`);
    
    const specificOrder = await prisma.order.findUnique({
      where: { id: specificOrderId },
      include: {
        items: true,
        seller: {
          select: { name: true, email: true }
        }
      }
    });

    if (specificOrder) {
      console.log('✅ Order found!');
      console.log(`   - Order Number: ${specificOrder.orderNumber || 'N/A'}`);
      console.log(`   - Customer: ${specificOrder.customerName}`);
      console.log(`   - Status: ${specificOrder.status}`);
      console.log(`   - Total: ${specificOrder.total}`);
      console.log(`   - Created: ${specificOrder.createdAt}`);
      console.log(`   - Seller: ${specificOrder.seller.name} (${specificOrder.seller.email})`);
      console.log(`   - Items: ${specificOrder.items.length}`);
    } else {
      console.log('❌ Order NOT found');
    }

    // 2. Get all orders and check their IDs
    console.log('\n2. Checking all order IDs for validity...');
    
    const allOrders = await prisma.order.findMany({
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        createdAt: true,
        tenantId: true,
        status: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`   Total orders in database: ${allOrders.length}`);

    // Check for non-UUID IDs
    const nonUuidOrders = allOrders.filter(order => !UUID_V4_REGEX.test(order.id));
    
    if (nonUuidOrders.length > 0) {
      console.log(`\n   ⚠️  Found ${nonUuidOrders.length} orders with non-UUID IDs:`);
      nonUuidOrders.forEach(order => {
        console.log(`      - ID: ${order.id}`);
        console.log(`        Order Number: ${order.orderNumber || 'N/A'}`);
        console.log(`        Customer: ${order.customerName}`);
        console.log(`        Status: ${order.status}`);
        console.log(`        Created: ${order.createdAt}`);
      });
    } else {
      console.log('   ✅ All order IDs are valid UUIDs');
    }

    // 3. Check for duplicate order numbers
    console.log('\n3. Checking for duplicate order numbers...');
    
    const orderNumberGroups = allOrders.reduce((acc, order) => {
      if (order.orderNumber) {
        if (!acc[order.orderNumber]) {
          acc[order.orderNumber] = [];
        }
        acc[order.orderNumber].push(order);
      }
      return acc;
    }, {} as Record<string, typeof allOrders>);

    const duplicates = Object.entries(orderNumberGroups)
      .filter(([_, orders]) => orders.length > 1);

    if (duplicates.length > 0) {
      console.log(`   ⚠️  Found ${duplicates.length} duplicate order numbers:`);
      duplicates.forEach(([orderNumber, orders]) => {
        console.log(`\n      Order Number: ${orderNumber} (${orders.length} occurrences)`);
        orders.forEach(order => {
          console.log(`         - ID: ${order.id}`);
          console.log(`           Customer: ${order.customerName}`);
          console.log(`           Created: ${order.createdAt}`);
          console.log(`           Tenant: ${order.tenantId}`);
        });
      });
    } else {
      console.log('   ✅ No duplicate order numbers found');
    }

    // 4. Check recent orders that might be from webhooks
    console.log('\n4. Checking recent orders (last 24 hours)...');
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const recentOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: yesterday
        }
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        createdAt: true,
        zapId: true,
        status: true,
        total: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`   Found ${recentOrders.length} orders created in the last 24 hours:`);
    recentOrders.forEach(order => {
      const idValid = UUID_V4_REGEX.test(order.id);
      console.log(`\n      ${idValid ? '✅' : '❌'} ID: ${order.id}`);
      console.log(`         Order Number: ${order.orderNumber || 'N/A'}`);
      console.log(`         Customer: ${order.customerName}`);
      console.log(`         Status: ${order.status}`);
      console.log(`         Total: ${order.total}`);
      console.log(`         Zap ID: ${order.zapId || 'N/A'}`);
      console.log(`         Created: ${order.createdAt}`);
    });

    // 5. Check webhook logs for order creation attempts
    console.log('\n5. Checking webhook logs for order creation attempts...');
    
    const webhookLogs = await prisma.webhookLog.findMany({
      where: {
        AND: [
          { orderId: { not: null } },
          { receivedAt: { gte: yesterday } }
        ]
      },
      select: {
        id: true,
        endpoint: true,
        orderId: true,
        isNewOrder: true,
        identifierField: true,
        identifierValue: true,
        status: true,
        error: true,
        receivedAt: true
      },
      orderBy: {
        receivedAt: 'desc'
      },
      take: 20
    });

    console.log(`   Found ${webhookLogs.length} webhook logs with order IDs:`);
    
    for (const log of webhookLogs) {
      console.log(`\n      Webhook Log ID: ${log.id}`);
      console.log(`         Endpoint: ${log.endpoint}`);
      console.log(`         Order ID: ${log.orderId}`);
      console.log(`         New Order: ${log.isNewOrder}`);
      console.log(`         Identifier: ${log.identifierField} = ${log.identifierValue}`);
      console.log(`         Status: ${log.status}`);
      if (log.error) {
        console.log(`         Error: ${log.error}`);
      }
      console.log(`         Received: ${log.receivedAt}`);
      
      // Check if the order exists
      if (log.orderId) {
        const orderExists = await prisma.order.findUnique({
          where: { id: log.orderId },
          select: { id: true }
        });
        console.log(`         Order Exists: ${orderExists ? '✅ Yes' : '❌ No'}`);
      }
    }

    // 6. Summary statistics
    console.log('\n=== Summary ===');
    console.log(`Total Orders: ${allOrders.length}`);
    console.log(`Orders with Invalid IDs: ${nonUuidOrders.length}`);
    console.log(`Duplicate Order Numbers: ${duplicates.length}`);
    console.log(`Recent Orders (24h): ${recentOrders.length}`);
    
    // Group by tenant
    const tenantCounts = allOrders.reduce((acc, order) => {
      acc[order.tenantId] = (acc[order.tenantId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\nOrders by Tenant:');
    Object.entries(tenantCounts).forEach(([tenantId, count]) => {
      console.log(`   ${tenantId}: ${count} orders`);
    });

  } catch (error) {
    console.error('Error checking order IDs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkOrderIds().catch(console.error);