import axios from 'axios';

const API_URL = 'http://localhost:3001';

// You'll need to replace this with a valid token from your application
// You can get this from the browser's localStorage or by logging in via the API
const AUTH_TOKEN = 'YOUR_AUTH_TOKEN_HERE';

async function testOrdersEndpoint() {
  console.log('Testing Orders API Endpoint...\n');

  try {
    // Test 1: Get all orders without filters
    console.log('1. GET ALL ORDERS (no filters):');
    try {
      const response1 = await axios.get(`${API_URL}/orders`, {
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`
        }
      });
      console.log(`   Status: ${response1.status}`);
      console.log(`   Total orders: ${response1.data.length}`);
      if (response1.data.length > 0) {
        console.log('   Order statuses:', response1.data.map((o: any) => o.status));
      }
    } catch (error: any) {
      console.log('   Error:', error.response?.status, error.response?.data || error.message);
    }

    // Test 2: Get orders with Analise status filter
    console.log('\n2. GET ORDERS WITH ANALISE STATUS:');
    try {
      const response2 = await axios.get(`${API_URL}/orders`, {
        params: { status: 'Analise' },
        headers: {
          Authorization: `Bearer ${AUTH_TOKEN}`
        }
      });
      console.log(`   Status: ${response2.status}`);
      console.log(`   Orders with Analise status: ${response2.data.length}`);
      if (response2.data.length > 0) {
        response2.data.forEach((order: any) => {
          console.log(`   - ${order.orderNumber} | ${order.customerName} | Status: ${order.status}`);
        });
      }
    } catch (error: any) {
      console.log('   Error:', error.response?.status, error.response?.data || error.message);
    }

    // Test 3: Test with different statuses
    console.log('\n3. TESTING OTHER STATUS FILTERS:');
    const statuses = ['Separacao', 'Transito', 'Completo', 'Cancelado'];
    for (const status of statuses) {
      try {
        const response = await axios.get(`${API_URL}/orders`, {
          params: { status },
          headers: {
            Authorization: `Bearer ${AUTH_TOKEN}`
          }
        });
        console.log(`   ${status}: ${response.data.length} orders`);
      } catch (error: any) {
        console.log(`   ${status}: Error - ${error.response?.status || error.message}`);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

console.log('='.repeat(60));
console.log('IMPORTANT: Before running this script, you need to:');
console.log('1. Make sure the backend is running (npm run start:dev)');
console.log('2. Get a valid auth token from the application');
console.log('3. Replace AUTH_TOKEN in this script with your token');
console.log('');
console.log('To get a token:');
console.log('- Login to the application in your browser');
console.log('- Open Developer Tools (F12)');
console.log('- Go to Application/Storage -> Local Storage');
console.log('- Find and copy the "token" value');
console.log('='.repeat(60));
console.log('\nIf you have set the AUTH_TOKEN, uncomment the line below:\n');
// testOrdersEndpoint();