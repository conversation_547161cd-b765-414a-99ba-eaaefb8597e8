import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { AntifraudService } from '../src/antifraud/antifraud.service';
import { PrismaService } from '../src/prisma/prisma.service';
import { OrderStatus } from '@prisma/client';

async function fixAnaliseOrdersReview() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const antifraudService = app.get(AntifraudService);
  const prismaService = app.get(PrismaService);

  try {
    console.log('🔧 Corrigindo pedidos em ANÁLISE para aparecerem no Anti-Fraud...\n');

    // Buscar todos os pedidos em análise que não estão marcados para revisão
    const ordersToFix = await prismaService.order.findMany({
      where: {
        status: OrderStatus.Analise,
        requiresReview: false,
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        tenantId: true,
        riskScore: true
      }
    });

    console.log(`📊 Encontrados ${ordersToFix.length} pedidos em ANÁLISE sem marcação para revisão.\n`);

    if (ordersToFix.length === 0) {
      console.log('✅ Nenhum pedido precisa ser corrigido!');
      return;
    }

    // Processar cada pedido
    for (const order of ordersToFix) {
      console.log(`🔄 Processando pedido ${order.orderNumber || order.id}...`);
      
      try {
        // Recalcular o risco (a nova lógica vai marcar requiresReview = true para pedidos em Análise)
        await antifraudService.assessOrderRisk(order.id, order.tenantId);
        
        // Verificar o resultado
        const updatedOrder = await prismaService.order.findUnique({
          where: { id: order.id },
          select: {
            requiresReview: true,
            riskScore: true,
            riskLevel: true
          }
        });

        console.log(`   ✅ Pedido atualizado:`);
        console.log(`      - Score de Risco: ${updatedOrder?.riskScore}`);
        console.log(`      - Nível de Risco: ${updatedOrder?.riskLevel}`);
        console.log(`      - Requer Revisão: ${updatedOrder?.requiresReview ? 'SIM' : 'NÃO'}`);
        
      } catch (error) {
        console.error(`   ❌ Erro ao processar pedido ${order.orderNumber}:`, error);
      }
    }

    console.log('\n✅ Processo concluído!');
    console.log('   Os pedidos em ANÁLISE agora devem aparecer no dashboard Anti-Fraud.');

  } catch (error) {
    console.error('❌ Erro durante o processo:', error);
  } finally {
    await app.close();
  }
}

// Executar a correção
fixAnaliseOrdersReview();