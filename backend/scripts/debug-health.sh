#!/bin/bash

echo "🔍 Railway Health Check Debug Script"
echo "===================================="
echo ""

# Check environment variables
echo "📋 Environment Variables:"
echo "   PORT: ${PORT:-not set}"
echo "   NODE_ENV: ${NODE_ENV:-not set}"
echo "   API_PREFIX: ${API_PREFIX:-not set}"
echo "   DATABASE_URL: ${DATABASE_URL:+set (hidden)}"
echo ""

# Check if the app is running
echo "🔍 Checking if app is running..."
if pgrep -f "node dist/main" > /dev/null; then
    echo "   ✅ Node process is running"
else
    echo "   ❌ Node process not found"
fi
echo ""

# Try to curl localhost
echo "🔍 Testing localhost endpoints:"
echo ""

# Test root
echo "Testing GET http://localhost:${PORT:-3000}/"
curl -s -o /dev/null -w "Status: %{http_code}\n" http://localhost:${PORT:-3000}/ || echo "Failed to connect"
echo ""

# Test ping
echo "Testing GET http://localhost:${PORT:-3000}/ping"
curl -s http://localhost:${PORT:-3000}/ping || echo "Failed to connect"
echo ""

# Test root health
echo "Testing GET http://localhost:${PORT:-3000}/health"
curl -s http://localhost:${PORT:-3000}/health | jq . 2>/dev/null || curl -s http://localhost:${PORT:-3000}/health || echo "Failed to connect"
echo ""

# Test API health
echo "Testing GET http://localhost:${PORT:-3000}/api/v1/health"
curl -s http://localhost:${PORT:-3000}/api/v1/health | jq . 2>/dev/null || curl -s http://localhost:${PORT:-3000}/api/v1/health || echo "Failed to connect"
echo ""

# Check listening ports
echo "🔍 Listening ports:"
netstat -tlnp 2>/dev/null | grep LISTEN | grep node || ss -tlnp 2>/dev/null | grep node || echo "Could not check ports (permission denied)"
echo ""

echo "===================================="
echo "✅ Debug script completed"