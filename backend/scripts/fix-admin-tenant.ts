import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixAdminTenant() {
  try {
    console.log('🔧 Fixing <EMAIL> tenant association...\n');

    // Use the tenant ID from environment or default
    const tenantId = process.env.TENANT_ID || '28a833c0-c2a1-4498-85ca-b028f982ffb2';
    console.log('🏢 Using tenant ID:', tenantId);

    // Find the admin user
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
    });

    if (!adminUser) {
      console.error('❌ <NAME_EMAIL> not found');
      return;
    }

    console.log('📧 Found admin user:', adminUser.email);
    console.log('🏢 Current tenantId:', adminUser.tenantId || 'NOT SET');

    // Update the admin user with the correct tenantId
    const updatedUser = await prisma.user.update({
      where: { id: adminUser.id },
      data: { tenantId: tenantId },
    });

    console.log('✅ Admin user updated with tenantId:', updatedUser.tenantId);

    // Verify orders exist for this tenant
    const orderCount = await prisma.order.count({
      where: { tenantId: tenantId },
    });

    console.log(`\n📊 Total orders for tenant ${tenantId}: ${orderCount}`);

    // Show a sample of orders
    if (orderCount > 0) {
      const sampleOrders = await prisma.order.findMany({
        where: { tenantId: tenantId },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          status: true,
          total: true,
          createdAt: true,
        },
      });

      console.log('\n📦 Sample orders:');
      sampleOrders.forEach(order => {
        console.log(`  - ${order.orderNumber}: ${order.customerName} - R$ ${order.total} - ${order.status}`);
      });
    }

    // Also check and update other test users if they exist
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    console.log('\n🔄 Checking other users...');
    for (const email of testEmails) {
      const user = await prisma.user.findFirst({
        where: { email },
      });
      
      if (user && !user.tenantId) {
        await prisma.user.update({
          where: { id: user.id },
          data: { tenantId: tenantId },
        });
        console.log(`  ✅ Updated ${email} with tenantId`);
      } else if (user) {
        console.log(`  ✓ ${email} already has tenantId: ${user.tenantId}`);
      }
    }

    console.log('\n✨ Fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing admin tenant:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixAdminTenant().catch(console.error);