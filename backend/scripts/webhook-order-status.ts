import { PrismaClient } from '@prisma/client';
import { format, subHours } from 'date-fns';

const prisma = new PrismaClient();

async function webhookOrderStatus(hours: number = 24) {
  const since = subHours(new Date(), hours);
  
  console.log(`\n=== WEBHOOK ORDER CREATION STATUS (Last ${hours} hours) ===`);
  console.log(`Since: ${format(since, 'dd/MM/yyyy HH:mm:ss')}\n`);

  try {
    // Get webhook logs from the specified time period
    const webhookLogs = await prisma.webhookLog.findMany({
      where: {
        receivedAt: { gte: since }
      },
      orderBy: { receivedAt: 'desc' }
    });

    const total = webhookLogs.length;
    const successful = webhookLogs.filter(log => log.status === 'success').length;
    const failed = webhookLogs.filter(log => log.status === 'error').length;
    const ordersCreated = webhookLogs.filter(log => log.orderId && log.isNewOrder).length;
    const ordersUpdated = webhookLogs.filter(log => log.orderId && !log.isNewOrder).length;
    const noOrderCreated = webhookLogs.filter(log => !log.orderId).length;

    console.log('SUMMARY:');
    console.log('--------');
    console.log(`Total Webhooks Received: ${total}`);
    console.log(`  ✅ Successful: ${successful}`);
    console.log(`  ❌ Failed: ${failed}`);
    console.log(`  📦 New Orders Created: ${ordersCreated}`);
    console.log(`  🔄 Orders Updated: ${ordersUpdated}`);
    console.log(`  ⚠️  No Order Created: ${noOrderCreated}`);

    // Show recent successes
    console.log('\nRECENT SUCCESSFUL ORDER CREATIONS (Last 10):');
    console.log('---------------------------------------------');
    
    const successfulCreations = webhookLogs
      .filter(log => log.orderId && log.isNewOrder && log.status === 'success')
      .slice(0, 10);

    for (const log of successfulCreations) {
      const order = await prisma.order.findUnique({
        where: { id: log.orderId! },
        select: {
          orderNumber: true,
          customerName: true,
          total: true,
          status: true
        }
      });

      if (order) {
        console.log(`${format(log.receivedAt, 'HH:mm:ss')} - Order: ${order.orderNumber || log.orderId}`);
        console.log(`  Customer: ${order.customerName}`);
        console.log(`  Total: R$ ${order.total}`);
        console.log(`  Status: ${order.status}`);
        console.log(`  Identifier: ${log.identifierField} = ${log.identifierValue}`);
      }
    }

    // Show recent failures
    console.log('\nRECENT FAILURES (Last 10):');
    console.log('---------------------------');
    
    const failures = webhookLogs
      .filter(log => log.status === 'error')
      .slice(0, 10);

    for (const log of failures) {
      console.log(`${format(log.receivedAt, 'HH:mm:ss')} - ${log.endpoint}`);
      console.log(`  Error: ${log.error}`);
      if (log.identifierValue) {
        console.log(`  Attempted Identifier: ${log.identifierField} = ${log.identifierValue}`);
      }
    }

    // Check mapping status
    const mappingCount = await prisma.webhookMapping.count({ where: { isActive: true } });
    
    console.log('\nCONFIGURATION STATUS:');
    console.log('---------------------');
    console.log(`Active Webhook Mappings: ${mappingCount}`);
    
    if (mappingCount === 0) {
      console.log('⚠️  WARNING: No active mappings! Webhooks cannot create orders.');
    }

    // Show order creation rate
    if (total > 0) {
      const successRate = ((successful / total) * 100).toFixed(1);
      const creationRate = ((ordersCreated / total) * 100).toFixed(1);
      
      console.log('\nPERFORMANCE METRICS:');
      console.log('--------------------');
      console.log(`Success Rate: ${successRate}%`);
      console.log(`Order Creation Rate: ${creationRate}%`);
    }

  } catch (error) {
    console.error('Error checking webhook status:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Check if hours argument was provided
const hours = process.argv[2] ? parseInt(process.argv[2]) : 24;

webhookOrderStatus(hours)
  .then(() => {
    console.log('\n✅ Done');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error:', error);
    process.exit(1);
  });