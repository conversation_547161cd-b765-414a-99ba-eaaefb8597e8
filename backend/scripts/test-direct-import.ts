import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

async function testDirectImport() {
  console.log('=== Testing Direct Import with Status ===\n');

  const API_URL = process.env.API_URL || 'http://localhost:3000';
  
  try {
    // First, login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'senha123',
    });

    const token = loginResponse.data.access_token;
    console.log('✅ Login successful\n');

    // Prepare test import data
    const importData = {
      idVenda: `TEST-IMPORT-${Date.now()}`,
      cliente: 'Test Import Customer',
      telefone: '11999999999',
      valorVenda: 150.00,
      dataVenda: '2025-01-09',
      situacaoVenda: 'Completo', // Explicitly set to Completo
      valorRecebido: 150.00,
      historico: 'Test import with Completo status',
      vendedor: 'Admin',
      operador: 'Admin',
    };

    console.log('2. Sending import request with data:');
    console.log(JSON.stringify(importData, null, 2));
    console.log('');

    // Send import request
    const importResponse = await axios.post(
      `${API_URL}/orders/import`,
      importData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('3. Import response:');
    console.log('  - Order ID:', importResponse.data.id);
    console.log('  - Order Number:', importResponse.data.orderNumber);
    console.log('  - Status:', importResponse.data.status);
    console.log('  - Expected Status: Completo');
    console.log('  - Match:', importResponse.data.status === 'Completo' ? '✅' : '❌');
    console.log('');

    // Query the order back
    console.log('4. Querying order back...');
    const getResponse = await axios.get(
      `${API_URL}/orders/${importResponse.data.id}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    console.log('Queried order:');
    console.log('  - Status:', getResponse.data.status);
    console.log('  - Status History:', JSON.stringify(getResponse.data.statusHistory, null, 2));

  } catch (error: any) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testDirectImport().catch(console.error);