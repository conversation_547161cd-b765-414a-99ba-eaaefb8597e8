import { PrismaClient } from '@prisma/client';
import { EncryptionUtil } from '../src/common/utils/encryption.util';
import { ConfigService } from '@nestjs/config';

const prisma = new PrismaClient();

async function testCPFDecryption() {
  try {
    console.log('🔍 Testando decryptação e mascaramento de CPF...\n');

    // Create encryption util instance
    const configService = new ConfigService({
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || 'your-32-character-encryption-key'
    });
    const encryptionUtil = new EncryptionUtil(configService);

    // Find orders with CPF
    const ordersWithCPF = await prisma.order.findMany({
      where: {
        customerCPF: {
          not: null
        }
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        customerCPF: true
      },
      take: 5
    });

    console.log(`📊 Encontrados ${ordersWithCPF.length} pedidos com CPF\n`);

    if (ordersWithCPF.length === 0) {
      console.log('⚠️  Nenhum pedido com CPF encontrado');
      return;
    }

    ordersWithCPF.forEach((order, index) => {
      console.log(`${index + 1}. Pedido ${order.orderNumber || order.id}`);
      console.log(`   Cliente: ${order.customerName}`);
      
      if (order.customerCPF) {
        try {
          // Try to decrypt
          const decryptedCPF = encryptionUtil.decrypt(order.customerCPF);
          console.log(`   CPF Descriptografado: ${decryptedCPF}`);
          
          // Mask CPF
          const maskedCPF = maskCPF(decryptedCPF);
          console.log(`   CPF Mascarado: ${maskedCPF}`);
        } catch (err) {
          console.log(`   ❌ Erro ao descriptografar CPF: ${err.message}`);
          console.log(`   CPF armazenado: ${order.customerCPF.substring(0, 20)}...`);
        }
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function maskCPF(cpf: string): string {
  if (!cpf || cpf.length < 11) return cpf;
  // Format: 123.***.***-45
  return `${cpf.substring(0, 3)}.***.**-${cpf.substring(9)}`;
}

// Run the test
testCPFDecryption();