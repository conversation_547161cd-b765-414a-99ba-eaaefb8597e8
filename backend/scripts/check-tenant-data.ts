import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkTenantData() {
  try {
    console.log('=== TENANT DATA ANALYSIS ===\n');

    // 1. Check what <NAME_EMAIL> user belongs to
    console.log('1. Checking <EMAIL> user:');
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      console.log(`   - User ID: ${adminUser.id}`);
      console.log(`   - Name: ${adminUser.name}`);
      console.log(`   - Role: ${adminUser.role}`);
      console.log(`   - Tenant ID: ${adminUser.tenantId}`);
      console.log(`   - Active: ${adminUser.active}`);
    } else {
      console.log('   - User <EMAIL> NOT FOUND');
    }

    // 2. List all unique tenants in the system
    console.log('\n2. All unique tenant IDs in the system:');
    
    // Get unique tenants from Users
    const userTenants = await prisma.user.findMany({
      select: { tenantId: true },
      distinct: ['tenantId']
    });
    
    // Get unique tenants from Orders
    const orderTenants = await prisma.order.findMany({
      select: { tenantId: true },
      distinct: ['tenantId'],
      where: { deletedAt: null }
    });
    
    // Combine and deduplicate
    const allTenantIds = new Set([
      ...userTenants.map(u => u.tenantId),
      ...orderTenants.map(o => o.tenantId)
    ]);
    
    console.log(`   - Total unique tenants: ${allTenantIds.size}`);
    allTenantIds.forEach(tenantId => {
      console.log(`   - ${tenantId}`);
    });

    // 3. Show tenant statistics
    console.log('\n3. Tenant statistics:');
    for (const tenantId of allTenantIds) {
      console.log(`\n   Tenant: ${tenantId}`);
      
      // Count users
      const userCount = await prisma.user.count({
        where: { tenantId }
      });
      console.log(`     - Users: ${userCount}`);
      
      // Count orders
      const orderCount = await prisma.order.count({
        where: { tenantId, deletedAt: null }
      });
      console.log(`     - Active Orders: ${orderCount}`);
      
      // Count deleted orders
      const deletedOrderCount = await prisma.order.count({
        where: { 
          tenantId, 
          deletedAt: { not: null } 
        }
      });
      console.log(`     - Deleted Orders: ${deletedOrderCount}`);
      
      // Count products
      const productCount = await prisma.product.count({
        where: { tenantId }
      });
      console.log(`     - Products: ${productCount}`);
      
      // Count customers
      const customerCount = await prisma.customer.count({
        where: { tenantId }
      });
      console.log(`     - Customers: ${customerCount}`);
    }

    // 4. Show recent orders per tenant
    console.log('\n4. Recent orders per tenant:');
    for (const tenantId of allTenantIds) {
      console.log(`\n   Tenant: ${tenantId}`);
      
      const recentOrders = await prisma.order.findMany({
        where: { tenantId, deletedAt: null },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          status: true,
          total: true,
          createdAt: true,
          seller: {
            select: {
              name: true,
              email: true
            }
          }
        }
      });
      
      if (recentOrders.length > 0) {
        recentOrders.forEach(order => {
          console.log(`     - Order #${order.orderNumber || order.id.substring(0, 8)}`);
          console.log(`       Customer: ${order.customerName}`);
          console.log(`       Status: ${order.status}`);
          console.log(`       Total: R$ ${order.total}`);
          console.log(`       Seller: ${order.seller.name} (${order.seller.email})`);
          console.log(`       Created: ${order.createdAt.toLocaleString()}`);
        });
      } else {
        console.log('     - No orders found');
      }
    }

    // 5. Check for potential tenant mismatches
    console.log('\n5. Checking for potential tenant mismatches:');
    
    // Check if orders have sellers from different tenants
    const orderSellerMismatches = await prisma.$queryRaw`
      SELECT o.id, o."orderNumber", o."tenantId" as order_tenant, 
             u."tenantId" as seller_tenant, u.email as seller_email
      FROM "Order" o
      JOIN "User" u ON o."sellerId" = u.id
      WHERE o."tenantId" != u."tenantId"
      AND o."deletedAt" IS NULL
    `;
    
    if (Array.isArray(orderSellerMismatches) && orderSellerMismatches.length > 0) {
      console.log('   - Found order-seller tenant mismatches:');
      (orderSellerMismatches as any[]).forEach(mismatch => {
        console.log(`     Order ${mismatch.orderNumber || mismatch.id}: order tenant=${mismatch.order_tenant}, seller tenant=${mismatch.seller_tenant} (${mismatch.seller_email})`);
      });
    } else {
      console.log('   - No order-seller tenant mismatches found');
    }

    // 6. If admin user exists, check orders visible to them
    if (adminUser) {
      console.log(`\n6. Orders <NAME_EMAIL> (tenant: ${adminUser.tenantId}):`);
      
      const adminTenantOrders = await prisma.order.count({
        where: { 
          tenantId: adminUser.tenantId,
          deletedAt: null 
        }
      });
      
      console.log(`   - Total orders in admin's tenant: ${adminTenantOrders}`);
      
      // Check if admin is seller of any orders
      const adminSellerOrders = await prisma.order.count({
        where: {
          sellerId: adminUser.id,
          deletedAt: null
        }
      });
      
      console.log(`   - Orders where admin is the seller: ${adminSellerOrders}`);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
checkTenantData();