import { PrismaClient, OrderStatus } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function testImportStatusIssue() {
  console.log('=== Testing Import Status Issue ===\n');

  try {
    // Get a valid tenant and user for testing
    const testUser = await prisma.user.findFirst({
      where: { role: 'VENDEDOR' },
      select: { id: true, tenantId: true, name: true },
    });

    if (!testUser || !testUser.tenantId) {
      console.error('No test user found. Please ensure you have at least one VENDEDOR user.');
      return;
    }

    console.log(`Using test user: ${testUser.name} (${testUser.id})`);
    console.log(`Tenant ID: ${testUser.tenantId}\n`);

    // Test 1: Create order with explicit status (simulating import)
    console.log('Test 1: Creating order with explicit status = Completo');
    const testOrder1 = await prisma.order.create({
      data: {
        orderNumber: `TEST-IMPORT-${Date.now()}`,
        customerName: 'Test Import Customer',
        customerPhone: '11999999999',
        status: OrderStatus.Completo, // Explicitly set to Completo
        total: 100,
        sellerId: testUser.id,
        tenantId: testUser.tenantId,
        items: {
          create: {
            productId: 'imported-product',
            productName: 'Imported Product',
            quantity: 1,
            unitPrice: 100,
          },
        },
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Completo,
            newStatus: OrderStatus.Completo,
            changedById: testUser.id,
          },
        },
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        createdAt: true,
      },
    });

    console.log(`Created order ${testOrder1.orderNumber}:`);
    console.log(`  - Status: ${testOrder1.status}`);
    console.log(`  - Expected: Completo`);
    console.log(`  - Match: ${testOrder1.status === OrderStatus.Completo ? '✅' : '❌'}\n`);

    // Test 2: Query the order back to see if status persisted
    console.log('Test 2: Querying the order back');
    const queriedOrder = await prisma.order.findUnique({
      where: { id: testOrder1.id },
      select: {
        status: true,
        statusHistory: {
          select: {
            previousStatus: true,
            newStatus: true,
            changedAt: true,
          },
        },
      },
    });

    console.log(`Queried order status: ${queriedOrder?.status}`);
    console.log(`Status history: ${JSON.stringify(queriedOrder?.statusHistory, null, 2)}\n`);

    // Test 3: Try with raw SQL to bypass any Prisma middleware
    console.log('Test 3: Checking with raw SQL');
    const rawResult = await prisma.$queryRaw`
      SELECT id, "orderNumber", status 
      FROM "Order" 
      WHERE id = ${testOrder1.id}
    `;
    console.log('Raw SQL result:', rawResult);

    // Test 4: Create order without explicit status (to see default behavior)
    console.log('\nTest 4: Creating order WITHOUT explicit status');
    const testOrder2 = await prisma.order.create({
      data: {
        orderNumber: `TEST-DEFAULT-${Date.now()}`,
        customerName: 'Test Default Customer',
        customerPhone: '11888888888',
        // status not set - should use default
        total: 200,
        sellerId: testUser.id,
        tenantId: testUser.tenantId,
        items: {
          create: {
            productId: 'default-product',
            productName: 'Default Product',
            quantity: 1,
            unitPrice: 200,
          },
        },
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
      },
    });

    console.log(`Created order ${testOrder2.orderNumber}:`);
    console.log(`  - Status: ${testOrder2.status}`);
    console.log(`  - Expected: Analise (default)`);
    console.log(`  - Match: ${testOrder2.status === OrderStatus.Analise ? '✅' : '❌'}\n`);

    // Clean up test orders
    console.log('Cleaning up test orders...');
    await prisma.order.delete({ where: { id: testOrder1.id } });
    await prisma.order.delete({ where: { id: testOrder2.id } });
    console.log('Test orders cleaned up.\n');

    // Conclusion
    console.log('=== Conclusion ===');
    if (testOrder1.status === OrderStatus.Completo) {
      console.log('✅ Status mapping is working correctly!');
      console.log('The issue might be:');
      console.log('1. Frontend not sending the correct situacaoVenda field');
      console.log('2. Status being filtered out during query');
      console.log('3. Frontend displaying the wrong status');
    } else {
      console.log('❌ Status is being overridden!');
      console.log('Possible causes:');
      console.log('1. Database trigger resetting status');
      console.log('2. Prisma middleware intercepting creates');
      console.log('3. Schema default overriding explicit values');
    }

  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testImportStatusIssue().catch(console.error);