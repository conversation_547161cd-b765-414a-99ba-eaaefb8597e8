-- Script to add Promessa status to OrderStatus enum
-- Run this in your production database

-- First, check current enum values
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'OrderStatus'
)
ORDER BY enumsortorder;

-- Add <PERSON>messa to the enum after Negociacao
-- This will only work if Promessa doesn't already exist
DO $$ 
BEGIN
    -- Check if Promessa already exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_enum 
        WHERE enumtypid = (
            SELECT oid 
            FROM pg_type 
            WHERE typname = 'OrderStatus'
        ) 
        AND enumlabel = 'Promessa'
    ) THEN
        -- Add the new value
        ALTER TYPE "OrderStatus" ADD VALUE 'Promessa' AFTER 'Negociacao';
        RAISE NOTICE 'Promessa status added successfully!';
    ELSE
        RAISE NOTICE 'Promessa status already exists, skipping...';
    END IF;
END $$;

-- Verify the addition
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'OrderStatus'
)
ORDER BY enumsortorder;

-- Check order statistics
SELECT status, COUNT(*) as count 
FROM "Order" 
GROUP BY status 
ORDER BY count DESC;