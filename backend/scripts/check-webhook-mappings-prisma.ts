import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkWebhookMappings() {
  console.log('=== Checking Webhook Mappings via Prisma ===\n');

  try {
    // Use raw query to access webhook_mappings table
    const allMappings = await prisma.$queryRaw<any[]>`
      SELECT * FROM webhook_mappings 
      ORDER BY "payloadKey"
    `;

    console.log(`Total mappings found: ${allMappings.length}\n`);

    // Show mappings related to 'id' field
    console.log('=== Mappings for ID fields ===');
    const idMappings = allMappings.filter((m: any) => 
      m.payloadKey.toLowerCase().includes('id') || 
      m.entityColumn?.toLowerCase().includes('id')
    );
    
    for (const mapping of idMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${mapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
      console.log(`Active: ${mapping.isActive}`);
    }

    // Show specific mapping for 'sale.id'
    console.log('\n=== Specific mapping for sale.id ===');
    const saleIdMapping = allMappings.find((m: any) => m.payloadKey === 'sale.id');
    if (saleIdMapping) {
      console.log(`Payload Key: ${saleIdMapping.payloadKey}`);
      console.log(`Entity Column: ${saleIdMapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${saleIdMapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${saleIdMapping.sampleValue || 'N/A'}`);
    } else {
      console.log('No mapping found for sale.id');
    }

    // Show mappings for orderNumber
    console.log('\n=== Mappings for orderNumber ===');
    const orderNumberMappings = allMappings.filter((m: any) => 
      m.entityColumn === 'orderNumber' || 
      (m.payloadKey.toLowerCase().includes('order') && m.payloadKey.toLowerCase().includes('number'))
    );
    
    for (const mapping of orderNumberMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
    }

    // Show all active mappings (with entityColumn set)
    console.log('\n=== All Active Mappings (with entity columns) ===');
    const activeMappings = allMappings.filter((m: any) => m.entityColumn && m.entityColumn !== '_ignore');
    console.log(`Found ${activeMappings.length} active mappings:\n`);
    
    for (const mapping of activeMappings) {
      console.log(`${mapping.payloadKey} => ${mapping.entityColumn}`);
    }

    // Check for problematic mappings
    console.log('\n=== Checking for problematic mappings ===');
    const idToIdMapping = allMappings.find((m: any) => 
      m.payloadKey.endsWith('.id') && m.entityColumn === 'id'
    );
    
    if (idToIdMapping) {
      console.log(`WARNING: Found mapping that maps ${idToIdMapping.payloadKey} to 'id' field!`);
      console.log('This will cause issues because id is a primary key UUID field.');
      console.log('This mapping should be changed to map to orderNumber instead.');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkWebhookMappings().catch(console.error);