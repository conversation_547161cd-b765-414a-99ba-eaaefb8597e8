#!/bin/bash

# Script de Restore do Banco de Dados ZenCash
# Uso: ./scripts/restore-database.sh [arquivo_backup.sql.gz]

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Verificar argumento
if [ $# -eq 0 ]; then
    echo -e "${RED}Erro: Especifique o arquivo de backup${NC}"
    echo "Uso: $0 <arquivo_backup.sql.gz>"
    echo ""
    echo "Backups disponíveis:"
    ls -lht ./backups/*.gz 2>/dev/null | head -10
    exit 1
fi

BACKUP_FILE="$1"

# Verificar se o arquivo existe
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}Erro: Arquivo $BACKUP_FILE não encontrado${NC}"
    exit 1
fi

# Carregar variáveis de ambiente
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo -e "${RED}Erro: Arquivo .env não encontrado${NC}"
    exit 1
fi

# Extrair informações de conexão do DATABASE_URL
if [[ $DATABASE_URL =~ postgresql://([^:]+):([^@]+)@([^:]+):([0-9]+)/(.+) ]]; then
    DB_USER="${BASH_REMATCH[1]}"
    DB_PASS="${BASH_REMATCH[2]}"
    DB_HOST="${BASH_REMATCH[3]}"
    DB_PORT="${BASH_REMATCH[4]}"
    DB_NAME="${BASH_REMATCH[5]}"
else
    echo -e "${RED}Erro: DATABASE_URL inválida${NC}"
    exit 1
fi

echo -e "${YELLOW}⚠️  ATENÇÃO: Este processo irá SUBSTITUIR todos os dados do banco!${NC}"
echo "Database: $DB_NAME"
echo "Host: $DB_HOST:$DB_PORT"
echo "Backup: $BACKUP_FILE"
echo ""
read -p "Tem certeza que deseja continuar? (yes/no): " CONFIRM

if [ "$CONFIRM" != "yes" ]; then
    echo -e "${RED}Operação cancelada${NC}"
    exit 0
fi

# Criar backup de segurança antes do restore
echo -e "${YELLOW}🔒 Criando backup de segurança do banco atual...${NC}"
./scripts/backup-database.sh

# Descomprimir backup se necessário
TEMP_SQL="/tmp/restore_$(date +%s).sql"
if [[ "$BACKUP_FILE" == *.gz ]]; then
    echo -e "${YELLOW}📦 Descomprimindo backup...${NC}"
    gunzip -c "$BACKUP_FILE" > "$TEMP_SQL"
else
    cp "$BACKUP_FILE" "$TEMP_SQL"
fi

# Executar restore
echo -e "${YELLOW}🔄 Restaurando banco de dados...${NC}"

# Primeiro, limpar o banco (opcional - descomente se necessário)
# PGPASSWORD="$DB_PASS" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"

# Restaurar dados
PGPASSWORD="$DB_PASS" psql \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    -f "$TEMP_SQL" 2>&1 | while read line; do
    echo -e "${GREEN}  $line${NC}"
done

# Limpar arquivo temporário
rm -f "$TEMP_SQL"

# Executar migrations do Prisma para garantir sincronização
echo -e "${YELLOW}🔧 Sincronizando schema do Prisma...${NC}"
npx prisma db push --skip-generate

echo -e "${GREEN}✅ Restore concluído!${NC}"
echo ""
echo -e "${YELLOW}📝 Próximos passos recomendados:${NC}"
echo "1. Verificar se os dados foram restaurados corretamente"
echo "2. Testar login e funcionalidades principais"
echo "3. Verificar logs de erro se houver problemas"