# Database Check Scripts

These scripts help monitor and verify the webhook order creation process in the Zencash system.

## Available Scripts

### 1. Check Recent Orders (`check-recent-orders.ts`)

A comprehensive script that provides detailed information about orders and webhook logs.

**Usage:**
```bash
npm run check:orders
```

**Features:**
- Lists the 20 most recent orders with full details
- Shows the 20 most recent webhook logs
- Cross-references orders created by webhooks
- Displays system statistics
- Checks webhook mapping configuration
- Shows details of failed webhooks

### 2. Webhook Order Status (`webhook-order-status.ts`)

A focused script for monitoring webhook performance and order creation success rates.

**Usage:**
```bash
# Check last 24 hours (default)
npm run check:webhooks

# Check last N hours
npm run check:webhooks 12
```

**Features:**
- Summary of webhook activity for the specified time period
- Success/failure rates
- Order creation vs update statistics
- Recent successful order creations
- Recent failures with error details
- Configuration status check
- Performance metrics

## What to Look For

### Signs of Healthy Operation:
- ✅ High success rate (>90%)
- ✅ Orders being created with proper details
- ✅ Active webhook mappings configured
- ✅ Recent webhook logs showing "success" status

### Common Issues:
- ❌ No active webhook mappings (webhooks won't create orders)
- ❌ High failure rate
- ❌ "No identifier field found" errors
- ❌ Missing required fields in webhook payload

## Troubleshooting

1. **No orders being created:**
   - Check if there are active webhook mappings
   - Verify webhook logs are being received
   - Look for error messages in failed webhooks

2. **High failure rate:**
   - Check the error messages in failed webhook logs
   - Verify the webhook payload structure matches the mappings
   - Check for missing required fields

3. **Orders created but with missing data:**
   - Review the webhook mappings configuration
   - Check if all required fields are mapped correctly

## Database Queries

If you need to run custom queries, connect to the database and use:

```sql
-- Recent orders
SELECT id, "orderNumber", "customerName", status, total, "createdAt" 
FROM "Order" 
ORDER BY "createdAt" DESC 
LIMIT 20;

-- Recent webhook logs
SELECT id, endpoint, status, "orderId", "isNewOrder", error, "receivedAt" 
FROM "WebhookLog" 
ORDER BY "receivedAt" DESC 
LIMIT 20;

-- Active webhook mappings
SELECT * FROM "WebhookMapping" WHERE "isActive" = true;
```