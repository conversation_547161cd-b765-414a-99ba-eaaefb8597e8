import { PrismaClient, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function testOrderLoading() {
  try {
    console.log('Testing order loading...\n');

    // 1. Count all orders by status
    console.log('1. COUNT OF ORDERS BY STATUS:');
    const statusCounts = await prisma.order.groupBy({
      by: ['status'],
      _count: {
        _all: true
      },
      where: {
        deletedAt: null
      }
    });
    
    statusCounts.forEach(({ status, _count }) => {
      console.log(`   ${status}: ${_count._all} orders`);
    });

    // 2. Check specifically for "Analise" status orders
    console.log('\n2. ORDERS WITH "ANALISE" STATUS:');
    const analiseOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        createdAt: true,
        status: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    if (analiseOrders.length === 0) {
      console.log('   No orders found with "Analise" status');
    } else {
      console.log(`   Found ${analiseOrders.length} orders with "Analise" status:`);
      analiseOrders.forEach(order => {
        console.log(`   - ${order.orderNumber} | ${order.customerName} | Status: ${order.status} | Created: ${order.createdAt.toISOString()}`);
      });
    }

    // 3. Test the findAll query exactly as used in the service
    console.log('\n3. TESTING SERVICE findAll QUERY:');
    const filters = { status: OrderStatus.Analise };
    const where: any = {
      deletedAt: null,
    };

    if (filters.status) {
      where.status = filters.status;
    }

    const serviceQueryResult = await prisma.order.findMany({
      where,
      include: {
        items: true,
        addressComponents: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    console.log(`   Service query found ${serviceQueryResult.length} orders with "Analise" status`);

    // 4. Check for any recently created orders
    console.log('\n4. RECENTLY CREATED ORDERS (LAST 24 HOURS):');
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const recentOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: yesterday
        },
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        status: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    if (recentOrders.length === 0) {
      console.log('   No orders created in the last 24 hours');
    } else {
      console.log(`   Found ${recentOrders.length} orders created in the last 24 hours:`);
      recentOrders.forEach(order => {
        console.log(`   - ${order.orderNumber} | ${order.customerName} | Status: ${order.status} | Created: ${order.createdAt.toISOString()}`);
      });
    }

    // 5. Check if there are any deleted orders
    console.log('\n5. CHECKING FOR SOFT-DELETED ORDERS:');
    const deletedOrders = await prisma.order.count({
      where: {
        deletedAt: {
          not: null
        }
      }
    });
    console.log(`   Found ${deletedOrders} soft-deleted orders`);

    // 6. Raw SQL query to double-check
    console.log('\n6. RAW SQL QUERY FOR ANALISE STATUS:');
    const rawResult = await prisma.$queryRaw`
      SELECT COUNT(*) as count, status 
      FROM "Order" 
      WHERE "deletedAt" IS NULL AND status = 'Analise'
      GROUP BY status
    `;
    console.log('   Raw query result:', rawResult);

  } catch (error) {
    console.error('Error testing order loading:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testOrderLoading();