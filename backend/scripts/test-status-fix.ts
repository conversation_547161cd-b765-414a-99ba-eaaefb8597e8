import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

async function testStatusFix() {
  console.log('=== Testing Status Field Fix ===\n');

  try {
    // Get test user
    const testUser = await prisma.user.findFirst({
      where: { role: 'VENDEDOR' },
    });

    if (!testUser || !testUser.tenantId) {
      console.error('No test user found');
      return;
    }

    // Simulate what the frontend sends - using "status" field instead of "situacaoVenda"
    const testCases = [
      { status: 'Completo', expected: 'Completo' },
      { status: 'Pagamento Pendente', expected: 'PagamentoPendente' },
      { status: 'Em Análise', expected: 'Analise' },
      { status: 'Cancelado', expected: 'Cancelado' },
      { situacaoVenda: 'Completo', expected: 'Completo' }, // Test old field still works
    ];

    console.log('Testing status mappings...\n');

    for (const testCase of testCases) {
      const importData: any = {
        idVenda: `TEST-${Date.now()}-${Math.random()}`,
        cliente: 'Test Customer',
        telefone: '11999999999',
        valorVenda: 100,
        vendedor: testUser.name,
      };

      // Add either status or situacaoVenda
      if (testCase.status) {
        importData.status = testCase.status;
      } else if (testCase.situacaoVenda) {
        importData.situacaoVenda = testCase.situacaoVenda;
      }

      console.log(`Test: ${JSON.stringify(testCase)}`);
      
      // Note: We would need to call the actual import endpoint or service here
      // For now, just showing what would be sent
      console.log(`  Would send: ${JSON.stringify(importData)}`);
      console.log(`  Expected result: ${testCase.expected}\n`);
    }

    console.log('=== Fix Summary ===');
    console.log('✅ The backend now checks both "status" and "situacaoVenda" fields');
    console.log('✅ This should resolve the import status issue');
    console.log('\nNext steps:');
    console.log('1. Restart the backend server');
    console.log('2. Try importing a CSV with status values');
    console.log('3. The orders should now have the correct status!');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testStatusFix().catch(console.error);