#!/bin/sh
# Alpine Linux compatible startup script with migrations
# Uses POSIX sh syntax for maximum compatibility
set -e

# Trap signals for graceful shutdown
trap 'echo "🛑 Received shutdown signal"; exit 0' TERM INT

echo "🚀 Starting application with database setup..."
echo "   Running as user: $(id -un) (UID: $(id -u), GID: $(id -g))"

# Function to wait for database
wait_for_database() {
    echo "⏳ Waiting for database to be ready..."
    retries=30
    count=0
    
    while [ $count -lt $retries ]; do
        if npx prisma db execute --sql "SELECT 1" >/dev/null 2>&1; then
            echo "✅ Database is ready!"
            return 0
        fi
        
        count=$((count + 1))
        echo "   Attempt $count/$retries - Database not ready yet..."
        sleep 2
    done
    
    echo "❌ Database connection timeout after $retries attempts"
    return 1
}

# Function to check if migrations table exists
check_migrations_table() {
    echo "🔍 Checking if _prisma_migrations table exists..."
    result=$(npx prisma db execute --sql "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '_prisma_migrations');" 2>&1 || echo "error")
    
    if echo "$result" | grep -q "true"; then
        return 0
    else
        return 1
    fi
}

# Main execution
echo "📋 Environment Check:"
echo "   DATABASE_URL: ${DATABASE_URL:+set}"
echo "   NODE_ENV: ${NODE_ENV:-not set}"
echo "   PORT: ${PORT:-3000}"
echo "   Working directory: $(pwd)"

# Check write permissions in critical directories
if [ -w "/app" ]; then
    echo "   Write permissions: ✅ /app is writable"
else
    echo "   Write permissions: ⚠️  /app is not writable (may cause issues)"
fi

# Wait for database
if wait_for_database; then
    echo "🗄️  Database connection established"
    
    # Check if this is a fresh database
    if ! check_migrations_table; then
        echo "⚠️  Fresh database detected. Initializing schema..."
        
        # For production, use db push to create schema
        echo "📦 Creating database schema..."
        if npx prisma db push --skip-generate 2>&1 | tee /tmp/schema-push.log; then
            echo "✅ Database schema created"
        else
            echo "❌ Failed to create schema. Check logs:"
            [ -f /tmp/schema-push.log ] && tail -20 /tmp/schema-push.log
            echo "   Continuing anyway..."
        fi
    else
        echo "✅ Database already initialized"
        
        # Try to run migrations
        echo "🔄 Checking for pending migrations..."
        if npx prisma migrate deploy 2>&1 | tee /tmp/migrate.log; then
            echo "✅ Migrations completed"
        else
            echo "⚠️  Migration failed, attempting schema sync..."
            if ! npx prisma db push --skip-generate 2>&1 | tee /tmp/schema-sync.log; then
                echo "⚠️  Schema sync also failed. Check logs:"
                [ -f /tmp/schema-sync.log ] && tail -20 /tmp/schema-sync.log
            fi
        fi
    fi
    
    # Run seeding only in development or if explicitly requested
    if [ "${RUN_SEED:-false}" = "true" ] || [ "${NODE_ENV}" = "development" ]; then
        echo "🌱 Running database seed..."
        if npx prisma db seed 2>&1 | tee /tmp/seed.log; then
            echo "✅ Database seeded"
        else
            echo "⚠️  Seeding failed. Check logs:"
            [ -f /tmp/seed.log ] && tail -20 /tmp/seed.log
        fi
    else
        echo "ℹ️  Skipping database seed (NODE_ENV=${NODE_ENV}, RUN_SEED=${RUN_SEED:-false})"
    fi
else
    echo "⚠️  Database not available, starting without migrations"
    echo "   The app will start but database features will not work"
fi

# Clean up temporary log files if they exist
rm -f /tmp/schema-push.log /tmp/migrate.log /tmp/schema-sync.log /tmp/seed.log 2>/dev/null || true

echo "🚀 Starting the application..."
echo "   Command: npm run start:prod"
echo "   Final PORT value: ${PORT}"
echo "   All env vars with PORT:"
env | grep -i port || true

# Check if dist/main.js exists
if [ ! -f "/app/dist/main.js" ]; then
    echo "❌ ERROR: dist/main.js not found!"
    echo "   Contents of /app:"
    ls -la /app/ || true
    echo "   Contents of /app/dist (if exists):"
    ls -la /app/dist/ || true
    exit 1
fi

echo "✅ Build output found at /app/dist/main.js"

# Add a small delay to ensure everything is ready
sleep 2

# Use exec to replace the shell process with node
exec npm run start:prod