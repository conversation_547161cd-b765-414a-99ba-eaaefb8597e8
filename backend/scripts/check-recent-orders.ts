import { PrismaClient } from '@prisma/client';
import { format } from 'date-fns';

const prisma = new PrismaClient();

interface OrderSummary {
  id: string;
  orderNumber: string | null;
  customerName: string;
  status: string;
  total: number;
  createdAt: Date;
  isFromWebhook: boolean;
  webhookLogId?: string;
}

interface WebhookLogSummary {
  id: string;
  endpoint: string;
  status: string;
  orderId: string | null;
  isNewOrder: boolean | null;
  identifierField: string | null;
  identifierValue: string | null;
  receivedAt: Date;
  error: string | null;
}

async function checkRecentOrders() {
  console.log('=== CHECKING RECENT ORDERS AND WEBHOOK LOGS ===\n');
  
  try {
    // 1. Get the most recent orders
    console.log('1. MOST RECENT ORDERS (Last 20):');
    console.log('--------------------------------');
    
    const recentOrders = await prisma.order.findMany({
      take: 20,
      orderBy: { createdAt: 'desc' },
      include: {
        seller: {
          select: { name: true, email: true }
        },
        items: {
          include: {
            product: { select: { name: true } },
            kit: { select: { name: true } }
          }
        }
      }
    });

    if (recentOrders.length === 0) {
      console.log('No orders found in the database.\n');
    } else {
      for (const order of recentOrders) {
        console.log(`Order ID: ${order.id}`);
        console.log(`  Order Number: ${order.orderNumber || 'N/A'}`);
        console.log(`  Customer: ${order.customerName} (${order.customerPhone})`);
        console.log(`  Status: ${order.status}`);
        console.log(`  Total: R$ ${order.total}`);
        console.log(`  Created: ${format(order.createdAt, 'dd/MM/yyyy HH:mm:ss')}`);
        console.log(`  Seller: ${order.seller.name} (${order.seller.email})`);
        console.log(`  Items: ${order.items.length} item(s)`);
        
        if (order.items.length > 0) {
          order.items.forEach((item, index) => {
            const itemName = item.product?.name || item.kit?.name || 'Unknown';
            console.log(`    ${index + 1}. ${itemName} - Qty: ${item.quantity} - R$ ${item.price}`);
          });
        }
        
        // Check if it has risk assessment
        if (order.riskScore !== null) {
          console.log(`  Anti-fraud: Risk Score: ${order.riskScore}, Level: ${order.riskLevel || 'N/A'}`);
          if (order.isDuplicate) {
            console.log(`  ⚠️  DUPLICATE DETECTED - Status: ${order.duplicateStatus}`);
          }
        }
        
        console.log('');
      }
    }

    // 2. Get recent webhook logs
    console.log('\n2. RECENT WEBHOOK LOGS (Last 20):');
    console.log('----------------------------------');
    
    const recentWebhookLogs = await prisma.webhookLog.findMany({
      take: 20,
      orderBy: { receivedAt: 'desc' }
    });

    if (recentWebhookLogs.length === 0) {
      console.log('No webhook logs found.\n');
    } else {
      for (const log of recentWebhookLogs) {
        console.log(`Webhook Log ID: ${log.id}`);
        console.log(`  Endpoint: ${log.endpoint}`);
        console.log(`  Status: ${log.status}`);
        console.log(`  Received: ${format(log.receivedAt, 'dd/MM/yyyy HH:mm:ss')}`);
        
        if (log.orderId) {
          console.log(`  ✅ Order Created/Updated: ${log.orderId}`);
          console.log(`  Is New Order: ${log.isNewOrder ? 'Yes' : 'No'}`);
        } else {
          console.log(`  ❌ No Order Created`);
        }
        
        if (log.identifierField && log.identifierValue) {
          console.log(`  Identifier: ${log.identifierField} = ${log.identifierValue}`);
        }
        
        if (log.error) {
          console.log(`  ⚠️  Error: ${log.error}`);
        }
        
        if (log.mappingsUsed !== null) {
          console.log(`  Mappings Used: ${log.mappingsUsed}`);
        }
        
        console.log('');
      }
    }

    // 3. Cross-reference: Find orders created by webhooks
    console.log('\n3. ORDERS CREATED BY WEBHOOKS (Last 10):');
    console.log('-----------------------------------------');
    
    const webhookOrderIds = recentWebhookLogs
      .filter(log => log.orderId && log.isNewOrder)
      .map(log => log.orderId);
    
    if (webhookOrderIds.length > 0) {
      const webhookOrders = await prisma.order.findMany({
        where: {
          id: { in: webhookOrderIds as string[] }
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      });

      for (const order of webhookOrders) {
        const webhookLog = recentWebhookLogs.find(log => log.orderId === order.id);
        console.log(`Order ID: ${order.id}`);
        console.log(`  Order Number: ${order.orderNumber || 'N/A'}`);
        console.log(`  Customer: ${order.customerName}`);
        console.log(`  Status: ${order.status}`);
        console.log(`  Total: R$ ${order.total}`);
        console.log(`  Created via Webhook: ${webhookLog?.id || 'Unknown'}`);
        console.log(`  Webhook Received: ${webhookLog ? format(webhookLog.receivedAt, 'dd/MM/yyyy HH:mm:ss') : 'Unknown'}`);
        console.log('');
      }
    } else {
      console.log('No orders were created by recent webhooks.\n');
    }

    // 4. Statistics
    console.log('\n4. STATISTICS:');
    console.log('--------------');
    
    const totalOrders = await prisma.order.count();
    const todayOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: new Date(new Date().setHours(0, 0, 0, 0))
        }
      }
    });
    
    const totalWebhookLogs = await prisma.webhookLog.count();
    const successfulWebhooks = await prisma.webhookLog.count({
      where: { status: 'success' }
    });
    const failedWebhooks = await prisma.webhookLog.count({
      where: { status: 'error' }
    });
    
    console.log(`Total Orders: ${totalOrders}`);
    console.log(`Orders Today: ${todayOrders}`);
    console.log(`Total Webhook Logs: ${totalWebhookLogs}`);
    console.log(`Successful Webhooks: ${successfulWebhooks}`);
    console.log(`Failed Webhooks: ${failedWebhooks}`);
    
    // 5. Check for webhook mapping configuration
    console.log('\n5. WEBHOOK MAPPING CONFIGURATION:');
    console.log('----------------------------------');
    
    const activeMappings = await prisma.webhookMapping.count({
      where: { isActive: true }
    });
    
    console.log(`Active Webhook Mappings: ${activeMappings}`);
    
    if (activeMappings === 0) {
      console.log('⚠️  WARNING: No active webhook mappings found. Webhooks will not create orders!');
    }

    // 6. Recent failed webhooks details
    console.log('\n6. RECENT FAILED WEBHOOKS (Last 5):');
    console.log('------------------------------------');
    
    const failedLogs = await prisma.webhookLog.findMany({
      where: { status: 'error' },
      take: 5,
      orderBy: { receivedAt: 'desc' }
    });

    if (failedLogs.length === 0) {
      console.log('No failed webhooks found.\n');
    } else {
      for (const log of failedLogs) {
        console.log(`Webhook Log ID: ${log.id}`);
        console.log(`  Endpoint: ${log.endpoint}`);
        console.log(`  Received: ${format(log.receivedAt, 'dd/MM/yyyy HH:mm:ss')}`);
        console.log(`  Error: ${log.error}`);
        
        // Show a sample of the payload for debugging
        if (log.payload && typeof log.payload === 'object') {
          console.log(`  Payload Sample: ${JSON.stringify(log.payload).substring(0, 200)}...`);
        }
        console.log('');
      }
    }

  } catch (error) {
    console.error('Error checking orders and webhooks:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
checkRecentOrders()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  });