import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDuplicateDetection() {
  try {
    console.log('🔍 Testando detecção de duplicatas...\n');

    // Buscar pedidos recentes (últimas 48 horas)
    const recentOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 48 * 60 * 60 * 1000)
        },
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        customerPhone: true,
        customerCPF: true,
        fullAddress: true,
        isDuplicate: true,
        duplicateMatchScore: true,
        riskScore: true,
        riskLevel: true,
        requiresReview: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Total de pedidos recentes: ${recentOrders.length}\n`);

    // Agrupar por telefone + nome para encontrar possíveis duplicatas
    const phoneNameGroups = new Map<string, typeof recentOrders>();
    
    recentOrders.forEach(order => {
      const key = `${order.customerPhone}_${order.customerName.toLowerCase()}`;
      if (!phoneNameGroups.has(key)) {
        phoneNameGroups.set(key, []);
      }
      phoneNameGroups.get(key)!.push(order);
    });

    // Encontrar grupos com mais de 1 pedido
    const duplicateGroups = Array.from(phoneNameGroups.entries())
      .filter(([_, orders]) => orders.length > 1);

    if (duplicateGroups.length === 0) {
      console.log('✅ Nenhum grupo de pedidos duplicados encontrado.\n');
    } else {
      console.log(`⚠️  Encontrados ${duplicateGroups.length} grupos de possíveis duplicatas:\n`);
      
      duplicateGroups.forEach(([key, orders], index) => {
        const [phone, name] = key.split('_');
        console.log(`\n${index + 1}. Grupo: ${name} - ${phone}`);
        console.log(`   Total de pedidos: ${orders.length}`);
        console.log('   ' + '─'.repeat(60));
        
        orders.forEach((order, idx) => {
          console.log(`\n   Pedido ${idx + 1}: ${order.orderNumber || order.id}`);
          console.log(`   - Criado em: ${order.createdAt.toLocaleString('pt-BR')}`);
          console.log(`   - É duplicata? ${order.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
          console.log(`   - Score duplicata: ${order.duplicateMatchScore || 'N/A'}`);
          console.log(`   - Score risco: ${order.riskScore || 'N/A'}`);
          console.log(`   - Nível risco: ${order.riskLevel || 'N/A'}`);
          console.log(`   - Requer revisão? ${order.requiresReview ? '✅ SIM' : '❌ NÃO'}`);
          console.log(`   - CPF: ${order.customerCPF ? '✅ Presente' : '❌ Ausente'}`);
          console.log(`   - Endereço: ${order.fullAddress ? '✅ Presente' : '❌ Ausente'}`);
        });

        // Verificar se algum pedido não foi marcado como duplicata
        const notMarkedAsDuplicate = orders.filter(o => !o.isDuplicate);
        if (notMarkedAsDuplicate.length > 0) {
          console.log(`\n   ⚠️  PROBLEMA: ${notMarkedAsDuplicate.length} pedidos neste grupo NÃO foram marcados como duplicatas!`);
        }
      });
    }

    // Estatísticas gerais
    console.log('\n' + '═'.repeat(80));
    console.log('\n📈 ESTATÍSTICAS GERAIS:');
    
    const ordersWithCPF = recentOrders.filter(o => o.customerCPF);
    const ordersWithAddress = recentOrders.filter(o => o.fullAddress);
    const ordersMarkedDuplicate = recentOrders.filter(o => o.isDuplicate);
    const ordersHighRisk = recentOrders.filter(o => o.riskLevel === 'HIGH' || o.riskLevel === 'CRITICAL');
    
    console.log(`- Pedidos com CPF: ${ordersWithCPF.length} (${(ordersWithCPF.length / recentOrders.length * 100).toFixed(1)}%)`);
    console.log(`- Pedidos com endereço: ${ordersWithAddress.length} (${(ordersWithAddress.length / recentOrders.length * 100).toFixed(1)}%)`);
    console.log(`- Pedidos marcados como duplicata: ${ordersMarkedDuplicate.length}`);
    console.log(`- Pedidos com risco alto/crítico: ${ordersHighRisk.length}`);

  } catch (error) {
    console.error('❌ Erro ao testar detecção de duplicatas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o teste
testDuplicateDetection();