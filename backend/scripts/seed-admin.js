const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function createAdmin() {
  try {
    console.log('🚀 Creating admin user...');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('');
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Try to create or update the user
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        name: '<PERSON><PERSON>',
        password: hashedPassword,
        role: 'ADMIN',
        active: true,
      },
      create: {
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        active: true,
      },
    });
    
    console.log('✅ Admin user created/updated successfully!');
    console.log('   ID:', user.id);
    console.log('   Name:', user.name);
    console.log('   Email:', user.email);
    console.log('   Role:', user.role);
    console.log('   Active:', user.active);
    
    // List all users
    const allUsers = await prisma.user.findMany();
    console.log('\n📊 Total users in database:', allUsers.length);
    allUsers.forEach(u => {
      console.log(`   - ${u.email} (${u.role}) ${u.active ? '✓' : '✗'}`);
    });
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin()
  .then(() => {
    console.log('\n✅ Done! You can now login with:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: admin123');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });