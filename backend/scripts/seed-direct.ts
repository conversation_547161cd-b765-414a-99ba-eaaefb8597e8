import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parse/sync';

const prisma = new PrismaClient();

async function seedFromCSV() {
  console.log('🚀 Seeding database directly from CSV...');

  try {
    // Read the CSV file
    const csvPath = path.join(process.cwd(), 'mock-orders-100.csv');
    const fileContent = fs.readFileSync(csvPath, 'utf-8');
    
    // Parse CSV
    const records = csv.parse(fileContent, {
      columns: true,
      skip_empty_lines: true
    });

    console.log(`📊 Found ${records.length} orders to import`);

    let successCount = 0;
    let errorCount = 0;

    for (const record of records) {
      try {
        // Parse values
        const valorVenda = parseFloat(record.valorVenda) || 0;
        const valorRecebido = parseFloat(record.valorRecebido) || 0;
        
        // Parse dates
        const parseDate = (dateStr: string) => {
          if (!dateStr || dateStr.trim() === '') return null;
          const [day, month, year] = dateStr.split('/');
          return new Date(`${year}-${month}-${day}T00:00:00`);
        };

        const dataVenda = parseDate(record.dataVenda);
        const dataRecebimento = record.dataRecebimento ? parseDate(record.dataRecebimento) : null;

        // Create order
        await prisma.order.create({
          data: {
            numeroVenda: record.numeroVenda,
            dataVenda: dataVenda?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
            customerName: record.nomeDestinatario,
            customerPhone: record.telefoneDestinatario,
            nomeDestinatario: record.nomeDestinatario,
            cpfDestinatario: record.cpfDestinatario,
            telefoneDestinatario: record.telefoneDestinatario,
            emailDestinatario: record.emailDestinatario,
            cepDestinatario: record.cepDestinatario,
            enderecoDestinatario: record.enderecoDestinatario,
            numeroEnderecoDestinatario: record.numeroEnderecoDestinatario,
            complementoDestinatario: record.complementoDestinatario || '',
            bairroDestinatario: record.bairroDestinatario,
            cidadeDestinatario: record.cidadeDestinatario,
            estadoDestinatario: record.estadoDestinatario,
            paisDestinatario: record.paisDestinatario || 'Brasil',
            oferta: record.oferta,
            formaPagamento: record.formaPagamento,
            vendedor: record.vendedor,
            operador: record.operador,
            situacaoVenda: record.situacaoVenda,
            valorVenda,
            valorRecebido,
            dataRecebimento: dataRecebimento?.toISOString().split('T')[0] || null,
            observacoes: record.observacoes || null
          }
        });

        successCount++;
        
        if (successCount % 10 === 0) {
          console.log(`   ✓ Imported ${successCount} orders...`);
        }
      } catch (error) {
        console.error(`Error importing order ${record.numeroVenda}:`, error);
        errorCount++;
      }
    }

    console.log('\n✅ Import completed!');
    console.log(`✓ Successfully imported: ${successCount} orders`);
    console.log(`✗ Failed: ${errorCount} orders`);

    // Show summary
    const summary = await prisma.order.groupBy({
      by: ['situacaoVenda'],
      _count: true,
      _sum: {
        valorVenda: true,
        valorRecebido: true
      }
    });

    console.log('\n📊 Database Summary:');
    summary.forEach(s => {
      console.log(`   ${s.situacaoVenda}: ${s._count} orders, Total: R$ ${s._sum.valorVenda?.toFixed(2)}, Received: R$ ${s._sum.valorRecebido?.toFixed(2) || '0.00'}`);
    });

  } catch (error) {
    console.error('❌ Error during seeding:', error);
  }
}

// Run the script
seedFromCSV()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect();
  });