import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixTenantMismatch() {
  try {
    console.log('=== FIXING TENANT MISMATCH ===\n');

    // Get statistics before fix
    console.log('1. Current state:');
    const beforeStats = await prisma.order.groupBy({
      by: ['tenantId'],
      _count: true,
      where: { deletedAt: null }
    });
    
    console.log('   Orders by tenant before fix:');
    beforeStats.forEach(stat => {
      console.log(`   - Tenant "${stat.tenantId}": ${stat._count} orders`);
    });

    // Find orders with tenant mismatch
    const mismatchedOrders = await prisma.$queryRaw<any[]>`
      SELECT o.id, o."orderNumber", o."tenantId" as order_tenant, 
             u."tenantId" as seller_tenant, u.email as seller_email, u.name as seller_name
      FROM "Order" o
      JOIN "User" u ON o."sellerId" = u.id
      WHERE o."tenantId" != u."tenantId"
      AND o."deletedAt" IS NULL
    `;

    console.log(`\n2. Found ${mismatchedOrders.length} orders with tenant mismatch`);

    if (mismatchedOrders.length > 0) {
      console.log('\n3. Fixing mismatched orders...');
      
      // Group by seller tenant for batch updates
      const updatesByTenant = new Map<string, string[]>();
      
      mismatchedOrders.forEach(order => {
        const sellerTenant = order.seller_tenant;
        if (!updatesByTenant.has(sellerTenant)) {
          updatesByTenant.set(sellerTenant, []);
        }
        updatesByTenant.get(sellerTenant)!.push(order.id);
      });

      // Perform batch updates
      for (const [sellerTenant, orderIds] of updatesByTenant) {
        const result = await prisma.order.updateMany({
          where: {
            id: { in: orderIds }
          },
          data: {
            tenantId: sellerTenant
          }
        });
        
        console.log(`   - Updated ${result.count} orders to tenant "${sellerTenant}"`);
      }

      // Show after statistics
      console.log('\n4. State after fix:');
      const afterStats = await prisma.order.groupBy({
        by: ['tenantId'],
        _count: true,
        where: { deletedAt: null }
      });
      
      console.log('   Orders by tenant after fix:');
      afterStats.forEach(stat => {
        console.log(`   - Tenant "${stat.tenantId}": ${stat._count} orders`);
      });

      // Verify no more mismatches
      const remainingMismatches = await prisma.$queryRaw<any[]>`
        SELECT COUNT(*) as count
        FROM "Order" o
        JOIN "User" u ON o."sellerId" = u.id
        WHERE o."tenantId" != u."tenantId"
        AND o."deletedAt" IS NULL
      `;

      console.log(`\n5. Remaining mismatches: ${remainingMismatches[0].count}`);

      // Check <EMAIL> visibility
      const adminUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });

      if (adminUser) {
        const adminOrders = await prisma.order.count({
          where: {
            tenantId: adminUser.tenantId,
            deletedAt: null
          }
        });

        console.log(`\n6. <EMAIL> can now see ${adminOrders} orders in their tenant`);
      }
    } else {
      console.log('\n✅ No tenant mismatches found!');
    }

    // Show detailed results for verification
    console.log('\n7. Sample orders after fix:');
    const sampleOrders = await prisma.order.findMany({
      where: { deletedAt: null },
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        orderNumber: true,
        tenantId: true,
        seller: {
          select: {
            email: true,
            tenantId: true
          }
        }
      }
    });

    sampleOrders.forEach(order => {
      console.log(`   - Order ${order.orderNumber}:`);
      console.log(`     Order tenant: ${order.tenantId}`);
      console.log(`     Seller: ${order.seller.email} (tenant: ${order.seller.tenantId})`);
      console.log(`     Match: ${order.tenantId === order.seller.tenantId ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Add confirmation prompt
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('This script will update orders to match their seller\'s tenant ID.');
console.log('This ensures users can see orders from sellers in their tenant.\n');

rl.question('Do you want to proceed? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    fixTenantMismatch().then(() => {
      rl.close();
    });
  } else {
    console.log('Operation cancelled.');
    rl.close();
  }
});