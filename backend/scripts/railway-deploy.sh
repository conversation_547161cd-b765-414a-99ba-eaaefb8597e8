#!/bin/bash
set -e

echo "🚀 Railway deployment script starting..."

# Function to check if database is accessible
check_database() {
    echo "🔍 Checking database connection..."
    npx prisma db execute --sql "SELECT 1" 2>&1 | grep -q "Error" && return 1 || return 0
}

# Function to check if migrations table exists
check_migrations_table() {
    echo "🔍 Checking if _prisma_migrations table exists..."
    npx prisma db execute --sql "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '_prisma_migrations')" 2>&1 | grep -q "true" && return 0 || return 1
}

# Check database connection
if ! check_database; then
    echo "⚠️  Database connection not available during build phase."
    echo "   This is expected - database setup will happen at runtime."
    exit 0
fi

echo "✅ Database connection successful"

# Check if this is a fresh database or corrupted state
if ! check_migrations_table; then
    echo "⚠️  _prisma_migrations table not found. Initializing database..."
    
    # For production, we'll use db push to create schema from scratch
    echo "📦 Creating database schema from schema.prisma..."
    npx prisma db push --skip-generate
    
    echo "✅ Database schema created"
else
    echo "✅ _prisma_migrations table exists"
    
    # Try to run migrations
    echo "🔄 Running database migrations..."
    if npx prisma migrate deploy; then
        echo "✅ Migrations applied successfully"
    else
        echo "⚠️  Migration failed. This might be due to corrupted migration history."
        echo "🔧 Attempting to repair by pushing schema..."
        npx prisma db push --skip-generate
        echo "✅ Schema synchronized"
    fi
fi

# Generate Prisma Client
echo "🏗️  Generating Prisma Client..."
npx prisma generate

# Run database seeding
echo "🌱 Seeding database..."
if npx prisma db seed; then
    echo "✅ Database seeded successfully"
else
    echo "⚠️  Seeding failed, but continuing deployment..."
fi

echo "🎉 Railway deployment script completed!"