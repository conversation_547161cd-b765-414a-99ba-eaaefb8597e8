import { PrismaClient, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function fixAnaliseOrders() {
  try {
    console.log('🔧 Corrigindo pedidos em ANÁLISE para aparecerem no Anti-Fraud...\n');

    // Buscar todos os pedidos em análise
    const ordersToFix = await prisma.order.findMany({
      where: {
        status: OrderStatus.Analise,
        requiresReview: false,
        deletedAt: null
      }
    });

    console.log(`📊 Encontrados ${ordersToFix.length} pedidos em ANÁLISE sem marcação para revisão.\n`);

    if (ordersToFix.length === 0) {
      console.log('✅ Nenhum pedido precisa ser corrigido!');
      return;
    }

    // Atualizar todos os pedidos em análise para requiresReview = true
    const result = await prisma.order.updateMany({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      },
      data: {
        requiresReview: true
      }
    });

    console.log(`✅ ${result.count} pedidos atualizados!`);
    console.log('\n📌 Todos os pedidos em ANÁLISE agora devem aparecer no dashboard Anti-Fraud.');

    // Verificar o resultado
    console.log('\n🔍 Verificando pedidos atualizados:');
    const updatedOrders = await prisma.order.findMany({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        requiresReview: true,
        riskScore: true,
        riskLevel: true
      }
    });

    updatedOrders.forEach((order, index) => {
      console.log(`\n${index + 1}. Pedido ${order.orderNumber || order.id}`);
      console.log(`   Cliente: ${order.customerName}`);
      console.log(`   Requer Revisão: ${order.requiresReview ? '✅ SIM' : '❌ NÃO'}`);
      console.log(`   Score de Risco: ${order.riskScore || 'Não calculado'}`);
      console.log(`   Nível de Risco: ${order.riskLevel || 'Não definido'}`);
    });

  } catch (error) {
    console.error('❌ Erro durante o processo:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a correção
fixAnaliseOrders();