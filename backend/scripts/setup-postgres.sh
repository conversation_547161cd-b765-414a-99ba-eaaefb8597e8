#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 PostgreSQL Setup Script${NC}"
echo "================================"

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

# Navigate to project root
cd "$(dirname "$0")/../.."

# Start PostgreSQL with Docker Compose
echo -e "${YELLOW}📦 Starting PostgreSQL with Docker Compose...${NC}"
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo -e "${YELLOW}⏳ Waiting for PostgreSQL to be ready...${NC}"
sleep 5

# Check if PostgreSQL is running
if docker-compose ps | grep -q "postgres.*Up"; then
    echo -e "${GREEN}✅ PostgreSQL is running${NC}"
else
    echo -e "${RED}❌ PostgreSQL failed to start${NC}"
    exit 1
fi

# Navigate to backend directory
cd backend

# Check if .env exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 Creating .env file from .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ .env file created${NC}"
    echo -e "${YELLOW}⚠️  Please update the .env file with your secure JWT secret!${NC}"
else
    echo -e "${GREEN}✅ .env file already exists${NC}"
fi

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm install

# Generate Prisma Client
echo -e "${YELLOW}🔧 Generating Prisma Client...${NC}"
npx prisma generate

# Push database schema
echo -e "${YELLOW}🗄️  Creating database schema...${NC}"
npx prisma db push

# Run migrations
echo -e "${YELLOW}🔄 Running migrations...${NC}"
npx prisma migrate dev --name init

# Seed the database
echo -e "${YELLOW}🌱 Seeding database...${NC}"
npm run db:seed

echo -e "${GREEN}✅ PostgreSQL setup completed!${NC}"
echo ""
echo -e "${GREEN}📧 Login credentials:${NC}"
echo "Admin: <EMAIL> / admin123"
echo "Supervisor: <EMAIL> / supervisor123"
echo "Vendedor: <EMAIL> / vendedor123"
echo "Cobrador: <EMAIL> / cobrador123"
echo ""
echo -e "${YELLOW}⚠️  Remember to:${NC}"
echo "1. Update JWT_SECRET in .env file"
echo "2. Change default passwords in production"
echo "3. Configure SSL for production database"