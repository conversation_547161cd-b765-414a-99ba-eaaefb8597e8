import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeTenantMismatch() {
  try {
    console.log('=== TENANT MISMATCH ANALYSIS ===\n');
    console.log('This script analyzes why orders might not be showing up for certain users.\n');

    // Get the admin user info
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('ERROR: <EMAIL> user not found!');
      return;
    }

    console.log('🔍 PROBLEM IDENTIFIED:');
    console.log('═══════════════════════\n');

    console.log(`1. The <EMAIL> user belongs to tenant: "${adminUser.tenantId}"`);
    console.log(`   - This user can ONLY see orders with tenantId = "${adminUser.tenantId}"\n`);

    // Count orders by tenant
    const ordersByTenant = await prisma.order.groupBy({
      by: ['tenantId'],
      _count: true,
      where: { deletedAt: null }
    });

    console.log('2. Current distribution of orders by tenant:');
    ordersByTenant.forEach(group => {
      console.log(`   - Tenant "${group.tenantId}": ${group._count} orders`);
    });

    // Show the mismatch
    console.log('\n3. THE ISSUE:');
    console.log('   Most orders are being created with tenantId = "default"');
    console.log(`   But <EMAIL> has tenantId = "${adminUser.tenantId}"`);
    console.log('   <NAME_EMAIL> CANNOT see these orders!\n');

    // Check webhook created orders
    const webhookOrders = await prisma.order.findMany({
      where: {
        OR: [
          { orderNumber: { startsWith: 'WH-' } },
          { orderNumber: { startsWith: 'WEBHOOK' } },
          { orderNumber: { startsWith: 'sal' } }
        ],
        deletedAt: null
      },
      select: {
        orderNumber: true,
        tenantId: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log('4. Recent webhook-created orders:');
    webhookOrders.forEach(order => {
      console.log(`   - ${order.orderNumber}: tenantId = "${order.tenantId}"`);
    });

    // Show users and their tenants
    console.log('\n5. User distribution by tenant:');
    const usersByTenant = await prisma.user.groupBy({
      by: ['tenantId'],
      _count: true
    });
    
    for (const group of usersByTenant) {
      console.log(`\n   Tenant "${group.tenantId}" has ${group._count} users:`);
      const users = await prisma.user.findMany({
        where: { tenantId: group.tenantId },
        select: { email: true, role: true }
      });
      users.forEach(user => {
        console.log(`     - ${user.email} (${user.role})`);
      });
    }

    console.log('\n\n💡 SOLUTIONS:');
    console.log('═════════════\n');
    console.log('Option 1: Update all "default" tenant orders to match admin\'s tenant');
    console.log(`   - Run: UPDATE "Order" SET "tenantId" = '${adminUser.tenantId}' WHERE "tenantId" = 'default';`);
    console.log('\nOption 2: Update the webhook service to use the correct tenant ID');
    console.log('   - Check webhook-sales.service.ts and ensure it uses the seller\'s tenantId');
    console.log('\nOption 3: Implement a tenant resolution strategy in the webhook');
    console.log('   - If seller exists, use their tenantId');
    console.log('   - Otherwise, use a configured default tenant\n');

    // Check if there's a configuration for default tenant
    const defaultTenantConfig = await prisma.configuration.findFirst({
      where: {
        key: 'defaultTenantId'
      }
    });

    if (defaultTenantConfig) {
      console.log(`Current default tenant configuration: ${JSON.stringify(defaultTenantConfig.value)}`);
    } else {
      console.log('No default tenant configuration found in the Configuration table.');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the analysis
analyzeTenantMismatch();