import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

async function testPermanentDelete() {
  try {
    // Test order ID that's failing
    const orderId = 'ff0ebae6-78ee-4fe1-af89-10bb4ef8b20e';
    const userId = 'd22193dd-763f-470b-a7ad-51efa31fdba3'; // Admin user ID
    
    console.log('Testing permanent delete for order:', orderId);
    
    // First, check if the order exists
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: true,
        billingHistory: true,
        statusHistory: true,
      }
    });

    if (!order) {
      console.log('Order not found');
      return;
    }

    console.log('Order found:', {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      hasItems: order.orderItems.length > 0,
      hasBillingHistory: order.billingHistory.length > 0,
      hasStatusHistory: order.statusHistory.length > 0
    });

    // Try to delete in a transaction
    console.log('\nAttempting to delete order...');
    
    try {
      await prisma.$transaction(async (tx) => {
        // Create audit log
        const auditData = {
          orderId,
          tenantId: order.tenantId,
          action: 'ORDER_PERMANENTLY_DELETED',
          performedBy: userId,
          performedByName: 'Admin',
          performedByRole: 'ADMIN',
          metadata: {
            orderNumber: order.orderNumber,
            customerName: order.customerName,
            totalAmount: order.totalAmount,
            reason: 'Admin manual deletion'
          }
        };

        // Generate signature
        const signingKey = process.env.AUDIT_SIGNING_KEY || 'default-signing-key';
        const signature = crypto
          .createHmac('sha256', signingKey)
          .update(JSON.stringify(auditData))
          .digest('hex');

        // Create the audit log
        await tx.orderAuditLog.create({
          data: {
            ...auditData,
            performedAt: new Date(),
            metadata: JSON.stringify(auditData.metadata),
            signature,
          },
        });

        // Delete related records first
        console.log('Deleting order items...');
        await tx.orderItem.deleteMany({
          where: { orderId }
        });

        console.log('Deleting billing history...');
        await tx.billingHistory.deleteMany({
          where: { orderId }
        });

        console.log('Deleting status history...');
        await tx.orderStatusHistory.deleteMany({
          where: { orderId }
        });

        console.log('Deleting order...');
        await tx.order.delete({
          where: { id: orderId }
        });
      });

      console.log('Order deleted successfully!');
    } catch (deleteError: any) {
      console.error('Delete failed:', deleteError.message);
      console.error('Error code:', deleteError.code);
      console.error('Error meta:', deleteError.meta);
      
      // If it's a foreign key constraint error, find out what's blocking
      if (deleteError.code === 'P2003') {
        console.log('\nChecking for foreign key constraints...');
        
        // Check for audit logs
        const auditLogs = await prisma.orderAuditLog.count({
          where: { orderId }
        });
        console.log('Audit logs for this order:', auditLogs);
      }
    }

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPermanentDelete();