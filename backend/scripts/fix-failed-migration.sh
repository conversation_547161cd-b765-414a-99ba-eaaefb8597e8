#!/bin/bash
set -e

echo "🔧 Fixing Failed Migration"
echo "========================="
echo ""

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Error: .env file not found"
    exit 1
fi

echo "⚠️  This script will mark the failed migration as resolved"
echo "⚠️  Make sure you understand the implications before proceeding"
echo ""
read -p "Continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Operation cancelled"
    exit 0
fi

echo ""
echo "🔍 Marking failed migration as resolved..."
npx prisma migrate resolve --applied "20250108_add_delete_fields"

echo ""
echo "📋 Checking migration status..."
npx prisma migrate status

echo ""
echo "🔧 Now applying pending migrations..."
npx prisma migrate deploy

echo ""
echo "✅ Migrations fixed and applied!"