import { PrismaClient, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient();

// Helper to generate random date within range
function randomDate(daysAgo: number): Date {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  date.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), 0, 0);
  return date;
}

// Helper to generate order number
function generateOrderNumber(index: number): string {
  const timestamp = Date.now() + index;
  return `PED-${timestamp}`;
}

// Generate Brazilian phone
function generatePhone(): string {
  const ddd = ['11', '21', '31', '41', '51', '61', '71', '81', '91'][Math.floor(Math.random() * 9)];
  const firstPart = Math.floor(Math.random() * 90000) + 10000;
  const secondPart = Math.floor(Math.random() * 9000) + 1000;
  return `(${ddd}) 9${firstPart}-${secondPart}`;
}

// Customer names for variety
const customerNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON> Bar<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', 'Gustavo Nunes', 'Priscila Mendes', 'Alexandre Pinto', 'Renata Vieira'
];

// Addresses for variety
const addresses = [
  'Rua das Flores, 123, Jardim Primavera, São Paulo, SP',
  'Av. Brasil, 456, Centro, Rio de Janeiro, RJ',
  'Rua dos Pinheiros, 789, Vila Nova, Belo Horizonte, MG',
  'Rua Augusta, 321, Consolação, São Paulo, SP',
  'Av. Paulista, 1000, Bela Vista, São Paulo, SP',
  'Rua Oscar Freire, 555, Jardins, São Paulo, SP',
  'Av. Atlântica, 2000, Copacabana, Rio de Janeiro, RJ',
  'Rua da Consolação, 333, Centro, São Paulo, SP',
  'Av. Ipiranga, 777, República, São Paulo, SP',
  'Rua Vergueiro, 888, Vila Mariana, São Paulo, SP'
];

// Order scenarios with realistic data
const orderScenarios = [
  // Recent completed orders (6)
  { status: OrderStatus.Completo, daysAgo: 1, value: 299.90, paid: 299.90, tracking: 'BR123456789BR', notes: 'Pagamento via PIX' },
  { status: OrderStatus.Completo, daysAgo: 3, value: 449.90, paid: 449.90, tracking: 'BR234567890BR', notes: 'Pagamento via cartão' },
  { status: OrderStatus.Completo, daysAgo: 5, value: 199.90, paid: 199.90, tracking: 'BR345678901BR', notes: 'Cliente satisfeito' },
  { status: OrderStatus.Completo, daysAgo: 7, value: 599.90, paid: 599.90, tracking: 'BR456789012BR', notes: 'Entrega expressa' },
  { status: OrderStatus.Completo, daysAgo: 10, value: 349.90, paid: 349.90, tracking: 'BR567890123BR', notes: 'Pagamento à vista' },
  { status: OrderStatus.Completo, daysAgo: 15, value: 799.90, paid: 799.90, tracking: 'BR678901234BR', notes: 'Compra recorrente' },
  
  // Partial payments (4)
  { status: OrderStatus.Parcial, daysAgo: 2, value: 499.90, paid: 250.00, tracking: null, notes: 'Pagou 50%, resta R$ 249,90' },
  { status: OrderStatus.Parcial, daysAgo: 4, value: 399.90, paid: 150.00, tracking: null, notes: 'Entrada de R$ 150, parcela pendente' },
  { status: OrderStatus.Parcial, daysAgo: 6, value: 699.90, paid: 400.00, tracking: 'BR789012345BR', notes: 'Pagamento parcial, aguardando resto' },
  { status: OrderStatus.Parcial, daysAgo: 8, value: 299.90, paid: 100.00, tracking: null, notes: 'Cliente pediu prazo para quitar' },
  
  // In negotiation (3)
  { status: OrderStatus.Negociacao, daysAgo: 1, value: 549.90, paid: 0, tracking: null, notes: 'Cliente negociando desconto' },
  { status: OrderStatus.Negociacao, daysAgo: 3, value: 449.90, paid: 50.00, tracking: null, notes: 'Proposta de parcelamento em análise' },
  { status: OrderStatus.Negociacao, daysAgo: 5, value: 899.90, paid: 0, tracking: null, notes: 'Cliente pediu 30% de desconto' },
  
  // Promise to pay (3)
  { status: OrderStatus.Promessa, daysAgo: 2, value: 399.90, paid: 0, tracking: null, notes: 'Prometeu pagar dia 20', promiseDays: 5 },
  { status: OrderStatus.Promessa, daysAgo: 4, value: 299.90, paid: 50.00, tracking: null, notes: 'Pagará o resto sexta-feira', promiseDays: 3 },
  { status: OrderStatus.Promessa, daysAgo: 7, value: 599.90, paid: 0, tracking: null, notes: 'Aguardando salário dia 5', promiseDays: 7 },
  
  // Payment pending (3)
  { status: OrderStatus.PagamentoPendente, daysAgo: 0, value: 249.90, paid: 0, tracking: 'BR890123456BR', notes: 'Produto entregue, aguardando pagamento' },
  { status: OrderStatus.PagamentoPendente, daysAgo: 1, value: 349.90, paid: 0, tracking: 'BR901234567BR', notes: 'Cliente confirmou recebimento' },
  { status: OrderStatus.PagamentoPendente, daysAgo: 2, value: 449.90, paid: 0, tracking: 'BR012345678BR', notes: 'Boleto enviado ao cliente' },
  
  // In transit (2)
  { status: OrderStatus.Transito, daysAgo: 0, value: 299.90, paid: 0, tracking: 'BR111222333BR', notes: 'Saiu para entrega hoje' },
  { status: OrderStatus.Transito, daysAgo: 1, value: 399.90, paid: 0, tracking: 'BR222333444BR', notes: 'Em rota de entrega' },
  
  // Confirm delivery (2)
  { status: OrderStatus.ConfirmarEntrega, daysAgo: 0, value: 599.90, paid: 0, tracking: 'BR333444555BR', notes: 'Entregue, aguardando confirmação' },
  { status: OrderStatus.ConfirmarEntrega, daysAgo: 1, value: 699.90, paid: 0, tracking: 'BR444555666BR', notes: 'Cliente não atendeu, deixado com vizinho' },
  
  // Failed/Cancelled (4)
  { status: OrderStatus.Frustrado, daysAgo: 5, value: 299.90, paid: 0, tracking: null, notes: 'Cliente não tem condições de pagar' },
  { status: OrderStatus.Frustrado, daysAgo: 10, value: 499.90, paid: 0, tracking: null, notes: 'Múltiplas tentativas sem sucesso' },
  { status: OrderStatus.Cancelado, daysAgo: 7, value: 399.90, paid: 0, tracking: null, notes: 'Cliente desistiu da compra' },
  { status: OrderStatus.Cancelado, daysAgo: 12, value: 599.90, paid: 0, tracking: null, notes: 'Produto em falta' },
  
  // Recovery attempts (2)
  { status: OrderStatus.Recuperacao, daysAgo: 3, value: 799.90, paid: 200.00, tracking: null, notes: 'Renegociando dívida antiga' },
  { status: OrderStatus.Recuperacao, daysAgo: 6, value: 999.90, paid: 300.00, tracking: null, notes: 'Cliente voltou a negociar após 30 dias' },
  
  // New order in analysis (1)
  { status: OrderStatus.Analise, daysAgo: 0, value: 1299.90, paid: 0, tracking: null, notes: 'Pedido de alto valor em análise' },
];

async function generateMockSales() {
  console.log('🚀 Starting mock sales generation...');
  
  try {
    // Use a default tenant ID for now
    const tenantId = 'default-tenant';
    
    // Get users
    const sellers = await prisma.user.findMany({
      where: { role: 'VENDEDOR', tenantId: tenantId },
      take: 4
    });
    
    const collectors = await prisma.user.findMany({
      where: { role: 'COBRADOR', tenantId: tenantId },
      take: 2
    });
    
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN', tenantId: tenantId }
    });
    
    if (sellers.length === 0 || collectors.length === 0 || !admin) {
      throw new Error('Please ensure you have sellers, collectors, and admin users created.');
    }
    
    console.log(`✅ Found ${sellers.length} sellers and ${collectors.length} collectors`);
    
    // Clear existing orders (optional - comment out if you want to keep existing data)
    // await prisma.order.deleteMany({ where: { tenantId: tenantId } });
    
    // Generate orders
    console.log('📦 Generating 30 mock orders...');
    
    for (let i = 0; i < orderScenarios.length; i++) {
      const scenario = orderScenarios[i];
      const customerName = customerNames[i % customerNames.length];
      const address = addresses[i % addresses.length];
      const seller = sellers[i % sellers.length];
      const collector = collectors[i % collectors.length];
      
      const orderDate = randomDate(scenario.daysAgo);
      
      const order = await prisma.order.create({
        data: {
          orderNumber: generateOrderNumber(i),
          customerName: customerName,
          customerPhone: generatePhone(),
          customerCPF: `${Math.floor(Math.random() * 900) + 100}.${Math.floor(Math.random() * 900) + 100}.${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 90) + 10}`,
          fullAddress: address,
          total: scenario.value,
          status: scenario.status,
          sellerId: seller.id,
          collectorId: collector.id,
          tenantId: tenantId,
          createdAt: orderDate,
          updatedAt: orderDate,
          paymentReceivedAmount: scenario.paid > 0 ? scenario.paid : null,
          paymentReceivedDate: scenario.paid > 0 ? orderDate : null,
          observation: scenario.notes,
          nextPaymentDate: scenario.promiseDays 
            ? new Date(Date.now() + scenario.promiseDays * 24 * 60 * 60 * 1000)
            : null,
          // Create tracking if exists
          tracking: scenario.tracking ? {
            create: {
              code: scenario.tracking,
              status: scenario.status === OrderStatus.Completo ? 'Entregue' :
                      scenario.status === OrderStatus.Transito ? 'Em trânsito' :
                      scenario.status === OrderStatus.ConfirmarEntrega ? 'Saiu para entrega' :
                      'Postado',
              lastUpdate: orderDate,
              events: []
            }
          } : undefined,
          // Add status history
          statusHistory: {
            create: {
              previousStatus: OrderStatus.Analise,
              newStatus: scenario.status,
              changedById: admin.id,
              changedAt: orderDate,
            }
          },
        }
      });
      
      console.log(`✅ Created order ${i + 1}/30: ${order.orderNumber} - ${scenario.status} - R$ ${scenario.value}`);
    }
    
    // Show summary
    console.log('\n📊 Order Summary:');
    const summary = await prisma.order.groupBy({
      by: ['status'],
      where: { tenantId: tenantId },
      _count: true,
      _sum: {
        total: true,
        paymentReceivedAmount: true
      }
    });
    
    let totalOrderValue = 0;
    let totalReceived = 0;
    
    summary.forEach(stat => {
      console.log(`  ${stat.status}: ${stat._count} orders - Total: R$ ${stat._sum.total?.toFixed(2) || '0'} - Received: R$ ${stat._sum.paymentReceivedAmount?.toFixed(2) || '0'}`);
      totalOrderValue += Number(stat._sum.total || 0);
      totalReceived += Number(stat._sum.paymentReceivedAmount || 0);
    });
    
    console.log(`\n💰 Financial Summary:`);
    console.log(`  Total Order Value: R$ ${totalOrderValue.toFixed(2)}`);
    console.log(`  Total Received: R$ ${totalReceived.toFixed(2)}`);
    console.log(`  Pending: R$ ${(totalOrderValue - totalReceived).toFixed(2)}`);
    console.log(`  Collection Rate: ${((totalReceived / totalOrderValue) * 100).toFixed(1)}%`);
    
    console.log('\n✅ Mock data generation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
generateMockSales()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });