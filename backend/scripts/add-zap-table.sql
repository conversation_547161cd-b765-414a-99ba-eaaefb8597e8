-- Create ZapStatus enum
CREATE TYPE "ZapStatus" AS ENUM ('Ativo', 'Aquecendo', 'Pronto', 'Bloqueado', 'EmAnalise', 'Recuperado', 'StandBy');

-- Create Zap table
CREATE TABLE "Zap" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "tenantId" TEXT NOT NULL,
    "status" "ZapStatus" NOT NULL DEFAULT 'Ativo',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Zap_pkey" PRIMARY KEY ("id")
);

-- Create unique index
CREATE UNIQUE INDEX "Zap_name_tenantId_key" ON "Zap"("name", "tenantId");

-- Create indexes
CREATE INDEX "Zap_tenantId_idx" ON "Zap"("tenantId");
CREATE INDEX "Zap_status_idx" ON "Zap"("status");

-- Add zapSourceId to Order table if not exists
ALTER TABLE "Order" ADD COLUMN IF NOT EXISTS "zapSourceId" TEXT;

-- Create foreign key constraint
ALTER TABLE "Order" ADD CONSTRAINT "Order_zapSourceId_fkey" 
    FOREIGN KEY ("zapSourceId") REFERENCES "Zap"("id") 
    ON DELETE SET NULL ON UPDATE CASCADE;

-- Create index on Order.zapSourceId
CREATE INDEX IF NOT EXISTS "Order_zapSourceId_idx" ON "Order"("zapSourceId");