import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixWebhookIdMapping() {
  console.log('=== Fixing Webhook ID Mapping ===\n');

  try {
    // First, let's check the current mapping
    const currentMapping = await prisma.$queryRaw<any[]>`
      SELECT * FROM webhook_mappings 
      WHERE "payloadKey" = 'sale.code'
    `;

    if (currentMapping.length > 0) {
      console.log('Current mapping found:');
      console.log(`sale.code => ${currentMapping[0].entityColumn}\n`);

      // Update the mapping to use orderNumber instead of id
      const updateResult = await prisma.$executeRaw`
        UPDATE webhook_mappings 
        SET "entityColumn" = 'orderNumber',
            "description" = 'Sale code from webhook mapped to orderNumber field',
            "updatedAt" = NOW()
        WHERE "payloadKey" = 'sale.code'
      `;

      console.log(`Updated ${updateResult} mapping(s)\n`);

      // Verify the update
      const updatedMapping = await prisma.$queryRaw<any[]>`
        SELECT * FROM webhook_mappings 
        WHERE "payloadKey" = 'sale.code'
      `;

      if (updatedMapping.length > 0) {
        console.log('Updated mapping:');
        console.log(`sale.code => ${updatedMapping[0].entityColumn}`);
        console.log(`Description: ${updatedMapping[0].description}`);
      }
    } else {
      console.log('No mapping found for sale.code');
    }

    // Also check if there's a better field for orderNumber
    console.log('\n=== Checking for other order number fields ===');
    const orderFields = await prisma.$queryRaw<any[]>`
      SELECT * FROM webhook_mappings 
      WHERE "payloadKey" LIKE '%order%' 
         OR "payloadKey" LIKE '%pedido%'
         OR "payloadKey" LIKE '%code%'
      ORDER BY "payloadKey"
    `;

    console.log('\nAvailable order-related fields:');
    for (const field of orderFields) {
      console.log(`${field.payloadKey} => ${field.entityColumn || 'NOT MAPPED'} (Sample: ${field.sampleValue})`);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixWebhookIdMapping().catch(console.error);