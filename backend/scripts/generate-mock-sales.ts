import { PrismaClient, OrderStatus, Role } from '@prisma/client';
import { faker } from '@faker-js/faker/locale/pt_BR';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// Helper to generate Brazilian CPF
function generateCPF(): string {
  const randomDigits = () => Math.floor(Math.random() * 10);
  const digits = Array.from({ length: 9 }, randomDigits);
  
  // Calculate first verification digit
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += digits[i] * (10 - i);
  }
  const firstVerifier = 11 - (sum % 11);
  digits.push(firstVerifier >= 10 ? 0 : firstVerifier);
  
  // Calculate second verification digit
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += digits[i] * (11 - i);
  }
  const secondVerifier = 11 - (sum % 11);
  digits.push(secondVerifier >= 10 ? 0 : secondVerifier);
  
  // Format as XXX.XXX.XXX-XX
  return `${digits.slice(0, 3).join('')}.${digits.slice(3, 6).join('')}.${digits.slice(6, 9).join('')}-${digits.slice(9).join('')}`;
}

// Helper to generate random date within range
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper to format date as Brazilian format
function formatDateBR(date: Date): string {
  return date.toLocaleDateString('pt-BR');
}

// Scenarios for different order types
const scenarios = [
  // Completed orders (6)
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 2, hasTracking: true },
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 5, hasTracking: true },
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 10, hasTracking: true },
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 15, hasTracking: true },
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 20, hasTracking: true },
  { status: OrderStatus.Completo, paymentReceived: 1, daysAgo: 30, hasTracking: true },
  
  // Partial payments (4)
  { status: OrderStatus.Parcial, paymentReceived: 0.5, daysAgo: 3, hasTracking: true },
  { status: OrderStatus.Parcial, paymentReceived: 0.3, daysAgo: 7, hasTracking: true },
  { status: OrderStatus.Parcial, paymentReceived: 0.7, daysAgo: 12, hasTracking: false },
  { status: OrderStatus.Parcial, paymentReceived: 0.6, daysAgo: 18, hasTracking: false },
  
  // In negotiation (3)
  { status: OrderStatus.Negociacao, paymentReceived: 0, daysAgo: 1, hasTracking: false },
  { status: OrderStatus.Negociacao, paymentReceived: 0.2, daysAgo: 4, hasTracking: false },
  { status: OrderStatus.Negociacao, paymentReceived: 0.1, daysAgo: 8, hasTracking: false },
  
  // Promise to pay (3)
  { status: OrderStatus.Promessa, paymentReceived: 0, daysAgo: 2, hasTracking: false, promiseDate: 5 },
  { status: OrderStatus.Promessa, paymentReceived: 0.1, daysAgo: 6, hasTracking: false, promiseDate: 3 },
  { status: OrderStatus.Promessa, paymentReceived: 0, daysAgo: 9, hasTracking: false, promiseDate: 7 },
  
  // Payment pending (3)
  { status: OrderStatus.PagamentoPendente, paymentReceived: 0, daysAgo: 0, hasTracking: true },
  { status: OrderStatus.PagamentoPendente, paymentReceived: 0, daysAgo: 1, hasTracking: true },
  { status: OrderStatus.PagamentoPendente, paymentReceived: 0, daysAgo: 3, hasTracking: true },
  
  // In transit (2)
  { status: OrderStatus.Transito, paymentReceived: 0, daysAgo: 1, hasTracking: true },
  { status: OrderStatus.Transito, paymentReceived: 0, daysAgo: 2, hasTracking: true },
  
  // Delivery confirmation (2)
  { status: OrderStatus.ConfirmarEntrega, paymentReceived: 0, daysAgo: 0, hasTracking: true },
  { status: OrderStatus.ConfirmarEntrega, paymentReceived: 0, daysAgo: 1, hasTracking: true },
  
  // Failed/Cancelled (4)
  { status: OrderStatus.Frustrado, paymentReceived: 0, daysAgo: 5, hasTracking: false },
  { status: OrderStatus.Frustrado, paymentReceived: 0, daysAgo: 10, hasTracking: false },
  { status: OrderStatus.Cancelado, paymentReceived: 0, daysAgo: 7, hasTracking: false },
  { status: OrderStatus.Cancelado, paymentReceived: 0, daysAgo: 14, hasTracking: false },
  
  // Recovery (2)
  { status: OrderStatus.Recuperacao, paymentReceived: 0.4, daysAgo: 3, hasTracking: false },
  { status: OrderStatus.Recuperacao, paymentReceived: 0.2, daysAgo: 6, hasTracking: false },
  
  // Analysis (1)
  { status: OrderStatus.Analise, paymentReceived: 0, daysAgo: 0, hasTracking: false },
];

async function generateMockSales() {
  console.log('🚀 Starting mock sales generation...');
  
  try {
    // Use a default tenant ID for now
    const tenantId = 'default-tenant';
    console.log(`✅ Using tenant ID: ${tenantId}`);
    
    // Get or create users
    let admin = await prisma.user.findFirst({
      where: { email: '<EMAIL>', tenantId: tenantId }
    });
    
    if (!admin) {
      admin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('admin123', 10),
          name: 'Admin User',
          role: Role.ADMIN,
          tenantId: tenantId,
        }
      });
      console.log('✅ Created admin user');
    }
    
    // Create sellers
    const sellers = [];
    const sellerNames = ['João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa'];
    
    for (let i = 0; i < sellerNames.length; i++) {
      let seller = await prisma.user.findFirst({
        where: { email: `vendedor${i + 1}@zencash.com`, tenantId: tenantId }
      });
      
      if (!seller) {
        seller = await prisma.user.create({
          data: {
            email: `vendedor${i + 1}@zencash.com`,
            password: await bcrypt.hash('vendedor123', 10),
            name: sellerNames[i],
            role: Role.VENDEDOR,
            tenantId: tenantId,
          }
        });
      }
      sellers.push(seller);
    }
    console.log(`✅ Ensured ${sellers.length} sellers exist`);
    
    // Create collectors
    const collectors = [];
    const collectorNames = ['Carlos Mendes', 'Patricia Lima'];
    
    for (let i = 0; i < collectorNames.length; i++) {
      let collector = await prisma.user.findFirst({
        where: { email: `cobrador${i + 1}@zencash.com`, tenantId: tenantId }
      });
      
      if (!collector) {
        collector = await prisma.user.create({
          data: {
            email: `cobrador${i + 1}@zencash.com`,
            password: await bcrypt.hash('cobrador123', 10),
            name: collectorNames[i],
            role: Role.COBRADOR,
            tenantId: tenantId,
          }
        });
      }
      collectors.push(collector);
    }
    console.log(`✅ Ensured ${collectors.length} collectors exist`);
    
    // Create products if they don't exist
    const products = [];
    const productData = [
      { name: 'Produto A', price: 150.00 },
      { name: 'Produto B', price: 250.00 },
      { name: 'Produto C', price: 350.00 },
      { name: 'Produto D', price: 450.00 },
    ];
    
    for (const prod of productData) {
      let product = await prisma.product.findFirst({
        where: { name: prod.name, tenantId: tenantId }
      });
      
      if (!product) {
        product = await prisma.product.create({
          data: {
            name: prod.name,
            description: `Descrição do ${prod.name}`,
            price: prod.price,
            cost: prod.price * 0.6,
            sku: `SKU-${prod.name.replace(' ', '')}`,
            active: true,
            tenantId: tenantId,
          }
        });
      }
      products.push(product);
    }
    console.log(`✅ Ensured ${products.length} products exist`);
    
    // Delete existing mock orders to start fresh
    console.log('🗑️  Cleaning up existing mock orders...');
    await prisma.order.deleteMany({
      where: {
        tenantId: tenantId,
        customerName: { contains: '[MOCK]' }
      }
    });
    
    // Generate orders
    console.log('📦 Generating 30 mock orders...');
    const orders = [];
    
    for (let i = 0; i < scenarios.length; i++) {
      const scenario = scenarios[i];
      const seller = sellers[i % sellers.length];
      const collector = collectors[i % collectors.length];
      const product = products[i % products.length];
      
      // Generate order date
      const orderDate = new Date();
      orderDate.setDate(orderDate.getDate() - scenario.daysAgo);
      
      // Generate customer data
      const customerName = `[MOCK] ${faker.person.fullName()}`;
      const customerPhone = faker.phone.number('(##) #####-####');
      const customerCPF = generateCPF();
      
      // Generate address
      const fullAddress = `${faker.location.street()}, ${faker.number.int({ min: 1, max: 9999 })}, ${faker.location.city()}, ${faker.location.state({ abbreviated: true })}`;
      
      // Calculate payment amounts
      const orderValue = product.price * (1 + Math.random() * 2); // 1x to 3x product price
      const paymentReceived = orderValue * scenario.paymentReceived;
      
      // Create order
      const order = await prisma.order.create({
        data: {
          orderNumber: `MOCK-${Date.now()}-${i}`,
          customerName,
          customerPhone,
          customerCPF,
          fullAddress,
          total: orderValue,
          status: scenario.status,
          sellerId: seller.id,
          collectorId: collector.id,
          tenantId: tenantId,
          createdAt: orderDate,
          updatedAt: orderDate,
          paymentReceivedAmount: paymentReceived > 0 ? paymentReceived : null,
          paymentReceivedDate: paymentReceived > 0 ? orderDate : null,
          // Add promise date if it's a promise scenario
          nextPaymentDate: scenario.promiseDate 
            ? new Date(Date.now() + scenario.promiseDate * 24 * 60 * 60 * 1000)
            : null,
          // Add items
          items: {
            create: [{
              productId: product.id,
              quantity: Math.floor(Math.random() * 3) + 1,
              unitPrice: product.price,
              total: product.price * (Math.floor(Math.random() * 3) + 1),
            }]
          },
          // Add tracking if needed
          tracking: scenario.hasTracking ? {
            create: {
              code: `BR${faker.string.numeric(9)}BR`,
              status: scenario.status === OrderStatus.Completo ? 'Entregue' :
                      scenario.status === OrderStatus.Transito ? 'Em trânsito' :
                      'Postado',
              lastUpdate: orderDate,
              events: [],
            }
          } : undefined,
          // Add status history
          statusHistory: {
            create: {
              previousStatus: OrderStatus.Analise,
              newStatus: scenario.status,
              changedById: admin.id,
              changedAt: orderDate,
            }
          },
        },
        include: {
          seller: true,
          collector: true,
          items: true,
          tracking: true,
          statusHistory: true,
        }
      });
      
      orders.push(order);
      console.log(`✅ Created order ${i + 1}/30: ${order.orderNumber} - Status: ${order.status}`);
    }
    
    // Generate summary statistics
    console.log('\n📊 Summary Statistics:');
    const statusCounts = await prisma.order.groupBy({
      by: ['status'],
      where: { tenantId: tenantId },
      _count: true,
    });
    
    console.log('\nOrders by Status:');
    statusCounts.forEach(stat => {
      console.log(`  ${stat.status}: ${stat._count} orders`);
    });
    
    const totalRevenue = await prisma.order.aggregate({
      where: { tenantId: tenantId },
      _sum: { total: true, paymentReceivedAmount: true },
    });
    
    console.log(`\nFinancial Summary:`);
    console.log(`  Total Order Value: R$ ${totalRevenue._sum.total?.toFixed(2) || '0.00'}`);
    console.log(`  Total Received: R$ ${totalRevenue._sum.paymentReceivedAmount?.toFixed(2) || '0.00'}`);
    
    console.log('\n✅ Mock data generation completed successfully!');
    console.log('\n📝 Created users:');
    console.log('  Admin: <EMAIL> / admin123');
    sellers.forEach((s, i) => {
      console.log(`  Seller ${i + 1}: vendedor${i + 1}@zencash.com / vendedor123`);
    });
    collectors.forEach((c, i) => {
      console.log(`  Collector ${i + 1}: cobrador${i + 1}@zencash.com / cobrador123`);
    });
    
  } catch (error) {
    console.error('❌ Error generating mock sales:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
generateMockSales()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });