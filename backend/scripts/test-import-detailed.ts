import axios, { AxiosError } from 'axios';
import * as dotenv from 'dotenv';
import { format } from 'date-fns';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';
const TENANT_ID = '28a833c0-c2a1-4498-85ca-b028f982ffb2';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') {
  const timestamp = new Date().toISOString();
  const colorMap = {
    info: colors.blue,
    success: colors.green,
    error: colors.red,
    warning: colors.yellow
  };
  console.log(`${colorMap[type]}[${timestamp}] ${message}${colors.reset}`);
}

function logSection(title: string) {
  console.log(`\n${colors.bright}${colors.cyan}${'='.repeat(60)}${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}${title}${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}${'='.repeat(60)}${colors.reset}\n`);
}

function logSubSection(title: string) {
  console.log(`\n${colors.magenta}${'-'.repeat(40)}${colors.reset}`);
  console.log(`${colors.magenta}${title}${colors.reset}`);
  console.log(`${colors.magenta}${'-'.repeat(40)}${colors.reset}\n`);
}

function logRequest(method: string, url: string, headers: any, data?: any) {
  logSubSection('REQUEST DETAILS');
  console.log(`${colors.yellow}Method:${colors.reset} ${method}`);
  console.log(`${colors.yellow}URL:${colors.reset} ${url}`);
  console.log(`${colors.yellow}Headers:${colors.reset}`);
  Object.entries(headers).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  if (data) {
    console.log(`${colors.yellow}Body:${colors.reset}`);
    console.log(JSON.stringify(data, null, 2));
  }
}

function logResponse(status: number, statusText: string, headers: any, data: any) {
  logSubSection('RESPONSE DETAILS');
  console.log(`${colors.green}Status:${colors.reset} ${status} ${statusText}`);
  console.log(`${colors.green}Headers:${colors.reset}`);
  Object.entries(headers).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log(`${colors.green}Body:${colors.reset}`);
  console.log(JSON.stringify(data, null, 2));
}

function logError(error: AxiosError) {
  logSubSection('ERROR DETAILS');
  if (error.response) {
    console.log(`${colors.red}Status:${colors.reset} ${error.response.status} ${error.response.statusText}`);
    console.log(`${colors.red}Response Headers:${colors.reset}`);
    Object.entries(error.response.headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    console.log(`${colors.red}Response Body:${colors.reset}`);
    console.log(JSON.stringify(error.response.data, null, 2));
  } else if (error.request) {
    console.log(`${colors.red}No response received${colors.reset}`);
    console.log(`${colors.red}Request:${colors.reset}`, error.request);
  } else {
    console.log(`${colors.red}Error Message:${colors.reset} ${error.message}`);
  }
  console.log(`${colors.red}Stack:${colors.reset}`);
  console.log(error.stack);
}

async function login(): Promise<string> {
  logSection('1. LOGIN TEST');
  
  const loginData = {
    email: '<EMAIL>',
    password: 'admin123',
  };
  
  const headers = {
    'x-tenant-id': TENANT_ID,
    'Content-Type': 'application/json'
  };
  
  const url = `${API_URL}/auth/login`;
  
  logRequest('POST', url, headers, loginData);
  
  try {
    const response = await axios.post(url, loginData, { headers });
    logResponse(response.status, response.statusText, response.headers, response.data);
    
    if (!response.data.access_token) {
      throw new Error('No access token in response');
    }
    
    log('Login successful! Token obtained', 'success');
    return response.data.access_token;
  } catch (error) {
    if (error instanceof AxiosError) {
      logError(error);
    }
    throw error;
  }
}

async function testCheckEndpoint(token: string, orderNumber: string) {
  logSection(`2. CHECK ORDER EXISTS TEST (Order: ${orderNumber})`);
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'x-tenant-id': TENANT_ID,
    'Content-Type': 'application/json'
  };
  
  const url = `${API_URL}/orders/check/${orderNumber}`;
  
  logRequest('GET', url, headers);
  
  try {
    const response = await axios.get(url, { headers });
    logResponse(response.status, response.statusText, response.headers, response.data);
    log(`Order ${orderNumber} exists: ${response.data.exists}`, 'success');
    return response.data.exists;
  } catch (error) {
    if (error instanceof AxiosError) {
      logError(error);
      // A 404 might be expected if order doesn't exist
      if (error.response?.status === 404) {
        log('Order not found (expected for new orders)', 'warning');
        return false;
      }
    }
    throw error;
  }
}

async function testImportEndpoint(token: string, importData: any) {
  logSection('3. IMPORT ORDER TEST');
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'x-tenant-id': TENANT_ID,
    'Content-Type': 'application/json'
  };
  
  const url = `${API_URL}/orders/import`;
  
  logRequest('POST', url, headers, importData);
  
  try {
    const response = await axios.post(url, importData, { headers });
    logResponse(response.status, response.statusText, response.headers, response.data);
    log('Order imported successfully!', 'success');
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      logError(error);
    }
    throw error;
  }
}

async function testFullWorkflow() {
  log('Starting comprehensive import order endpoint test', 'info');
  log(`API URL: ${API_URL}`, 'info');
  log(`Tenant ID: ${TENANT_ID}`, 'info');
  
  try {
    // Step 1: Login
    const token = await login();
    
    // Generate unique order ID
    const uniqueOrderId = `TEST_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    
    // Step 2: Check if order exists (should not exist)
    log(`\nChecking if order ${uniqueOrderId} exists...`, 'info');
    const existsBefore = await testCheckEndpoint(token, uniqueOrderId);
    
    if (existsBefore) {
      log('Order already exists! This is unexpected for a new test order.', 'warning');
    }
    
    // Step 3: Import the order with comprehensive test data
    const importData = {
      // Required fields
      idVenda: uniqueOrderId,
      cliente: 'João da Silva - Test Customer',
      telefone: '11987654321',
      valorVenda: 299.99,
      
      // Optional fields - testing all of them
      dataVenda: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      oferta: 'Produto Teste Premium',
      status: 'PENDENTE',
      situacaoVenda: 'Analise',
      valorRecebido: 0,
      historico: 'Pedido criado via script de teste detalhado',
      ultimaAtualizacao: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
      codigoRastreio: 'BR123456789BR',
      statusCorreios: 'Postado',
      vendedor: 'Vendedor Teste',
      operador: 'Operador Teste',
      zap: 'https://wa.me/5511987654321',
      
      // Address fields
      estadoDestinatario: 'SP',
      cidadeDestinatario: 'São Paulo',
      ruaDestinatario: 'Rua Teste de Importação',
      cepDestinatario: '01310-100',
      complementoDestinatario: 'Apto 123',
      bairroDestinatario: 'Bela Vista',
      numeroEnderecoDestinatario: '1000',
      dataEstimadaChegada: format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
      
      // Affiliate fields
      codigoAfiliado: 'AFF001',
      nomeAfiliado: 'Afiliado Teste',
      emailAfiliado: '<EMAIL>',
      documentoAfiliado: '12345678901',
      
      // Payment fields
      dataRecebimento: null,
      dataNegociacao: format(new Date(), 'yyyy-MM-dd'),
      formaPagamento: 'PIX',
      documentoCliente: '98765432109',
      
      // Partial payment fields
      parcial: false,
      pagamentoParcial: 0,
      formaPagamentoParcial: null,
      dataPagamentoParcial: null
    };
    
    const importedOrder = await testImportEndpoint(token, importData);
    
    // Step 4: Verify the order now exists
    log(`\nVerifying order ${uniqueOrderId} was created...`, 'info');
    const existsAfter = await testCheckEndpoint(token, uniqueOrderId);
    
    if (existsAfter) {
      log('Order successfully created and verified!', 'success');
    } else {
      log('Order was imported but check endpoint says it doesn\'t exist!', 'error');
    }
    
    // Summary
    logSection('TEST SUMMARY');
    console.log(`${colors.green}✓ Login successful${colors.reset}`);
    console.log(`${colors.green}✓ Check endpoint working${colors.reset}`);
    console.log(`${colors.green}✓ Import endpoint working${colors.reset}`);
    console.log(`${colors.green}✓ Order verification successful${colors.reset}`);
    
    if (importedOrder.id) {
      console.log(`\n${colors.bright}Created Order ID: ${importedOrder.id}${colors.reset}`);
      console.log(`${colors.bright}Order Number: ${uniqueOrderId}${colors.reset}`);
    }
    
  } catch (error) {
    logSection('TEST FAILED');
    if (error instanceof Error) {
      log(`Error: ${error.message}`, 'error');
    } else {
      log(`Unknown error: ${error}`, 'error');
    }
    process.exit(1);
  }
}

// Test edge cases
async function testEdgeCases(token: string) {
  logSection('4. EDGE CASE TESTS');
  
  // Test 1: Missing required fields
  logSubSection('Test 1: Missing required fields');
  try {
    await testImportEndpoint(token, {
      idVenda: `INVALID_${Date.now()}`,
      // Missing cliente, telefone, valorVenda
    });
    log('Should have failed but didn\'t!', 'error');
  } catch (error) {
    log('Expected failure for missing required fields', 'success');
  }
  
  // Test 2: Invalid data types
  logSubSection('Test 2: Invalid data types');
  try {
    await testImportEndpoint(token, {
      idVenda: `INVALID_TYPE_${Date.now()}`,
      cliente: 'Test Client',
      telefone: '11999999999',
      valorVenda: 'not-a-number', // Should be number
    });
    log('Should have failed but didn\'t!', 'error');
  } catch (error) {
    log('Expected failure for invalid data types', 'success');
  }
  
  // Test 3: Duplicate order
  logSubSection('Test 3: Duplicate order');
  const duplicateId = `DUPLICATE_${Date.now()}`;
  const validData = {
    idVenda: duplicateId,
    cliente: 'Duplicate Test',
    telefone: '11888888888',
    valorVenda: 100.00,
  };
  
  try {
    // First import should succeed
    await testImportEndpoint(token, validData);
    log('First import succeeded', 'success');
    
    // Second import with same ID
    await testImportEndpoint(token, validData);
    log('Duplicate import succeeded (might be intended behavior)', 'warning');
  } catch (error) {
    log('Duplicate import failed (might be intended behavior)', 'info');
  }
}

// Main execution
async function main() {
  try {
    await testFullWorkflow();
    
    // Optional: Run edge case tests
    const runEdgeCases = process.argv.includes('--edge-cases');
    if (runEdgeCases) {
      const token = await login();
      await testEdgeCases(token);
    }
    
    log('\nAll tests completed!', 'success');
  } catch (error) {
    log('Test suite failed', 'error');
    process.exit(1);
  }
}

// Run the tests
main();