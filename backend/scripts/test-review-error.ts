import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testReviewError() {
  try {
    // Get the specific order that's failing
    const orderId = '54598d12-3648-4d2b-8455-3e8b75650bd6';
    
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        seller: true,
        collector: true,
      }
    });

    if (!order) {
      console.log('Order not found with ID:', orderId);
      return;
    }

    console.log('Order found:', {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      tenantId: order.tenantId,
      reviewedBy: order.reviewedBy,
      reviewDecision: order.reviewDecision,
      requiresReview: order.requiresReview,
      riskScore: order.riskScore,
      riskLevel: order.riskLevel
    });

    // Check if there's a field mismatch
    console.log('\nOrder schema fields:', Object.keys(order));

    // Try to simulate the update
    console.log('\nTrying to update order...');
    try {
      const updated = await prisma.order.update({
        where: { id: orderId },
        data: {
          reviewedBy: 'd22193dd-763f-470b-a7ad-51efa31fdba3', // Admin ID
          reviewedByName: 'Admin',
          reviewedByRole: 'ADMIN',
          reviewedAt: new Date(),
          reviewDecision: 'APPROVE_ORDER',
          reviewDuration: 1000,
          requiresReview: false
        }
      });
      console.log('Update successful!');
    } catch (updateError: any) {
      console.error('Update failed:', updateError.message);
      console.error('Error code:', updateError.code);
    }

  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testReviewError();