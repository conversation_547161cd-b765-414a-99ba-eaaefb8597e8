import axios from 'axios';

async function testLoginDetailed() {
  console.log('=== Testing Login with Different Methods ===\n');
  
  const apiUrl = 'https://zencash-production.up.railway.app/api/v1';
  const email = '<EMAIL>';
  const password = 'admin123';
  const tenantId = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
  
  // Test 1: With axios (like our script)
  console.log('Test 1: Login with axios (like our script)');
  try {
    const response = await axios.post(`${apiUrl}/auth/login`, {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId
      }
    });
    console.log('✅ Success with axios:', response.data.user);
  } catch (error: any) {
    console.log('❌ Failed with axios:', error.response?.data);
  }
  
  // Test 2: With fetch (like frontend)
  console.log('\nTest 2: Login with fetch (like frontend)');
  try {
    const response = await fetch(`${apiUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId
      },
      body: JSON.stringify({
        email,
        password
      })
    });
    
    const data = await response.json();
    if (response.ok) {
      console.log('✅ Success with fetch:', data.user);
    } else {
      console.log('❌ Failed with fetch:', data);
    }
  } catch (error: any) {
    console.log('❌ Failed with fetch:', error.message);
  }
  
  // Test 3: Check if user exists
  console.log('\nTest 3: Reset password again to be sure');
  try {
    const resetResponse = await axios.post(`${apiUrl}/auth/reset-admin`, {
      secret: 'zencash-reset-2025'
    }, {
      headers: {
        'x-tenant-id': tenantId
      }
    });
    console.log('✅ Password reset:', resetResponse.data);
    
    // Try login again
    console.log('\nTest 4: Login after reset');
    const loginResponse = await axios.post(`${apiUrl}/auth/login`, {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId
      }
    });
    console.log('✅ Login after reset:', loginResponse.data.user);
  } catch (error: any) {
    console.log('❌ Error:', error.response?.data || error.message);
  }
}

testLoginDetailed();