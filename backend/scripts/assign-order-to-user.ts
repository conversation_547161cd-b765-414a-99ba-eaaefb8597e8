import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function assignOrderToUser(orderNumber: string, userEmail: string) {
  try {
    // Find the user
    const user = await prisma.user.findFirst({
      where: { email: userEmail },
      select: { id: true, name: true, role: true }
    });

    if (!user) {
      console.error(`User with email ${userEmail} not found`);
      return;
    }

    // Find the order
    const order = await prisma.order.findFirst({
      where: { orderNumber },
      select: { id: true, orderNumber: true, customerName: true, sellerId: true }
    });

    if (!order) {
      console.error(`Order ${orderNumber} not found`);
      return;
    }

    // Update the order based on user role
    if (user.role === 'VENDEDOR') {
      await prisma.order.update({
        where: { id: order.id },
        data: { sellerId: user.id }
      });
      console.log(`Order ${orderNumber} assigned to seller ${user.name}`);
    } else if (user.role === 'COBRADOR') {
      await prisma.order.update({
        where: { id: order.id },
        data: { collectorId: user.id }
      });
      console.log(`Order ${orderNumber} assigned to collector ${user.name}`);
    } else {
      console.log(`User ${user.name} is ${user.role} and can see all orders`);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Usage: ts-node assign-order-to-user.ts <orderNumber> <userEmail>
const orderNumber = process.argv[2] || 'ID00001';
const userEmail = process.argv[3] || '<EMAIL>';

assignOrderToUser(orderNumber, userEmail);