const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function seedProducts() {
  try {
    console.log('🚀 Creating test products...\n');
    
    // Create products with variations
    const products = [
      {
        name: 'Whey Protein Isolado',
        description: 'Proteína isolada de alta qualidade',
        imageUrl: 'https://via.placeholder.com/300x300/4CAF50/FFFFFF?text=Whey',
        active: true,
        variations: [
          { variation: 'Cápsulas', sku: 'WHE-CAP-1', price: 89.90 },
          { variation: 'Gotas', sku: 'WHE-GOT-1', price: 79.90 },
          { variation: '1kg Baunilha', sku: 'WHE-1KG-BAU', price: 149.90 },
        ],
      },
      {
        name: 'C<PERSON>tina Monohidratada',
        description: 'Creatina pura para performance',
        imageUrl: 'https://via.placeholder.com/300x300/2196F3/FFFFFF?text=Creatina',
        active: true,
        variations: [
          { variation: 'Cápsulas', sku: 'CRE-CAP-1', price: 59.90 },
          { variation: '300g Pó', sku: 'CRE-300G', price: 89.90 },
        ],
      },
      {
        name: 'Ômega 3',
        description: 'Ácidos graxos essenciais',
        imageUrl: 'https://via.placeholder.com/300x300/FF9800/FFFFFF?text=Omega3',
        active: true,
        variations: [
          { variation: 'Cápsulas', sku: 'OME-CAP-1', price: 49.90 },
          { variation: 'Gotas', sku: 'OME-GOT-1', price: 39.90 },
        ],
      },
      {
        name: 'Vitamina D3',
        description: 'Vitamina D3 2000UI',
        imageUrl: 'https://via.placeholder.com/300x300/9C27B0/FFFFFF?text=VitD3',
        active: true,
        variations: [
          { variation: 'Cápsulas', sku: 'VTD-CAP-1', price: 29.90 },
          { variation: 'Gotas', sku: 'VTD-GOT-1', price: 24.90 },
          { variation: 'Spray', sku: 'VTD-SPR-1', price: 34.90 },
        ],
      },
      {
        name: 'Colágeno Hidrolisado',
        description: 'Colágeno tipo I e III',
        imageUrl: 'https://via.placeholder.com/300x300/E91E63/FFFFFF?text=Colageno',
        active: true,
        variations: [
          { variation: 'Cápsulas', sku: 'COL-CAP-1', price: 69.90 },
          { variation: 'Gel', sku: 'COL-GEL-1', price: 79.90 },
          { variation: 'Creme', sku: 'COL-CRE-1', price: 89.90 },
          { variation: '250g Pó Limão', sku: 'COL-250G-LIM', price: 99.90 },
        ],
      },
    ];
    
    for (const productData of products) {
      const { variations, ...product } = productData;
      
      // Check if product already exists
      const existingProduct = await prisma.product.findFirst({
        where: { name: product.name },
      });
      
      if (existingProduct) {
        console.log(`⚠️  Product "${product.name}" already exists, skipping...`);
        continue;
      }
      
      // Create product with variations
      const createdProduct = await prisma.product.create({
        data: {
          ...product,
          variations: {
            create: variations.map(v => ({
              ...v,
              active: true,
              inventory: {
                create: {
                  quantity: Math.floor(Math.random() * 100) + 50, // Random stock between 50-150
                  minAlert: 20,
                },
              },
            })),
          },
        },
        include: {
          variations: {
            include: {
              inventory: true,
            },
          },
        },
      });
      
      console.log(`✅ Created product: ${createdProduct.name}`);
      createdProduct.variations.forEach(v => {
        console.log(`   - ${v.variation} (SKU: ${v.sku}) - Stock: ${v.inventory?.quantity}`);
      });
      console.log('');
    }
    
    // List all products
    const allProducts = await prisma.product.findMany({
      include: {
        variations: true,
        _count: {
          select: { variations: true },
        },
      },
    });
    
    console.log('📊 Total products in database:', allProducts.length);
    allProducts.forEach(p => {
      console.log(`   - ${p.name} (${p._count.variations} variations) ${p.active ? '✓' : '✗'}`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding products:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

seedProducts()
  .then(() => {
    console.log('\n✅ Product seeding completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });