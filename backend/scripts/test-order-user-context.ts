import { PrismaClient, OrderStatus, Role } from '@prisma/client';

const prisma = new PrismaClient();

async function testOrderUserContext() {
  try {
    console.log('Testing order visibility by user role...\n');

    // 1. First, let's find the order with Analise status
    const analiseOrder = await prisma.order.findFirst({
      where: {
        status: OrderStatus.Analise,
        deletedAt: null
      },
      include: {
        seller: true,
        collector: true
      }
    });

    if (!analiseOrder) {
      console.log('No order with Analise status found!');
      return;
    }

    console.log('Found order with Analise status:');
    console.log(`- Order: ${analiseOrder.orderNumber}`);
    console.log(`- Customer: ${analiseOrder.customerName}`);
    console.log(`- Seller: ${analiseOrder.seller?.name} (ID: ${analiseOrder.sellerId})`);
    console.log(`- Collector: ${analiseOrder.collector?.name || 'None'} (ID: ${analiseOrder.collectorId || 'None'})`);
    console.log(`- Tenant: ${analiseOrder.tenantId}`);

    // 2. Test different user contexts
    console.log('\n2. TESTING USER ROLE FILTERS:');

    // Test as ADMIN (should see all orders)
    console.log('\na) As ADMIN user:');
    const adminWhere = { deletedAt: null, status: OrderStatus.Analise };
    const adminOrders = await prisma.order.count({ where: adminWhere });
    console.log(`   Can see ${adminOrders} orders with Analise status`);

    // Test as SUPERVISOR (should see all orders)
    console.log('\nb) As SUPERVISOR user:');
    const supervisorWhere = { deletedAt: null, status: OrderStatus.Analise };
    const supervisorOrders = await prisma.order.count({ where: supervisorWhere });
    console.log(`   Can see ${supervisorOrders} orders with Analise status`);

    // Test as VENDEDOR (should only see their own orders)
    console.log('\nc) As VENDEDOR user:');
    const vendedorWhere = { 
      deletedAt: null, 
      status: OrderStatus.Analise,
      sellerId: analiseOrder.sellerId 
    };
    const vendedorOrders = await prisma.order.count({ where: vendedorWhere });
    console.log(`   Seller ${analiseOrder.sellerId} can see ${vendedorOrders} orders with Analise status`);

    // Test as different VENDEDOR
    console.log('\nd) As different VENDEDOR user:');
    const otherSeller = await prisma.user.findFirst({
      where: {
        role: Role.VENDEDOR,
        id: { not: analiseOrder.sellerId }
      }
    });
    if (otherSeller) {
      const otherVendedorWhere = { 
        deletedAt: null, 
        status: OrderStatus.Analise,
        sellerId: otherSeller.id 
      };
      const otherVendedorOrders = await prisma.order.count({ where: otherVendedorWhere });
      console.log(`   Seller ${otherSeller.id} can see ${otherVendedorOrders} orders with Analise status`);
    }

    // Test as COBRADOR
    console.log('\ne) As COBRADOR user:');
    if (analiseOrder.collectorId) {
      const cobradorWhere = { 
        deletedAt: null, 
        status: OrderStatus.Analise,
        collectorId: analiseOrder.collectorId 
      };
      const cobradorOrders = await prisma.order.count({ where: cobradorWhere });
      console.log(`   Collector ${analiseOrder.collectorId} can see ${cobradorOrders} orders with Analise status`);
    } else {
      console.log('   Order has no collector assigned');
    }

    // 3. List all users and their roles
    console.log('\n3. ALL USERS IN THE SYSTEM:');
    const users = await prisma.user.findMany({
      where: {
        tenantId: analiseOrder.tenantId
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true
      }
    });

    users.forEach(user => {
      console.log(`   ${user.name} (${user.email}) - Role: ${user.role} - Active: ${user.active}`);
    });

    // 4. Check if the logged-in user might be different
    console.log('\n4. DEBUGGING TIPS:');
    console.log('- Check the browser console for the logged-in user info');
    console.log('- The user ID in the JWT token must match one of the users above');
    console.log('- If logged in as VENDEDOR, they can only see orders where they are the seller');
    console.log('- If logged in as COBRADOR, they can only see orders where they are the collector');
    console.log(`- The order's seller is: ${analiseOrder.sellerId}`);
    console.log(`- The order's collector is: ${analiseOrder.collectorId || 'None'}`);

  } catch (error) {
    console.error('Error testing user context:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testOrderUserContext();