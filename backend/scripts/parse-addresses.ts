import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface AddressComponents {
  street: string;
  streetNumber: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
}

function parseAddress(fullAddress: string): AddressComponents | null {
  if (!fullAddress || fullAddress.trim() === '') {
    return null;
  }

  // Try to parse address in common Brazilian formats
  // Format 1: "Rua Name, Number - Complement, Neighborhood, City - State, CEP 12345-678"
  // Format 2: "Rua Name, Number, Neighborhood, City - State, CEP 12345-678"
  
  // Clean up the address
  const cleanAddress = fullAddress.trim();
  
  // Extract CEP (ZIP code) - Brazilian format: 12345-678 or 12345678
  const cepMatch = cleanAddress.match(/CEP\s*([0-9]{5}-?[0-9]{3})/i);
  const zipCode = cepMatch ? cepMatch[1].replace(/\D/g, '') : '';
  
  // Remove CEP from address for easier parsing
  let addressWithoutCep = cleanAddress.replace(/,?\s*CEP\s*[0-9]{5}-?[0-9]{3}/i, '').trim();
  
  // Try to extract state (usually after last dash or comma before CEP)
  const stateMatch = addressWithoutCep.match(/[-,]\s*([A-Z]{2})\s*$/);
  const state = stateMatch ? stateMatch[1] : '';
  
  // Remove state from address
  if (state) {
    addressWithoutCep = addressWithoutCep.replace(new RegExp(`[-,]\\s*${state}\\s*$`), '').trim();
  }
  
  // Split remaining address by comma
  const parts = addressWithoutCep.split(',').map(part => part.trim());
  
  if (parts.length < 3) {
    // Not enough parts to parse reliably
    return {
      street: parts[0] || '',
      streetNumber: '',
      complement: '',
      neighborhood: parts[1] || '',
      city: parts[2] || '',
      state: state || '',
      zipCode: zipCode
    };
  }
  
  // First part is usually street and number
  const streetPart = parts[0];
  const streetNumberMatch = streetPart.match(/(.+?)\s+(\d+\w?)(?:\s*-\s*(.+))?$/);
  
  let street = '';
  let streetNumber = '';
  let complement = '';
  
  if (streetNumberMatch) {
    street = streetNumberMatch[1].trim();
    streetNumber = streetNumberMatch[2].trim();
    complement = streetNumberMatch[3] ? streetNumberMatch[3].trim() : '';
  } else {
    // Couldn't parse street number, use whole part as street
    street = streetPart;
  }
  
  // Check if second part has complement (usually after dash)
  if (parts[1] && parts[1].includes('-')) {
    const complementParts = parts[1].split('-');
    if (!complement) {
      complement = complementParts[0].trim();
    }
    parts[1] = complementParts[1] ? complementParts[1].trim() : parts[1];
  }
  
  // Remaining parts
  const neighborhood = parts[1] || '';
  const city = parts[parts.length - 1] || ''; // Last part is usually city
  
  return {
    street,
    streetNumber,
    complement,
    neighborhood,
    city,
    state,
    zipCode
  };
}

async function migrateAddresses() {
  console.log('Starting address migration...');
  
  try {
    // Get all orders without address components
    const orders = await prisma.order.findMany({
      where: {
        addressComponents: null,
        customerAddress: {
          not: null
        }
      },
      select: {
        id: true,
        customerAddress: true,
        orderNumber: true
      }
    });
    
    console.log(`Found ${orders.length} orders to process`);
    
    let successCount = 0;
    let failCount = 0;
    
    for (const order of orders) {
      try {
        const components = parseAddress(order.customerAddress!);
        
        if (components) {
          await prisma.orderAddressComponents.create({
            data: {
              orderId: order.id,
              street: components.street,
              streetNumber: components.streetNumber,
              complement: components.complement,
              neighborhood: components.neighborhood,
              city: components.city,
              state: components.state,
              zipCode: components.zipCode,
              // Normalized fields
              streetNormalized: components.street.toUpperCase(),
              streetSoundex: components.street.substring(0, 4).toUpperCase(),
              streetMetaphone: components.street.substring(0, 4).toUpperCase(),
              neighborhoodNorm: components.neighborhood.toUpperCase(),
              neighborhoodSoundex: components.neighborhood.substring(0, 4).toUpperCase(),
              cityNormalized: components.city.toUpperCase()
            }
          });
          
          successCount++;
          console.log(`✓ Processed order ${order.orderNumber}`);
        } else {
          failCount++;
          console.log(`✗ Failed to parse address for order ${order.orderNumber}`);
        }
      } catch (error) {
        failCount++;
        console.error(`✗ Error processing order ${order.orderNumber}:`, error);
      }
    }
    
    console.log('\nMigration completed!');
    console.log(`Success: ${successCount}`);
    console.log(`Failed: ${failCount}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateAddresses();