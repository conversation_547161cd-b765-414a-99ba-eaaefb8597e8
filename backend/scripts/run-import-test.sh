#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}Running detailed import order endpoint test...${NC}"
echo ""

# Check if we should run edge cases
if [ "$1" == "--edge-cases" ]; then
    echo -e "${YELLOW}Running with edge case tests enabled${NC}"
    npx ts-node scripts/test-import-detailed.ts --edge-cases
else
    echo -e "${GREEN}Running standard tests (use --edge-cases to include edge case tests)${NC}"
    npx ts-node scripts/test-import-detailed.ts
fi