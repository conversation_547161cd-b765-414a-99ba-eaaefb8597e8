import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { OrdersService } from '../src/orders/orders.service';
import { PrismaService } from '../src/prisma/prisma.service';
import { OrderStatus } from '@prisma/client';

async function testFixedAntifraud() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const ordersService = app.get(OrdersService);
  const prismaService = app.get(PrismaService);

  try {
    console.log('🧪 Testando sistema Anti-Fraud corrigido...\n');

    // Get test user
    const testUser = await prismaService.user.findFirst({
      where: {
        role: 'VENDEDOR',
        active: true
      }
    });

    if (!testUser) {
      console.error('❌ Nenhum vendedor ativo encontrado para teste');
      return;
    }

    console.log(`👤 Usando vendedor: ${testUser.name} (${testUser.email})\n`);

    // Create test orders with same data
    const testData = {
      customerName: '<PERSON>',
      customerPhone: '11999888777',
      customerCPF: '12345678901',
      items: [{
        productId: 'test-product',
        productName: 'Produto Teste',
        quantity: 1,
        unitPrice: 100
      }],
      address: {
        cep: '01310100',
        street: 'Avenida Paulista',
        number: '1234',
        neighborhood: 'Bela Vista',
        city: 'São Paulo',
        state: 'SP'
      }
    };

    console.log('📝 Criando primeiro pedido de teste...');
    const order1 = await ordersService.create(testData, testUser.id);
    console.log(`✅ Pedido 1 criado: ${order1.orderNumber}`);
    console.log(`   - CPF salvo? ${order1.customerCPF ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - É duplicata? ${order1.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - Score de risco: ${order1.riskScore || 'N/A'}`);
    console.log(`   - Nível de risco: ${order1.riskLevel || 'N/A'}\n`);

    // Wait a bit before creating second order
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('📝 Criando segundo pedido com mesmos dados...');
    const order2 = await ordersService.create(testData, testUser.id);
    console.log(`✅ Pedido 2 criado: ${order2.orderNumber}`);
    console.log(`   - CPF salvo? ${order2.customerCPF ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - É duplicata? ${order2.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - Score duplicata: ${order2.duplicateMatchScore || 'N/A'}`);
    console.log(`   - Score de risco: ${order2.riskScore || 'N/A'}`);
    console.log(`   - Nível de risco: ${order2.riskLevel || 'N/A'}`);
    console.log(`   - Requer revisão? ${order2.requiresReview ? '✅ SIM' : '❌ NÃO'}\n`);

    // Create third order with same address but different name
    console.log('📝 Criando terceiro pedido com mesmo endereço...');
    const testData3 = {
      ...testData,
      customerName: 'Maria Santos',
      customerPhone: '11888777666',
      customerCPF: '98765432109'
    };
    
    const order3 = await ordersService.create(testData3, testUser.id);
    console.log(`✅ Pedido 3 criado: ${order3.orderNumber}`);
    console.log(`   - Cliente: ${order3.customerName}`);
    console.log(`   - É duplicata? ${order3.isDuplicate ? '✅ SIM' : '❌ NÃO'}`);
    console.log(`   - Score de risco: ${order3.riskScore || 'N/A'}`);
    console.log(`   - Nível de risco: ${order3.riskLevel || 'N/A'}\n`);

    console.log('✅ Teste concluído!');
    console.log('\n📊 RESUMO:');
    console.log(`- Pedido 1: ${order1.isDuplicate ? 'DUPLICATA' : 'ORIGINAL'} | Risco: ${order1.riskLevel || 'N/A'}`);
    console.log(`- Pedido 2: ${order2.isDuplicate ? 'DUPLICATA' : 'ORIGINAL'} | Risco: ${order2.riskLevel || 'N/A'}`);
    console.log(`- Pedido 3: ${order3.isDuplicate ? 'DUPLICATA' : 'ORIGINAL'} | Risco: ${order3.riskLevel || 'N/A'}`);

  } catch (error) {
    console.error('❌ Erro durante o teste:', error);
  } finally {
    await app.close();
  }
}

// Run the test
testFixedAntifraud();