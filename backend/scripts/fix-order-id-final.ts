import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function fixOrderId() {
  console.log('=== Fixing Order with Invalid ID ===\n');

  try {
    // Find the order with the invalid ID using raw query
    const invalidIdOrders = await prisma.$queryRaw`
      SELECT * FROM "Order" WHERE id = 'sal47952'
    ` as any[];

    if (invalidIdOrders.length === 0) {
      console.log('No order found with ID "sal47952"');
      return;
    }

    const order = invalidIdOrders[0];
    console.log('Found order with invalid ID:');
    console.log(`  Current ID: ${order.id}`);
    console.log(`  Order Number: ${order.orderNumber}`);
    console.log(`  Customer: ${order.customerName}`);
    console.log(`  Status: ${order.status}`);

    // Generate a new UUID
    const newId = randomUUID();
    console.log(`\nNew UUID to be assigned: ${newId}`);

    // Use a transaction with raw SQL to handle everything
    await prisma.$transaction(async (tx) => {
      // Step 1: Create a temporary order number to avoid unique constraint
      const tempOrderNumber = `TEMP-${Date.now()}`;
      await tx.$executeRaw`
        UPDATE "Order" SET "orderNumber" = ${tempOrderNumber} WHERE id = 'sal47952'
      `;
      console.log('Set temporary order number');

      // Step 2: Create new order with valid UUID
      await tx.$executeRaw`
        INSERT INTO "Order" (
          id, "orderNumber", "customerName", "customerPhone", status, total,
          "sellerId", "tenantId", "createdAt", "updatedAt", "commissionApprovalStatus",
          "paymentReceivedAmount", "paymentReceivedDate", "commissionApproved",
          "customerId", "collectorId", "nextPaymentDate", "lastContactDate", "zapId",
          "customerCPF", "customerCPFHash", "fullAddress", "isDuplicate", "duplicateStatus",
          "duplicateMatchScore", "duplicateCheckVersion", "originalOrderIds", "reviewedBy",
          "reviewedByName", "reviewedByRole", "reviewedAt", "reviewDecision", "reviewDuration",
          "observation", "riskScore", "riskLevel", "riskFactors", "requiresReview",
          "fraudCheckCompletedAt", "deletedAt", "deletedBy"
        )
        SELECT 
          ${newId}, ${order.orderNumber}, "customerName", "customerPhone", status, total,
          "sellerId", "tenantId", "createdAt", "updatedAt", "commissionApprovalStatus",
          "paymentReceivedAmount", "paymentReceivedDate", "commissionApproved",
          "customerId", "collectorId", "nextPaymentDate", "lastContactDate", "zapId",
          "customerCPF", "customerCPFHash", "fullAddress", "isDuplicate", "duplicateStatus",
          "duplicateMatchScore", "duplicateCheckVersion", "originalOrderIds", "reviewedBy",
          "reviewedByName", "reviewedByRole", "reviewedAt", "reviewDecision", "reviewDuration",
          "observation", "riskScore", "riskLevel", "riskFactors", "requiresReview",
          "fraudCheckCompletedAt", "deletedAt", "deletedBy"
        FROM "Order"
        WHERE id = 'sal47952'
      `;
      console.log('Created new order with valid UUID');

      // Step 3: Copy related records
      
      // Copy OrderItems
      const itemsCopied = await tx.$executeRaw`
        INSERT INTO "OrderItem" (id, "orderId", "productVariationId", "productId", "productName", quantity, "unitPrice", "createdAt", "updatedAt")
        SELECT gen_random_uuid(), ${newId}, "productVariationId", "productId", "productName", quantity, "unitPrice", "createdAt", "updatedAt"
        FROM "OrderItem"
        WHERE "orderId" = 'sal47952'
      `;
      console.log(`Copied ${itemsCopied} order items`);

      // Copy OrderStatusHistory
      const historyCount = await tx.$executeRaw`
        INSERT INTO "OrderStatusHistory" (id, "orderId", "previousStatus", "newStatus", "changedAt", "changedById")
        SELECT gen_random_uuid(), ${newId}, "previousStatus", "newStatus", "changedAt", "changedById"
        FROM "OrderStatusHistory"
        WHERE "orderId" = 'sal47952'
      `;
      console.log(`Copied ${historyCount} status history records`);

      // Update WebhookLog references
      const webhookLogUpdated = await tx.$executeRaw`
        UPDATE "WebhookLog" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${webhookLogUpdated} webhook log records`);

      // Step 4: Delete the old order
      await tx.$executeRaw`
        DELETE FROM "Order" WHERE id = 'sal47952'
      `;
      console.log('Deleted old order with invalid ID');
    });

    console.log('\n✅ Successfully fixed order ID!');

    // Verify the fix
    const fixedOrder = await prisma.order.findUnique({
      where: { id: newId },
      include: {
        items: {
          select: {
            productName: true,
            quantity: true,
            unitPrice: true
          }
        },
        seller: {
          select: { name: true, email: true }
        }
      }
    });

    if (fixedOrder) {
      console.log('\nVerification - Order details:');
      console.log(`  ✅ New ID: ${fixedOrder.id}`);
      console.log(`  Order Number: ${fixedOrder.orderNumber}`);
      console.log(`  Customer: ${fixedOrder.customerName}`);
      console.log(`  Status: ${fixedOrder.status}`);
      console.log(`  Total: R$ ${fixedOrder.total}`);
      console.log(`  Seller: ${fixedOrder.seller.name}`);
      console.log(`  Items (${fixedOrder.items.length}):`);
      fixedOrder.items.forEach(item => {
        console.log(`    - ${item.productName} x${item.quantity} = R$ ${item.unitPrice}`);
      });
      console.log('\n🎉 Order should now appear in the UI with valid UUID!');
    }

    // Run the original check script again to verify
    console.log('\n=== Running final verification ===');
    const allOrders = await prisma.order.findMany({
      select: {
        id: true,
        orderNumber: true,
        customerName: true
      }
    });

    const invalidIds = allOrders.filter(o => !o.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i));
    console.log(`\nTotal orders: ${allOrders.length}`);
    console.log(`Orders with invalid IDs: ${invalidIds.length}`);
    
    if (invalidIds.length === 0) {
      console.log('✅ All orders now have valid UUID format!');
    }

  } catch (error) {
    console.error('Error fixing order ID:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixOrderId().catch(console.error);