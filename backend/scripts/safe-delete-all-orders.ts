import { PrismaClient } from '@prisma/client';
import * as readline from 'readline';

const prisma = new PrismaClient();

// ANSI color codes
const RED = '\x1b[31m';
const YELLOW = '\x1b[33m';
const GREEN = '\x1b[32m';
const RESET = '\x1b[0m';

async function promptUser(question: string): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

async function safeDeleteAllOrders() {
  try {
    // Environment check
    if (process.env.NODE_ENV === 'production' || process.env.RAILWAY_ENVIRONMENT === 'production') {
      console.error(`${RED}❌ ERROR: This script cannot run in production environment!${RESET}`);
      console.error(`${RED}Current environment: ${process.env.NODE_ENV || process.env.RAILWAY_ENVIRONMENT}${RESET}`);
      process.exit(1);
    }

    console.log(`${RED}╔════════════════════════════════════════════════════════════╗${RESET}`);
    console.log(`${RED}║                    ⚠️  DANGER ZONE ⚠️                       ║${RESET}`);
    console.log(`${RED}║                                                            ║${RESET}`);
    console.log(`${RED}║  This script will PERMANENTLY DELETE ALL ORDERS and        ║${RESET}`);
    console.log(`${RED}║  related data from the database!                          ║${RESET}`);
    console.log(`${RED}║                                                            ║${RESET}`);
    console.log(`${RED}║  This includes:                                            ║${RESET}`);
    console.log(`${RED}║  - All orders                                              ║${RESET}`);
    console.log(`${RED}║  - All order items                                         ║${RESET}`);
    console.log(`${RED}║  - All order audit logs                                    ║${RESET}`);
    console.log(`${RED}║  - All order status history                                ║${RESET}`);
    console.log(`${RED}║  - All tracking information                                ║${RESET}`);
    console.log(`${RED}║  - All commission payments                                 ║${RESET}`);
    console.log(`${RED}║  - All order address components                            ║${RESET}`);
    console.log(`${RED}╚════════════════════════════════════════════════════════════╝${RESET}`);
    console.log('');

    // Show current data counts
    console.log(`${YELLOW}📊 Current database state:${RESET}`);
    const orderCount = await prisma.order.count();
    const itemCount = await prisma.orderItem.count();
    const auditCount = await prisma.orderAuditLog.count();
    
    console.log(`   Orders: ${orderCount}`);
    console.log(`   Order Items: ${itemCount}`);
    console.log(`   Audit Logs: ${auditCount}`);
    console.log('');

    // First confirmation
    const confirm1 = await promptUser(
      `${YELLOW}Are you ABSOLUTELY SURE you want to delete all orders? Type 'yes' to continue: ${RESET}`
    );
    
    if (confirm1.toLowerCase() !== 'yes') {
      console.log(`${GREEN}✅ Operation cancelled. No data was deleted.${RESET}`);
      process.exit(0);
    }

    // Second confirmation with typed confirmation
    const confirmPhrase = 'delete all orders permanently';
    const confirm2 = await promptUser(
      `${RED}Type "${confirmPhrase}" to confirm deletion: ${RESET}`
    );
    
    if (confirm2.toLowerCase() !== confirmPhrase) {
      console.log(`${GREEN}✅ Operation cancelled. No data was deleted.${RESET}`);
      process.exit(0);
    }

    // Final warning
    console.log('');
    console.log(`${RED}⏰ Starting deletion in 5 seconds... Press Ctrl+C to cancel${RESET}`);
    for (let i = 5; i > 0; i--) {
      process.stdout.write(`${RED}${i}...${RESET}`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    console.log('');

    // Perform deletion in transaction
    console.log(`${YELLOW}🗑️  Starting deletion process...${RESET}`);

    const result = await prisma.$transaction(async (tx) => {
      // Delete in correct order to respect foreign key constraints
      console.log('Deleting order audit logs...');
      const auditLogResult = await tx.orderAuditLog.deleteMany({});
      console.log(`  ✓ Deleted ${auditLogResult.count} audit logs`);

      console.log('Deleting order status history...');
      const statusHistoryResult = await tx.orderStatusHistory.deleteMany({});
      console.log(`  ✓ Deleted ${statusHistoryResult.count} status history records`);

      console.log('Deleting order items...');
      const orderItemsResult = await tx.orderItem.deleteMany({});
      console.log(`  ✓ Deleted ${orderItemsResult.count} order items`);

      console.log('Deleting tracking information...');
      const trackingResult = await tx.tracking.deleteMany({});
      console.log(`  ✓ Deleted ${trackingResult.count} tracking records`);

      console.log('Deleting commission payments...');
      const commissionResult = await tx.commissionPayment.deleteMany({});
      console.log(`  ✓ Deleted ${commissionResult.count} commission payments`);

      console.log('Deleting order address components...');
      const addressResult = await tx.orderAddressComponents.deleteMany({});
      console.log(`  ✓ Deleted ${addressResult.count} address components`);

      console.log('Deleting orders...');
      const ordersResult = await tx.order.deleteMany({});
      console.log(`  ✓ Deleted ${ordersResult.count} orders`);

      return {
        auditLogs: auditLogResult.count,
        statusHistory: statusHistoryResult.count,
        orderItems: orderItemsResult.count,
        tracking: trackingResult.count,
        commissions: commissionResult.count,
        addresses: addressResult.count,
        orders: ordersResult.count,
      };
    });

    console.log('');
    console.log(`${GREEN}✅ Deletion completed successfully!${RESET}`);
    console.log(`${GREEN}Summary:${RESET}`);
    console.log(`  - Orders deleted: ${result.orders}`);
    console.log(`  - Order items deleted: ${result.orderItems}`);
    console.log(`  - Audit logs deleted: ${result.auditLogs}`);
    console.log(`  - Status history deleted: ${result.statusHistory}`);
    console.log(`  - Tracking records deleted: ${result.tracking}`);
    console.log(`  - Commission payments deleted: ${result.commissions}`);
    console.log(`  - Address components deleted: ${result.addresses}`);

  } catch (error) {
    console.error(`${RED}❌ Error during deletion:${RESET}`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Check if running directly
if (require.main === module) {
  safeDeleteAllOrders();
}