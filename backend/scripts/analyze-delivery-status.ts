import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function analyzeDeliveryStatus() {
  console.log('Searching for delivery-related statuses in webhook logs...\n');

  try {
    // Get all webhook logs
    const logs = await prisma.webhookLog.findMany({
      orderBy: { receivedAt: 'desc' },
      take: 2000, // Analyze more logs
    });

    console.log(`Analyzing ${logs.length} webhook logs...\n`);

    const deliveryRelatedStatuses = new Map<string, { count: number, paths: Set<string>, samples: any[] }>();
    const correiosDescriptions = new Map<string, number>();
    
    // Keywords to search for
    const deliveryKeywords = [
      'saiu para entrega',
      'saído para entrega',
      'saiu para entrega ao destinatário',
      'objeto saiu para entrega ao destinatário',
      'saiu para entrega ao dest',
      'em rota de entrega',
      'rota de entrega',
      'rota para entrega',
      'out for delivery',
      'em entrega',
      'entregador',
      'objeto saiu',
      'saiu para',
      'em distribuição',
      'entrega hoje',
      'tentativa de entrega',
      'será entregue',
      'em deslocamento',
      'em trânsito',
      'encaminhado para entrega',
      'liberado para entrega',
      'preparando para entrega',
      'pronto para entrega',
      'disponível para entrega',
      'agência de destino',
      'unidade de destino',
      'centro de distribuição',
      'objeto encaminhado',
      'objeto liberado'
    ];

    for (const log of logs) {
      const payload = log.payload as any;
      if (!payload) continue;

      // Deep search for any text containing delivery keywords
      const searchObject = (obj: any, path: string = '') => {
        if (!obj) return;

        if (typeof obj === 'string') {
          const lowerText = obj.toLowerCase();
          
          // Check correios_description specifically
          if (path.includes('correios_description')) {
            correiosDescriptions.set(obj, (correiosDescriptions.get(obj) || 0) + 1);
          }
          
          // Check for delivery keywords
          for (const keyword of deliveryKeywords) {
            if (lowerText.includes(keyword)) {
              const key = `${keyword} (found in: ${obj})`;
              if (!deliveryRelatedStatuses.has(key)) {
                deliveryRelatedStatuses.set(key, { count: 0, paths: new Set(), samples: [] });
              }
              const entry = deliveryRelatedStatuses.get(key)!;
              entry.count++;
              entry.paths.add(path);
              if (entry.samples.length < 3) {
                entry.samples.push({
                  value: obj,
                  path: path,
                  event: payload.event,
                  status: payload.status,
                  date: log.receivedAt
                });
              }
              break;
            }
          }
        } else if (Array.isArray(obj)) {
          obj.forEach((item, index) => {
            searchObject(item, `${path}[${index}]`);
          });
        } else if (typeof obj === 'object' && obj !== null) {
          Object.entries(obj).forEach(([key, value]) => {
            searchObject(value, path ? `${path}.${key}` : key);
          });
        }
      };

      searchObject(payload);
    }

    // Print results
    console.log('=== DELIVERY-RELATED STATUSES FOUND ===\n');
    
    if (deliveryRelatedStatuses.size === 0) {
      console.log('No delivery-related statuses found with the specified keywords.\n');
    } else {
      const sorted = Array.from(deliveryRelatedStatuses.entries())
        .sort((a, b) => b[1].count - a[1].count);
      
      for (const [status, data] of sorted) {
        console.log(`\n📦 ${status}`);
        console.log(`   Count: ${data.count}`);
        console.log(`   Found in paths: ${Array.from(data.paths).join(', ')}`);
        console.log(`   Samples:`);
        data.samples.forEach((sample, i) => {
          console.log(`     ${i + 1}. Event: ${sample.event}, Status: ${sample.status}`);
          console.log(`        Path: ${sample.path}`);
          console.log(`        Date: ${sample.date}`);
        });
      }
    }

    // Print all correios descriptions
    console.log('\n\n=== ALL CORREIOS DESCRIPTIONS (TOP 50) ===\n');
    const sortedDescriptions = Array.from(correiosDescriptions.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 50);
    
    for (const [description, count] of sortedDescriptions) {
      console.log(`${count}x: ${description}`);
      
      // Highlight potential delivery statuses
      const lower = description.toLowerCase();
      if (
        lower.includes('entrega') || 
        lower.includes('entregue') ||
        lower.includes('distribuição') ||
        lower.includes('deslocamento') ||
        lower.includes('rota') ||
        lower.includes('saiu') ||
        lower.includes('saída') ||
        lower.includes('encaminhado') ||
        lower.includes('liberado') ||
        lower.includes('destino') ||
        lower.includes('destinatário')
      ) {
        console.log('   ⚠️  POTENTIAL DELIVERY STATUS');
        
        // Check specifically for "saiu para entrega" pattern
        if (lower.includes('saiu') && lower.includes('entrega')) {
          console.log('   🚚 LIKELY "SAIU PARA ENTREGA" STATUS!');
        }
      }
    }

    // Look for specific event types
    console.log('\n\n=== ANALYZING EVENT TYPES ===\n');
    const eventTypes = new Map<string, number>();
    
    for (const log of logs) {
      const payload = log.payload as any;
      if (payload?.event) {
        eventTypes.set(payload.event, (eventTypes.get(payload.event) || 0) + 1);
      }
    }
    
    const sortedEvents = Array.from(eventTypes.entries())
      .sort((a, b) => b[1] - a[1]);
    
    for (const [event, count] of sortedEvents) {
      console.log(`${event}: ${count} occurrences`);
      
      // Check if this might be a delivery event
      if (
        event.includes('DELIVERY') ||
        event.includes('ENTREGA') ||
        event.includes('OUT_FOR') ||
        event.includes('TRANSIT')
      ) {
        console.log('   ⚠️  POTENTIAL DELIVERY EVENT');
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeDeliveryStatus();