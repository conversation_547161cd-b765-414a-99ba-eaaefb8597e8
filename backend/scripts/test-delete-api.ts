import axios from 'axios';

async function testDeleteAPI() {
  try {
    // First, login to get a token
    console.log('1. Logging in...');
    const loginResponse = await axios.post('https://zencash-production.up.railway.app/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      headers: {
        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
      }
    });

    const token = loginResponse.data.access_token;
    console.log('Login successful, got token');

    // Test order ID
    const orderId = 'ff0ebae6-78ee-4fe1-af89-10bb4ef8b20e';
    
    // First, check if the order exists
    console.log('\n2. Checking if order exists...');
    try {
      const orderResponse = await axios.get(
        `https://zencash-production.up.railway.app/api/v1/orders/${orderId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('Order exists:', {
        id: orderResponse.data.id,
        orderNumber: orderResponse.data.orderNumber,
        status: orderResponse.data.status
      });
    } catch (error: any) {
      console.error('Error fetching order:', error.response?.status, error.response?.data);
    }

    // Try to delete the order
    console.log('\n3. Attempting to permanently delete order...');
    try {
      const deleteResponse = await axios.delete(
        `https://zencash-production.up.railway.app/api/v1/orders/${orderId}/permanent`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('Delete successful:', deleteResponse.data);
    } catch (deleteError: any) {
      console.error('Delete failed:', {
        status: deleteError.response?.status,
        statusText: deleteError.response?.statusText,
        data: deleteError.response?.data,
        headers: deleteError.response?.headers
      });
      
      // If we get error details, log them
      if (deleteError.response?.data?.message) {
        console.error('Error message:', deleteError.response.data.message);
      }
      if (deleteError.response?.data?.error) {
        console.error('Error details:', deleteError.response.data.error);
      }
    }

  } catch (error: any) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testDeleteAPI();