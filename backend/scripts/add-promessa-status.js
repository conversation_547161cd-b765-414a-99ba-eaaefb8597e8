const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addPromessaStatus() {
  console.log('🚀 Starting migration to add Promessa status...');
  
  try {
    // First, let's check current enum values
    console.log('📋 Checking current OrderStatus enum values...');
    const currentEnumValues = await prisma.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'OrderStatus'
      )
      ORDER BY enumsortorder;
    `;
    
    console.log('Current enum values:', currentEnumValues);
    
    // Check if Promessa already exists
    const promessaExists = currentEnumValues.some(row => row.enumlabel === 'Promessa');
    
    if (promessaExists) {
      console.log('✅ Promessa status already exists in the enum. No action needed.');
      return;
    }
    
    // Add Promessa to the enum
    console.log('➕ Adding Promessa to OrderStatus enum...');
    await prisma.$executeRaw`
      ALTER TYPE "OrderStatus" ADD VALUE 'Promessa' AFTER 'Negociacao';
    `;
    
    console.log('✅ Promessa status added successfully!');
    
    // Verify the addition
    console.log('🔍 Verifying the addition...');
    const updatedEnumValues = await prisma.$queryRaw`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'OrderStatus'
      )
      ORDER BY enumsortorder;
    `;
    
    console.log('Updated enum values:', updatedEnumValues);
    
    // Check some statistics
    console.log('\n📊 Checking order statistics...');
    const orderStats = await prisma.order.groupBy({
      by: ['status'],
      _count: true,
    });
    
    console.log('Order count by status:');
    orderStats.forEach(stat => {
      console.log(`  ${stat.status}: ${stat._count} orders`);
    });
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
addPromessaStatus()
  .then(() => {
    console.log('\n✅ Migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  });