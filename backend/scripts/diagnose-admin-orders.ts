import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function diagnoseAdminOrders() {
  try {
    console.log('🔍 Diagnosing <EMAIL> order visibility issue...\n');

    // 1. Check admin user details
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
    });

    if (!adminUser) {
      console.error('❌ <NAME_EMAIL> not found');
      return;
    }

    console.log('👤 Admin User Details:');
    console.log(`  - ID: ${adminUser.id}`);
    console.log(`  - Email: ${adminUser.email}`);
    console.log(`  - Name: ${adminUser.name}`);
    console.log(`  - Role: ${adminUser.role}`);
    console.log(`  - TenantId: ${adminUser.tenantId}`);
    console.log(`  - Active: ${adminUser.active}`);

    // 2. Check all unique tenant IDs in the system
    const uniqueTenantIds = await prisma.order.findMany({
      distinct: ['tenantId'],
      select: { tenantId: true },
    });

    console.log('\n🏢 Unique Tenant IDs in Orders:');
    for (const { tenantId } of uniqueTenantIds) {
      const count = await prisma.order.count({ where: { tenantId } });
      console.log(`  - ${tenantId}: ${count} orders`);
    }

    // 3. Check orders for admin's tenant
    const adminTenantOrders = await prisma.order.count({
      where: { tenantId: adminUser.tenantId },
    });

    console.log(`\n📊 Orders for admin's tenant (${adminUser.tenantId}): ${adminTenantOrders}`);

    // 4. Check if there are any orders without tenant ID
    const ordersWithoutTenant = await prisma.order.count({
      where: { tenantId: null as any },
    });

    if (ordersWithoutTenant > 0) {
      console.log(`\n⚠️  Found ${ordersWithoutTenant} orders without tenantId!`);
    }

    // 5. Check recent orders
    console.log('\n📦 Most recent 5 orders in the system:');
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        status: true,
        tenantId: true,
        createdAt: true,
        seller: { select: { email: true } },
      },
    });

    recentOrders.forEach(order => {
      console.log(`  - ${order.orderNumber}: ${order.customerName}`);
      console.log(`    Tenant: ${order.tenantId}`);
      console.log(`    Seller: ${order.seller.email}`);
      console.log(`    Created: ${order.createdAt.toISOString()}`);
    });

    // 6. Check other users' tenant IDs
    console.log('\n👥 Other Users and their Tenant IDs:');
    const users = await prisma.user.findMany({
      take: 10,
      select: {
        email: true,
        role: true,
        tenantId: true,
        active: true,
      },
    });

    users.forEach(user => {
      console.log(`  - ${user.email} (${user.role}): ${user.tenantId} ${user.active ? '✓' : '✗ INACTIVE'}`);
    });

    // 7. Simulate the query that would be run for admin
    console.log('\n🔍 Simulating admin order query:');
    const simulatedQuery = await prisma.order.findMany({
      where: {
        tenantId: adminUser.tenantId,
        deletedAt: null,
      },
      take: 3,
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        status: true,
      },
    });

    console.log(`  Found ${simulatedQuery.length} orders for admin`);
    simulatedQuery.forEach(order => {
      console.log(`  - ${order.orderNumber}: ${order.customerName} (${order.status})`);
    });

  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the diagnosis
diagnoseAdminOrders().catch(console.error);