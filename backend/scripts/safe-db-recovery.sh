#!/bin/bash
set -e

echo "🚨 Safe Database Recovery Script"
echo "================================"
echo "This script will apply migrations WITHOUT losing data"
echo ""

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Error: .env file not found"
    exit 1
fi

echo "🔍 Checking current database state..."
npx prisma db pull

echo ""
echo "📋 Current migration status:"
npx prisma migrate status || true

echo ""
echo "🔧 Applying migrations (WITHOUT data loss)..."
# Use deploy instead of dev to avoid interactive prompts
npx prisma migrate deploy

echo ""
echo "🏗️  Generating Prisma Client..."
npx prisma generate

echo ""
echo "✅ Database recovery completed WITHOUT data loss!"
echo ""
echo "⚠️  If you still see no data, it means the data was already lost before running this script."
echo "    In that case, you'll need to:"
echo "    1. Check Railway/hosting provider for automatic backups"
echo "    2. Restore from a manual backup if available"
echo "    3. Re-import data from CSV or recreate webhook mappings"