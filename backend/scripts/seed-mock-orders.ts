import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker/locale/pt_BR';

const prisma = new PrismaClient();

// Lists of realistic Brazilian data
const vendedores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'
];

const cobradores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const produtos = [
  { name: 'Plano Premium Mensal', price: 299.90 },
  { name: 'Plano Básico Mensal', price: 149.90 },
  { name: 'Plano Premium Anual', price: 2999.90 },
  { name: 'Plano Básico Anual', price: 1499.90 },
  { name: 'Pacote Empresarial', price: 999.90 },
  { name: 'Pacote Profissional', price: 599.90 },
  { name: 'Assinatura Gold', price: 399.90 },
  { name: 'Assinatura Silver', price: 199.90 },
  { name: 'Combo Família', price: 549.90 },
  { name: 'Combo Individual', price: 249.90 }
];

const statuses = ['Pendente', 'Analise', 'Negociacao', 'Parcial', 'Completo', 'Cancelado'];

// Helper function to generate a date within the last 90 days
function randomDate(daysBack: number = 90): Date {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - Math.floor(Math.random() * daysBack));
  return pastDate;
}

// Helper function to generate CPF
function generateCPF(): string {
  const n1 = Math.floor(Math.random() * 10);
  const n2 = Math.floor(Math.random() * 10);
  const n3 = Math.floor(Math.random() * 10);
  const n4 = Math.floor(Math.random() * 10);
  const n5 = Math.floor(Math.random() * 10);
  const n6 = Math.floor(Math.random() * 10);
  const n7 = Math.floor(Math.random() * 10);
  const n8 = Math.floor(Math.random() * 10);
  const n9 = Math.floor(Math.random() * 10);
  
  return `${n1}${n2}${n3}.${n4}${n5}${n6}.${n7}${n8}${n9}-00`;
}

// Helper function to generate phone
function generatePhone(): string {
  const ddd = Math.floor(Math.random() * (99 - 11 + 1)) + 11;
  const firstPart = Math.floor(Math.random() * (99999 - 90000 + 1)) + 90000;
  const secondPart = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
  return `(${ddd}) ${firstPart}-${secondPart}`;
}

async function seedMockOrders() {
  console.log('🚀 Starting to seed 100 mock orders...');

  // Get tenant ID - assuming default tenant
  const defaultTenant = 'default-tenant';

  // First, ensure we have users for sellers and collectors
  const existingSellers = await prisma.user.findMany({
    where: { role: 'VENDEDOR', tenantId: defaultTenant }
  });

  const existingCollectors = await prisma.user.findMany({
    where: { role: 'COBRADOR', tenantId: defaultTenant }
  });

  // Create sellers if they don't exist
  if (existingSellers.length === 0) {
    console.log('Creating seller users...');
    for (const vendedor of vendedores) {
      await prisma.user.create({
        data: {
          name: vendedor,
          email: vendedor.toLowerCase().replace(/\s+/g, '.') + '@empresa.com',
          password: '$2a$10$defaultHashedPassword', // You should use proper hashing
          role: 'VENDEDOR',
          tenantId: defaultTenant,
          active: true
        }
      });
    }
  }

  // Create collectors if they don't exist
  if (existingCollectors.length === 0) {
    console.log('Creating collector users...');
    for (const cobrador of cobradores) {
      await prisma.user.create({
        data: {
          name: cobrador,
          email: cobrador.toLowerCase().replace(/\s+/g, '.') + '@empresa.com',
          password: '$2a$10$defaultHashedPassword', // You should use proper hashing
          role: 'COBRADOR',
          tenantId: defaultTenant,
          active: true
        }
      });
    }
  }

  // Get all sellers and collectors
  const sellers = await prisma.user.findMany({
    where: { role: 'VENDEDOR', tenantId: defaultTenant }
  });

  const collectors = await prisma.user.findMany({
    where: { role: 'COBRADOR', tenantId: defaultTenant }
  });

  if (sellers.length === 0 || collectors.length === 0) {
    console.error('❌ No sellers or collectors found. Please run user seeding first.');
    return;
  }

  // Create 100 orders
  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < 100; i++) {
    try {
      const createdAt = randomDate(90);
      const status = faker.helpers.arrayElement(statuses);
      const product = faker.helpers.arrayElement(produtos);
      const seller = faker.helpers.arrayElement(sellers);
      const collector = status !== 'Pendente' && status !== 'Cancelado' ? faker.helpers.arrayElement(collectors) : null;
      
      // Calculate total with some variations
      const quantity = faker.number.int({ min: 1, max: 3 });
      const discount = Math.random() > 0.7 ? faker.number.float({ min: 0, max: 0.2, precision: 0.01 }) : 0;
      const total = product.price * quantity * (1 - discount);
      
      // Calculate payment received based on status
      let paymentReceivedAmount = null;
      let paymentReceivedDate = null;
      
      if (status === 'Completo') {
        paymentReceivedAmount = total;
        paymentReceivedDate = new Date(createdAt);
        paymentReceivedDate.setDate(paymentReceivedDate.getDate() + faker.number.int({ min: 1, max: 15 }));
      } else if (status === 'Parcial') {
        paymentReceivedAmount = total * faker.number.float({ min: 0.2, max: 0.8, precision: 0.01 });
        paymentReceivedDate = new Date(createdAt);
        paymentReceivedDate.setDate(paymentReceivedDate.getDate() + faker.number.int({ min: 1, max: 20 }));
      } else if (status === 'Negociacao') {
        paymentReceivedAmount = total * faker.number.float({ min: 0.05, max: 0.15, precision: 0.01 });
        paymentReceivedDate = new Date(createdAt);
        paymentReceivedDate.setDate(paymentReceivedDate.getDate() + faker.number.int({ min: 1, max: 10 }));
      }

      const customerName = faker.person.fullName();
      const customerPhone = generatePhone();
      const customerCPF = generateCPF();

      const order = await prisma.order.create({
        data: {
          orderNumber: `VND-2024-${(i + 1).toString().padStart(4, '0')}`,
          customerName,
          customerPhone,
          customerCPF,
          status: status as any,
          total,
          sellerId: seller.id,
          collectorId: collector?.id,
          tenantId: defaultTenant,
          createdAt,
          paymentReceivedAmount,
          paymentReceivedDate,
          commissionApproved: status === 'Completo',
          fullAddress: `${faker.location.streetAddress()}, ${faker.location.city()}, ${faker.location.state()} - ${faker.location.zipCode()}`,
          zapId: faker.string.alphanumeric(10),
          lastContactDate: status !== 'Pendente' && status !== 'Cancelado' ? faker.date.between({ from: createdAt, to: new Date() }) : null,
          nextPaymentDate: status === 'Parcial' || status === 'Negociacao' ? faker.date.future({ years: 0.25 }) : null,
        }
      });

      // Create order items
      await prisma.orderItem.create({
        data: {
          orderId: order.id,
          productId: faker.string.uuid(), // You might want to create actual products
          productName: product.name,
          quantity,
          unitPrice: product.price,
        }
      });

      // Create status history
      await prisma.orderStatusHistory.create({
        data: {
          orderId: order.id,
          fromStatus: 'Pendente',
          toStatus: status as any,
          changedById: seller.id,
        }
      });

      successCount++;
    } catch (error) {
      console.error(`Error creating order ${i + 1}:`, error);
      errorCount++;
    }
  }

  console.log('\n✅ Mock data seeding completed!');
  console.log(`✓ Successfully created: ${successCount} orders`);
  console.log(`✗ Failed: ${errorCount} orders`);

  // Print summary
  const summary = await prisma.order.groupBy({
    by: ['status'],
    where: { tenantId: defaultTenant },
    _count: true,
    _sum: {
      total: true,
      paymentReceivedAmount: true
    }
  });

  console.log('\n📊 Order Summary:');
  summary.forEach(s => {
    console.log(`   ${s.status}: ${s._count} orders, Total: R$ ${s._sum.total?.toFixed(2)}, Received: R$ ${s._sum.paymentReceivedAmount?.toFixed(2) || '0.00'}`);
  });
}

// Run the script
seedMockOrders()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect();
  });