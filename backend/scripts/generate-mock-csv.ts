import { faker } from '@faker-js/faker/locale/pt_BR';
import * as fs from 'fs';
import * as path from 'path';

// Lists of realistic Brazilian data
const vendedores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'
];

const operadores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const ofertas = [
  'Plano Premium Mensal', 'Plano Básico Mensal', 'Plano Premium Anual', 'Plano Básico Anual',
  'Pacote Empresarial', 'Pacote Profissional', 'Assinatura Gold', 'Assinatura Silver',
  'Combo Família', 'Combo Individual'
];

const situacoes = ['pendente', 'analise', 'negociacao', 'parcial', 'completo', 'cancelado'];

const estados = ['SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA', 'PE', 'CE', 'GO', 'DF', 'ES', 'PB', 'RN', 'AM', 'PA'];

const formasPagamento = ['Cartão de Crédito', 'Boleto', 'PIX', 'Transferência', 'Dinheiro'];

// Helper function to generate a date within the last 90 days
function randomDate(daysBack: number = 90): Date {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - Math.floor(Math.random() * daysBack));
  return pastDate;
}

// Helper function to format date to DD/MM/YYYY
function formatDate(date: Date): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

// Helper function to generate CPF
function generateCPF(): string {
  const n = () => Math.floor(Math.random() * 10);
  return `${n()}${n()}${n()}.${n()}${n()}${n()}.${n()}${n()}${n()}-${n()}${n()}`;
}

// Helper function to generate phone
function generatePhone(): string {
  const ddd = Math.floor(Math.random() * (99 - 11 + 1)) + 11;
  const firstPart = Math.floor(Math.random() * (99999 - 90000 + 1)) + 90000;
  const secondPart = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
  return `(${ddd}) ${firstPart}-${secondPart}`;
}

// CSV Headers matching the import format
const headers = [
  'numeroVenda',
  'dataVenda',
  'nomeDestinatario',
  'cpfDestinatario',
  'telefoneDestinatario',
  'emailDestinatario',
  'cepDestinatario',
  'enderecoDestinatario',
  'numeroEnderecoDestinatario',
  'complementoDestinatario',
  'bairroDestinatario',
  'cidadeDestinatario',
  'estadoDestinatario',
  'paisDestinatario',
  'oferta',
  'formaPagamento',
  'vendedor',
  'operador',
  'situacaoVenda',
  'valorVenda',
  'valorRecebido',
  'dataRecebimento',
  'observacoes'
];

function generateMockCSV() {
  console.log('🚀 Generating mock CSV with 100 orders...');

  const rows = [headers.join(',')]; // Start with headers

  for (let i = 0; i < 100; i++) {
    const dataVenda = randomDate(90);
    const situacao = faker.helpers.arrayElement(situacoes);
    const valorVenda = faker.number.float({ min: 100, max: 5000, precision: 0.01 });
    
    // Calculate valorRecebido based on situation
    let valorRecebido = 0;
    let dataRecebimento = '';
    
    if (situacao === 'completo') {
      valorRecebido = valorVenda;
      const recDate = new Date(dataVenda);
      recDate.setDate(recDate.getDate() + Math.floor(Math.random() * 15) + 1);
      dataRecebimento = formatDate(recDate);
    } else if (situacao === 'parcial') {
      valorRecebido = parseFloat((valorVenda * (0.2 + Math.random() * 0.6)).toFixed(2));
      const recDate = new Date(dataVenda);
      recDate.setDate(recDate.getDate() + Math.floor(Math.random() * 20) + 1);
      dataRecebimento = formatDate(recDate);
    } else if (situacao === 'negociacao') {
      valorRecebido = parseFloat((valorVenda * (0.05 + Math.random() * 0.1)).toFixed(2));
      const recDate = new Date(dataVenda);
      recDate.setDate(recDate.getDate() + Math.floor(Math.random() * 10) + 1);
      dataRecebimento = formatDate(recDate);
    }

    const row = [
      `VND-2024-${(i + 1).toString().padStart(4, '0')}`, // numeroVenda
      formatDate(dataVenda), // dataVenda
      faker.person.fullName(), // nomeDestinatario
      generateCPF(), // cpfDestinatario
      generatePhone(), // telefoneDestinatario
      faker.internet.email().toLowerCase(), // emailDestinatario
      faker.location.zipCode('########'), // cepDestinatario
      faker.location.streetAddress(), // enderecoDestinatario
      faker.location.buildingNumber(), // numeroEnderecoDestinatario
      Math.random() > 0.7 ? `${faker.helpers.arrayElement(['Apto', 'Casa', 'Sala'])} ${faker.number.int({ min: 1, max: 200 })}` : '', // complementoDestinatario
      faker.location.county(), // bairroDestinatario
      faker.location.city(), // cidadeDestinatario
      faker.helpers.arrayElement(estados), // estadoDestinatario
      'Brasil', // paisDestinatario
      faker.helpers.arrayElement(ofertas), // oferta
      faker.helpers.arrayElement(formasPagamento), // formaPagamento
      faker.helpers.arrayElement(vendedores), // vendedor
      faker.helpers.arrayElement(operadores), // operador
      situacao, // situacaoVenda
      valorVenda.toFixed(2), // valorVenda
      valorRecebido.toFixed(2), // valorRecebido
      dataRecebimento, // dataRecebimento
      Math.random() > 0.7 ? faker.lorem.sentence() : '' // observacoes
    ];

    // Wrap fields that might contain commas in quotes
    const escapedRow = row.map(field => {
      const strField = String(field);
      if (strField.includes(',') || strField.includes('"') || strField.includes('\n')) {
        return `"${strField.replace(/"/g, '""')}"`;
      }
      return strField;
    });

    rows.push(escapedRow.join(','));
  }

  // Write to file
  const csvContent = rows.join('\n');
  const filePath = path.join(process.cwd(), 'mock-orders-100.csv');
  
  fs.writeFileSync(filePath, csvContent, 'utf-8');
  
  console.log(`✅ CSV file created successfully: ${filePath}`);
  console.log(`📊 Total orders: 100`);
  console.log('\n📌 You can now import this file through the ZenCash interface:');
  console.log('   1. Go to the Orders page');
  console.log('   2. Click on "Importar Dados"');
  console.log('   3. Select the file: mock-orders-100.csv');
  console.log('   4. Review the field mappings and import');
}

// Run the script
generateMockCSV();