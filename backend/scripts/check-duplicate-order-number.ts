import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDuplicateOrderNumber() {
  const orders = await prisma.order.findMany({
    where: {
      orderNumber: 'WH-1753741987156'
    },
    select: {
      id: true,
      orderNumber: true,
      customerName: true,
      status: true,
      createdAt: true,
      tenantId: true
    }
  });

  console.log(`Found ${orders.length} orders with order number 'WH-1753741987156':`);
  orders.forEach(order => {
    console.log(`\nID: ${order.id}`);
    console.log(`Customer: ${order.customerName}`);
    console.log(`Status: ${order.status}`);
    console.log(`Created: ${order.createdAt}`);
    console.log(`Tenant: ${order.tenantId}`);
  });

  await prisma.$disconnect();
}

checkDuplicateOrderNumber().catch(console.error);