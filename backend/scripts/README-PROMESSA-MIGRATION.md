# Migration: Adicionar Status "Promessa"

## Contexto
Precisamos adicionar o status "Promessa" ao enum OrderStatus no banco de dados. Promessa e Negociação são estados diferentes:
- **Negociação**: Cliente está negociando valores/condições
- **Promessa**: Cliente fez uma promessa de pagamento em data específica

## Opções para executar a migration

### Opção 1: Via Railway CLI (Recomendado)
```bash
# No diretório backend
railway run npm run db:add-promessa
```

### Opção 2: Via Railway Dashboard
1. Acesse o dashboard do Railway
2. Vá para o serviço do backend
3. Clique em "Connect" no banco de dados
4. Execute o conteúdo do arquivo `add-promessa-status.sql`

### Opção 3: Localmente com DATABASE_URL
```bash
# Configure a DATABASE_URL do production
export DATABASE_URL="sua-database-url-do-railway"

# Execute o script
npm run db:add-promessa
```

### Opção 4: SQL direto no banco
Se você tem acesso direto ao PostgreSQL, execute:
```sql
ALTER TYPE "OrderStatus" ADD VALUE 'Promessa' AFTER 'Negociacao';
```

## Verificação
Após executar a migration, verifique se funcionou:

1. No banco de dados:
```sql
SELECT enumlabel FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'OrderStatus')
ORDER BY enumsortorder;
```

2. Na aplicação:
- Acesse como cobrador
- Tente mudar um pedido para status "Promessa"
- Verifique se o status persiste corretamente

## Importante
- Esta migration só precisa ser executada uma vez
- O script verifica se "Promessa" já existe antes de adicionar
- Não é possível reverter alterações em enums no PostgreSQL facilmente