import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function deleteAllOrders() {
  try {
    console.log('🗑️  Starting to delete all orders...\n');

    // First, delete all related data in the correct order due to foreign key constraints
    
    // 1. Delete order audit logs
    console.log('Deleting order audit logs...');
    const auditLogResult = await prisma.orderAuditLog.deleteMany({});
    console.log(`✅ Deleted ${auditLogResult.count} audit log records`);

    // 2. Delete status history
    console.log('\nDeleting status history...');
    const statusHistoryResult = await prisma.orderStatusHistory.deleteMany({});
    console.log(`✅ Deleted ${statusHistoryResult.count} status history records`);

    // 3. Delete order items
    console.log('\nDeleting order items...');
    const orderItemsResult = await prisma.orderItem.deleteMany({});
    console.log(`✅ Deleted ${orderItemsResult.count} order items`);

    // 4. Delete tracking info
    console.log('\nDeleting tracking info...');
    const trackingResult = await prisma.tracking.deleteMany({});
    console.log(`✅ Deleted ${trackingResult.count} tracking records`);

    // 5. Delete commission payments
    console.log('\nDeleting commission payments...');
    const commissionResult = await prisma.commissionPayment.deleteMany({});
    console.log(`✅ Deleted ${commissionResult.count} commission payment records`);

    // 6. Delete address components
    console.log('\nDeleting address components...');
    const addressResult = await prisma.orderAddressComponents.deleteMany({});
    console.log(`✅ Deleted ${addressResult.count} address components`);

    // 7. Finally, delete all orders
    console.log('\nDeleting all orders...');
    const ordersResult = await prisma.order.deleteMany({});
    console.log(`✅ Deleted ${ordersResult.count} orders`);

    console.log('\n🎉 Successfully deleted all orders and related data!');
    
  } catch (error) {
    console.error('❌ Error deleting orders:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Add confirmation prompt
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('⚠️  WARNING: This will permanently delete ALL orders and related data from the database!');
console.log('This action cannot be undone.\n');

rl.question('Are you sure you want to continue? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes') {
    deleteAllOrders();
  } else {
    console.log('Operation cancelled.');
    process.exit(0);
  }
  rl.close();
});