import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function fixAdminUser() {
  try {
    const tenantId = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
    
    // Check if admin user exists
    console.log('Checking for admin user...');
    const adminUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
        tenantId: tenantId
      }
    });

    if (adminUser) {
      console.log('Admin user found:', {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        active: adminUser.active
      });

      // Update password
      console.log('Updating admin password...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await prisma.user.update({
        where: { id: adminUser.id },
        data: {
          password: hashedPassword,
          active: true // Ensure user is active
        }
      });
      
      console.log('Admin password updated successfully!');
    } else {
      console.log('Admin user not found. Creating new admin user...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Admin',
          role: 'ADMIN',
          active: true,
          tenantId: tenantId
        }
      });
      
      console.log('Admin user created successfully:', {
        id: newAdmin.id,
        email: newAdmin.email,
        name: newAdmin.name
      });
    }

    // Also check for other users in the tenant
    console.log('\nAll users in tenant:');
    const allUsers = await prisma.user.findMany({
      where: { tenantId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        active: true
      }
    });
    
    allUsers.forEach(user => {
      console.log(`- ${user.email} (${user.role}) - Active: ${user.active}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAdminUser();