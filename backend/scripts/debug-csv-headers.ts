import * as fs from 'fs';
import * as path from 'path';
import * as <PERSON> from 'papa<PERSON><PERSON>';

// Look for CSV files in common locations
const possiblePaths = [
  '../sample.csv',
  '../vendas.csv',
  '../orders.csv',
  '../../sample.csv',
  '../../vendas.csv',
  '../../orders.csv',
  '../../../sample.csv',
  '../../../vendas.csv',
  '../../../orders.csv',
];

console.log('=== Looking for CSV files to check headers ===\n');

for (const csvPath of possiblePaths) {
  const fullPath = path.resolve(__dirname, csvPath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`Found CSV file: ${fullPath}`);
    
    try {
      const csvContent = fs.readFileSync(fullPath, 'utf-8');
      const parsed = Papa.parse(csvContent, {
        header: true,
        preview: 1, // Only parse first row to get headers
      });
      
      if (parsed.meta.fields) {
        console.log('\nCSV Headers:');
        parsed.meta.fields.forEach((field, index) => {
          console.log(`  ${index + 1}. "${field}"`);
          
          // Check for status-related fields
          if (field.toLowerCase().includes('status') || 
              field.toLowerCase().includes('situação') ||
              field.toLowerCase().includes('situacao')) {
            console.log(`     ⚠️  Status-related field found!`);
          }
        });
        
        // Check if sample data has status values
        if (parsed.data.length > 0) {
          const firstRow = parsed.data[0] as any;
          console.log('\nFirst row status-related values:');
          
          Object.keys(firstRow).forEach(key => {
            if (key.toLowerCase().includes('status') || 
                key.toLowerCase().includes('situação') ||
                key.toLowerCase().includes('situacao')) {
              console.log(`  ${key}: "${firstRow[key]}"`);
            }
          });
        }
      }
      
      break; // Found one file, that's enough
    } catch (error) {
      console.error(`Error reading ${fullPath}:`, error);
    }
  }
}

console.log('\n=== Analysis ===');
console.log('The issue appears to be that:');
console.log('1. The CSV has a "Status" column (not "Situação Venda")');
console.log('2. The frontend maps "Status" to "status" field');
console.log('3. But the backend importOrder only checks "situacaoVenda" field');
console.log('4. So the status mapping code is never executed!');
console.log('\nFIX: Update the backend to also check the "status" field.');