import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';
import { OrdersService } from '../src/orders/orders.service';
import { NotificationsService } from '../src/notifications/notifications.service';
import { AntifraudService } from '../src/antifraud/antifraud.service';
import { DuplicateDetectionService } from '../src/antifraud/services/duplicate-detection.service';
import { BrazilianAddressParser } from '../src/antifraud/services/brazilian-address.parser';
import { EncryptionUtil } from '../src/common/utils/encryption.util';
import { ConfigService } from '@nestjs/config';

dotenv.config();

async function testImportWithPrisma() {
  console.log('=== Testing Import with Prisma Directly ===\n');

  const prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  });

  // Mock services
  const configService = new ConfigService();
  const encryptionUtil = new EncryptionUtil(configService);
  const notificationsService = {} as NotificationsService;
  const addressParser = new BrazilianAddressParser();
  const duplicateDetectionService = new DuplicateDetectionService(prisma, addressParser, encryptionUtil);
  const antifraudService = new AntifraudService(
    prisma,
    configService,
    duplicateDetectionService,
    addressParser,
    encryptionUtil
  );

  // Create OrdersService instance
  const ordersService = new OrdersService(
    prisma,
    notificationsService,
    antifraudService
  );

  try {
    // Get a test user and tenant
    const testUser = await prisma.user.findFirst({
      where: { role: 'VENDEDOR' },
      select: { id: true, tenantId: true, name: true },
    });

    if (!testUser || !testUser.tenantId) {
      console.error('No test user found');
      return;
    }

    console.log(`Using test user: ${testUser.name} (${testUser.id})`);
    console.log(`Tenant ID: ${testUser.tenantId}\n`);

    // Test import data with explicit status
    const importDto = {
      idVenda: `TEST-STATUS-${Date.now()}`,
      cliente: 'Test Status Customer',
      telefone: '11999999999',
      valorVenda: 200.00,
      dataVenda: '09/01/2025',
      situacaoVenda: 'Completo', // Explicitly set to Completo
      valorRecebido: 200.00,
      historico: 'Testing status mapping in import',
      vendedor: testUser.name,
    };

    console.log('Import DTO:');
    console.log(JSON.stringify(importDto, null, 2));
    console.log('');

    // Call importOrder method directly
    console.log('Calling importOrder method...\n');
    const importedOrder = await ordersService.importOrder(
      importDto,
      testUser.id,
      testUser.tenantId
    );

    console.log('Import Result:');
    console.log('  - Order Number:', importedOrder.orderNumber);
    console.log('  - Status:', importedOrder.status);
    console.log('  - Expected: Completo');
    console.log('  - Match:', importedOrder.status === 'Completo' ? '✅' : '❌');
    console.log('');

    // Check status history
    const orderWithHistory = await prisma.order.findUnique({
      where: { id: importedOrder.id },
      include: {
        statusHistory: {
          orderBy: { changedAt: 'asc' },
        },
      },
    });

    console.log('Status History:');
    orderWithHistory?.statusHistory.forEach((history, index) => {
      console.log(`  ${index + 1}. ${history.previousStatus} → ${history.newStatus}`);
    });

    // Clean up
    console.log('\nCleaning up test order...');
    await prisma.order.delete({ where: { id: importedOrder.id } });
    console.log('✅ Test order cleaned up');

  } catch (error: any) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

testImportWithPrisma().catch(console.error);