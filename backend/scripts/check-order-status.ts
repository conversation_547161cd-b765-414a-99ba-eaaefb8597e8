import { PrismaClient, OrderStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function checkOrderStatus() {
  try {
    console.log('🔍 Verificando status dos pedidos...\n');

    // Get all orders
    const orders = await prisma.order.findMany({
      where: {
        deletedAt: null
      },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        customerName: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    });

    console.log(`📊 Total de pedidos: ${orders.length}\n`);

    // Group by status
    const statusGroups = new Map<string, number>();
    orders.forEach(order => {
      const count = statusGroups.get(order.status) || 0;
      statusGroups.set(order.status, count + 1);
    });

    console.log('📈 Pedidos por status (backend enum):');
    console.log('═'.repeat(40));
    Array.from(statusGroups.entries()).forEach(([status, count]) => {
      console.log(`${status}: ${count} pedidos`);
    });

    // Show orders in Analise status
    console.log('\n🔎 Pedidos em status ANÁLISE:');
    console.log('═'.repeat(60));
    const analiseOrders = orders.filter(o => o.status === OrderStatus.Analise);
    console.log(`Total: ${analiseOrders.length} pedidos\n`);
    
    analiseOrders.forEach((order, index) => {
      console.log(`${index + 1}. ${order.orderNumber || order.id}`);
      console.log(`   Cliente: ${order.customerName}`);
      console.log(`   Status (enum): ${order.status}`);
      console.log(`   Criado: ${order.createdAt.toLocaleString('pt-BR')}\n`);
    });

    // Check if there are any special characters or spaces
    console.log('🔬 Análise detalhada dos valores de status:');
    console.log('═'.repeat(60));
    statusGroups.forEach((count, status) => {
      console.log(`Status: "${status}"`);
      console.log(`  - Length: ${status.length}`);
      console.log(`  - Char codes: ${Array.from(status).map(c => c.charCodeAt(0)).join(', ')}`);
      console.log(`  - Lowercase: "${status.toLowerCase()}"`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOrderStatus();