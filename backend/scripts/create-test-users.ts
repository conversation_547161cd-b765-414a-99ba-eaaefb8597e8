import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function createTestUsers() {
  console.log('🚀 Creating test users...');
  
  try {
    const tenantId = 'default-tenant';
    const hashedPassword = await bcrypt.hash('123456', 10);
    
    // Create admin user
    const admin = await prisma.user.upsert({
      where: { 
        email_tenantId: {
          email: '<EMAIL>',
          tenantId: tenantId
        }
      },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: Role.ADMIN,
        tenantId: tenantId,
      }
    });
    console.log('✅ Created/Updated admin user:', admin.email);
    
    // Create sellers
    const sellerNames = ['<PERSON>', '<PERSON>', 'Pedro Oliveira', 'Ana Costa'];
    for (let i = 0; i < sellerNames.length; i++) {
      const seller = await prisma.user.upsert({
        where: { 
          email_tenantId: {
            email: `vendedor${i + 1}@zencash.com`,
            tenantId: tenantId
          }
        },
        update: {},
        create: {
          email: `vendedor${i + 1}@zencash.com`,
          password: hashedPassword,
          name: sellerNames[i],
          role: Role.VENDEDOR,
          tenantId: tenantId,
        }
      });
      console.log(`✅ Created/Updated seller ${i + 1}:`, seller.email);
    }
    
    // Create collectors
    const collectorNames = ['Carlos Mendes', 'Patricia Lima'];
    for (let i = 0; i < collectorNames.length; i++) {
      const collector = await prisma.user.upsert({
        where: { 
          email_tenantId: {
            email: `cobrador${i + 1}@zencash.com`,
            tenantId: tenantId
          }
        },
        update: {},
        create: {
          email: `cobrador${i + 1}@zencash.com`,
          password: hashedPassword,
          name: collectorNames[i],
          role: Role.COBRADOR,
          tenantId: tenantId,
        }
      });
      console.log(`✅ Created/Updated collector ${i + 1}:`, collector.email);
    }
    
    console.log('\n✅ All test users created successfully!');
    console.log('\n📝 Login credentials:');
    console.log('  All users use password: 123456');
    
  } catch (error) {
    console.error('❌ Error creating test users:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createTestUsers()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });