import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker/locale/pt_BR';

const prisma = new PrismaClient();

// Lists of realistic Brazilian data
const vendedores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'
];

const operadores = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const ofertas = [
  'Plano Premium Mensal', 'Plano Básico Mensal', 'Plano Premium Anual', 'Plano Básico Anual',
  'Pacote Empresarial', 'Pacote Profissional', 'Assinatura Gold', 'Assinatura Silver',
  'Combo Família', 'Combo Individual'
];

const situacoes = ['pendente', 'analise', 'negociacao', 'parcial', 'completo', 'cancelado'];

const estados = ['SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA', 'PE', 'CE', 'GO', 'DF', 'ES', 'PB', 'RN', 'AM', 'PA'];

const formasPagamento = ['Cartão de Crédito', 'Boleto', 'PIX', 'Transferência', 'Dinheiro'];

// Helper function to generate a date within the last 90 days
function randomDate(daysBack: number = 90): Date {
  const today = new Date();
  const pastDate = new Date(today);
  pastDate.setDate(today.getDate() - Math.floor(Math.random() * daysBack));
  return pastDate;
}

// Helper function to format date to DD/MM/YYYY
function formatDate(date: Date): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

// Helper function to generate CPF
function generateCPF(): string {
  const n1 = Math.floor(Math.random() * 10);
  const n2 = Math.floor(Math.random() * 10);
  const n3 = Math.floor(Math.random() * 10);
  const n4 = Math.floor(Math.random() * 10);
  const n5 = Math.floor(Math.random() * 10);
  const n6 = Math.floor(Math.random() * 10);
  const n7 = Math.floor(Math.random() * 10);
  const n8 = Math.floor(Math.random() * 10);
  const n9 = Math.floor(Math.random() * 10);
  
  return `${n1}${n2}${n3}.${n4}${n5}${n6}.${n7}${n8}${n9}-00`;
}

// Helper function to generate phone
function generatePhone(): string {
  const ddd = Math.floor(Math.random() * (99 - 11 + 1)) + 11;
  const firstPart = Math.floor(Math.random() * (99999 - 90000 + 1)) + 90000;
  const secondPart = Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000;
  return `(${ddd}) ${firstPart}-${secondPart}`;
}

async function generateMockOrders() {
  console.log('🚀 Starting to generate 100 mock orders...');

  const orders = [];

  for (let i = 0; i < 100; i++) {
    const dataVenda = randomDate(90);
    const situacao = faker.helpers.arrayElement(situacoes);
    const valorVenda = faker.number.float({ min: 100, max: 5000, precision: 0.01 });
    
    // Calculate valorRecebido based on situation
    let valorRecebido = 0;
    let dataRecebimento = null;
    
    if (situacao === 'completo') {
      valorRecebido = valorVenda;
      // Payment received 1-15 days after sale
      dataRecebimento = new Date(dataVenda);
      dataRecebimento.setDate(dataRecebimento.getDate() + Math.floor(Math.random() * 15) + 1);
    } else if (situacao === 'parcial') {
      // Partial payment between 20-80% of total
      valorRecebido = valorVenda * (0.2 + Math.random() * 0.6);
      dataRecebimento = new Date(dataVenda);
      dataRecebimento.setDate(dataRecebimento.getDate() + Math.floor(Math.random() * 20) + 1);
    } else if (situacao === 'negociacao') {
      // Small initial payment 5-15%
      valorRecebido = valorVenda * (0.05 + Math.random() * 0.1);
      dataRecebimento = new Date(dataVenda);
      dataRecebimento.setDate(dataRecebimento.getDate() + Math.floor(Math.random() * 10) + 1);
    }

    const nomeDestinatario = faker.person.fullName();
    
    const order = {
      numeroVenda: `VND-2024-${(i + 1).toString().padStart(4, '0')}`,
      dataVenda: formatDate(dataVenda),
      customerName: nomeDestinatario, // Required field
      nomeDestinatario: nomeDestinatario,
      cpfDestinatario: generateCPF(),
      telefoneDestinatario: generatePhone(),
      emailDestinatario: faker.internet.email().toLowerCase(),
      cepDestinatario: faker.location.zipCode('########'),
      enderecoDestinatario: faker.location.streetAddress(),
      numeroEnderecoDestinatario: faker.location.buildingNumber(),
      complementoDestinatario: Math.random() > 0.7 ? faker.helpers.arrayElement(['Apto', 'Casa', 'Sala']) + ' ' + faker.number.int({ min: 1, max: 200 }) : '',
      bairroDestinatario: faker.location.county(),
      cidadeDestinatario: faker.location.city(),
      estadoDestinatario: faker.helpers.arrayElement(estados),
      paisDestinatario: 'Brasil',
      oferta: faker.helpers.arrayElement(ofertas),
      formaPagamento: faker.helpers.arrayElement(formasPagamento),
      vendedor: faker.helpers.arrayElement(vendedores),
      operador: faker.helpers.arrayElement(operadores),
      situacaoVenda: situacao,
      valorVenda: valorVenda,
      valorRecebido: parseFloat(valorRecebido.toFixed(2)),
      dataRecebimento: dataRecebimento ? formatDate(dataRecebimento) : null,
      observacoes: Math.random() > 0.7 ? faker.lorem.sentence() : null
    };

    orders.push(order);
  }

  // Insert all orders
  console.log('📝 Inserting orders into database...');
  
  for (const order of orders) {
    try {
      await prisma.order.create({
        data: order
      });
    } catch (error) {
      console.error(`Error creating order ${order.numeroVenda}:`, error);
    }
  }

  // Print summary
  const summary = {
    total: orders.length,
    pendente: orders.filter(o => o.situacaoVenda === 'pendente').length,
    analise: orders.filter(o => o.situacaoVenda === 'analise').length,
    negociacao: orders.filter(o => o.situacaoVenda === 'negociacao').length,
    parcial: orders.filter(o => o.situacaoVenda === 'parcial').length,
    completo: orders.filter(o => o.situacaoVenda === 'completo').length,
    cancelado: orders.filter(o => o.situacaoVenda === 'cancelado').length,
    totalRevenue: orders.reduce((sum, o) => sum + o.valorVenda, 0).toFixed(2),
    totalReceived: orders.reduce((sum, o) => sum + o.valorRecebido, 0).toFixed(2)
  };

  console.log('\n✅ Mock data generation completed!');
  console.log('📊 Summary:');
  console.log(`   Total Orders: ${summary.total}`);
  console.log(`   - Pendente: ${summary.pendente}`);
  console.log(`   - Análise: ${summary.analise}`);
  console.log(`   - Negociação: ${summary.negociacao}`);
  console.log(`   - Parcial: ${summary.parcial}`);
  console.log(`   - Completo: ${summary.completo}`);
  console.log(`   - Cancelado: ${summary.cancelado}`);
  console.log(`   Total Revenue: R$ ${summary.totalRevenue}`);
  console.log(`   Total Received: R$ ${summary.totalReceived}`);
}

// Run the script
generateMockOrders()
  .catch(console.error)
  .finally(async () => {
    await prisma.$disconnect();
  });