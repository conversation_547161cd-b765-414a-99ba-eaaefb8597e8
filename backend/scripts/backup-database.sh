#!/bin/bash

# Script de Backup do Banco de Dados ZenCash
# Uso: ./scripts/backup-database.sh

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Carregar variáveis de ambiente
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo -e "${RED}Erro: Arquivo .env não encontrado${NC}"
    exit 1
fi

# Configurações
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="zencash_backup_${TIMESTAMP}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# Extrair informações de conexão do DATABASE_URL
# Formato: postgresql://username:password@host:port/database
if [[ $DATABASE_URL =~ postgresql://([^:]+):([^@]+)@([^:]+):([0-9]+)/(.+) ]]; then
    DB_USER="${BASH_REMATCH[1]}"
    DB_PASS="${BASH_REMATCH[2]}"
    DB_HOST="${BASH_REMATCH[3]}"
    DB_PORT="${BASH_REMATCH[4]}"
    DB_NAME="${BASH_REMATCH[5]}"
else
    echo -e "${RED}Erro: DATABASE_URL inválida${NC}"
    exit 1
fi

echo -e "${YELLOW}🔄 Iniciando backup do banco de dados...${NC}"
echo "Database: $DB_NAME"
echo "Host: $DB_HOST:$DB_PORT"
echo "Backup: $BACKUP_PATH"

# Executar backup usando pg_dump
PGPASSWORD="$DB_PASS" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --no-owner \
    --no-privileges \
    --verbose \
    -f "$BACKUP_PATH" 2>&1 | while read line; do
    echo -e "${GREEN}  $line${NC}"
done

# Verificar se o backup foi criado com sucesso
if [ -f "$BACKUP_PATH" ]; then
    # Comprimir o backup
    echo -e "${YELLOW}📦 Comprimindo backup...${NC}"
    gzip "$BACKUP_PATH"
    COMPRESSED_PATH="${BACKUP_PATH}.gz"
    
    # Tamanho do arquivo
    SIZE=$(ls -lh "$COMPRESSED_PATH" | awk '{print $5}')
    
    echo -e "${GREEN}✅ Backup concluído com sucesso!${NC}"
    echo "Arquivo: $COMPRESSED_PATH"
    echo "Tamanho: $SIZE"
    
    # Limpar backups antigos (manter apenas os últimos 7 dias)
    echo -e "${YELLOW}🧹 Limpando backups antigos...${NC}"
    find "$BACKUP_DIR" -name "zencash_backup_*.sql.gz" -mtime +7 -exec rm {} \;
    
    # Listar backups disponíveis
    echo -e "${YELLOW}📋 Backups disponíveis:${NC}"
    ls -lht "$BACKUP_DIR"/*.gz 2>/dev/null | head -10
else
    echo -e "${RED}❌ Erro ao criar backup${NC}"
    exit 1
fi

# Opção para enviar backup para storage externo (S3, Google Cloud, etc)
if [ ! -z "$BACKUP_S3_BUCKET" ]; then
    echo -e "${YELLOW}☁️  Enviando backup para S3...${NC}"
    aws s3 cp "$COMPRESSED_PATH" "s3://$BACKUP_S3_BUCKET/database-backups/" --storage-class GLACIER
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Backup enviado para S3${NC}"
    else
        echo -e "${RED}❌ Erro ao enviar backup para S3${NC}"
    fi
fi

echo -e "${GREEN}🎉 Processo de backup finalizado!${NC}"