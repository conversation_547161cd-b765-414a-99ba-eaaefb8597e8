import * as fs from 'fs';
import * as path from 'path';
import * as <PERSON> from 'papa<PERSON><PERSON>';
import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3003/api/v1';
const TENANT_ID = '28a833c0-c2a1-4498-85ca-b028f982ffb2';

// Same mappings as frontend
const DEFAULT_MAPPINGS: { [key: string]: string } = {
  'Data Venda': 'dataVenda',
  'ID Venda': 'idVenda',
  'Cliente': 'cliente',
  'Telefone': 'telefone',
  'Oferta': 'oferta',
  'Valor Venda': 'valorVenda',
  'Status': 'status',
  'Situação Venda': 'situacaoVenda',
  'Valor Recebido': 'valorRecebido',
  'Historico': 'historico',
  'Ultima Atualização': 'ultimaAtualizacao',
  'Código de Rastreio': 'codigoRastreio',
  'Status Correios': 'statusCorreios',
  'Vendedor': 'vendedor',
  'Operador': 'operador',
  'Zap': 'zap',
  'ESTADO DO DESTINATÁRIO': 'estadoDestinatario',
  'CIDADE DO DESTINATÁRIO': 'cidadeDestinatario',
  'RUA DO DESTINATÁRIO': 'ruaDestinatario',
  'CEP DO DESTINATÁRIO': 'cepDestinatario',
  'COMPLEMENTO DO DESTINATÁRIO': 'complementoDestinatario',
  'BAIRRO DO DESTINATÁRIO': 'bairroDestinatario',
  'NÚMERO DO ENDEREÇO DO DESTINATÁRIO': 'numeroEnderecoDestinatario',
  'DATA ESTIMADA DE CHEGADA': 'dataEstimadaChegada',
  'CÓDIGO DO AFILIADO': 'codigoAfiliado',
  'NOME DO AFILIADO': 'nomeAfiliado',
  'E-MAIL DO AFILIADO': 'emailAfiliado',
  'DOCUMENTO DO AFILIADO': 'documentoAfiliado',
  'DATA DE RECEBIMENTO': 'dataRecebimento',
  'Data_Negociacao': 'dataNegociacao',
  'FormaPagamento': 'formaPagamento',
  'DOCUMENTO CLIENTE': 'documentoCliente',
  'Parcial': 'parcial',
  'Pagamento_Parcial': 'pagamentoParcial',
  'FormaPagamentoParcial': 'formaPagamentoParcial',
  'DataPagamentoParcial': 'dataPagamentoParcial',
};

async function testCSVParsing() {
  console.log('\n=== CSV PARSING TEST ===\n');
  
  // Read the CSV file
  const csvPath = path.join(__dirname, '../test-import.csv');
  const csvContent = fs.readFileSync(csvPath, 'utf-8');
  
  console.log('CSV file read successfully');
  console.log('First 200 chars:', csvContent.substring(0, 200));
  
  // Parse CSV
  const parseResult = Papa.parse(csvContent, {
    header: true,
    skipEmptyLines: true,
  });
  
  console.log('\n=== PARSE RESULTS ===');
  console.log('Total rows:', parseResult.data.length);
  console.log('Headers found:', parseResult.data.length > 0 ? Object.keys((parseResult.data as any[])[0]) : 'No data');
  
  // Test mapping first row
  const firstRow = (parseResult.data as any[])[0];
  console.log('\n=== FIRST ROW RAW DATA ===');
  console.log(JSON.stringify(firstRow, null, 2));
  
  // Map the row
  const mappedOrder = mapRowToOrder(firstRow);
  console.log('\n=== MAPPED ORDER ===');
  console.log(JSON.stringify(mappedOrder, null, 2));
  
  // Test import if we have a valid token
  if (process.argv.includes('--import')) {
    await testImport(mappedOrder);
  }
}

function mapRowToOrder(row: any): any {
  const order: any = {};
  
  console.log('\n=== FIELD MAPPING PROCESS ===');
  
  Object.keys(DEFAULT_MAPPINGS).forEach(csvField => {
    const systemField = DEFAULT_MAPPINGS[csvField];
    const value = row[csvField];
    
    if (value && value.trim() !== '') {
      console.log(`Mapping: "${csvField}" (value: "${value}") -> "${systemField}"`);
      
      // Handle monetary values
      if (['valorVenda', 'valorRecebido', 'pagamentoParcial'].includes(systemField)) {
        // Handle both formats: "350.00" and "1.350,00"
        let cleanValue = value.replace('R$', '').trim();
        
        // If value contains comma, it's Brazilian format (1.350,00)
        if (cleanValue.includes(',')) {
          cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
        }
        // Otherwise it's already in decimal format (350.00)
        
        const numValue = parseFloat(cleanValue);
        order[systemField] = numValue;
        console.log(`  Converted to number: ${numValue}`);
      }
      // Handle boolean values
      else if (systemField === 'parcial') {
        order[systemField] = value.toLowerCase() === 'sim' || value === '1' || value === 'true';
        console.log(`  Converted to boolean: ${order[systemField]}`);
      }
      // Handle dates
      else if (systemField.includes('data') || systemField.includes('Data')) {
        order[systemField] = value;
        console.log(`  Date field: ${value}`);
      }
      // Default to string
      else {
        order[systemField] = value;
      }
    } else {
      console.log(`Skipping empty field: "${csvField}"`);
    }
  });
  
  return order;
}

async function testImport(orderData: any) {
  console.log('\n=== TESTING IMPORT ===');
  
  try {
    // Login first
    console.log('Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
    }, {
      headers: {
        'x-tenant-id': TENANT_ID
      }
    });
    
    const token = loginResponse.data.access_token;
    console.log('Login successful');
    
    // Test import
    console.log('\nImporting order...');
    const importResponse = await axios.post(`${API_URL}/orders/import`, orderData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': TENANT_ID,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n=== IMPORT RESPONSE ===');
    console.log(JSON.stringify(importResponse.data, null, 2));
    
  } catch (error: any) {
    console.error('\n=== IMPORT ERROR ===');
    console.error('Status:', error.response?.status);
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
testCSVParsing().catch(console.error);