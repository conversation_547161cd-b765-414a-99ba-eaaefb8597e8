import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import { join } from 'path';

// Load environment variables
dotenv.config({ path: join(__dirname, '../.env') });

async function checkWebhookMappings() {
  console.log('=== Checking Webhook Mappings ===\n');

  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    username: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'postgres',
    database: process.env.DATABASE_NAME || 'zencash',
    entities: [join(__dirname, '../src/**/*.entity{.ts,.js}')],
    synchronize: false,
  });

  try {
    await dataSource.initialize();
    console.log('Connected to database\n');

    // Query all webhook mappings
    const allMappings = await dataSource.query(`
      SELECT * FROM webhook_mappings 
      ORDER BY "payloadKey"
    `);

    console.log(`Total mappings found: ${allMappings.length}\n`);

    // Show mappings related to 'id' field
    console.log('=== Mappings for ID fields ===');
    const idMappings = allMappings.filter((m: any) => 
      m.payloadKey.toLowerCase().includes('id') || 
      m.entityColumn?.toLowerCase().includes('id')
    );
    
    for (const mapping of idMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${mapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
      console.log(`Description: ${mapping.description || 'N/A'}`);
      console.log(`Active: ${mapping.isActive}`);
    }

    // Show specific mapping for 'sale.id'
    console.log('\n=== Specific mapping for sale.id ===');
    const saleIdMapping = allMappings.find((m: any) => m.payloadKey === 'sale.id');
    if (saleIdMapping) {
      console.log(`Payload Key: ${saleIdMapping.payloadKey}`);
      console.log(`Entity Column: ${saleIdMapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${saleIdMapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${saleIdMapping.sampleValue || 'N/A'}`);
      console.log(`Description: ${saleIdMapping.description || 'N/A'}`);
    } else {
      console.log('No mapping found for sale.id');
    }

    // Show mappings for orderNumber
    console.log('\n=== Mappings for orderNumber ===');
    const orderNumberMappings = allMappings.filter((m: any) => 
      m.entityColumn === 'orderNumber' || 
      (m.payloadKey.toLowerCase().includes('order') && m.payloadKey.toLowerCase().includes('number'))
    );
    
    for (const mapping of orderNumberMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
    }

    // Show all active mappings (with entityColumn set)
    console.log('\n=== All Active Mappings (with entity columns) ===');
    const activeMappings = allMappings.filter((m: any) => m.entityColumn && m.entityColumn !== '_ignore');
    for (const mapping of activeMappings) {
      console.log(`${mapping.payloadKey} => ${mapping.entityColumn}`);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await dataSource.destroy();
  }
}

checkWebhookMappings().catch(console.error);