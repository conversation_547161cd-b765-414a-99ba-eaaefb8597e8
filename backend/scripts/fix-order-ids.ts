import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function fixOrderIds() {
  console.log('=== Fixing Order IDs ===\n');

  try {
    // Find the order with the invalid ID
    const invalidIdOrder = await prisma.$queryRaw`
      SELECT * FROM "Order" WHERE id = 'sal47952'
    ` as any[];

    if (invalidIdOrder.length === 0) {
      console.log('No order found with ID "sal47952"');
      return;
    }

    const order = invalidIdOrder[0];
    console.log('Found order with invalid ID:');
    console.log(`  Current ID: ${order.id}`);
    console.log(`  Order Number: ${order.orderNumber}`);
    console.log(`  Customer: ${order.customerName}`);
    console.log(`  Status: ${order.status}`);
    console.log(`  Created: ${order.createdAt}`);

    // Generate a new UUID for this order
    const newId = randomUUID();
    console.log(`\nNew UUID to be assigned: ${newId}`);

    // Start a transaction to update the order and all related records
    await prisma.$transaction(async (tx) => {
      // First, we need to update all foreign key references
      
      // Update OrderItems
      const orderItemsUpdated = await tx.$executeRaw`
        UPDATE "OrderItem" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${orderItemsUpdated} OrderItem records`);

      // Update OrderStatusHistory
      const statusHistoryUpdated = await tx.$executeRaw`
        UPDATE "OrderStatusHistory" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${statusHistoryUpdated} OrderStatusHistory records`);

      // Update CommissionPayments
      const commissionPaymentsUpdated = await tx.$executeRaw`
        UPDATE "CommissionPayment" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${commissionPaymentsUpdated} CommissionPayment records`);

      // Update PaymentApprovals
      const paymentApprovalsUpdated = await tx.$executeRaw`
        UPDATE "PaymentApproval" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${paymentApprovalsUpdated} PaymentApproval records`);

      // Update Tracking
      const trackingUpdated = await tx.$executeRaw`
        UPDATE "Tracking" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${trackingUpdated} Tracking records`);

      // Update ShipmentLabel
      const shipmentLabelUpdated = await tx.$executeRaw`
        UPDATE "ShipmentLabel" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${shipmentLabelUpdated} ShipmentLabel records`);

      // Update OrderAddressComponents
      const addressComponentsUpdated = await tx.$executeRaw`
        UPDATE "OrderAddressComponents" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${addressComponentsUpdated} OrderAddressComponents records`);

      // Update OrderAuditLog
      const auditLogUpdated = await tx.$executeRaw`
        UPDATE "OrderAuditLog" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${auditLogUpdated} OrderAuditLog records`);

      // Update WebhookLog references
      const webhookLogUpdated = await tx.$executeRaw`
        UPDATE "WebhookLog" SET "orderId" = ${newId} WHERE "orderId" = 'sal47952'
      `;
      console.log(`Updated ${webhookLogUpdated} WebhookLog records`);

      // Finally, update the Order itself
      const orderUpdated = await tx.$executeRaw`
        UPDATE "Order" SET "id" = ${newId} WHERE "id" = 'sal47952'
      `;
      console.log(`\nUpdated ${orderUpdated} Order record`);
    });

    console.log('\n✅ Successfully fixed order ID!');

    // Verify the fix
    const fixedOrder = await prisma.order.findUnique({
      where: { id: newId },
      include: {
        items: true,
        statusHistory: true,
        tracking: true
      }
    });

    if (fixedOrder) {
      console.log('\nVerification - Order now has valid UUID:');
      console.log(`  ID: ${fixedOrder.id}`);
      console.log(`  Order Number: ${fixedOrder.orderNumber}`);
      console.log(`  Customer: ${fixedOrder.customerName}`);
      console.log(`  Items: ${fixedOrder.items.length}`);
      console.log(`  Status History: ${fixedOrder.statusHistory.length}`);
      console.log(`  Has Tracking: ${fixedOrder.tracking ? 'Yes' : 'No'}`);
    }

  } catch (error) {
    console.error('Error fixing order IDs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixOrderIds().catch(console.error);