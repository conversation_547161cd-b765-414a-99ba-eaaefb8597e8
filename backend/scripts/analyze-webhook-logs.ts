import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';

const prisma = new PrismaClient();

interface PathAnalysis {
  path: string;
  occurrences: number;
  presentInStatuses: Set<string>;
  sampleValues: Set<string>;
}

async function analyzeWebhookLogs() {
  console.log('Analyzing webhook logs...\n');

  try {
    // Get all webhook logs
    const logs = await prisma.webhookLog.findMany({
      orderBy: { receivedAt: 'desc' },
      take: 1000, // Analyze last 1000 logs
    });

    console.log(`Found ${logs.length} webhook logs to analyze\n`);

    // Analysis results
    const pathAnalysis: Map<string, PathAnalysis> = new Map();
    const deliveryStatuses: Set<string> = new Set();
    const statusPatterns: Map<string, number> = new Map();

    // Analyze each log
    for (const log of logs) {
      const payload = log.payload as any;
      
      if (!payload) continue;

      // Check for delivery-related statuses
      const checkDeliveryStatus = (obj: any, path: string = '') => {
        if (!obj || typeof obj !== 'object') return;

        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          // Check for delivery keywords
          if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (
              lowerValue.includes('saiu para entrega') ||
              lowerValue.includes('saído para entrega') ||
              lowerValue.includes('em rota de entrega') ||
              lowerValue.includes('out for delivery') ||
              lowerValue.includes('em entrega') ||
              lowerValue.includes('entregador') ||
              lowerValue.includes('objeto saiu para entrega')
            ) {
              console.log(`\n🚚 DELIVERY STATUS FOUND:`);
              console.log(`Path: ${currentPath}`);
              console.log(`Value: ${value}`);
              console.log(`Status: ${payload.status || 'N/A'}`);
              console.log(`Event: ${payload.event || 'N/A'}`);
              deliveryStatuses.add(value);
            }

            // Track all status patterns
            if (key === 'status' || key === 'event' || key.includes('correios')) {
              statusPatterns.set(value, (statusPatterns.get(value) || 0) + 1);
            }
          }

          // Recursively check nested objects
          checkDeliveryStatus(value, currentPath);
        }
      };

      checkDeliveryStatus(payload);

      // Analyze common data paths
      const analyzePaths = (obj: any, path: string = '', status: string = '') => {
        if (!obj || typeof obj !== 'object') return;

        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          // Track important fields
          if (
            key === 'product_name' || 
            key === 'title' ||
            key === 'customer_name' ||
            key === 'customer_address' ||
            key === 'customer_phone' ||
            key === 'tracking_code' ||
            key === 'delivery_date' ||
            key === 'estimated_delivery'
          ) {
            if (value && typeof value === 'string') {
              if (!pathAnalysis.has(currentPath)) {
                pathAnalysis.set(currentPath, {
                  path: currentPath,
                  occurrences: 0,
                  presentInStatuses: new Set(),
                  sampleValues: new Set(),
                });
              }
              
              const analysis = pathAnalysis.get(currentPath)!;
              analysis.occurrences++;
              analysis.presentInStatuses.add(status || payload.status || 'unknown');
              if (analysis.sampleValues.size < 3) {
                analysis.sampleValues.add(value);
              }
            }
          }

          // Check for delivery date patterns
          if (
            key.includes('delivery') || 
            key.includes('entrega') ||
            key.includes('estimat')
          ) {
            if (value && (typeof value === 'string' || typeof value === 'number')) {
              console.log(`\n📅 Potential delivery date field:`);
              console.log(`Path: ${currentPath}`);
              console.log(`Value: ${value}`);
            }
          }

          // Recursively analyze nested objects
          analyzePaths(value, currentPath, status || payload.status);
        }
      };

      analyzePaths(payload);
    }

    // Print analysis results
    console.log('\n=== PATH RELIABILITY ANALYSIS ===\n');
    
    const sortedPaths = Array.from(pathAnalysis.entries())
      .sort((a, b) => b[1].occurrences - a[1].occurrences);

    for (const [path, analysis] of sortedPaths) {
      const reliability = (analysis.occurrences / logs.length * 100).toFixed(1);
      console.log(`Path: ${path}`);
      console.log(`  Occurrences: ${analysis.occurrences} (${reliability}%)`);
      console.log(`  Present in statuses: ${Array.from(analysis.presentInStatuses).join(', ')}`);
      console.log(`  Sample values: ${Array.from(analysis.sampleValues).join(' | ')}`);
      console.log('');
    }

    // Print delivery statuses found
    console.log('\n=== DELIVERY STATUSES FOUND ===\n');
    if (deliveryStatuses.size > 0) {
      deliveryStatuses.forEach(status => {
        console.log(`- ${status}`);
      });
    } else {
      console.log('No delivery statuses found containing "saiu para entrega" or similar');
    }

    // Print all status patterns
    console.log('\n=== ALL STATUS PATTERNS ===\n');
    const sortedStatuses = Array.from(statusPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 50); // Top 50 statuses

    for (const [status, count] of sortedStatuses) {
      console.log(`${status}: ${count} occurrences`);
    }

    // Analyze specific product name paths
    console.log('\n=== PRODUCT NAME PATH ANALYSIS ===\n');
    const productPaths = [
      'sale.offer.main_product.title',
      'sale.offers[0].product.title',
      'offer.product.title',
      'sale.product_name',
      'product_name',
    ];

    for (const path of productPaths) {
      let count = 0;
      let samples = new Set<string>();
      
      logs.forEach(log => {
        const value = getValueByPath(log.payload as any, path);
        if (value) {
          count++;
          if (samples.size < 3) samples.add(value);
        }
      });
      
      if (count > 0) {
        console.log(`${path}: ${count} occurrences (${(count/logs.length*100).toFixed(1)}%)`);
        console.log(`  Samples: ${Array.from(samples).join(' | ')}`);
      }
    }

  } catch (error) {
    console.error('Error analyzing webhook logs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getValueByPath(obj: any, path: string): any {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (!current) return null;
    
    // Handle array notation
    const arrayMatch = part.match(/(.+)\[(\d+)\]/);
    if (arrayMatch) {
      current = current[arrayMatch[1]];
      if (Array.isArray(current)) {
        current = current[parseInt(arrayMatch[2])];
      }
    } else {
      current = current[part];
    }
  }
  
  return current;
}

// Run the analysis
analyzeWebhookLogs();