import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { WebhookMappingService } from '../src/webhooks/webhook-mapping.service';

async function checkWebhookMappings() {
  console.log('=== Checking Webhook Mappings ===\n');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  const webhookMappingService = app.get(WebhookMappingService);

  try {
    // Get all mappings
    const allMappings = await webhookMappingService.listMappings();
    console.log(`Total mappings found: ${allMappings.length}\n`);

    // Get active mappings (with entityColumn set)
    const activeMappings = await webhookMappingService.getActiveMappings();
    console.log(`Active mappings (with entity columns): ${activeMappings.length}\n`);

    // Show mappings related to 'id' field
    console.log('=== Mappings for ID fields ===');
    const idMappings = allMappings.filter(m => 
      m.payloadKey.toLowerCase().includes('id') || 
      m.entityColumn?.toLowerCase().includes('id')
    );
    
    for (const mapping of idMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${mapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
      console.log(`Description: ${mapping.description || 'N/A'}`);
      console.log(`Active: ${mapping.isActive}`);
    }

    // Show specific mapping for 'sale.id'
    console.log('\n=== Specific mapping for sale.id ===');
    const saleIdMapping = allMappings.find(m => m.payloadKey === 'sale.id');
    if (saleIdMapping) {
      console.log(`Payload Key: ${saleIdMapping.payloadKey}`);
      console.log(`Entity Column: ${saleIdMapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Data Type: ${saleIdMapping.dataType || 'N/A'}`);
      console.log(`Sample Value: ${saleIdMapping.sampleValue || 'N/A'}`);
      console.log(`Description: ${saleIdMapping.description || 'N/A'}`);
    } else {
      console.log('No mapping found for sale.id');
    }

    // Show mappings for orderNumber
    console.log('\n=== Mappings for orderNumber ===');
    const orderNumberMappings = allMappings.filter(m => 
      m.entityColumn === 'orderNumber' || 
      m.payloadKey.toLowerCase().includes('order') && m.payloadKey.toLowerCase().includes('number')
    );
    
    for (const mapping of orderNumberMappings) {
      console.log(`\nPayload Key: ${mapping.payloadKey}`);
      console.log(`Entity Column: ${mapping.entityColumn || 'NOT MAPPED'}`);
      console.log(`Sample Value: ${mapping.sampleValue || 'N/A'}`);
    }

    // Show all active mappings
    console.log('\n=== All Active Mappings ===');
    for (const mapping of activeMappings) {
      console.log(`\n${mapping.payloadKey} => ${mapping.entityColumn}`);
    }

  } catch (error) {
    console.error('Error checking webhook mappings:', error);
  } finally {
    await app.close();
  }
}

checkWebhookMappings().catch(console.error);