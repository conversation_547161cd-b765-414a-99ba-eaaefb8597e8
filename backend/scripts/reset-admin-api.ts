import axios from 'axios';

async function resetAdmin() {
  try {
    console.log('Resetting admin password...');
    
    const response = await axios.post('https://zencash-production.up.railway.app/api/v1/auth/reset-admin', {
      secret: 'zencash-reset-2025'
    }, {
      headers: {
        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
      }
    });
    
    console.log('Response:', response.data);
  } catch (error: any) {
    console.error('Error:', error.response?.data || error.message);
  }
}

resetAdmin();