#!/bin/sh
# Test script to verify Alpine Linux compatibility

echo "🧪 Testing Alpine Linux compatibility..."

# Test 1: Check shell
echo "1. Shell check:"
echo "   Current shell: $SHELL"
echo "   sh location: $(which sh)"

# Test 2: Check POSIX compliance
echo ""
echo "2. POSIX compliance check:"
# Test variable expansion
test_var="hello"
echo "   Variable expansion: ${test_var:-default}"
echo "   Command substitution: $(echo "works")"

# Test 3: Check signal handling
echo ""
echo "3. Signal handling:"
trap 'echo "   Signal trapped correctly"' USR1
kill -USR1 $$
trap - USR1

# Test 4: Check arithmetic
echo ""
echo "4. Arithmetic operations:"
count=0
count=$((count + 1))
echo "   Arithmetic: 0 + 1 = $count"

# Test 5: Check conditional syntax
echo ""
echo "5. Conditional syntax:"
if [ -f "/etc/alpine-release" ]; then
    echo "   Running on Alpine Linux: $(cat /etc/alpine-release)"
else
    echo "   Not running on Alpine Linux"
fi

# Test 6: Check function syntax (without 'local')
echo ""
echo "6. Function syntax:"
test_function() {
    func_var="function works"
    echo "   $func_var"
}
test_function

echo ""
echo "✅ All compatibility tests passed!"