import axios from 'axios';

async function testAntifraudReview() {
  try {
    // First, login to get a token
    console.log('1. Logging in...');
    const loginResponse = await axios.post('https://zencash-production.up.railway.app/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'zencash'
    });

    const token = loginResponse.data.access_token;
    console.log('Login successful, got token');

    // Get an order to review
    const orderId = '54598d12-3648-4d2b-8455-3e8b75650bd6';
    
    console.log('\n2. Testing anti-fraud review endpoint...');
    try {
      const reviewResponse = await axios.post(
        `https://zencash-production.up.railway.app/api/v1/antifraud/orders/${orderId}/review`,
        {
          decision: 'APPROVE_ORDER',
          notes: 'Pedido verificado e aprovado'
        },
        {
          headers: {
            'Authorization': `Bear<PERSON> ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Review successful:', reviewResponse.data);
    } catch (reviewError: any) {
      console.error('Review failed:', {
        status: reviewError.response?.status,
        statusText: reviewError.response?.statusText,
        data: reviewError.response?.data,
        headers: reviewError.response?.headers
      });
    }

  } catch (error: any) {
    console.error('Test failed:', error.message);
  }
}

testAntifraudReview();