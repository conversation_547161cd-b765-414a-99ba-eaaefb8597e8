import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';

async function testImportEndpoints() {
  console.log('Testing import endpoints at:', API_URL);
  
  try {
    // First, let's try to login to get a token
    console.log('\n1. Testing login...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
    }, {
      headers: {
        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
      }
    });
    
    const token = loginResponse.data.access_token;
    console.log('✓ Login successful, got token');
    
    // Test check endpoint
    console.log('\n2. Testing check order exists endpoint...');
    try {
      const checkResponse = await axios.get(`${API_URL}/orders/check/TEST123`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
        }
      });
      console.log('✓ Check endpoint response:', checkResponse.data);
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log('✓ Check endpoint working (404 = order not found)');
      } else {
        console.error('✗ Check endpoint error:', error.response?.status, error.response?.data);
      }
    }
    
    // Test import endpoint
    console.log('\n3. Testing import endpoint...');
    const importData = {
      idVenda: 'TEST' + Date.now(),
      cliente: 'Test Customer',
      telefone: '11999999999',
      valorVenda: 100.00,
      situacaoVenda: 'Analise',
      vendedor: 'Admin',
      operador: 'Gemima'
    };
    
    try {
      const importResponse = await axios.post(`${API_URL}/orders/import`, importData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2',
          'Content-Type': 'application/json'
        }
      });
      console.log('✓ Import endpoint response:', importResponse.data);
    } catch (error: any) {
      console.error('✗ Import endpoint error:', error.response?.status, error.response?.statusText);
      console.error('Error details:', error.response?.data);
    }
    
  } catch (error: any) {
    console.error('Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.status, error.response.data);
    }
  }
}

testImportEndpoints();