#!/bin/bash
set -e

echo "🚨 Railway Database Backup Restore"
echo "=================================="
echo ""
echo "This script will help you restore from Railway's daily backup"
echo ""

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ Error: .env file not found"
    exit 1
fi

echo "📋 Steps to restore from Railway backup:"
echo ""
echo "1. Go to your Railway dashboard: https://railway.app/dashboard"
echo "2. Select your project"
echo "3. Click on your PostgreSQL service"
echo "4. Go to the 'Settings' tab"
echo "5. Scroll down to 'Backups' section"
echo "6. Click on the backup you want to restore (usually the most recent one)"
echo "7. Click 'Restore' button"
echo ""
echo "⚠️  IMPORTANT: Railway will create a NEW database instance with the restored data"
echo "   You'll need to update your DATABASE_URL after restore"
echo ""
echo "🔧 After restoring in Railway:"
echo "1. Copy the new DATABASE_URL from the restored database"
echo "2. Update your .env file with the new DATABASE_URL"
echo "3. Update your Railway environment variables with the new URL"
echo "4. Restart your application"
echo ""
echo "📌 Current DATABASE_URL (for reference):"
echo "   ${DATABASE_URL}"
echo ""
echo "Would you like me to help you verify the restore after you complete it?"
read -p "Press Enter when you've completed the restore in Railway..."

echo ""
echo "🔍 Checking new database connection..."
echo "Make sure you've updated your .env file with the new DATABASE_URL"
echo ""
read -p "Have you updated the DATABASE_URL in .env? (yes/no): " updated

if [ "$updated" != "yes" ]; then
    echo "❌ Please update your .env file first"
    exit 1
fi

# Reload environment variables
export $(cat .env | grep -v '^#' | xargs)

echo ""
echo "🔍 Testing new database connection..."
npx prisma db pull

echo ""
echo "✅ Connected to restored database!"
echo ""
echo "📊 Checking restored data..."
node scripts/check-db-state.js

echo ""
echo "🎉 Restore verification complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update Railway environment variables with new DATABASE_URL"
echo "2. Redeploy your application"
echo "3. Test that everything is working correctly"