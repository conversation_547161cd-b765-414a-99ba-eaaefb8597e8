import axios from 'axios';

async function checkAdminStatus() {
  try {
    // First try to login
    console.log('Attempting to login as admin...');
    
    try {
      const loginResponse = await axios.post('https://zencash-production.up.railway.app/api/v1/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      }, {
        headers: {
          'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
        }
      });
      
      console.log('Login successful!');
      console.log('Token:', loginResponse.data.access_token);
      console.log('User:', loginResponse.data.user);
    } catch (loginError: any) {
      console.error('Login failed:', {
        status: loginError.response?.status,
        data: loginError.response?.data,
        message: loginError.message
      });
    }
    
  } catch (error: any) {
    console.error('Error:', error.message);
  }
}

checkAdminStatus();