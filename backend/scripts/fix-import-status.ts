import { PrismaClient, OrderStatus } from '@prisma/client';
import * as dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

async function analyzeAndFixImportStatus() {
  console.log('=== Analyzing Import Status Issue ===\n');

  try {
    // 1. Check if we can create an order with a specific status
    console.log('1. Testing if we can create an order with specific status...');
    
    const testUser = await prisma.user.findFirst({
      where: { role: 'VENDEDOR' },
    });

    if (!testUser || !testUser.tenantId) {
      console.error('No test user found');
      return;
    }

    // Create a test order with Completo status
    const testOrder = await prisma.order.create({
      data: {
        orderNumber: `FIX-TEST-${Date.now()}`,
        customerName: 'Fix Test',
        customerPhone: '11999999999',
        status: OrderStatus.Completo, // <-- Explicitly set
        total: 100,
        sellerId: testUser.id,
        tenantId: testUser.tenantId,
      },
    });

    console.log(`Created test order with status: ${testOrder.status}`);
    console.log(`Expected: Completo, Got: ${testOrder.status}, Match: ${testOrder.status === OrderStatus.Completo ? '✅' : '❌'}`);

    // Clean up
    await prisma.order.delete({ where: { id: testOrder.id } });

    // 2. Analyze the issue
    console.log('\n2. Issue Analysis:');
    
    if (testOrder.status !== OrderStatus.Completo) {
      console.log('❌ The Prisma @default(Analise) is overriding explicit status values!');
      console.log('   This is the root cause of the import issue.');
      console.log('\n   SOLUTION: Remove or modify the @default(Analise) in the Prisma schema.');
    } else {
      console.log('✅ Status can be set explicitly.');
      console.log('   The issue might be in the import mapping or frontend data.');
    }

    // 3. Check existing imported orders
    console.log('\n3. Checking a sample of imported orders...');
    const sampleOrders = await prisma.order.findMany({
      where: {
        orderNumber: { startsWith: 'sal' },
      },
      select: {
        orderNumber: true,
        status: true,
        observation: true,
      },
      take: 5,
    });

    console.log('Sample imported orders:');
    sampleOrders.forEach(order => {
      console.log(`  - ${order.orderNumber}: ${order.status} (obs: ${order.observation?.substring(0, 30) || 'none'})`);
    });

    // 4. Provide fix recommendations
    console.log('\n=== RECOMMENDED FIX ===');
    console.log('1. Update the Prisma schema to remove the default:');
    console.log('   Change: status OrderStatus @default(Analise)');
    console.log('   To:     status OrderStatus');
    console.log('');
    console.log('2. Or, if a default is needed, make it nullable with default:');
    console.log('   status OrderStatus? @default(Analise)');
    console.log('');
    console.log('3. After schema change:');
    console.log('   - Run: npx prisma generate');
    console.log('   - Run: npx prisma db push (or create a migration)');
    console.log('');
    console.log('4. Update the import logic to ensure status is always set:');
    console.log('   - The current mapping logic looks correct');
    console.log('   - Just ensure the frontend sends "situacaoVenda" field');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

analyzeAndFixImportStatus().catch(console.error);