import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function createTestUsers() {
  console.log('🌱 Creating test users for Dashboard Inteligente...\n');

  try {
    const hashedPassword = await bcrypt.hash('senha123', 10);

    // Create supervisor
    const supervisor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Supervisor Teste',
        password: hashedPassword,
        role: Role.SUPERVISOR,
        active: true,
      },
    });
    console.log('✓ Supervisor created:', supervisor.email);

    // Create vendedor
    const vendedor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Vendedor Dashboard',
        password: hashedPassword,
        role: Role.VENDEDOR,
        active: true,
      },
    });
    console.log('✓ Vendedor created:', vendedor.email);

    // Create cobrador
    const cobrador = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Cobrador Dashboard',
        password: hashedPassword,
        role: Role.COBRADOR,
        active: true,
      },
    });
    console.log('✓ Cobrador created:', cobrador.email);

    console.log('\n✅ Test users created successfully!');
    console.log('\nLogin credentials:');
    console.log('- <EMAIL> (senha123)');
    console.log('- <EMAIL> (senha123)');
    console.log('- <EMAIL> (senha123)');

  } catch (error) {
    console.error('❌ Error creating users:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();