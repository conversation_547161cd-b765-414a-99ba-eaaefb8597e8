# CORS Configuration Guide

## Overview
This document explains how CORS (Cross-Origin Resource Sharing) is configured in the ZenCash backend to support various deployment scenarios including Vercel previews, local development, and production.

## Current Configuration

The CORS configuration automatically handles:

1. **Vercel Preview Deployments** - All `*.vercel.app` domains are automatically allowed
2. **Local Development** - localhost ports 3000, 3001, and 5173 are allowed
3. **Production Domains** - Configurable via environment variables
4. **Railway Deployments** - All `*.railway.app` domains are allowed
5. **Server-to-Server** - Requests without origin header are allowed

## Environment Variables

### CORS_ORIGINS
Comma-separated list of allowed origins.

Example:
```env
CORS_ORIGINS=https://app.zencash.com,https://admin.zencash.com
```

### PRODUCTION_DOMAIN
Your main production domain. All subdomains will be automatically allowed.

Example:
```env
PRODUCTION_DOMAIN=zencash.com
```

This will allow:
- https://zencash.com
- https://app.zencash.com
- https://admin.zencash.com
- etc.

## How It Works

The CORS configuration (`src/config/cors.config.ts`) checks origins in this order:

1. **No Origin** - Allowed (for Postman, mobile apps, server-side requests)
2. **Fixed Origins** - Checked against CORS_ORIGINS list
3. **Vercel Previews** - Any `*.vercel.app` domain is allowed
4. **Railway Apps** - Any `*.railway.app` domain is allowed
5. **Production Domain** - Checked if origin contains PRODUCTION_DOMAIN
6. **Default** - Blocked with error message

## Usage Examples

### Local Development
No configuration needed. These are automatically allowed:
- http://localhost:3000
- http://localhost:3001
- http://localhost:5173
- http://127.0.0.1:3000
- http://127.0.0.1:3001
- http://127.0.0.1:5173

### Vercel Preview Deployments
No configuration needed. All preview URLs are automatically allowed:
- https://zencash-abc123.vercel.app
- https://zencash-pr-45.vercel.app
- https://zencash-k74w5trce-grupogls-projects.vercel.app

### Production
Set environment variables in Railway:
```env
NODE_ENV=production
CORS_ORIGINS=https://zencash.com,https://app.zencash.com
PRODUCTION_DOMAIN=zencash.com
```

## Debugging CORS Issues

### Check Logs
The backend logs all CORS decisions:
```
✅ Allowing Vercel preview: https://zencash-k74w5trce-grupogls-projects.vercel.app
⚠️  CORS blocked origin: https://unauthorized-domain.com
```

### Common Issues

1. **"Not allowed by CORS" error**
   - Check if the origin is in CORS_ORIGINS
   - For production, ensure PRODUCTION_DOMAIN is set
   - Check Railway logs for the exact origin being blocked

2. **Credentials not working**
   - Ensure `credentials: true` is set (it is by default)
   - Frontend must use `credentials: 'include'` in fetch requests

3. **Headers missing**
   - The configuration allows common headers
   - Custom headers need to be added to `allowedHeaders`

## Testing CORS

### Using cURL
```bash
# Test with origin header
curl -H "Origin: https://zencash-preview.vercel.app" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://your-api.railway.app/api/v1/health

# Should return CORS headers in response
```

### Browser DevTools
1. Open Network tab
2. Look for OPTIONS preflight requests
3. Check response headers for `Access-Control-Allow-Origin`

## Security Considerations

1. **Never use wildcard (*) in production** - Our configuration doesn't use wildcards
2. **Be specific with origins** - List exact domains in CORS_ORIGINS
3. **Use PRODUCTION_DOMAIN carefully** - It allows all subdomains
4. **Monitor logs** - Check for unexpected origins being blocked or allowed

## Adding New Origins

### For Development/Preview
If it's a development or preview environment pattern:
1. Edit `src/config/cors.config.ts`
2. Add pattern check similar to Vercel/Railway

### For Production
1. Add to CORS_ORIGINS environment variable in Railway
2. Restart the service

Example:
```env
CORS_ORIGINS=https://zencash.com,https://app.zencash.com,https://new-app.zencash.com
```

## Troubleshooting Commands

```bash
# Check current environment variables in Railway
railway variables

# Update CORS origins
railway variables set CORS_ORIGINS=https://app1.com,https://app2.com

# Restart to apply changes
railway restart

# Check logs for CORS decisions
railway logs | grep CORS
```