# Checklist de Validação Final - ZenCash

## ✅ Funcionalidades Core

### Autenticação e Autorização
- [ ] Login funciona corretamente
- [ ] JWT é gerado e validado
- [ ] Roles restringem acesso apropriadamente
- [ ] Logout invalida token (se implementado)
- [ ] Refresh token funciona (se implementado)

### Gestão de Usuários
- [ ] ADMIN pode criar/editar/desativar usuários
- [ ] Senhas são hasheadas corretamente
- [ ] Validação de email único
- [ ] Perfis de acesso funcionam (ADMIN, SUPERVISOR, VENDEDOR, COBRADOR)

### Produtos e Kits
- [ ] CRUD de produtos funciona
- [ ] Variações de produtos com SKU único
- [ ] Controle de estoque atualiza corretamente
- [ ] Kits calculam preço total
- [ ] Busca e filtros funcionam

### Clientes
- [ ] Cadastro com CPF único
- [ ] Múl<PERSON>los endereços por cliente
- [ ] Validação de campos obrigatórios
- [ ] Busca por nome/CPF/telefone

### Pedidos
- [ ] Criação de pedido calcula total corretamente
- [ ] Vendedor é atribuído automaticamente
- [ ] Fluxo de status funciona:
  - [ ] PENDENTE → SEPARACAO (atribui cobrador)
  - [ ] SEPARACAO → ENVIADO
  - [ ] ENVIADO → ENTREGUE/FALHA
  - [ ] Estados de recuperação funcionam
- [ ] Histórico de status é registrado
- [ ] Permissões por role são respeitadas

### Rastreamento
- [ ] Criação de tracking para pedidos ENVIADOS
- [ ] Sincronização com API dos Correios (mock)
- [ ] Alertas são gerados para problemas
- [ ] Status do pedido atualiza com entrega

### Notificações
- [ ] Templates são parseados corretamente
- [ ] Fila processa notificações
- [ ] Retry automático funciona
- [ ] WhatsApp/Email providers (mock)
- [ ] Histórico é mantido

### Relatórios
- [ ] Summary mostra métricas corretas
- [ ] Filtros por data funcionam
- [ ] VENDEDOR vê apenas seus dados
- [ ] COBRADOR vê apenas dados atribuídos
- [ ] Agregações estão corretas
- [ ] Performance é aceitável

### Configurações
- [ ] Dados sensíveis são criptografados
- [ ] Cache carrega no startup
- [ ] ADMIN pode gerenciar todas
- [ ] SUPERVISOR tem acesso limitado
- [ ] Auditoria registra mudanças

## 🔒 Segurança

- [ ] Helmet headers estão ativos
- [ ] CORS está configurado corretamente
- [ ] Rate limiting funciona (100 req/min)
- [ ] Validação de entrada em todas rotas
- [ ] SQL injection prevenido (Prisma)
- [ ] XSS prevenido
- [ ] Senhas têm requisitos mínimos
- [ ] Logs não expõem dados sensíveis
- [ ] .env não está no repositório

## 🚀 Performance e Estabilidade

- [ ] Health check responde rapidamente
- [ ] Aplicação inicia sem erros
- [ ] Conexão com banco é estável
- [ ] Logs são gerados corretamente
- [ ] Memória não tem vazamentos óbvios
- [ ] Queries não têm N+1 problems
- [ ] Paginação funciona em listagens
- [ ] Timeout configurado em requests externos

## 📝 Documentação e Testes

- [ ] Swagger está atualizado e acessível
- [ ] README tem instruções claras
- [ ] .env.example está completo
- [ ] Testes unitários passam
- [ ] Testes E2E passam
- [ ] Scripts de seed funcionam
- [ ] Backup/restore testado

## 🔄 Fluxo Completo de Teste

### 1. Setup Inicial
```bash
npm install
cp .env.example .env
# Configurar DATABASE_URL e JWT_SECRET
npx prisma migrate deploy
npm run seed
```

### 2. Teste de Fluxo de Pedido
1. <NAME_EMAIL>
2. Criar um produto com variações
3. Criar um cliente
4. <NAME_EMAIL>
5. Criar pedido para o cliente
6. <NAME_EMAIL>
7. Mudar status para SEPARACAO
8. Mudar status para ENVIADO
9. Criar tracking com código dos Correios
10. Sincronizar tracking
11. Verificar se notificações foram enviadas
12. Consultar relatórios

### 3. Validação de Permissões
- [ ] Vendedor NÃO pode:
  - [ ] Criar usuários
  - [ ] Alterar pedido para ENVIADO
  - [ ] Ver pedidos de outros vendedores
  - [ ] Acessar configurações
  
- [ ] Cobrador NÃO pode:
  - [ ] Criar produtos
  - [ ] Criar pedidos
  - [ ] Ver relatório de vendas por vendedor
  - [ ] Alterar configurações

## 🚨 Problemas Conhecidos

Lista de issues que precisam ser resolvidas:
1. [ ] _Adicionar problemas encontrados durante validação_

## 📋 Sign-off

- [ ] Todos os itens acima foram verificados
- [ ] Sistema está pronto para produção
- [ ] Backup da base de dados foi realizado
- [ ] Documentação está completa
- [ ] Equipe foi treinada

---

**Data da Validação:** ___________
**Responsável:** ___________
**Versão:** 1.0.0