# Webhook Mapping System Examples

## Overview

The webhook mapping system allows dynamic discovery and mapping of webhook payload fields to entity columns. When a webhook is received, the system:

1. Validates the webhook signature
2. Discovers new payload keys automatically
3. Uses configured mappings to update database records
4. Extracts and stores tracking events

## Setup

### 1. Environment Variables

Add to your `.env` file:

```env
WEBHOOK_SECRET=your-secure-webhook-secret-here
```

### 2. Run Migration

Execute the migration to create the webhook_mappings table:

```bash
# If using TypeORM migrations
npm run typeorm migration:run

# Or execute the SQL directly
psql -U postgres -d zencash -f prisma/migrations/create_webhook_mapping_table.sql
```

## Example Requests

### 1. Initial Webhook - Discovers Keys

Send a webhook to discover payload keys:

```bash
# Calculate signature
PAYLOAD='{"saleId":"VENDA001","cliente":"<PERSON>","telefone":"11999999999","codigoRastreio":"BR123456789BR","statusCorreios":"Objeto postado","valorVenda":150.50,"dataVenda":"2025-07-22T10:30:00Z","eventos":[{"descricao":"Objeto postado","data":"2025-07-22","hora":"10:30:00","local":"São Paulo - SP"}]}'
SECRET="your-secure-webhook-secret-here"
SIGNATURE=$(echo -n "$PAYLOAD" | openssl dgst -sha256 -hmac "$SECRET" | awk '{print $2}')

# Send webhook
curl -X POST http://localhost:3000/webhooks/shipments \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Signature: $SIGNATURE" \
  -d "$PAYLOAD"
```

Response:
```json
{
  "success": true,
  "message": "Webhook received but no mappings configured",
  "discoveredKeys": 7
}
```

### 2. List Discovered Mappings

```bash
curl -X GET http://localhost:3000/api/mappings \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Response:
```json
[
  {
    "id": "uuid-1",
    "payloadKey": "saleId",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "string",
    "sampleValue": "VENDA001",
    "isActive": true
  },
  {
    "id": "uuid-2",
    "payloadKey": "cliente",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "string",
    "sampleValue": "João Silva",
    "isActive": true
  },
  {
    "id": "uuid-3",
    "payloadKey": "telefone",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "string",
    "sampleValue": "11999999999",
    "isActive": true
  },
  {
    "id": "uuid-4",
    "payloadKey": "codigoRastreio",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "string",
    "sampleValue": "BR123456789BR",
    "isActive": true
  },
  {
    "id": "uuid-5",
    "payloadKey": "statusCorreios",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "string",
    "sampleValue": "Objeto postado",
    "isActive": true
  },
  {
    "id": "uuid-6",
    "payloadKey": "valorVenda",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "number",
    "sampleValue": "150.5",
    "isActive": true
  },
  {
    "id": "uuid-7",
    "payloadKey": "dataVenda",
    "entityColumn": null,
    "entityType": "Sale",
    "dataType": "date",
    "sampleValue": "2025-07-22T10:30:00Z",
    "isActive": true
  }
]
```

### 3. Configure Mappings

Map webhook fields to entity columns:

```bash
# Map saleId to orderNumber
curl -X PUT http://localhost:3000/api/mappings/uuid-1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityColumn": "orderNumber",
    "description": "Maps sale ID to order number"
  }'

# Map cliente to customer
curl -X PUT http://localhost:3000/api/mappings/uuid-2 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityColumn": "customer",
    "description": "Maps client name"
  }'

# Map telefone to customerPhone
curl -X PUT http://localhost:3000/api/mappings/uuid-3 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityColumn": "customerPhone",
    "description": "Maps phone number"
  }'

# Map valorVenda to total
curl -X PUT http://localhost:3000/api/mappings/uuid-6 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityColumn": "total",
    "description": "Maps sale value to order total"
  }'
```

### 4. Resend Webhook - Now Mapped

Send the same webhook again to see it process with mappings:

```bash
# Same payload and signature calculation as before
curl -X POST http://localhost:3000/webhooks/shipments \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Signature: $SIGNATURE" \
  -d "$PAYLOAD"
```

Response:
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "saleId": "order-uuid",
  "mappedFields": 4,
  "eventsProcessed": 1
}
```

## Field Mapping Reference

Common webhook fields and their entity column mappings:

| Webhook Field | Entity Column | Type | Description |
|--------------|---------------|------|-------------|
| saleId | orderNumber | string | Unique sale identifier |
| cliente | customer | string | Customer name |
| telefone | customerPhone | string | Customer phone |
| email | customerEmail | string | Customer email |
| valorVenda | total | number | Order total value |
| statusVenda | status | string | Order status |
| codigoRastreio | trackingCode | string | Tracking code |
| statusCorreios | - | string | Tracking status (stored in tracking) |
| dataVenda | createdAt | date | Sale date |
| eventos | - | array | Tracking events array |

## Testing with Different Payloads

### Minimal Payload
```json
{
  "saleId": "VENDA002",
  "codigoRastreio": "BR987654321BR"
}
```

### Full Payload with Events
```json
{
  "saleId": "VENDA003",
  "cliente": "Maria Santos",
  "telefone": "21888888888",
  "email": "<EMAIL>",
  "valorVenda": 299.99,
  "statusVenda": "Aprovado",
  "codigoRastreio": "BR111222333BR",
  "statusCorreios": "Saiu para entrega",
  "dataVenda": "2025-07-22T14:00:00Z",
  "eventos": [
    {
      "descricao": "Saiu para entrega ao destinatário",
      "data": "2025-07-22",
      "hora": "14:00:00",
      "local": "Rio de Janeiro - RJ"
    },
    {
      "descricao": "Objeto em trânsito",
      "data": "2025-07-21",
      "hora": "10:00:00",
      "local": "São Paulo - SP"
    }
  ]
}
```

## Monitoring

Check application logs for webhook processing:

```bash
# View webhook discovery logs
docker logs zencash-backend-1 | grep "Discovered new webhook key"

# View mapping logs
docker logs zencash-backend-1 | grep "Upserted order"

# View error logs
docker logs zencash-backend-1 | grep "Error processing webhook"
```

## Troubleshooting

1. **Invalid signature error**: Ensure WEBHOOK_SECRET matches between sender and receiver
2. **No mappings found**: Check that mappings have entityColumn configured
3. **TypeORM connection error**: Verify database credentials in .env file
4. **Upsert fails**: Ensure at least one identifier field (saleId, orderNumber, trackingCode) is mapped

## Re-enabling Cron Job

To re-enable the Correios polling cron job:

1. Edit `src/correios/correios.tasks.ts`
2. Uncomment the `@Cron` decorator:
   ```typescript
   @Cron('0 */30 * * * *') // Every 30 minutes
   async pollAll() {
   ```
3. Rebuild and redeploy the application