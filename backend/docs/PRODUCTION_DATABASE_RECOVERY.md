# Production Database Recovery Guide

## Current Issue
The production database is in a broken state due to manual deletion of the `_prisma_migrations` table. This causes:
- Prisma errors: "relation _prisma_migrations does not exist"
- Missing tables like "Order"
- Failed health checks
- API never becomes healthy

## Immediate Recovery Steps

### Step 1: Run One-Time Recovery on Railway

1. **Access Railway CLI or Dashboard**
   ```bash
   railway login
   railway link
   ```

2. **Run the recovery script in production**
   ```bash
   railway run ./scripts/fix-production-db.sh
   ```
   
   This script will:
   - Force reset the database schema using `prisma db push --force-reset`
   - Recreate all tables from schema.prisma
   - Seed the admin user

3. **Verify the fix**
   ```bash
   # Check if tables exist
   railway run npx prisma db execute --sql "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
   
   # Test the API health
   curl https://your-api.railway.app/api/v1/health
   ```

### Alternative: Manual Recovery Steps

If the script fails, run these commands manually:

```bash
# 1. Connect to Railway environment
railway run bash

# 2. Force push the schema (WARNING: This will drop and recreate all tables)
npx prisma db push --force-reset

# 3. Generate Prisma Client
npx prisma generate

# 4. Seed the database
npx prisma db seed

# 5. Exit and restart the service
exit
railway restart
```

## Automated Deployment Setup

The following has been configured to prevent this issue in the future:

### 1. Railway Configuration (railway.toml)
```toml
[deploy]
startCommand = "./scripts/railway-deploy.sh && npm run start:prod"
```

### 2. Deployment Script (scripts/railway-deploy.sh)
This script runs on every deployment and:
- Checks database connectivity
- Verifies _prisma_migrations table exists
- Runs migrations or creates schema as needed
- Seeds the database
- Handles errors gracefully

### 3. Package.json Prisma Configuration
```json
"prisma": {
  "seed": "ts-node prisma/seed.ts"
}
```

## Best Practices Going Forward

### DO's:
1. **Always use Prisma commands** for database changes
2. **Use migrations in production**: `npx prisma migrate deploy`
3. **Test migrations locally first**: `npx prisma migrate dev`
4. **Keep migration files** in version control
5. **Use the deployment script** for all deployments

### DON'Ts:
1. **Never manually delete** Prisma tables (_prisma_migrations)
2. **Never use db push** in production (except for recovery)
3. **Never skip migrations** when deploying
4. **Never modify the database** schema manually

## Monitoring

After deployment, always verify:

1. **Health Check**
   ```bash
   curl https://your-api.railway.app/api/v1/health
   ```
   Should return: `{"status":"ok","timestamp":"..."}`

2. **Admin Login**
   - Email: <EMAIL>
   - Password: admin123

3. **Database Tables**
   ```bash
   railway run npx prisma studio
   ```

## Troubleshooting

### Issue: Migrations still failing
```bash
# Reset migrations baseline
railway run npx prisma migrate reset --force --skip-seed
railway run npx prisma migrate deploy
railway run npx prisma db seed
```

### Issue: Tables missing after deployment
```bash
# Force schema sync
railway run npx prisma db push --accept-data-loss
```

### Issue: Seed failing
```bash
# Run seed manually
railway run npx ts-node prisma/seed.ts
```

## CI/CD Pipeline

The automated pipeline now ensures:
1. Build phase: Install deps → Generate Prisma Client → Build app
2. Deploy phase: Run migrations → Seed database → Start app
3. Health check: Verify app is running

This prevents any manual intervention and ensures consistency across deployments.