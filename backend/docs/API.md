# API Documentation - ZenCash

Base URL: `http://localhost:3000`

## Autenticação

Todas as rotas (exceto login e health) requerem autenticação via JWT.

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin@123"
}

Response:
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "uuid",
    "name": "Administrador",
    "email": "<EMAIL>",
    "role": "ADMIN"
  }
}
```

### Me (Current User)
```http
GET /auth/me
Authorization: Bearer {token}

Response:
{
  "id": "uuid",
  "name": "Administrador",
  "email": "<EMAIL>",
  "role": "ADMIN",
  "active": true
}
```

## Users

### List Users
```http
GET /users
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

Query params:
- page: number (default: 1)
- limit: number (default: 10)
- search: string
- role: ADMIN | SUPERVISOR | VENDEDOR | COBRADOR
- active: boolean

Response:
{
  "data": [...],
  "total": 10,
  "page": 1,
  "lastPage": 1
}
```

### Create User
```http
POST /users
Authorization: Bearer {token}
Roles: ADMIN

{
  "name": "João Silva",
  "email": "<EMAIL>",
  "password": "Senha@123",
  "role": "VENDEDOR"
}
```

### Update User
```http
PUT /users/:id
Authorization: Bearer {token}
Roles: ADMIN

{
  "name": "João Silva Updated",
  "active": true
}
```

## Products

### List Products
```http
GET /products
Authorization: Bearer {token}

Query params:
- page: number
- limit: number
- search: string
- active: boolean

Response:
{
  "data": [
    {
      "id": "uuid",
      "name": "Smartphone Galaxy",
      "description": "...",
      "active": true,
      "variations": [
        {
          "id": "uuid",
          "variation": "128GB Preto",
          "price": "1899.90",
          "sku": "GALAXY-128-BLACK",
          "active": true
        }
      ]
    }
  ],
  "total": 20
}
```

### Create Product
```http
POST /products
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

{
  "name": "Novo Produto",
  "description": "Descrição do produto",
  "variations": [
    {
      "variation": "Tamanho M",
      "price": 99.90,
      "sku": "PROD-M-001"
    }
  ]
}
```

### Update Stock
```http
PATCH /products/stock/:variationId
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

{
  "quantity": 50,
  "operation": "add" | "set"
}
```

## Kits

### List Kits
```http
GET /kits
Authorization: Bearer {token}

Response:
{
  "data": [
    {
      "id": "uuid",
      "name": "Kit Home Office",
      "description": "...",
      "active": true,
      "items": [
        {
          "productVariation": {...},
          "quantity": 1
        }
      ],
      "totalPrice": "3499.80"
    }
  ]
}
```

### Create Kit
```http
POST /kits
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

{
  "name": "Kit Gamer",
  "description": "Kit completo",
  "items": [
    {
      "productVariationId": "uuid",
      "quantity": 1
    }
  ]
}
```

## Customers

### List Customers
```http
GET /customers
Authorization: Bearer {token}

Query params:
- search: string (nome, cpf, telefone)
- page: number
- limit: number
```

### Create Customer
```http
POST /customers
Authorization: Bearer {token}

{
  "name": "Maria Silva",
  "cpf": "12345678901",
  "phone": "11999999999",
  "email": "<EMAIL>",
  "addresses": [
    {
      "cep": "01310-100",
      "street": "Av. Paulista",
      "number": "1000",
      "neighborhood": "Bela Vista",
      "city": "São Paulo",
      "state": "SP",
      "main": true
    }
  ]
}
```

## Orders

### List Orders
```http
GET /orders
Authorization: Bearer {token}

Query params:
- status: PENDENTE | SEPARACAO | ENVIADO | ENTREGUE | FALHA | CANCELADO | RECUPERACAO | NEGOCIACAO
- sellerId: uuid
- collectorId: uuid
- customerId: uuid
- startDate: date
- endDate: date
- page: number
- limit: number

Note: VENDEDOR só vê seus pedidos, COBRADOR vê pedidos atribuídos
```

### Create Order
```http
POST /orders
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR, VENDEDOR

{
  "customerId": "uuid",
  "items": [
    {
      "productVariationId": "uuid",
      "productId": "uuid", 
      "productName": "Produto - Variação",
      "quantity": 2,
      "unitPrice": 99.90
    }
  ]
}
```

### Update Order Status
```http
PATCH /orders/:id/status
Authorization: Bearer {token}

{
  "status": "SEPARACAO"
}

Regras:
- VENDEDOR: pode alterar apenas de PENDENTE para CANCELADO
- COBRADOR: pode alterar para qualquer status exceto PENDENTE
- Ao mudar para SEPARACAO+, atribui o cobrador automaticamente
```

### Get Order Details
```http
GET /orders/:id
Authorization: Bearer {token}

Response inclui:
- Dados do pedido
- Cliente
- Itens
- Histórico de status
- Vendedor/Cobrador
- Rastreamento (se houver)
```

## Tracking

### Create Tracking
```http
POST /tracking
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

{
  "orderId": "uuid",
  "code": "BR123456789BR"
}
```

### Get Tracking
```http
GET /tracking/:code
Authorization: Bearer {token}

Response:
{
  "id": "uuid",
  "code": "BR123456789BR",
  "status": "Em trânsito",
  "lastUpdate": "2024-01-15T10:00:00Z",
  "events": [...],
  "isDelivered": false,
  "hasAlert": false,
  "order": {...}
}
```

### Sync Tracking
```http
POST /tracking/:code/sync
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

Response: Tracking atualizado
```

### List Alerts
```http
GET /tracking/alerts
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

Query params:
- startDate: date
- endDate: date
- limit: number
```

## Notifications

### Send Notification
```http
POST /notifications/send
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

{
  "type": "ORDER_CREATED" | "STATUS_CHANGED" | "LOW_STOCK" | "PAYMENT_REMINDER" | "CUSTOM",
  "data": {
    "customerId": "uuid",
    "orderId": "uuid",
    ...contexto
  },
  "channel": "WHATSAPP" | "EMAIL" | "SMS"
}
```

### List Notifications
```http
GET /notifications
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

Query params:
- type: NotificationType
- channel: NotificationChannel
- status: PENDING | PROCESSING | SENT | FAILED
- startDate: date
- endDate: date
```

### Retry Failed
```http
POST /notifications/retry
Authorization: Bearer {token}
Roles: ADMIN

Response:
{
  "retried": 5,
  "success": 3,
  "failed": 2
}
```

## Reports

### Summary Report
```http
GET /reports/summary
Authorization: Bearer {token}

Query params:
- startDate: date
- endDate: date
- sellerId: uuid (para VENDEDOR é automático)
- collectorId: uuid (para COBRADOR é automático)

Response:
{
  "totalOrders": 100,
  "totalRevenue": "25000.00",
  "totalDelivered": 60,
  "totalPending": 20,
  "totalFailed": 5,
  "totalInTransit": 15,
  "averageOrderValue": "250.00",
  "periodStart": "2024-01-01",
  "periodEnd": "2024-01-31"
}
```

### By Collector Report
```http
GET /reports/collector
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR, COBRADOR

Response:
{
  "total": 5,
  "collectors": [
    {
      "collectorId": "uuid",
      "collectorName": "João",
      "totalOrders": 50,
      "totalRevenue": "15000.00",
      "deliveredCount": 30,
      "failedCount": 5,
      "inProgressCount": 15,
      "successRate": 85.71
    }
  ]
}
```

### By Status Report
```http
GET /reports/status
Authorization: Bearer {token}

Response:
{
  "total": 8,
  "statuses": [
    {
      "status": "ENTREGUE",
      "count": 150,
      "totalRevenue": "45000.00",
      "percentage": 60
    }
  ]
}
```

### By Product Report
```http
GET /reports/product
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR, VENDEDOR

Response:
{
  "total": 50,
  "products": [
    {
      "productId": "uuid",
      "productName": "Smartphone Galaxy",
      "totalQuantity": 85,
      "totalRevenue": "161491.50",
      "orderCount": 45,
      "averagePrice": "1899.90"
    }
  ]
}
```

### Tracking Alerts Report
```http
GET /reports/alerts
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR

Response:
{
  "total": 10,
  "alerts": [
    {
      "orderId": "uuid",
      "trackingCode": "BR123456789BR",
      "status": "Destinatário ausente",
      "alertReason": "3 tentativas de entrega",
      "customerName": "Maria Silva",
      "customerPhone": "11999999999",
      "lastUpdate": "2024-01-15T10:00:00Z"
    }
  ]
}
```

## Configuration

### Set Configuration
```http
POST /config
Authorization: Bearer {token}
Roles: ADMIN

{
  "key": "system_preferences",
  "value": {
    "language": "pt-BR",
    "timezone": "America/Sao_Paulo"
  }
}
```

### Get Configuration
```http
GET /config/:key
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR (limitado)

Response:
{
  "key": "system_preferences",
  "value": {...},
  "exists": true
}
```

### List All Configurations
```http
GET /config
Authorization: Bearer {token}
Roles: ADMIN, SUPERVISOR (limitado)

Response:
{
  "configs": {
    "key1": value1,
    "key2": value2
  },
  "total": 10
}
```

### Delete Configuration
```http
DELETE /config/:key
Authorization: Bearer {token}
Roles: ADMIN
```

## Health Check

### Basic Health
```http
GET /health

Response:
{
  "status": "ok",
  "timestamp": "2024-01-15T10:00:00Z",
  "uptime": 12345,
  "database": "connected",
  "version": "1.0.0",
  "node": "v18.0.0"
}
```

### Ready Check
```http
GET /health/ready

Response: 200 OK ou 503 Service Unavailable
```

### Live Check
```http
GET /health/live

Response:
{
  "status": "alive",
  "timestamp": "2024-01-15T10:00:00Z"
}
```

## Status de Resposta

- `200 OK`: Sucesso
- `201 Created`: Recurso criado
- `400 Bad Request`: Erro de validação
- `401 Unauthorized`: Não autenticado
- `403 Forbidden`: Sem permissão
- `404 Not Found`: Recurso não encontrado
- `409 Conflict`: Conflito (ex: email duplicado)
- `429 Too Many Requests`: Rate limit excedido
- `500 Internal Server Error`: Erro do servidor

## Rate Limiting

- Limite padrão: 100 requisições por 60 segundos
- Headers retornados:
  - `X-RateLimit-Limit`: Limite total
  - `X-RateLimit-Remaining`: Requisições restantes
  - `X-RateLimit-Reset`: Timestamp do reset

## Paginação

Parâmetros padrão:
- `page`: Número da página (default: 1)
- `limit`: Itens por página (default: 10, max: 100)

Resposta padrão:
```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "lastPage": 10,
  "limit": 10
}
```