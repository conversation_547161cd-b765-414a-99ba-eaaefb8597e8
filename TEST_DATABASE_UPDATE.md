# Test Database Update Instructions

## 1. Test the Database Update Endpoint

First, let's verify that basic database updates are working. Open your browser or use a tool like <PERSON><PERSON> to test:

```
GET https://zencash-production.up.railway.app/api/v1/orders/************************************/test-update
```

You'll need to be logged in as an admin. This endpoint will:
1. Get the current order
2. Update the customerName to TEST_[timestamp]
3. Verify the update persisted

Expected response if updates are working:
```json
{
  "success": true,
  "original": "Gilson Teste 6",
  "updated": "TEST_1234567890",
  "verified": "TEST_1234567890"
}
```

## 2. Check the Logs

After running the test, check the Railway logs for these entries:

```
[OrdersService.update] Starting transaction...
[OrdersService.update] Update completed in transaction. Order ID: xxx
[OrdersService.update] Verification query - order from DB: {...}
[OrdersService.update] Raw SQL verification: [...]
```

## 3. What to Look For

### If Updates ARE Working:
- The test endpoint returns `success: true`
- The `verified` value matches the `updated` value
- Logs show successful transaction completion

### If Updates ARE NOT Working:
- The test endpoint returns `success: false` with an error
- The `verified` value doesn't match the `updated` value
- Logs show transaction errors or verification mismatches

## 4. Next Steps

### If the test succeeds:
The database is working correctly. The issue might be:
- Frontend not sending the correct data
- A specific field causing problems
- Permission/auth issues

### If the test fails:
We have a database issue. Check:
- Database connection string
- Database permissions
- Transaction logs for specific errors

## 5. Manual Edit Test

After running the test endpoint, try editing an order through the UI and check:
1. Do you see the success alert?
2. Check the Railway logs for the update attempt
3. Refresh the page - do the changes persist?

## Important Log Entries to Share

Please share these specific log entries:
- Any lines containing `[OrdersService.update]`
- Any lines containing `Transaction failed`
- Any lines containing `Verification query`
- Any error messages

This will help us pinpoint exactly where the issue is occurring.