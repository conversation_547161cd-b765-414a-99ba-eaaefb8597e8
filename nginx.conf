server {
    listen 80;
    server_name localhost;
    
    # Root directory and index file
    root /usr/share/nginx/html;
    index index.html;
    
    # Handle React Router and Landing Page
    location / {
        # Check if user has auth token for root path
        if ($request_uri = "/") {
            set $check_auth 1;
        }
        
        if ($cookie_authToken = "") {
            set $check_auth "${check_auth}1";
        }
        
        # If accessing root without auth, show landing page
        if ($check_auth = "11") {
            rewrite ^/$ /landing.html last;
        }
        
        try_files $uri $uri/ /index.html;
    }
    
    # Ensure landing page and assets are accessible
    location = /landing.html {
        root /usr/share/nginx/html;
    }
    
    location /landing-assets/ {
        root /usr/share/nginx/html;
    }
    
    # Cache static assets
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
    
    # Proxy API requests to backend
    location /api {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
