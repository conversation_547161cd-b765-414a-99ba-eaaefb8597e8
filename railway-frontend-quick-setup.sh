#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Railway Frontend Quick Setup${NC}"
echo ""

# Step 1: Check Railway CLI
echo -e "${YELLOW}Step 1: Checking Railway CLI...${NC}"
if command -v railway &> /dev/null; then
    echo -e "${GREEN}✅ Railway CLI installed${NC}"
else
    echo -e "${RED}❌ Railway CLI not found. Please install it first.${NC}"
    exit 1
fi

# Step 2: Login check
echo -e "${YELLOW}Step 2: Checking Railway login...${NC}"
if railway whoami &> /dev/null; then
    echo -e "${GREEN}✅ Logged in as: $(railway whoami)${NC}"
else
    echo -e "${YELLOW}Please login to Railway:${NC}"
    railway login
fi

# Step 3: Navigate to project
cd /Users/<USER>/zencash
echo -e "${YELLOW}Step 3: Linking to Railway project...${NC}"
railway link

# Step 4: Create frontend service
echo -e "${YELLOW}Step 4: Creating frontend service...${NC}"
railway service create zencash-frontend 2>/dev/null || echo "Service may already exist"

# Step 5: Set all environment variables at once
echo -e "${YELLOW}Step 5: Setting environment variables...${NC}"
cat > /tmp/railway-env.txt << 'EOF'
REACT_APP_API_URL=https://zencash-production.up.railway.app/api/v1
REACT_APP_TENANT_ID=************************************
NODE_OPTIONS=--max-old-space-size=16384
GENERATE_SOURCEMAP=false
CI=false
REACT_APP_ENV=production
REACT_APP_ENABLE_DEBUG=false
EOF

# Apply variables
while IFS='=' read -r key value; do
    railway vars set "$key=$value" --service zencash-frontend
done < /tmp/railway-env.txt

rm /tmp/railway-env.txt

# Step 6: Deploy
echo -e "${YELLOW}Step 6: Deploying frontend...${NC}"
cd frontend
railway up --service zencash-frontend

# Step 7: Get URL
echo ""
echo -e "${GREEN}🎉 Deployment initiated!${NC}"
echo -e "${YELLOW}Getting deployment URL...${NC}"
railway open --service zencash-frontend

echo ""
echo -e "${GREEN}✅ Setup complete!${NC}"
echo ""
echo "📝 Important notes:"
echo "1. The build will take 5-10 minutes (check Railway dashboard)"
echo "2. Your frontend URL will be shown in the Railway dashboard"
echo "3. Update backend CORS to include the new frontend URL"
echo ""
echo -e "${YELLOW}Backend CORS Update needed in main.ts:${NC}"
echo "Add your Railway frontend URL to allowedOrigins array"