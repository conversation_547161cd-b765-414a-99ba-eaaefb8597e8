#!/bin/bash

echo "🚀 Deploying Frontend to Railway..."
echo ""

# Check if logged in
if ! railway whoami > /dev/null 2>&1; then
    echo "❌ Not logged in to Railway. Please run: railway login"
    exit 1
fi

# Check if in project root
if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
    echo "❌ Must be run from project root (zencash)"
    exit 1
fi

# Link to project if not already linked
if [ ! -f ".railway/config.json" ]; then
    echo "🔗 Linking to Railway project..."
    railway link
fi

# Create frontend service if it doesn't exist
echo "📦 Creating/updating frontend service..."
railway service create frontend 2>/dev/null || echo "Frontend service already exists"

# Deploy frontend
echo "🏗️  Deploying frontend (this may take 5-10 minutes)..."
cd frontend
railway up --service frontend

# Set environment variables
echo "🔧 Setting environment variables..."
railway vars set \
    REACT_APP_API_URL="https://zencash-backend.railway.app/api/v1" \
    REACT_APP_TENANT_ID="************************************" \
    NODE_OPTIONS="--max-old-space-size=16384" \
    GENERATE_SOURCEMAP="false" \
    CI="false" \
    REACT_APP_ENV="production" \
    REACT_APP_ENABLE_DEBUG="false" \
    --service frontend

# Get the deployment URL
echo ""
echo "✅ Deployment complete!"
echo "🌐 Getting deployment URL..."
railway domain --service frontend

echo ""
echo "📝 Next steps:"
echo "1. Wait for the build to complete in Railway dashboard"
echo "2. Access your frontend at the provided URL"
echo "3. Update CORS settings in backend if needed"