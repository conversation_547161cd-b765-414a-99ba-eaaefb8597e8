# Sidebar Status Count Fix - Análise Status

## Problem
The sidebar was showing 0 for "Análise" status even though there were 5 orders with that status in the backend.

## Root Cause
The issue was related to accent-sensitive string comparison:
- Backend sends status as "Analise" (without accent)
- Frontend maps it to "Anális<PERSON>" (with accent) in OrderService
- Sidebar was comparing "análise" (lowercase with accent) to "analise" (lowercase without accent)
- These strings are NOT equal due to the accented character

## Solution
1. Created a string normalization utility that removes accents before comparison
2. Updated all status comparison logic in:
   - PedidosPage.tsx - Fixed filtering logic
   - ModernSidebar.tsx - Fixed status counting
   - Sidebar.tsx - Fixed status counting

## Code Changes

### String Normalization
```javascript
const normalizeString = (str: string) => {
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .toLowerCase()
    .replace(/\s+/g, ''); // Remove spaces
};
```

This converts "Análise" → "analise" and "Separa<PERSON>" → "separacao" for proper comparison.

## Testing
The sidebar should now correctly show the count of orders in each status, including "Análise".