# Anti-Fraud Status Transition Fix

## Problem
Orders approved in the anti-fraud system were not transitioning to "Separação" status as expected.

## Investigation
1. Verified the anti-fraud approval endpoint correctly sets status to `OrderStatus.Separacao`
2. Found that status transitions are validated in OrderService with `isValidStatusTransition`
3. Confirmed "<PERSON><PERSON><PERSON>" → "Separacao" is a valid transition
4. Added logging to track the status update process

## Solution Implemented

### 1. Backend Changes (antifraud.service.ts)
- Added detailed logging to track status changes during review
- Added creation of status history entry for audit trail
- Ensured transaction properly updates both order status and history

### 2. Frontend Changes (RiskBasedReviewQueue.tsx)
- Added 1-second delay after approval to ensure backend processing completes
- Added explicit event dispatch to update sidebar counts
- Ensures both anti-fraud queue and orders context are refreshed

### 3. Additional Debugging (OrderService.ts)
- Added console logging to track order status from API responses
- Helps identify if status is being properly received from backend

## Code Changes

### Backend - Status History Creation
```typescript
// Create status history entry if status changed
if (review.decision === 'APPROVE_ORDER' || review.decision === 'DENY_ORDER') {
  await tx.orderStatusHistory.create({
    data: {
      orderId: orderId,
      previousStatus: order.status,
      newStatus: review.decision === 'APPROVE_ORDER' ? OrderStatus.Separacao : OrderStatus.Cancelado,
      changedById: review.reviewerId,
    }
  });
}
```

### Frontend - Refresh with Delay
```typescript
// Add a small delay to ensure backend has processed the update
await new Promise(resolve => setTimeout(resolve, 1000));

// Refresh both anti-fraud queue and orders context (for sidebar)
await Promise.all([
  loadOrdersForReview(),
  fetchOrders()
]);

// Dispatch event to update sidebar
window.dispatchEvent(new Event('orders-updated'));
```

## Testing
1. Approve an order in the anti-fraud system
2. Check console logs for status transition
3. Verify order appears in "Separação" status in the sidebar
4. Check order details to confirm status change