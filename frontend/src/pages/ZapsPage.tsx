import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ZapService from '../services/ZapService';
import { Zap, ZapStatus, CreateZapDto, UpdateZapDto } from '../types/Zap';
import useUserPermissions from '../hooks/useUserPermissions';

const ZapsPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAdmin, isSupervisor } = useUserPermissions();
  const [zaps, setZaps] = useState<Zap[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingZap, setEditingZap] = useState<Zap | null>(null);
  const [formData, setFormData] = useState<CreateZapDto>({
    name: '',
    phoneNumber: '',
    status: ZapStatus.Ativo,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [retroactiveLoading, setRetroactiveLoading] = useState(false);

  useEffect(() => {
    loadZaps();
  }, []);

  const loadZaps = async () => {
    try {
      setLoading(true);
      const data = await ZapService.getAll();
      setZaps(data);
    } catch (error: any) {
      console.error('Error loading zaps:', error);
      showSnackbar('Erro ao carregar Zaps', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (zap?: Zap) => {
    if (zap) {
      setEditingZap(zap);
      setFormData({
        name: zap.name,
        phoneNumber: zap.phoneNumber || '',
        status: zap.status,
      });
    } else {
      setEditingZap(null);
      setFormData({
        name: '',
        phoneNumber: '',
        status: ZapStatus.Ativo,
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingZap(null);
    setFormData({
      name: '',
      status: ZapStatus.Ativo,
    });
  };

  const handleSave = async () => {
    try {
      if (editingZap) {
        const updateData: UpdateZapDto = {};
        if (formData.name !== editingZap.name) updateData.name = formData.name;
        if (formData.status !== editingZap.status) updateData.status = formData.status;
        
        await ZapService.update(editingZap.id, updateData);
        showSnackbar('Zap atualizado com sucesso', 'success');
      } else {
        await ZapService.create(formData);
        showSnackbar('Zap criado com sucesso', 'success');
      }
      handleCloseDialog();
      loadZaps();
    } catch (error: any) {
      const message = error.response?.data?.message || 'Erro ao salvar Zap';
      showSnackbar(message, 'error');
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Tem certeza que deseja excluir este Zap?')) return;
    
    try {
      await ZapService.delete(id);
      showSnackbar('Zap excluído com sucesso', 'success');
      loadZaps();
    } catch (error: any) {
      const message = error.response?.data?.message || 'Erro ao excluir Zap';
      showSnackbar(message, 'error');
    }
  };

  const handleRetroactiveUpdate = async () => {
    if (!window.confirm('Isso atualizará todos os pedidos antigos com informações de Zap. Continuar?')) return;
    
    try {
      setRetroactiveLoading(true);
      const result = await ZapService.updateRetroactively();
      showSnackbar(
        `Atualização concluída: ${result.updated} pedidos atualizados, ${result.failed} falharam`,
        result.failed > 0 ? 'error' : 'success'
      );
      loadZaps();
    } catch (error: any) {
      showSnackbar('Erro ao atualizar pedidos retroativamente', 'error');
    } finally {
      setRetroactiveLoading(false);
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbar({ open: true, message, severity });
  };

  const getStatusColor = (status: ZapStatus) => {
    const colors: Record<ZapStatus, string> = {
      [ZapStatus.Ativo]: '#4caf50',
      [ZapStatus.Aquecendo]: '#ff9800',
      [ZapStatus.Pronto]: '#2196f3',
      [ZapStatus.Bloqueado]: '#f44336',
      [ZapStatus.EmAnalise]: '#9c27b0',
      [ZapStatus.Recuperado]: '#00bcd4',
      [ZapStatus.StandBy]: '#607d8b',
    };
    return colors[status] || '#757575';
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('pt-BR');
  };

  if (!isAdmin && !isSupervisor) {
    return (
      <Container>
        <Alert severity="error">Você não tem permissão para acessar esta página</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            Gerenciamento de Zaps
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {isAdmin && (
              <Button
                variant="outlined"
                startIcon={retroactiveLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
                onClick={handleRetroactiveUpdate}
                disabled={retroactiveLoading}
              >
                Atualizar Retroativamente
              </Button>
            )}
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              disabled={!isAdmin}
            >
              Novo Zap
            </Button>
          </Box>
        </Box>
      </Box>

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Telefone</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Pedidos</TableCell>
                <TableCell>Criado em</TableCell>
                <TableCell align="right">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {zaps.map((zap) => (
                <TableRow key={zap.id}>
                  <TableCell>
                    <Typography variant="body1" fontWeight={500}>
                      {zap.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="textSecondary">
                      {zap.phoneNumber || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={zap.status}
                      size="small"
                      style={{
                        backgroundColor: getStatusColor(zap.status),
                        color: '#fff',
                      }}
                    />
                  </TableCell>
                  <TableCell align="center">
                    {zap._count?.orders || 0}
                  </TableCell>
                  <TableCell>{formatDate(zap.createdAt)}</TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      onClick={() => navigate(`/dashboard/zap-reports/${zap.id}`)}
                      title="Ver relatórios"
                    >
                      <AnalyticsIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(zap)}
                      disabled={!isAdmin}
                      title="Editar"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(zap.id)}
                      disabled={!isAdmin || (zap._count?.orders || 0) > 0}
                      title="Excluir"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {zaps.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Nenhum Zap cadastrado
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Create/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingZap ? 'Editar Zap' : 'Novo Zap'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Nome"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              required
              helperText="Ex: zap10, zap20, etc."
            />
            <TextField
              label="Número do WhatsApp"
              value={formData.phoneNumber || ''}
              onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
              fullWidth
              helperText="Opcional - apenas para referência"
            />
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as ZapStatus })}
                label="Status"
              >
                {Object.values(ZapStatus).map((status) => (
                  <MenuItem key={status} value={status}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box
                        sx={{
                          width: 10,
                          height: 10,
                          borderRadius: '50%',
                          backgroundColor: getStatusColor(status),
                        }}
                      />
                      {status}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={!formData.name}
          >
            {editingZap ? 'Salvar' : 'Criar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ZapsPage;