import React, { useState } from 'react';
import { Box, Select, MenuItem, FormControl, Typography, Button } from '@mui/material';

export default function TestDropdown() {
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [value3, setValue3] = useState('');

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Test Dropdown Page</Typography>
      
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6">Test 1: Basic Select</Typography>
        <FormControl fullWidth>
          <Select
            value={value1}
            onChange={(e) => {
              console.log('Test 1 selected:', e.target.value);
              setValue1(e.target.value);
            }}
          >
            <MenuItem value="">None</MenuItem>
            <MenuItem value="status">Status</MenuItem>
            <MenuItem value="customerName">Customer Name</MenuItem>
          </Select>
        </FormControl>
        <Typography>Selected: {value1}</Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h6">Test 2: Select with Categories (like webhook mappings)</Typography>
        <FormControl fullWidth>
          <Select
            value={value2}
            onChange={(e) => {
              console.log('Test 2 selected:', e.target.value);
              setValue2(e.target.value);
            }}
          >
            <MenuItem value="">None</MenuItem>
            <MenuItem disabled>
              <strong>Order Fields</strong>
            </MenuItem>
            <MenuItem value="status">Status</MenuItem>
            <MenuItem value="customerName">Customer Name</MenuItem>
            <MenuItem disabled>
              <strong>Address Fields</strong>
            </MenuItem>
            <MenuItem value="address.street">Street</MenuItem>
            <MenuItem value="address.city">City</MenuItem>
          </Select>
        </FormControl>
        <Typography>Selected: {value2}</Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="h6">Test 3: Exactly like webhook mappings</Typography>
        <FormControl size="small" fullWidth>
          <Select
            value={value3}
            onChange={(e) => {
              console.log('Test 3 selected:', e.target.value);
              setValue3(e.target.value);
            }}
            displayEmpty
            sx={{ bgcolor: 'background.paper' }}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: 400,
                },
              },
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
              transformOrigin: {
                vertical: 'top',
                horizontal: 'left',
              },
            }}
          >
            <MenuItem value="">
              <em>Selecione uma coluna</em>
            </MenuItem>
            <MenuItem disabled>
              <strong>📋 Campos do Pedido</strong>
            </MenuItem>
            <MenuItem value="status">Status</MenuItem>
            <MenuItem value="customerName">Nome do Cliente</MenuItem>
          </Select>
        </FormControl>
        <Typography>Selected: {value3}</Typography>
      </Box>

      <Button 
        variant="contained" 
        onClick={() => {
          console.log('All values:', { value1, value2, value3 });
          alert(`Values: ${value1}, ${value2}, ${value3}`);
        }}
      >
        Log All Values
      </Button>
    </Box>
  );
}