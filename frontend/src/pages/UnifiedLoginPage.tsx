import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  TextField,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Link,
  IconButton,
  InputAdornment
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import UnifiedAuthService from '../services/UnifiedAuthService';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { debugLogin } from '../utils/debug-login';

const UnifiedLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Clear any existing auth data on page load
  useEffect(() => {
    // Clear all potential auth storage keys to ensure clean state
    const authKeys = [
      'unified_auth_tokens',
      'unified_user_info', 
      'unified_token_expiry',
      'auth_tokens',
      'current_user',
      'token_expiry',
      'user_info',
      'unified_users',
      'unified_passwords'
    ];
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log('Auth storage cleared for fresh login');
    
    // Run debug on page load
    debugLogin();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Limpar mensagens de erro quando o usuário digita
    if (error) setError('');
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Função auxiliar para navegar após o login
  const navigateAfterLogin = () => {
    // Force a storage event to notify other contexts
    window.dispatchEvent(new Event('storage'));
    
    // Navigate immediately - the OrderDataContext will handle fetching
    navigate('/dashboard');
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError('');
    setLoading(true);

    try {
      console.log('Tentando login com:', formData.email);

      // Usar o UnifiedAuthService para login
      await UnifiedAuthService.login({
        email: formData.email,
        password: formData.password
      });

      console.log('Login bem-sucedido!');
      setSuccessMessage('Login bem-sucedido! Redirecionando...');

      // Navegar para o dashboard após login bem-sucedido
      navigateAfterLogin();
    } catch (err: any) {
      console.error('Erro no login:', err);
      
      // Run debug to see what's happening
      await debugLogin();
      
      // More detailed error handling
      let errorMessage = 'Erro no servidor. Por favor, tente novamente.';
      
      if (err.response) {
        // The request was made and the server responded with a status code
        console.error('Error response:', err.response);
        errorMessage = err.response.data?.message || err.response.data?.error || errorMessage;
      } else if (err.request) {
        // The request was made but no response was received
        console.error('No response received:', err.request);
        errorMessage = 'Servidor não está respondendo. Verifique sua conexão.';
      } else {
        // Something happened in setting up the request
        errorMessage = err.message || errorMessage;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (event: React.FormEvent) => {
    event.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Simulate password reset request
      setSuccessMessage('Se o email existir no sistema, você receberá instruções para redefinir sua senha.');
      setShowForgotPassword(false);
    } catch (err: any) {
      console.error('Erro ao redefinir senha:', err);
      setError('Erro ao processar a solicitação. Por favor, tente novamente.');
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    const autoLogin = async () => {
      setLoading(true);
      try {
        await UnifiedAuthService.login({
          email: '<EMAIL>',
          password: 'admin123'
        });
        navigateAfterLogin();
      } catch (err) {
        setError('Falha no login automático. Verifique as credenciais e o servidor.');
      } finally {
        setLoading(false);
      }
    };

    // Disable auto-login for now
    // if (process.env.NODE_ENV === 'development') {
    //   autoLogin();
    // }
  }, []);

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%', borderRadius: '12px' }}>
          <Typography component="h1" variant="h5" align="center" gutterBottom>
            Sistema de Cobrança Inteligente
          </Typography>

          {!showForgotPassword ? (
            <>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {successMessage && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {successMessage}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Email"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={formData.email}
                  onChange={handleChange}
                  sx={{ mb: 2 }}
                />
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Senha"
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={handleChange}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  sx={{ mt: 3, mb: 2 }}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Entrar'}
                </Button>
                <Box sx={{ textAlign: 'center' }}>
                  <Link
                    component="button"
                    variant="body2"
                    onClick={() => setShowForgotPassword(true)}
                  >
                    Esqueceu sua senha?
                  </Link>
                </Box>
              </Box>
            </>
          ) : (
            <>
              <Typography variant="h6" gutterBottom>
                Redefinir Senha
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {successMessage && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {successMessage}
                </Alert>
              )}

              <Box component="form" onSubmit={handleForgotPassword} sx={{ mt: 1 }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="resetEmail"
                  label="Email"
                  name="resetEmail"
                  autoComplete="email"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                />
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  sx={{ mt: 3, mb: 2 }}
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Enviar Link de Redefinição'}
                </Button>
                <Box sx={{ textAlign: 'center' }}>
                  <Link
                    component="button"
                    variant="body2"
                    onClick={() => setShowForgotPassword(false)}
                  >
                    Voltar para o login
                  </Link>
                </Box>
              </Box>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default UnifiedLoginPage;