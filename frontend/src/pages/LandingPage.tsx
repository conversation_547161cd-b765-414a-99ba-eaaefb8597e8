import React, { useEffect } from 'react';
import UnifiedAuthService from '../services/UnifiedAuthService';

const LandingPage: React.FC = () => {
  useEffect(() => {
    // If user is authenticated, redirect to dashboard
    if (UnifiedAuthService.isAuthenticated()) {
      window.location.href = '/dashboard';
    } else {
      // Otherwise, redirect to the static landing page
      window.location.href = '/landing.html';
    }
  }, []);

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      Redirecting...
    </div>
  );
};

export default LandingPage;