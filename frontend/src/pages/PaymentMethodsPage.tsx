import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';
import api from '../services/api';

interface PaymentMethod {
  id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const PaymentMethodsPage: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    isActive: true,
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await api.get('/payment-methods');
      setPaymentMethods(response.data);
    } catch (err: any) {
      setError('Erro ao carregar métodos de pagamento');
      console.error('Error fetching payment methods:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (method?: PaymentMethod) => {
    if (method) {
      setEditingMethod(method);
      setFormData({
        name: method.name,
        isActive: method.isActive,
      });
    } else {
      setEditingMethod(null);
      setFormData({
        name: '',
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingMethod(null);
    setFormData({
      name: '',
      isActive: true,
    });
  };

  const handleSubmit = async () => {
    try {
      if (editingMethod) {
        await api.patch(`/payment-methods/${editingMethod.id}`, formData);
        setSuccess('Método de pagamento atualizado com sucesso');
      } else {
        await api.post('/payment-methods', formData);
        setSuccess('Método de pagamento criado com sucesso');
      }
      handleCloseDialog();
      fetchPaymentMethods();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao salvar método de pagamento');
    }
  };

  const handleToggleActive = async (method: PaymentMethod) => {
    try {
      await api.patch(`/payment-methods/${method.id}`, {
        isActive: !method.isActive,
      });
      setSuccess('Status atualizado com sucesso');
      fetchPaymentMethods();
    } catch (err: any) {
      setError('Erro ao atualizar status');
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir este método de pagamento?')) {
      try {
        await api.delete(`/payment-methods/${id}`);
        setSuccess('Método de pagamento excluído com sucesso');
        fetchPaymentMethods();
      } catch (err: any) {
        setError(err.response?.data?.message || 'Erro ao excluir método de pagamento');
      }
    }
  };

  const handleSeedDefaults = async () => {
    try {
      await api.post('/payment-methods/seed-defaults');
      setSuccess('Métodos de pagamento padrão criados com sucesso');
      fetchPaymentMethods();
    } catch (err: any) {
      setError('Erro ao criar métodos padrão');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Métodos de Pagamento
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {paymentMethods.length === 0 && (
            <Button
              variant="outlined"
              startIcon={<PaymentIcon />}
              onClick={handleSeedDefaults}
            >
              Criar Métodos Padrão
            </Button>
          )}
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Novo Método
          </Button>
        </Box>
      </Box>

      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell align="center">Status</TableCell>
                <TableCell>Criado em</TableCell>
                <TableCell>Atualizado em</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    Carregando...
                  </TableCell>
                </TableRow>
              ) : paymentMethods.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    Nenhum método de pagamento cadastrado
                  </TableCell>
                </TableRow>
              ) : (
                paymentMethods.map((method) => (
                  <TableRow key={method.id} hover>
                    <TableCell>{method.name}</TableCell>
                    <TableCell align="center">
                      <Switch
                        checked={method.isActive}
                        onChange={() => handleToggleActive(method)}
                        color="primary"
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(method.createdAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell>
                      {new Date(method.updatedAt).toLocaleDateString('pt-BR')}
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(method)}
                        color="primary"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(method.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingMethod ? 'Editar Método de Pagamento' : 'Novo Método de Pagamento'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Nome"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
              required
            />
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="Ativo"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.name.trim()}
          >
            {editingMethod ? 'Atualizar' : 'Criar'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setError('')} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess('')}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess('')} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PaymentMethodsPage;