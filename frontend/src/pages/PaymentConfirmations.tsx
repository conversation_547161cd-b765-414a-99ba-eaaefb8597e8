import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  TablePagination,
  Tooltip,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as DenyIcon,
  Visibility as ViewIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useOrderData } from '../contexts/OrderDataContext';
import { Order } from '../types/Order';
import OrderService from '../services/OrderService';

const PaymentConfirmations: React.FC = () => {
  const { updateOrder } = useOrderData();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [pendingOrders, setPendingOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    orderId: string;
    approve: boolean;
    order: Order | null;
  }>({ open: false, orderId: '', approve: false, order: null });
  const [denialReason, setDenialReason] = useState('');
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  useEffect(() => {
    fetchPendingPayments();
  }, []);

  const fetchPendingPayments = async () => {
    try {
      setLoading(true);
      const response = await OrderService.getPendingPaymentConfirmations();
      setPendingOrders(response);
    } catch (err) {
      console.error('Error fetching pending payments:', err);
      setError('Erro ao carregar pagamentos pendentes');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirm = (order: Order, approve: boolean) => {
    setConfirmDialog({ open: true, orderId: order.id, approve, order });
    if (!approve) {
      setDenialReason('');
    }
  };

  const handleConfirmSubmit = async () => {
    if (!confirmDialog.approve && !denialReason.trim()) {
      setError('Por favor, forneça um motivo para a negação');
      return;
    }

    try {
      setLoading(true);
      const updatedOrder = await OrderService.confirmPayment(
        confirmDialog.orderId,
        confirmDialog.approve,
        confirmDialog.approve ? undefined : denialReason
      );

      // Update the order in context
      updateOrder(updatedOrder.id, updatedOrder);

      // Remove from pending list
      setPendingOrders(prev => prev.filter(o => o.id !== confirmDialog.orderId));

      // Close dialog
      setConfirmDialog({ open: false, orderId: '', approve: false, order: null });
      setDenialReason('');
      setError('');
    } catch (err) {
      console.error('Error confirming payment:', err);
      setError('Erro ao confirmar pagamento');
    } finally {
      setLoading(false);
    }
  };

  const handleViewOrder = (order: Order) => {
    navigate(`/pedidos?orderId=${order.id}`);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (date: string | Date | null | undefined) => {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR');
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  if (loading && pendingOrders.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Confirmação de Pagamentos
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Pagamentos que aguardam aprovação do supervisor
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {pendingOrders.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Nenhum pagamento pendente de confirmação
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Pedido</TableCell>
                <TableCell>Cliente</TableCell>
                <TableCell>Vendedor</TableCell>
                <TableCell>Cobrador</TableCell>
                <TableCell align="right">Valor Total</TableCell>
                <TableCell align="right">Valor Recebido</TableCell>
                <TableCell>Data Pagamento</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {pendingOrders
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      #{order.orderNumber || order.id.slice(0, 8)}
                    </Typography>
                  </TableCell>
                  <TableCell>{order.customerName}</TableCell>
                  <TableCell>{order.sellerName || '-'}</TableCell>
                  <TableCell>{order.collectorName || '-'}</TableCell>
                  <TableCell align="right">
                    {formatCurrency(order.total)}
                  </TableCell>
                  <TableCell align="right">
                    <Chip
                      label={formatCurrency(order.paymentReceivedAmount || 0)}
                      color={order.paymentReceivedAmount === order.total ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {formatDate(order.paymentReceivedDate)}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={order.status}
                      size="small"
                      color={order.status === 'Completo' ? 'success' : 'default'}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                      <Tooltip title="Visualizar Pedido">
                        <IconButton
                          size="small"
                          onClick={() => handleViewOrder(order)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Aprovar Pagamento">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleConfirm(order, true)}
                        >
                          <ApproveIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Negar Pagamento">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleConfirm(order, false)}
                        >
                          <DenyIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={pendingOrders.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="Linhas por página:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
          />
        </TableContainer>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, orderId: '', approve: false, order: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {confirmDialog.approve ? 'Aprovar Pagamento' : 'Negar Pagamento'}
        </DialogTitle>
        <DialogContent>
          {confirmDialog.order && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Pedido #{confirmDialog.order.orderNumber || confirmDialog.order.id.slice(0, 8)}
              </Typography>
              <Typography variant="body1" gutterBottom>
                Cliente: <strong>{confirmDialog.order.customerName}</strong>
              </Typography>
              <Typography variant="body1" gutterBottom>
                Valor Total: <strong>{formatCurrency(confirmDialog.order.total)}</strong>
              </Typography>
              <Typography variant="body1" gutterBottom>
                Valor Recebido: <strong>{formatCurrency(confirmDialog.order.paymentReceivedAmount || 0)}</strong>
              </Typography>
            </Box>
          )}

          {confirmDialog.approve ? (
            <Alert severity="info" icon={<ApproveIcon />}>
              Tem certeza que deseja aprovar este pagamento? O pedido manterá seu status atual e as comissões serão calculadas.
            </Alert>
          ) : (
            <>
              <Alert severity="warning" icon={<WarningIcon />} sx={{ mb: 2 }}>
                Ao negar este pagamento, o pedido será movido para o status "Confirmar Pagamento" e as comissões serão recalculadas.
              </Alert>
              <TextField
                fullWidth
                label="Motivo da Negação"
                value={denialReason}
                onChange={(e) => setDenialReason(e.target.value)}
                multiline
                rows={3}
                required
                error={!denialReason.trim() && confirmDialog.open}
                helperText="Por favor, explique o motivo da negação do pagamento"
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialog({ open: false, orderId: '', approve: false, order: null })}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirmSubmit}
            variant="contained"
            color={confirmDialog.approve ? 'success' : 'error'}
            disabled={loading || (!confirmDialog.approve && !denialReason.trim())}
          >
            {loading ? <CircularProgress size={20} /> : (confirmDialog.approve ? 'Aprovar' : 'Negar')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentConfirmations;