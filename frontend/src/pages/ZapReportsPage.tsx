import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  ShoppingCart as ShoppingCartIcon,
  AttachMoney as MoneyIcon,
  WhatsApp as WhatsAppIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { ptBR } from 'date-fns/locale';
import ZapService from '../services/ZapService';
import OrderService from '../services/OrderService';
import { Zap, ZapStats } from '../types/Zap';
import { Order } from '../types/Order';
import useUserPermissions from '../hooks/useUserPermissions';
// Simple StatusChip component
const StatusChip: React.FC<{ status: string }> = ({ status }) => {
  const getStatusColor = (status: string) => {
    const normalizedStatus = status.toLowerCase().replace(/\s+/g, '');
    const colors: Record<string, string> = {
      'completo': '#4caf50',
      'analise': '#ff9800',
      'separacao': '#2196f3',
      'transito': '#9c27b0',
      'pagamentopendente': '#ffc107',
      'cancelado': '#f44336',
      'frustrado': '#795548',
      'negociacao': '#3f51b5',
      'promessa': '#00bcd4',
    };
    return colors[normalizedStatus] || '#757575';
  };

  return (
    <Chip
      label={status}
      size="small"
      sx={{
        bgcolor: getStatusColor(status),
        color: '#fff',
        fontWeight: 500,
      }}
    />
  );
};

const ZapReportsPage: React.FC = () => {
  const { zapId } = useParams<{ zapId?: string }>();
  const navigate = useNavigate();
  const { isAdmin, isSupervisor } = useUserPermissions();
  
  const [zaps, setZaps] = useState<Zap[]>([]);
  const [selectedZap, setSelectedZap] = useState<string>(zapId || '');
  const [zapDetails, setZapDetails] = useState<Zap | null>(null);
  const [zapStats, setZapStats] = useState<ZapStats | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState<Date | null>(
    new Date(new Date().setDate(new Date().getDate() - 30))
  );
  const [endDate, setEndDate] = useState<Date | null>(new Date());

  useEffect(() => {
    loadZaps();
  }, []);

  useEffect(() => {
    if (selectedZap) {
      loadZapData();
    }
  }, [selectedZap, startDate, endDate]);

  const loadZaps = async () => {
    try {
      const data = await ZapService.getAll();
      setZaps(data);
      if (!selectedZap && data.length > 0) {
        setSelectedZap(data[0].id);
      }
    } catch (error: any) {
      console.error('Error loading zaps:', error);
    }
  };

  const loadZapData = async () => {
    if (!selectedZap) return;

    try {
      setLoading(true);
      
      // Load Zap details
      const zap = await ZapService.getOne(selectedZap);
      setZapDetails(zap);

      // Load Zap statistics
      const stats = await ZapService.getStats(
        selectedZap,
        startDate || undefined,
        endDate || undefined
      );
      setZapStats(stats);

      // Load orders for this Zap
      const allOrders = await OrderService.getOrders();
      const zapOrders = allOrders.filter(order => order.zapSourceId === selectedZap);
      
      // Filter by date if dates are set
      const filteredOrders = zapOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        if (startDate && orderDate < startDate) return false;
        if (endDate && orderDate > endDate) return false;
        return true;
      });

      setOrders(filteredOrders);
    } catch (error: any) {
      console.error('Error loading zap data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'Ativo': '#4caf50',
      'Aquecendo': '#ff9800',
      'Pronto': '#2196f3',
      'Bloqueado': '#f44336',
      'EmAnalise': '#9c27b0',
      'Recuperado': '#00bcd4',
      'StandBy': '#607d8b',
    };
    return colors[status] || '#757575';
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('pt-BR');
  };

  if (!isAdmin && !isSupervisor) {
    return (
      <Container>
        <Typography color="error">
          Você não tem permissão para acessar esta página
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Relatórios de WhatsApp Sources
        </Typography>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>WhatsApp Source</InputLabel>
              <Select
                value={selectedZap}
                onChange={(e) => setSelectedZap(e.target.value)}
                label="WhatsApp Source"
              >
                {zaps.map((zap) => (
                  <MenuItem key={zap.id} value={zap.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {zap.name}
                      <Chip
                        label={zap.status}
                        size="small"
                        sx={{
                          height: 20,
                          fontSize: '0.70rem',
                          bgcolor: getStatusColor(zap.status),
                          color: '#fff',
                        }}
                      />
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <DatePicker
                label="Data Inicial"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <DatePicker
                label="Data Final"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="contained"
              fullWidth
              onClick={loadZapData}
              disabled={!selectedZap}
            >
              Filtrar
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Statistics Cards */}
          {zapStats && (
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <ShoppingCartIcon sx={{ color: '#2196f3', mr: 1 }} />
                      <Typography color="textSecondary" variant="h6">
                        Total de Pedidos
                      </Typography>
                    </Box>
                    <Typography variant="h4">{zapStats.totalOrders}</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <MoneyIcon sx={{ color: '#4caf50', mr: 1 }} />
                      <Typography color="textSecondary" variant="h6">
                        Receita Total
                      </Typography>
                    </Box>
                    <Typography variant="h4">
                      {formatCurrency(zapStats.totalRevenue)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <TrendingUpIcon sx={{ color: '#ff9800', mr: 1 }} />
                      <Typography color="textSecondary" variant="h6">
                        Ticket Médio
                      </Typography>
                    </Box>
                    <Typography variant="h4">
                      {zapStats.totalOrders > 0
                        ? formatCurrency(zapStats.totalRevenue / zapStats.totalOrders)
                        : formatCurrency(0)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <WhatsAppIcon sx={{ color: '#25D366', mr: 1 }} />
                      <Typography color="textSecondary" variant="h6">
                        Status
                      </Typography>
                    </Box>
                    {zapDetails && (
                      <Chip
                        label={zapDetails.status}
                        sx={{
                          bgcolor: getStatusColor(zapDetails.status),
                          color: '#fff',
                          fontWeight: 600,
                          fontSize: '1rem',
                          height: 32,
                        }}
                      />
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Orders by Status */}
          {zapStats && zapStats.ordersByStatus.length > 0 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Pedidos por Status
              </Typography>
              <Grid container spacing={2}>
                {zapStats.ordersByStatus.map((item) => (
                  <Grid item xs={12} sm={6} md={4} key={item.status}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                      <StatusChip status={item.status} />
                      <Typography variant="h6">{item.count}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          )}

          {/* Orders Table */}
          <Paper>
            <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
              <Typography variant="h6">
                Pedidos ({orders.length})
              </Typography>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>ID Venda</TableCell>
                    <TableCell>Data</TableCell>
                    <TableCell>Cliente</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Valor</TableCell>
                    <TableCell>Vendedor</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow
                      key={order.id}
                      hover
                      sx={{ cursor: 'pointer' }}
                      onClick={() => navigate(`/dashboard/pedidos?orderId=${order.id}`)}
                    >
                      <TableCell>{order.orderNumber}</TableCell>
                      <TableCell>{formatDate(order.createdAt)}</TableCell>
                      <TableCell>{order.customerName}</TableCell>
                      <TableCell>
                        <StatusChip status={order.status} />
                      </TableCell>
                      <TableCell>{formatCurrency(order.total)}</TableCell>
                      <TableCell>{order.sellerName || '-'}</TableCell>
                    </TableRow>
                  ))}
                  {orders.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography variant="body2" color="text.secondary">
                          Nenhum pedido encontrado para o período selecionado
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </>
      )}
    </Container>
  );
};

export default ZapReportsPage;