import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Button, CircularProgress, TextField, Paper, Grid } from '@mui/material';
import PageHeader from '../components/PageHeader';
import TrackingDashboard from '../components/TrackingDashboard';
import OrdersTable from '../components/OrdersTable';
import TrackingHistory from '../components/TrackingHistory';
import { Order } from '../types/Order';
import api from '../services/api';
import { useOrderData } from '../contexts/OrderDataContext';

const TrackingPage: React.FC = () => {
  const { orders, loading } = useOrderData();
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // TrackingPage uses orders from context

  // Filtrar pedidos quando o termo de busca mudar
  useEffect(() => {
    filterOrders();
  }, [searchTerm, orders]);

  // Filter orders with tracking code from context
  useEffect(() => {
    const ordersWithTracking = orders.filter(order => !!order.codigoRastreio);
    setFilteredOrders(ordersWithTracking);
  }, [orders]);

  const filterOrders = () => {
    if (!searchTerm.trim()) {
      setFilteredOrders(orders);
      return;
    }

    const lowercaseTerm = searchTerm.toLowerCase();
    const filtered = orders.filter(order =>
      order.cliente.toLowerCase().includes(lowercaseTerm) ||
      order.idVenda.toLowerCase().includes(lowercaseTerm) ||
      order.codigoRastreio.toLowerCase().includes(lowercaseTerm) ||
      (order.statusCorreios && order.statusCorreios.toLowerCase().includes(lowercaseTerm))
    );

    setFilteredOrders(filtered);
  };

  const updateTrackingStatuses = async () => {
    // Tracking functionality has been removed
    setError('A funcionalidade de rastreamento dos Correios foi desativada.');
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <PageHeader title="Rastreamento de Pedidos" />

      {error && (
        <Box mb={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              bgcolor: 'error.light',
              color: 'error.dark',
              borderRadius: 2
            }}
          >
            <Typography>{error}</Typography>
          </Paper>
        </Box>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" my={6}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Box mb={4}>
            <TrackingDashboard
              orders={orders}
              onRefresh={() => {
                // Tracking refresh functionality removed
                setError('A funcionalidade de atualização de rastreamento foi desativada.');
              }}
            />
          </Box>

          <Box mb={4}>
            <TrackingHistory orders={orders} maxEntries={10} />
          </Box>

          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider',
              mb: 4
            }}
          >
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6">
                Pedidos com Rastreio ({filteredOrders.length})
              </Typography>

              <Box display="flex" gap={2}>
                <TextField
                  placeholder="Buscar pedido..."
                  variant="outlined"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {/* Tracking update button removed */}
              </Box>
            </Box>

            <OrdersTable
              orders={filteredOrders}
              onOrderUpdate={(updatedOrder) => {
                // Update the order in the orders array
                const updatedOrders = [...orders];
                const index = updatedOrders.findIndex(order => order.idVenda === updatedOrder.idVenda);
                if (index !== -1) {
                  updatedOrders[index] = updatedOrder;
                  // setOrders is not available in this context
                  // Orders are managed by the OrderDataContext
                }
              }}
            />
          </Paper>
        </>
      )}
    </Container>
  );
};

export default TrackingPage;