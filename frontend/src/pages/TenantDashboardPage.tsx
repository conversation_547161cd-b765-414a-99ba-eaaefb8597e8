import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  LinearProgress,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  CircularProgress,
} from '@mui/material';
import {
  Business as BusinessIcon,
  People as PeopleIcon,
  ShoppingCart as ShoppingCartIcon,
  Storage as StorageIcon,
  TrendingUp as TrendingUpIcon,
  Settings as SettingsIcon,
  CreditCard as CreditCardIcon,
  Edit as EditIcon,
  CloudUpload as CloudUploadIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div hidden={value !== index} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function TenantDashboardPage() {
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [tenantData, setTenantData] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    logoUrl: '',
    primaryColor: '',
    customDomain: '',
  });

  useEffect(() => {
    fetchTenantData();
    fetchUsers();
  }, []);

  const fetchTenantData = async () => {
    try {
      const response = await axios.get('/api/v1/tenant-management/dashboard');
      setTenantData(response.data);
      setEditForm({
        name: response.data.tenant.name,
        logoUrl: response.data.tenant.logoUrl || '',
        primaryColor: response.data.tenant.primaryColor,
        customDomain: response.data.tenant.customDomain || '',
      });
    } catch (error: any) {
      console.error('Error fetching tenant data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await axios.get('/api/v1/tenant-management/users');
      setUsers(response.data.data);
    } catch (error: any) {
      console.error('Error fetching users:', error);
    }
  };

  const handleUpdateSettings = async () => {
    try {
      await axios.put('/api/v1/tenant-management/settings', editForm);
      await fetchTenantData();
      setEditDialogOpen(false);
    } catch (error: any) {
      console.error('Error updating settings:', error);
    }
  };

  const handleExportData = async () => {
    try {
      const response = await axios.get('/api/v1/tenant-management/export');
      // Convert to JSON and download
      const dataStr = JSON.stringify(response.data, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
      const exportFileDefaultName = `tenant-data-${format(new Date(), 'yyyy-MM-dd')}.json`;
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    } catch (error: any) {
      console.error('Error exporting data:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!tenantData) {
    return (
      <Alert severity="error">Erro ao carregar dados do tenant</Alert>
    );
  }

  const { tenant, stats, usage, limits, planConfig } = tenantData;

  const usagePercentage = {
    users: limits.maxUsers > 0 ? (usage.activeUsers / limits.maxUsers) * 100 : 0,
    orders: limits.maxOrders > 0 ? (usage.ordersCreated / limits.maxOrders) * 100 : 0,
    storage: limits.storageQuotaMB > 0 ? (usage.avgStorageUsedMB / limits.storageQuotaMB) * 100 : 0,
  };

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        {/* Header */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" gutterBottom>
                Gestão do Tenant
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {tenant.name} • {tenant.domain}.zencash.com.br
              </Typography>
            </Box>
            <Box>
              <Chip
                label={`Plano ${planConfig.name}`}
                color="primary"
                sx={{ mr: 1 }}
              />
              <Chip
                label={tenant.status}
                color={tenant.status === 'ACTIVE' ? 'success' : 'warning'}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Stats Cards */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PeopleIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Usuários</Typography>
              </Box>
              <Typography variant="h4">{stats.totalUsers}</Typography>
              <LinearProgress
                variant="determinate"
                value={usagePercentage.users}
                sx={{ mt: 2, mb: 1 }}
                color={usagePercentage.users > 80 ? 'warning' : 'primary'}
              />
              <Typography variant="body2" color="text.secondary">
                {stats.totalUsers} de {limits.maxUsers === -1 ? 'ilimitado' : limits.maxUsers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShoppingCartIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Pedidos/Mês</Typography>
              </Box>
              <Typography variant="h4">{usage.ordersCreated}</Typography>
              <LinearProgress
                variant="determinate"
                value={usagePercentage.orders}
                sx={{ mt: 2, mb: 1 }}
                color={usagePercentage.orders > 80 ? 'warning' : 'success'}
              />
              <Typography variant="body2" color="text.secondary">
                {usage.ordersCreated} de {limits.maxOrders === -1 ? 'ilimitado' : limits.maxOrders}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BusinessIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Produtos</Typography>
              </Box>
              <Typography variant="h4">{stats.totalProducts}</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Limite: {limits.maxProducts === -1 ? 'ilimitado' : limits.maxProducts}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StorageIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Armazenamento</Typography>
              </Box>
              <Typography variant="h4">
                {usage.avgStorageUsedMB.toFixed(1)} MB
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usagePercentage.storage}
                sx={{ mt: 2, mb: 1 }}
                color={usagePercentage.storage > 80 ? 'warning' : 'primary'}
              />
              <Typography variant="body2" color="text.secondary">
                {usage.avgStorageUsedMB.toFixed(1)} de {limits.storageQuotaMB} MB
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Tabs */}
        <Grid item xs={12}>
          <Paper>
            <Tabs value={tabValue} onChange={(e, v) => setTabValue(v)}>
              <Tab label="Configurações" icon={<SettingsIcon />} />
              <Tab label="Usuários" icon={<PeopleIcon />} />
              <Tab label="Faturamento" icon={<CreditCardIcon />} />
              <Tab label="Uso" icon={<TrendingUpIcon />} />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6">Configurações do Tenant</Typography>
                    <Button
                      variant="contained"
                      startIcon={<EditIcon />}
                      onClick={() => setEditDialogOpen(true)}
                    >
                      Editar
                    </Button>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Nome da Empresa"
                    value={tenant.name}
                    disabled
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Domínio"
                    value={`${tenant.domain}.zencash.com.br`}
                    disabled
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Domínio Personalizado"
                    value={tenant.customDomain || 'Não configurado'}
                    disabled
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Cor Primária"
                    value={tenant.primaryColor}
                    disabled
                    InputProps={{
                      startAdornment: (
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            bgcolor: tenant.primaryColor,
                            borderRadius: 1,
                            mr: 1,
                          }}
                        />
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExportData}
                  >
                    Exportar Dados (GDPR)
                  </Button>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Função</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Criado em</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.fullName}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip label={user.role} size="small" />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={user.isActive ? 'Ativo' : 'Inativo'}
                            color={user.isActive ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {format(new Date(user.createdAt), 'dd/MM/yyyy', { locale: ptBR })}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Alert severity="info">
                    Integração com Stripe em desenvolvimento
                  </Alert>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Plano Atual: {planConfig.name}
                  </Typography>
                  <Typography variant="body1" paragraph>
                    R$ {planConfig.priceMonthly}/mês
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Recursos incluídos:
                  </Typography>
                  <Box sx={{ mt: 1 }}>
                    {planConfig.features.map((feature: string, index: number) => (
                      <Typography key={index} variant="body2" sx={{ ml: 2 }}>
                        • {feature}
                      </Typography>
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={3}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Uso Atual do Mês
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      Chamadas de API: {usage.apiCalls}
                    </Typography>
                    <Typography variant="body2">
                      Pedidos criados: {usage.ordersCreated}
                    </Typography>
                    <Typography variant="body2">
                      Usuários ativos: {usage.activeUsers}
                    </Typography>
                    <Typography variant="body2">
                      Armazenamento médio: {usage.avgStorageUsedMB.toFixed(2)} MB
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      {/* Edit Settings Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Editar Configurações</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nome da Empresa"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="URL do Logo"
                value={editForm.logoUrl}
                onChange={(e) => setEditForm({ ...editForm, logoUrl: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Cor Primária"
                type="color"
                value={editForm.primaryColor}
                onChange={(e) => setEditForm({ ...editForm, primaryColor: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Domínio Personalizado"
                value={editForm.customDomain}
                onChange={(e) => setEditForm({ ...editForm, customDomain: e.target.value })}
                helperText="Ex: app.suaempresa.com.br"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancelar</Button>
          <Button onClick={handleUpdateSettings} variant="contained">
            Salvar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}