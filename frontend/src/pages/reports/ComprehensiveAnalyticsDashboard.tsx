import React, { useState, useEffect, useMemo } from 'react';
import {
  Grid, Paper, Typography, Box, Card, CardContent, Tabs, Tab,
  FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,
  IconButton, Tooltip, Chip, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Button, LinearProgress,
  Alert, Divider
} from '@mui/material';
import {
  LineChart, Line, BarChart, Bar, PieChart, Pie, AreaChart, Area,
  XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend,
  ResponsiveContainer, Cell, ComposedChart, Radar, RadarChart,
  PolarGrid, PolarAngleAxis, PolarRadiusAxis, Treemap, FunnelChart, Funnel, LabelList
} from 'recharts';
import { Order } from '../../types/Order';
import {
  TrendingUp, TrendingDown, TrendingFlat, AttachMoney,
  People, ShoppingCart, CheckCircle, Warning, Timer,
  LocationOn, Category, Analytics, Download, Refresh,
  Speed, Timeline, EmojiEvents, Assessment
} from '@mui/icons-material';
import { format, subDays, startOfWeek, startOfMonth, startOfQuarter, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { normalizeOrdersForReports, parseOrderDate } from '../../utils/reportOrderNormalizer';

// Colors palette
const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  secondary: '#6366f1',
  info: '#06b6d4',
  purple: '#8b5cf6',
  pink: '#ec4899'
};

const CHART_COLORS = [
  COLORS.primary, COLORS.success, COLORS.warning, COLORS.danger,
  COLORS.secondary, COLORS.info, COLORS.purple, COLORS.pink
];

interface ComprehensiveAnalyticsDashboardProps {
  orders: Order[];
  startDate?: Date | null;
  endDate?: Date | null;
}

interface KPIData {
  title: string;
  value: number | string;
  change: number;
  trend: 'up' | 'down' | 'flat';
  icon: React.ReactNode;
  color: string;
  prefix?: string;
  suffix?: string;
}

const ComprehensiveAnalyticsDashboard: React.FC<ComprehensiveAnalyticsDashboardProps> = ({
  orders: rawOrders, startDate, endDate
}) => {
  const [timeFrame, setTimeFrame] = useState('monthly');
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);

  // Normalize orders to ensure both English and Portuguese fields exist
  const orders = useMemo(() => normalizeOrdersForReports(rawOrders), [rawOrders]);

  // Filter orders by date range and exclude cancelled/deleted orders
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      // Exclude cancelled and deleted orders
      const status = order.situacaoVenda?.toLowerCase();
      if (status === 'deletado' || status === 'cancelado') return false;
      
      if (!order.dataVenda) return false;
      const orderDate = parseOrderDate(order.dataVenda);
      if (!orderDate) return false;
      
      const afterStart = !startDate || orderDate >= startDate;
      const beforeEnd = !endDate || orderDate <= endDate;
      
      return afterStart && beforeEnd;
    });
  }, [orders, startDate, endDate]);

  // Calculate KPIs
  const kpiData = useMemo((): KPIData[] => {
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.valorVenda, 0);
    const totalReceived = filteredOrders.reduce((sum, order) => sum + order.valorRecebido, 0);
    const totalOrders = filteredOrders.length;
    const completedOrders = filteredOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'completo').length;
    const conversionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
    const averageTicket = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Calculate previous period for comparison
    const currentPeriodDays = endDate && startDate ? 
      Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) : 30;
    const previousStartDate = startDate ? subDays(startDate, currentPeriodDays) : subDays(new Date(), 60);
    const previousEndDate = startDate ? subDays(startDate, 1) : subDays(new Date(), 31);
    
    const previousOrders = orders.filter(order => {
      // Exclude cancelled and deleted orders
      const status = order.situacaoVenda?.toLowerCase();
      if (status === 'deletado' || status === 'cancelado') return false;
      
      const orderDate = parseOrderDate(order.dataVenda);
      return orderDate && orderDate >= previousStartDate && orderDate <= previousEndDate;
    });
    
    const previousRevenue = previousOrders.reduce((sum, order) => sum + order.valorVenda, 0);
    const previousOrdersCount = previousOrders.length;
    
    const revenueChange = previousRevenue > 0 ? 
      ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const ordersChange = previousOrdersCount > 0 ? 
      ((totalOrders - previousOrdersCount) / previousOrdersCount) * 100 : 0;

    return [
      {
        title: 'Receita Total',
        value: totalRevenue,
        change: revenueChange,
        trend: revenueChange > 0 ? 'up' : revenueChange < 0 ? 'down' : 'flat',
        icon: <AttachMoney />,
        color: COLORS.success,
        prefix: 'R$ '
      },
      {
        title: 'Total de Pedidos',
        value: totalOrders,
        change: ordersChange,
        trend: ordersChange > 0 ? 'up' : ordersChange < 0 ? 'down' : 'flat',
        icon: <ShoppingCart />,
        color: COLORS.primary
      },
      {
        title: 'Taxa de Conversão',
        value: conversionRate.toFixed(1),
        change: 0,
        trend: 'flat',
        icon: <CheckCircle />,
        color: COLORS.info,
        suffix: '%'
      },
      {
        title: 'Ticket Médio',
        value: averageTicket,
        change: 0,
        trend: 'flat',
        icon: <Speed />,
        color: COLORS.warning,
        prefix: 'R$ '
      },
      {
        title: 'Taxa de Recuperação',
        value: totalRevenue > 0 ? ((totalReceived / totalRevenue) * 100).toFixed(1) : '0',
        change: 0,
        trend: 'flat',
        icon: <Timeline />,
        color: COLORS.purple,
        suffix: '%'
      },
      {
        title: 'Pedidos Pendentes',
        value: filteredOrders.filter(o => 
          o.situacaoVenda?.toLowerCase() === 'pendente' || 
          o.situacaoVenda?.toLowerCase() === 'analise'
        ).length,
        change: 0,
        trend: 'flat',
        icon: <Warning />,
        color: COLORS.danger
      }
    ];
  }, [filteredOrders, orders, startDate, endDate]);

  // Revenue trend data
  const revenueTrendData = useMemo(() => {
    const groupedData = new Map<string, { revenue: number; orders: number; received: number }>();
    
    filteredOrders.forEach(order => {
      const orderDate = parseOrderDate(order.dataVenda);
      if (!orderDate) return;
      
      let key: string;
      if (timeFrame === 'daily') {
        key = format(orderDate, 'dd/MM', { locale: ptBR });
      } else if (timeFrame === 'weekly') {
        key = format(startOfWeek(orderDate), 'dd/MM', { locale: ptBR });
      } else if (timeFrame === 'monthly') {
        key = format(orderDate, 'MMM/yy', { locale: ptBR });
      } else {
        key = format(startOfQuarter(orderDate), 'QQQ/yy', { locale: ptBR });
      }
      
      const existing = groupedData.get(key) || { revenue: 0, orders: 0, received: 0 };
      existing.revenue += order.valorVenda;
      existing.orders += 1;
      existing.received += order.valorRecebido;
      groupedData.set(key, existing);
    });
    
    return Array.from(groupedData.entries())
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([date, data]) => ({
        date,
        receita: data.revenue,
        pedidos: data.orders,
        recebido: data.received,
        ticketMedio: data.orders > 0 ? data.revenue / data.orders : 0
      }));
  }, [filteredOrders, timeFrame]);

  // Conversion funnel data
  const conversionFunnelData = useMemo(() => {
    const total = filteredOrders.length;
    const analise = filteredOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'analise').length;
    const negociacao = filteredOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'negociacao').length;
    const parcial = filteredOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'parcial').length;
    const completo = filteredOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'completo').length;
    
    return [
      { name: 'Total de Pedidos', value: total, fill: COLORS.primary },
      { name: 'Em Análise', value: analise, fill: COLORS.info },
      { name: 'Em Negociação', value: negociacao, fill: COLORS.warning },
      { name: 'Pagamento Parcial', value: parcial, fill: COLORS.secondary },
      { name: 'Completo', value: completo, fill: COLORS.success }
    ];
  }, [filteredOrders]);

  // Product performance data
  const productPerformanceData = useMemo(() => {
    const productMap = new Map<string, { orders: number; revenue: number; avgTicket: number }>();
    
    filteredOrders.forEach(order => {
      const product = order.oferta || 'Sem Produto';
      const existing = productMap.get(product) || { orders: 0, revenue: 0, avgTicket: 0 };
      existing.orders += 1;
      existing.revenue += order.valorVenda;
      existing.avgTicket = existing.revenue / existing.orders;
      productMap.set(product, existing);
    });
    
    return Array.from(productMap.entries())
      .map(([product, data]) => ({
        produto: product,
        pedidos: data.orders,
        receita: data.revenue,
        ticketMedio: data.avgTicket
      }))
      .sort((a, b) => b.receita - a.receita)
      .slice(0, 10);
  }, [filteredOrders]);

  // Seller performance radar data
  const sellerRadarData = useMemo(() => {
    const sellerMap = new Map<string, {
      vendas: number;
      receita: number;
      conversao: number;
      ticket: number;
      velocidade: number;
    }>();
    
    filteredOrders.forEach(order => {
      const seller = order.vendedor || 'Sem Vendedor';
      const existing = sellerMap.get(seller) || {
        vendas: 0,
        receita: 0,
        conversao: 0,
        ticket: 0,
        velocidade: 0
      };
      
      existing.vendas += 1;
      existing.receita += order.valorVenda;
      if (order.situacaoVenda?.toLowerCase() === 'completo') {
        existing.conversao += 1;
      }
      
      sellerMap.set(seller, existing);
    });
    
    // Calculate metrics and normalize to 0-100 scale
    const maxVendas = Math.max(...Array.from(sellerMap.values()).map(s => s.vendas));
    const maxReceita = Math.max(...Array.from(sellerMap.values()).map(s => s.receita));
    
    return Array.from(sellerMap.entries())
      .map(([seller, data]) => ({
        vendedor: seller,
        vendas: (data.vendas / maxVendas) * 100,
        receita: (data.receita / maxReceita) * 100,
        conversao: data.vendas > 0 ? (data.conversao / data.vendas) * 100 : 0,
        ticketMedio: data.vendas > 0 ? ((data.receita / data.vendas) / 1000) * 100 : 0, // Normalized to 0-100
        eficiencia: Math.random() * 100 // Placeholder for efficiency metric
      }))
      .slice(0, 5);
  }, [filteredOrders]);

  // Geographic distribution data
  const geographicData = useMemo(() => {
    const stateMap = new Map<string, number>();
    
    filteredOrders.forEach(order => {
      const state = order.estadoDestinatario || 'Não Informado';
      stateMap.set(state, (stateMap.get(state) || 0) + 1);
    });
    
    return Array.from(stateMap.entries())
      .map(([state, count]) => ({
        estado: state,
        pedidos: count,
        percentual: (count / filteredOrders.length) * 100
      }))
      .sort((a, b) => b.pedidos - a.pedidos);
  }, [filteredOrders]);

  // Time to conversion data
  const timeToConversionData = useMemo(() => {
    const conversionTimes = filteredOrders
      .filter(order => order.situacaoVenda?.toLowerCase() === 'completo')
      .map(order => {
        const orderDate = parseOrderDate(order.dataVenda);
        const receivedDate = order.dataRecebimento ? parseOrderDate(order.dataRecebimento) : null;
        
        if (orderDate && receivedDate) {
          const days = Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
          return days;
        }
        return null;
      })
      .filter(days => days !== null && days >= 0) as number[];
    
    // Group by time ranges
    const ranges = [
      { label: '0-3 dias', min: 0, max: 3 },
      { label: '4-7 dias', min: 4, max: 7 },
      { label: '8-15 dias', min: 8, max: 15 },
      { label: '16-30 dias', min: 16, max: 30 },
      { label: '30+ dias', min: 31, max: Infinity }
    ];
    
    return ranges.map(range => ({
      periodo: range.label,
      quantidade: conversionTimes.filter(days => days >= range.min && days <= range.max).length
    }));
  }, [filteredOrders]);

  // Export functionality
  const handleExport = () => {
    // Implement CSV export of the current view data
    const csvContent = "data:text/csv;charset=utf-8," + 
      "Relatório de Analytics Comprehensive\n" +
      "Período: " + (startDate ? format(startDate, 'dd/MM/yyyy') : 'Início') + 
      " até " + (endDate ? format(endDate, 'dd/MM/yyyy') : 'Fim') + "\n\n" +
      "KPIs\n" +
      kpiData.map(kpi => `${kpi.title},${kpi.prefix || ''}${kpi.value}${kpi.suffix || ''}`).join('\n');
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "analytics_report.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Box sx={{ p: 3, bgcolor: '#f8fafc' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: '#1e293b' }}>
            Analytics Avançado
          </Typography>
          <Typography variant="body2" sx={{ color: '#64748b', mt: 0.5 }}>
            Insights detalhados sobre o desempenho do negócio
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Período</InputLabel>
            <Select
              value={timeFrame}
              label="Período"
              onChange={(e) => setTimeFrame(e.target.value)}
            >
              <MenuItem value="daily">Diário</MenuItem>
              <MenuItem value="weekly">Semanal</MenuItem>
              <MenuItem value="monthly">Mensal</MenuItem>
              <MenuItem value="quarterly">Trimestral</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Atualizar dados">
            <IconButton onClick={() => setLoading(true)} sx={{ bgcolor: 'white' }}>
              <Refresh />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="contained"
            startIcon={<Download />}
            onClick={handleExport}
            sx={{ textTransform: 'none' }}
          >
            Exportar
          </Button>
        </Box>
      </Box>

      {/* KPI Cards */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {kpiData.map((kpi, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
            <Card sx={{ 
              height: '100%', 
              background: 'white',
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
              '&:hover': {
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                transform: 'translateY(-2px)',
                transition: 'all 0.3s'
              }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <Box>
                    <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 500 }}>
                      {kpi.title}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: 700, color: '#1e293b', mt: 0.5 }}>
                      {kpi.prefix}{typeof kpi.value === 'number' ? kpi.value.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : kpi.value}{kpi.suffix}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      {kpi.trend === 'up' && <TrendingUp sx={{ fontSize: 16, color: COLORS.success, mr: 0.5 }} />}
                      {kpi.trend === 'down' && <TrendingDown sx={{ fontSize: 16, color: COLORS.danger, mr: 0.5 }} />}
                      {kpi.trend === 'flat' && <TrendingFlat sx={{ fontSize: 16, color: COLORS.warning, mr: 0.5 }} />}
                      <Typography variant="caption" sx={{ 
                        color: kpi.trend === 'up' ? COLORS.success : kpi.trend === 'down' ? COLORS.danger : COLORS.warning,
                        fontWeight: 600
                      }}>
                        {kpi.change > 0 ? '+' : ''}{kpi.change.toFixed(1)}%
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ 
                    p: 1, 
                    borderRadius: 2, 
                    bgcolor: `${kpi.color}15`,
                    color: kpi.color
                  }}>
                    {kpi.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Main Content Tabs */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Tabs 
          value={activeTab} 
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{
            bgcolor: 'white',
            borderBottom: '1px solid #e2e8f0',
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '0.875rem',
              minHeight: 48
            }
          }}
        >
          <Tab label="Visão Geral" icon={<Analytics />} iconPosition="start" />
          <Tab label="Análise de Vendas" icon={<TrendingUp />} iconPosition="start" />
          <Tab label="Performance" icon={<EmojiEvents />} iconPosition="start" />
          <Tab label="Insights Geográficos" icon={<LocationOn />} iconPosition="start" />
          <Tab label="Análise de Produtos" icon={<Category />} iconPosition="start" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Tab 0: Overview */}
          {activeTab === 0 && (
            <Grid container spacing={3}>
              {/* Revenue Trend */}
              <Grid item xs={12} lg={8}>
                <Paper sx={{ p: 3, height: '100%' }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Tendência de Receita
                  </Typography>
                  <ResponsiveContainer width="100%" height={350}>
                    <ComposedChart data={revenueTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="date" stroke="#64748b" />
                      <YAxis yAxisId="left" stroke="#64748b" />
                      <YAxis yAxisId="right" orientation="right" stroke="#64748b" />
                      <RechartsTooltip 
                        contentStyle={{ backgroundColor: '#fff', border: '1px solid #e2e8f0', borderRadius: 8 }}
                        formatter={(value: any) => `R$ ${Number(value).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                      />
                      <Legend />
                      <Area
                        yAxisId="left"
                        type="monotone"
                        dataKey="receita"
                        name="Receita"
                        stroke={COLORS.primary}
                        fill={COLORS.primary}
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="recebido"
                        name="Recebido"
                        stroke={COLORS.success}
                        strokeWidth={2}
                        dot={{ r: 4 }}
                      />
                      <Bar
                        yAxisId="right"
                        dataKey="pedidos"
                        name="Nº Pedidos"
                        fill={COLORS.info}
                        fillOpacity={0.5}
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>

              {/* Conversion Funnel */}
              <Grid item xs={12} lg={4}>
                <Paper sx={{ p: 3, height: '100%' }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Funil de Conversão
                  </Typography>
                  <ResponsiveContainer width="100%" height={350}>
                    <FunnelChart>
                      <RechartsTooltip />
                      <Funnel
                        dataKey="value"
                        data={conversionFunnelData}
                        isAnimationActive
                      >
                        <LabelList position="center" fill="#fff" />
                      </Funnel>
                    </FunnelChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>

              {/* Time to Conversion */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Tempo até Conversão
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={timeToConversionData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="periodo" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <RechartsTooltip />
                      <Bar dataKey="quantidade" fill={COLORS.secondary} radius={[8, 8, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>

              {/* Status Distribution */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Distribuição por Status
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={conversionFunnelData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {conversionFunnelData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <RechartsTooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Tab 1: Sales Analysis */}
          {activeTab === 1 && (
            <Grid container spacing={3}>
              {/* Revenue by Product */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Receita por Produto
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={productPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="produto" angle={-45} textAnchor="end" height={100} stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <RechartsTooltip 
                        formatter={(value: any) => `R$ ${Number(value).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
                      />
                      <Legend />
                      <Bar dataKey="receita" name="Receita Total" fill={COLORS.primary} radius={[8, 8, 0, 0]} />
                      <Bar dataKey="ticketMedio" name="Ticket Médio" fill={COLORS.success} radius={[8, 8, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>

              {/* Product Performance Table */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Detalhamento por Produto
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Produto</TableCell>
                          <TableCell align="right">Pedidos</TableCell>
                          <TableCell align="right">Receita Total</TableCell>
                          <TableCell align="right">Ticket Médio</TableCell>
                          <TableCell align="right">% do Total</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {productPerformanceData.map((row) => (
                          <TableRow key={row.produto}>
                            <TableCell>{row.produto}</TableCell>
                            <TableCell align="right">{row.pedidos}</TableCell>
                            <TableCell align="right">
                              R$ {row.receita.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </TableCell>
                            <TableCell align="right">
                              R$ {row.ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                            </TableCell>
                            <TableCell align="right">
                              {((row.receita / filteredOrders.reduce((sum, o) => sum + o.valorVenda, 0)) * 100).toFixed(1)}%
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Tab 2: Performance */}
          {activeTab === 2 && (
            <Grid container spacing={3}>
              {/* Seller Performance Radar */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Performance dos Vendedores (Top 5)
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <RadarChart data={sellerRadarData}>
                      <PolarGrid stroke="#e2e8f0" />
                      <PolarAngleAxis dataKey="vendedor" stroke="#64748b" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} stroke="#64748b" />
                      <Radar name="Vendas" dataKey="vendas" stroke={COLORS.primary} fill={COLORS.primary} fillOpacity={0.6} />
                      <Radar name="Receita" dataKey="receita" stroke={COLORS.success} fill={COLORS.success} fillOpacity={0.6} />
                      <Radar name="Conversão" dataKey="conversao" stroke={COLORS.warning} fill={COLORS.warning} fillOpacity={0.6} />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </Paper>
              </Grid>

              {/* Seller Ranking */}
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Ranking de Vendedores
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {sellerRadarData.slice(0, 5).map((seller, index) => (
                      <Box
                        key={seller.vendedor}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          p: 2,
                          bgcolor: '#f8fafc',
                          borderRadius: 2,
                          border: '1px solid #e2e8f0'
                        }}
                      >
                        <Box
                          sx={{
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            bgcolor: index === 0 ? COLORS.warning : index === 1 ? '#c0c0c0' : index === 2 ? '#cd7f32' : '#e2e8f0',
                            color: index < 3 ? 'white' : '#64748b',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontWeight: 700,
                            mr: 2
                          }}
                        >
                          {index + 1}
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            {seller.vendedor}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            {Math.round(seller.vendas)} vendas • Taxa de conversão: {seller.conversao.toFixed(0)}%
                          </Typography>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 700, color: COLORS.success }}>
                            R$ {((seller.receita / 100) * Math.max(...sellerRadarData.map(s => s.receita)) / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Tab 3: Geographic Insights */}
          {activeTab === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                    Distribuição Geográfica de Pedidos
                  </Typography>
                  <ResponsiveContainer width="100%" height={400}>
                    <Treemap
                      data={geographicData.map(item => ({
                        name: item.estado,
                        size: item.pedidos,
                        fill: CHART_COLORS[Math.floor(Math.random() * CHART_COLORS.length)]
                      }))}
                      dataKey="size"
                      ratio={4 / 3}
                      stroke="#fff"
                      fill="#8884d8"
                    />
                  </ResponsiveContainer>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                    Top 10 Estados por Volume
                  </Typography>
                  <Grid container spacing={2}>
                    {geographicData.slice(0, 10).map((state, index) => (
                      <Grid item xs={12} sm={6} md={4} key={state.estado}>
                        <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5, bgcolor: '#f8fafc', borderRadius: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, minWidth: 60 }}>
                            #{index + 1} {state.estado}
                          </Typography>
                          <Box sx={{ flex: 1, mx: 2 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={state.percentual} 
                              sx={{ height: 8, borderRadius: 4 }}
                            />
                          </Box>
                          <Typography variant="caption" sx={{ minWidth: 45, textAlign: 'right' }}>
                            {state.pedidos} ({state.percentual.toFixed(1)}%)
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Tab 4: Product Analysis */}
          {activeTab === 4 && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                Análise detalhada de produtos em desenvolvimento. Em breve você poderá ver insights sobre:
                performance por categoria, sazonalidade, correlação entre produtos e muito mais.
              </Typography>
            </Alert>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default ComprehensiveAnalyticsDashboard;