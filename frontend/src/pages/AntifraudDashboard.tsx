import React from 'react';
import { Box, Container, Typography, Card, CardContent } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import RiskBasedReviewQueue from '../components/Antifraud/RiskBasedReviewQueue';
import { useFeatureFlags } from '../contexts/FeatureFlagContext';
import { Warning as WarningIcon } from '@mui/icons-material';

const AntifraudDashboard: React.FC = () => {
  const { userInfo, isAdmin, isSupervisor } = useAuth();
  const { isEnabled } = useFeatureFlags();

  // Check if user has permission - using helper functions
  const hasPermission = isAdmin() || isSupervisor();

  if (!hasPermission) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WarningIcon style={{ marginRight: 8, color: '#f44336' }} />
                <Typography variant="h6">Acesso Negado</Typography>
              </Box>
              <Typography variant="body2" color="textSecondary" style={{ marginTop: 8 }}>
                Você não tem permissão para acessar o painel anti-fraude.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box py={4}>
        <RiskBasedReviewQueue />
      </Box>
    </Container>
  );
};

export default AntifraudDashboard;