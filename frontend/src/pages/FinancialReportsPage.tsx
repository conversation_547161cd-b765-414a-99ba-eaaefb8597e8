import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Button,
  CircularProgress,
  Tabs,
  Tab,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Receipt as ReceiptIcon,
  Person as PersonIcon,
  DateRange as DateRangeIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import api from '../services/api';
import useAuth from '../hooks/useAuth';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`financial-tabpanel-${index}`}
      aria-labelledby={`financial-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface PaymentSummary {
  paymentMethod: {
    id: string;
    name: string;
  };
  totalAmount: number;
  count: number;
}

interface CommissionReport {
  id: string;
  orderId: string;
  userId: string;
  userRole: string;
  baseAmount: number;
  percentage: number;
  commissionAmount: number;
  paymentDate: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  order: {
    orderNumber: string;
    customerName: string;
  };
}

interface Payment {
  id: string;
  orderId: string;
  amount: number;
  paymentMethodId: string;
  collectorId: string;
  notes: string;
  createdAt: string;
  order: {
    orderNumber: string;
    customerName: string;
  };
  paymentMethod: {
    name: string;
  };
  collector: {
    name: string;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  commissionRate?: number;
}

const FinancialReportsPage: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Date filters
  const [startDate, setStartDate] = useState(
    new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  
  // Data
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummary[]>([]);
  const [commissionReport, setCommissionReport] = useState<CommissionReport[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  
  // Summary statistics
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalPayments, setTotalPayments] = useState(0);
  const [totalCommissions, setTotalCommissions] = useState(0);

  useEffect(() => {
    fetchUsers();
    fetchData();
  }, []);

  useEffect(() => {
    fetchData();
  }, [startDate, endDate, selectedUserId, tabValue]);

  const fetchUsers = async () => {
    try {
      const response = await api.get('/users', {
        params: { role: 'COBRADOR,VENDEDOR' }
      });
      setUsers(response.data);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    setError('');
    try {
      if (tabValue === 0) {
        // Payment Summary by Method
        const response = await api.get('/payments/by-method', {
          params: { startDate, endDate }
        });
        setPaymentSummary(response.data);
        
        // Calculate total revenue
        const total = response.data.reduce((sum: number, item: PaymentSummary) => 
          sum + parseFloat(item.totalAmount.toString()), 0
        );
        setTotalRevenue(total);
        setTotalPayments(response.data.reduce((sum: number, item: PaymentSummary) => 
          sum + item.count, 0
        ));
      } else if (tabValue === 1) {
        // Commission Report
        const response = await api.get('/payments/commission-report', {
          params: { 
            startDate, 
            endDate,
            ...(selectedUserId && { userId: selectedUserId })
          }
        });
        setCommissionReport(response.data);
        
        // Calculate total commissions
        const total = response.data.reduce((sum: number, item: CommissionReport) => 
          sum + parseFloat(item.commissionAmount.toString()), 0
        );
        setTotalCommissions(total);
      } else if (tabValue === 2) {
        // Detailed Payments
        if (selectedUserId) {
          const response = await api.get(`/payments/collector/${selectedUserId}`, {
            params: { startDate, endDate }
          });
          setPayments(response.data);
        }
      }
    } catch (err: any) {
      setError('Erro ao carregar dados');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExportCSV = () => {
    let csvContent = '';
    let filename = '';
    
    if (tabValue === 0) {
      // Export payment summary
      csvContent = 'Método de Pagamento,Total Recebido,Quantidade\n';
      paymentSummary.forEach(item => {
        csvContent += `${item.paymentMethod.name},${item.totalAmount},${item.count}\n`;
      });
      filename = `resumo-pagamentos-${startDate}-${endDate}.csv`;
    } else if (tabValue === 1) {
      // Export commission report
      csvContent = 'Data,Pedido,Cliente,Colaborador,Valor Base,Percentual,Comissão\n';
      commissionReport.forEach(item => {
        csvContent += `${format(new Date(item.paymentDate), 'dd/MM/yyyy')},${item.order.orderNumber},${item.order.customerName},${item.user.name},${item.baseAmount},${item.percentage}%,${item.commissionAmount}\n`;
      });
      filename = `relatorio-comissoes-${startDate}-${endDate}.csv`;
    }
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
          <MoneyIcon color="primary" />
          Relatórios Financeiros
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Atualizar">
            <IconButton onClick={fetchData} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportCSV}
            disabled={loading}
          >
            Exportar CSV
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="white" variant="h6">
                    Receita Total
                  </Typography>
                  <Typography color="white" variant="h4" sx={{ fontWeight: 600 }}>
                    R$ {totalRevenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </Typography>
                </Box>
                <TrendingUpIcon sx={{ fontSize: 48, color: 'rgba(255,255,255,0.3)' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="white" variant="h6">
                    Total de Pagamentos
                  </Typography>
                  <Typography color="white" variant="h4" sx={{ fontWeight: 600 }}>
                    {totalPayments}
                  </Typography>
                </Box>
                <ReceiptIcon sx={{ fontSize: 48, color: 'rgba(255,255,255,0.3)' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="white" variant="h6">
                    Comissões
                  </Typography>
                  <Typography color="white" variant="h4" sx={{ fontWeight: 600 }}>
                    R$ {totalCommissions.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </Typography>
                </Box>
                <PersonIcon sx={{ fontSize: 48, color: 'rgba(255,255,255,0.3)' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Data Inicial"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
              InputProps={{
                startAdornment: <DateRangeIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Data Final"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              InputLabelProps={{ shrink: true }}
              InputProps={{
                startAdornment: <DateRangeIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          {(tabValue === 1 || tabValue === 2) && (
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Colaborador</InputLabel>
                <Select
                  value={selectedUserId}
                  onChange={(e) => setSelectedUserId(e.target.value)}
                  label="Colaborador"
                >
                  <MenuItem value="">Todos</MenuItem>
                  {users.map(user => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.name} ({user.role})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}
        </Grid>
      </Paper>

      {/* Tabs */}
      <Paper sx={{ borderRadius: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Resumo por Método" />
          <Tab label="Relatório de Comissões" />
          <Tab label="Pagamentos Detalhados" disabled={!selectedUserId} />
        </Tabs>

        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TabPanel value={tabValue} index={0}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Método de Pagamento</TableCell>
                      <TableCell align="right">Quantidade</TableCell>
                      <TableCell align="right">Total Recebido</TableCell>
                      <TableCell align="right">Percentual</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymentSummary.map((item) => (
                      <TableRow key={item.paymentMethod.id} hover>
                        <TableCell>{item.paymentMethod.name}</TableCell>
                        <TableCell align="right">{item.count}</TableCell>
                        <TableCell align="right">
                          R$ {parseFloat(item.totalAmount.toString()).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </TableCell>
                        <TableCell align="right">
                          {totalRevenue > 0 ? ((parseFloat(item.totalAmount.toString()) / totalRevenue) * 100).toFixed(1) : 0}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Data</TableCell>
                      <TableCell>Pedido</TableCell>
                      <TableCell>Cliente</TableCell>
                      <TableCell>Colaborador</TableCell>
                      <TableCell align="right">Valor Base</TableCell>
                      <TableCell align="center">%</TableCell>
                      <TableCell align="right">Comissão</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {commissionReport.map((item) => (
                      <TableRow key={item.id} hover>
                        <TableCell>{format(new Date(item.paymentDate), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>{item.order.orderNumber}</TableCell>
                        <TableCell>{item.order.customerName}</TableCell>
                        <TableCell>{item.user.name}</TableCell>
                        <TableCell align="right">
                          R$ {parseFloat(item.baseAmount.toString()).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </TableCell>
                        <TableCell align="center">{item.percentage}%</TableCell>
                        <TableCell align="right">
                          R$ {parseFloat(item.commissionAmount.toString()).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Data</TableCell>
                      <TableCell>Pedido</TableCell>
                      <TableCell>Cliente</TableCell>
                      <TableCell>Método</TableCell>
                      <TableCell align="right">Valor</TableCell>
                      <TableCell>Observações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {payments.map((payment) => (
                      <TableRow key={payment.id} hover>
                        <TableCell>{format(new Date(payment.createdAt), 'dd/MM/yyyy HH:mm')}</TableCell>
                        <TableCell>{payment.order.orderNumber}</TableCell>
                        <TableCell>{payment.order.customerName}</TableCell>
                        <TableCell>{payment.paymentMethod.name}</TableCell>
                        <TableCell align="right">
                          R$ {parseFloat(payment.amount.toString()).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </TableCell>
                        <TableCell>{payment.notes || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
          </>
        )}
      </Paper>
    </Box>
  );
};

export default FinancialReportsPage;