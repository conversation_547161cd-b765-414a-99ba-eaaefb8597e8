import React, { useState, useEffect, useMemo } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  LinearProgress,
  IconButton,
  Tooltip,
  Divider,
  useTheme,
  alpha,
  Button,
  Menu,
  MenuItem,
  ToggleButtonGroup,
  ToggleButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as AttachMoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon,
  Warning as WarningIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Groups as GroupsIcon,
  CalendarToday as CalendarTodayIcon,
  DateRange as DateRangeIcon,
  UploadFile as UploadFileIcon,
  FilterList as FilterListIcon,
  EmojiEvents as EmojiEventsIcon,
} from '@mui/icons-material';
import {
  LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie,
  XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend,
  ResponsiveContainer, Cell, ComposedChart
} from 'recharts';
import { Order } from '../types/Order';
import { useOrderData } from '../contexts/OrderDataContext';
import { format, subDays, startOfDay, endOfDay, startOfYesterday, endOfYesterday, isWithinInterval, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { parseDate, formatDate, formatDateTime } from '../utils/dateUtils';
import { normalizeOrders } from '../utils/orderFieldMapping';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import SmartImportDialog from '../components/SmartImportDialog';

// Interface for outlet context
interface LayoutOutletContext {
  selectedStatus: any;
  setSelectedStatus: (filter: any) => void;
}

// Color palette
const COLORS = {
  primary: '#1e40af',
  success: '#059669',
  warning: '#d97706',
  danger: '#dc2626',
  info: '#0891b2',
  purple: '#7c3aed',
  dark: '#1e293b',
  light: '#f1f5f9',
};

const CHART_COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899', '#06b6d4'];


type DateFilterType = 'today' | 'yesterday' | 'last7days' | 'last30days' | 'custom';

const AdminAnalyticsDashboard: React.FC = () => {
  const theme = useTheme();
  const { orders: rawOrders = [], loading: ordersLoading } = useOrderData();
  
  // Normalize orders to ensure both English and Portuguese field names exist
  const orders = useMemo(() => normalizeOrders(rawOrders), [rawOrders]);
  
  const [dateFilter, setDateFilter] = useState<DateFilterType>('today');
  const [customDateRange, setCustomDateRange] = useState<{ start: Date | null; end: Date | null }>({ 
    start: null, 
    end: null 
  });
  const [customDateDialogOpen, setCustomDateDialogOpen] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [importDialogOpen, setImportDialogOpen] = useState(false);

  // Use the standardized parseDate from dateUtils
  // It will handle DD/MM/YYYY and other formats

  // Filter orders based on date filter and deletion/cancellation status
  const filteredOrders = useMemo(() => {
    let filtered = orders.filter(order => {
      // Check both English and Portuguese status fields
      const status = (order.status || order.situacaoVenda || '').toLowerCase();
      return status !== 'deletado' && status !== 'cancelado' && status !== 'deleted' && status !== 'cancelled';
    });
    
    const now = new Date();
    const today = startOfDay(now);
    const todayEnd = endOfDay(now);
    
    switch (dateFilter) {
      case 'today':
        filtered = filtered.filter(order => {
          // Check both English and Portuguese date fields
          const orderDate = parseDate(order.createdAt || order.dataVenda);
          return orderDate && isWithinInterval(orderDate, { start: today, end: todayEnd });
        });
        break;
      
      case 'yesterday':
        const yesterdayStart = startOfYesterday();
        const yesterdayEnd = endOfYesterday();
        filtered = filtered.filter(order => {
          const orderDate = parseDate(order.createdAt || order.dataVenda);
          return orderDate && isWithinInterval(orderDate, { start: yesterdayStart, end: yesterdayEnd });
        });
        break;
      
      case 'last7days':
        const sevenDaysAgo = subDays(today, 7);
        filtered = filtered.filter(order => {
          const orderDate = parseDate(order.createdAt || order.dataVenda);
          return orderDate && orderDate >= sevenDaysAgo;
        });
        break;
      
      case 'last30days':
        const thirtyDaysAgo = subDays(today, 30);
        filtered = filtered.filter(order => {
          const orderDate = parseDate(order.createdAt || order.dataVenda);
          return orderDate && orderDate >= thirtyDaysAgo;
        });
        break;
      
      case 'custom':
        if (customDateRange.start && customDateRange.end) {
          filtered = filtered.filter(order => {
            const orderDate = parseDate(order.createdAt || order.dataVenda);
            return orderDate && isWithinInterval(orderDate, { 
              start: startOfDay(customDateRange.start), 
              end: endOfDay(customDateRange.end) 
            });
          });
        }
        break;
    }
    
    return filtered;
  }, [orders, dateFilter, customDateRange]);
  
  const activeOrders = filteredOrders;

  // Calculate metrics
  const metrics = useMemo(() => {
    // Get the current date range based on filter
    let dateRangeStart: Date;
    let dateRangeEnd: Date;
    let previousRangeStart: Date;
    let previousRangeEnd: Date;
    
    const now = new Date();
    const today = startOfDay(now);
    const todayEnd = endOfDay(now);
    
    switch (dateFilter) {
      case 'today':
        dateRangeStart = today;
        dateRangeEnd = todayEnd;
        previousRangeStart = startOfYesterday();
        previousRangeEnd = endOfYesterday();
        break;
        
      case 'yesterday':
        dateRangeStart = startOfYesterday();
        dateRangeEnd = endOfYesterday();
        previousRangeStart = subDays(dateRangeStart, 1);
        previousRangeEnd = subDays(dateRangeEnd, 1);
        break;
        
      case 'last7days':
        dateRangeStart = subDays(today, 7);
        dateRangeEnd = todayEnd;
        previousRangeStart = subDays(dateRangeStart, 7);
        previousRangeEnd = subDays(dateRangeEnd, 7);
        break;
        
      case 'last30days':
        dateRangeStart = subDays(today, 30);
        dateRangeEnd = todayEnd;
        previousRangeStart = subDays(dateRangeStart, 30);
        previousRangeEnd = subDays(dateRangeEnd, 30);
        break;
        
      case 'custom':
        if (customDateRange.start && customDateRange.end) {
          dateRangeStart = startOfDay(customDateRange.start);
          dateRangeEnd = endOfDay(customDateRange.end);
          const daysDiff = Math.ceil((dateRangeEnd.getTime() - dateRangeStart.getTime()) / (1000 * 60 * 60 * 24));
          previousRangeStart = subDays(dateRangeStart, daysDiff);
          previousRangeEnd = subDays(dateRangeEnd, daysDiff);
        } else {
          dateRangeStart = today;
          dateRangeEnd = todayEnd;
          previousRangeStart = startOfYesterday();
          previousRangeEnd = endOfYesterday();
        }
        break;
        
      default:
        dateRangeStart = today;
        dateRangeEnd = todayEnd;
        previousRangeStart = startOfYesterday();
        previousRangeEnd = endOfYesterday();
    }
    
    // Filter all orders by date range (not from activeOrders which is already filtered)
    const allOrdersInRange = orders.filter(order => {
      const status = (order.status || order.situacaoVenda || '').toLowerCase();
      if (status === 'deletado' || status === 'cancelado' || status === 'deleted' || status === 'cancelled') return false;
      const orderDate = parseDate(order.createdAt || order.dataVenda);
      return orderDate && orderDate >= dateRangeStart && orderDate <= dateRangeEnd;
    });
    
    // Previous period orders
    const previousPeriodOrders = orders.filter(order => {
      const status = (order.status || order.situacaoVenda || '').toLowerCase();
      if (status === 'deletado' || status === 'cancelado' || status === 'deleted' || status === 'cancelled') return false;
      const orderDate = parseDate(order.createdAt || order.dataVenda);
      return orderDate && orderDate >= previousRangeStart && orderDate <= previousRangeEnd;
    });
    
    // NEW SALES in the selected period (vendas novas) - already excluding cancelled orders
    const newSalesInPeriod = allOrdersInRange.length;
    const newSalesValueInPeriod = allOrdersInRange.reduce((sum, order) => sum + (order.total || order.valorVenda || 0), 0);
    
    // PAYMENTS RECEIVED in the selected period (only completed orders with valorRecebido)
    const completedOrdersInPeriod = allOrdersInRange.filter(order => 
      order.situacaoVenda?.toLowerCase() === 'completo' && order.valorRecebido > 0
    );
    const paymentsReceivedInPeriod = completedOrdersInPeriod.reduce((sum, order) => sum + (order.valorRecebido || 0), 0);
    
    // Previous period calculations - already excluding cancelled orders
    const previousNewSales = previousPeriodOrders.length;
    const previousNewSalesValue = previousPeriodOrders.reduce((sum, order) => sum + (order.valorVenda || 0), 0);
    const previousPaymentsReceived = previousPeriodOrders
      .filter(order => order.situacaoVenda?.toLowerCase() === 'completo' && order.valorRecebido > 0)
      .reduce((sum, order) => sum + (order.valorRecebido || 0), 0);
    
    // Calculate changes
    const salesChange = previousNewSales > 0 
      ? ((newSalesInPeriod - previousNewSales) / previousNewSales) * 100 
      : 0;
      
    const paymentsChange = previousPaymentsReceived > 0
      ? ((paymentsReceivedInPeriod - previousPaymentsReceived) / previousPaymentsReceived) * 100
      : 0;
    
    // Overall metrics (all time) - excluding cancelled and deleted orders
    const allActiveOrders = orders.filter(order => {
      const status = order.situacaoVenda?.toLowerCase();
      return status !== 'deletado' && status !== 'cancelado';
    });
    const totalRevenue = allActiveOrders.reduce((sum, order) => sum + (order.valorVenda || 0), 0);
    const totalReceived = allActiveOrders.reduce((sum, order) => sum + (order.valorRecebido || 0), 0);
    const completedOrders = allActiveOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'completo').length;
    
    return {
      // Period-specific metrics
      newSalesInPeriod,
      newSalesValueInPeriod,
      paymentsReceivedInPeriod,
      salesChange: Math.round(salesChange),
      paymentsChange: Math.round(paymentsChange),
      
      // Overall metrics
      totalRevenue,
      totalReceived,
      totalOrders: allActiveOrders.length,
      completedOrders,
      pendingOrders: allActiveOrders.filter(o => o.situacaoVenda?.toLowerCase() === 'pendente').length,
      conversionRate: allActiveOrders.length > 0 
        ? Math.round((completedOrders / allActiveOrders.length) * 100)
        : 0,
      averageTicket: newSalesInPeriod > 0
        ? newSalesValueInPeriod / newSalesInPeriod
        : 0,
      collectionRate: newSalesValueInPeriod > 0
        ? Math.round((paymentsReceivedInPeriod / newSalesValueInPeriod) * 100)
        : 0
    };
  }, [orders, dateFilter, customDateRange]);

  // Revenue trend data
  const revenueTrendData = useMemo(() => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const data = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const dateStr = formatDate(date).substring(0, 5); // Get DD/MM from DD/MM/YYYY
      
      const dayOrders = activeOrders.filter(order => {
        const orderDate = parseDate(order.createdAt || order.dataVenda);
        return orderDate && 
               orderDate >= startOfDay(date) && 
               orderDate <= endOfDay(date);
      });

      const revenue = dayOrders.reduce((sum, order) => sum + (order.total || order.valorVenda || 0), 0);
      const received = dayOrders.reduce((sum, order) => sum + (order.paymentReceivedAmount || order.valorRecebido || 0), 0);

      data.push({
        date: dateStr,
        revenue,
        received,
        orders: dayOrders.length
      });
    }
    
    return data;
  }, [activeOrders, timeRange]);

  // Status distribution
  const statusDistribution = useMemo(() => {
    const distribution: Record<string, number> = {};
    
    activeOrders.forEach(order => {
      const status = order.status || order.situacaoVenda || 'Sem Status';
      distribution[status] = (distribution[status] || 0) + 1;
    });

    return Object.entries(distribution).map(([status, count]) => ({
      name: status,
      value: count,
      percentage: Math.round((count / activeOrders.length) * 100)
    }));
  }, [activeOrders]);

  // Top sellers
  const topSellers = useMemo(() => {
    const sellerStats: Record<string, { orders: number; revenue: number }> = {};
    
    activeOrders.forEach(order => {
      const seller = order.vendedorName || order.seller?.name || order.sellerName || 'Sem Vendedor';
      if (!sellerStats[seller]) {
        sellerStats[seller] = { orders: 0, revenue: 0 };
      }
      sellerStats[seller].orders += 1;
      sellerStats[seller].revenue += order.total || order.valorVenda || 0;
    });

    return Object.entries(sellerStats)
      .map(([name, stats]) => ({
        name,
        orders: stats.orders,
        revenue: stats.revenue,
        averageTicket: stats.revenue / stats.orders
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  }, [activeOrders]);

  // Recent activities
  const recentActivities = useMemo(() => {
    return [...activeOrders]
      .sort((a, b) => {
        const dateA = parseDate(a.createdAt || a.dataVenda);
        const dateB = parseDate(b.createdAt || b.dataVenda);
        if (!dateA || !dateB) return 0;
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 10)
      .map(order => ({
        id: order.id || order.orderNumber,
        customer: order.customerName || 'Cliente não informado',
        seller: order.vendedorName || order.seller?.name || order.sellerName || 'Vendedor não atribuído',
        value: order.total || order.valorVenda || 0,
        status: order.status || order.situacaoVenda || 'Pendente',
        date: formatDate(order.createdAt || order.dataVenda)
      }));
  }, [activeOrders]);

  // Performance by day of week
  const performanceByDayOfWeek = useMemo(() => {
    const dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    const dayStats: Record<number, { orders: number; revenue: number }> = {};
    
    // Initialize all days
    for (let i = 0; i < 7; i++) {
      dayStats[i] = { orders: 0, revenue: 0 };
    }

    activeOrders.forEach(order => {
      const orderDate = parseDate(order.createdAt || order.dataVenda);
      if (orderDate) {
        const dayOfWeek = orderDate.getDay();
        dayStats[dayOfWeek].orders += 1;
        dayStats[dayOfWeek].revenue += order.total || order.valorVenda || 0;
      }
    });

    return Object.entries(dayStats).map(([day, stats]) => ({
      day: dayNames[parseInt(day)],
      orders: stats.orders,
      revenue: stats.revenue
    }));
  }, [activeOrders]);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    handleMenuClose();
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export data');
    handleMenuClose();
  };

  const handleDateFilterChange = (newFilter: DateFilterType) => {
    if (newFilter === 'custom') {
      setCustomDateDialogOpen(true);
    } else {
      setDateFilter(newFilter);
    }
  };

  const handleCustomDateApply = () => {
    if (customDateRange.start && customDateRange.end) {
      setDateFilter('custom');
      setCustomDateDialogOpen(false);
    }
  };

  const handleImportSuccess = (results: any) => {
    setImportDialogOpen(false);
    // The OrderDataContext will automatically refresh when new orders are created
    // Show a success message if you have a notification system
    console.log('Import results:', results);
    // Optionally refresh the page to ensure all data is updated
    if (results.created > 0 || results.updated > 0) {
      window.location.reload();
    }
  };

  const getFilterLabel = () => {
    switch (dateFilter) {
      case 'today': return 'Hoje';
      case 'yesterday': return 'Ontem';
      case 'last7days': return 'Últimos 7 dias';
      case 'last30days': return 'Últimos 30 dias';
      case 'custom': 
        if (customDateRange.start && customDateRange.end) {
          return `${formatDate(customDateRange.start)} - ${formatDate(customDateRange.end)}`;
        }
        return 'Personalizado';
      default: return 'Hoje';
    }
  };

  // Loading state
  if (ordersLoading && orders.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Typography>Loading dashboard...</Typography>
      </Box>
    );
  }

  // Error boundary for render
  try {
    return (
      <Box sx={{ 
        bgcolor: '#f0f2f5',
        minHeight: '100vh',
        pb: 3
      }}>
      {/* Modern Header */}
      <Box sx={{ 
        background: 'white',
        borderBottom: '1px solid #e5e7eb',
        px: { xs: 2, sm: 4 },
        py: 2.5,
        mb: 3
      }}>
        <Box sx={{ maxWidth: 1400, mx: 'auto' }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, alignItems: { xs: 'start', sm: 'center' }, justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, color: '#1e293b', mb: 0.5 }}>
                Painel de Controle
              </Typography>
              <Typography variant="body2" sx={{ color: '#64748b', display: 'flex', alignItems: 'center', gap: 1 }}>
                <CalendarTodayIcon sx={{ fontSize: 16 }} />
                {formatDate(new Date())}
              </Typography>
            </Box>
          
            <Stack direction="row" spacing={1} alignItems="center">
              {/* Date Filter Buttons */}
              <ToggleButtonGroup
                value={dateFilter}
                exclusive
                onChange={(_, value) => value && handleDateFilterChange(value)}
                size="small"
                sx={{
                  '& .MuiToggleButton-root': {
                    border: '1px solid #e5e7eb',
                    color: '#64748b',
                    textTransform: 'none',
                    fontSize: '0.875rem',
                    px: 2,
                    py: 0.75,
                    '&.Mui-selected': {
                      bgcolor: '#1e40af',
                      color: 'white',
                      borderColor: '#1e40af',
                      '&:hover': {
                        bgcolor: '#1e3a8a',
                      }
                    },
                    '&:hover': {
                      bgcolor: '#f8fafc',
                    }
                  }
                }}
              >
                <ToggleButton value="today">Hoje</ToggleButton>
                <ToggleButton value="yesterday">Ontem</ToggleButton>
                <ToggleButton value="last7days">7 dias</ToggleButton>
                <ToggleButton value="last30days">30 dias</ToggleButton>
                <ToggleButton value="custom">
                  <DateRangeIcon sx={{ fontSize: 18, mr: 0.5 }} />
                  Personalizado
                </ToggleButton>
              </ToggleButtonGroup>
              
              <Tooltip title="Import Data">
                <IconButton 
                  onClick={() => setImportDialogOpen(true)}
                  size="small"
                  sx={{ 
                    border: '1px solid #e5e7eb',
                    color: '#64748b',
                    '&:hover': { 
                      bgcolor: '#f8fafc',
                      borderColor: '#cbd5e1' 
                    }
                  }}
                >
                  <UploadFileIcon sx={{ fontSize: 20 }} />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Refresh">
                <IconButton 
                  onClick={() => window.location.reload()}
                  size="small"
                  sx={{ 
                    border: '1px solid #e5e7eb',
                    color: '#64748b',
                    '&:hover': { 
                      bgcolor: '#f8fafc',
                      borderColor: '#cbd5e1' 
                    }
                  }}
                >
                  <RefreshIcon sx={{ fontSize: 20 }} />
                </IconButton>
              </Tooltip>
            </Stack>
          </Box>
        </Box>
      </Box>

      {/* Main Content Container */}
      <Box sx={{ px: { xs: 2, sm: 4 }, maxWidth: 1400, mx: 'auto' }}>
        {/* Primary Metrics - Vendas e Recebimentos */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #1e40af 0%, #3730a3 100%)',
              color: 'white',
              borderRadius: 2,
              boxShadow: '0 10px 30px -5px rgba(30, 64, 175, 0.3)',
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, opacity: 0.9 }}>
                      Vendas {getFilterLabel()}
                    </Typography>
                    <Typography variant="h2" sx={{ fontWeight: 800, letterSpacing: '-1px' }}>
                      {metrics.newSalesInPeriod}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1, opacity: 0.8 }}>
                      vendas realizadas
                    </Typography>
                  </Box>
                  <Box sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    borderRadius: 2, 
                    p: 2,
                    backdropFilter: 'blur(10px)'
                  }}>
                    <ShoppingCartIcon sx={{ fontSize: 40 }} />
                  </Box>
                </Box>
                
                <Divider sx={{ bgcolor: 'rgba(255,255,255,0.2)', my: 3 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      R$ {(metrics.newSalesValueInPeriod || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
                      valor total em vendas
                    </Typography>
                  </Box>
                  {metrics.salesChange !== 0 && (
                    <Chip
                      icon={metrics.salesChange > 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                      label={`${Math.abs(metrics.salesChange)}%`}
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        fontWeight: 600,
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  )}
                </Box>
              </CardContent>
              
              {/* Decorative Element */}
              <Box sx={{
                position: 'absolute',
                top: -50,
                right: -50,
                width: 200,
                height: 200,
                borderRadius: '50%',
                bgcolor: 'rgba(255,255,255,0.1)',
              }} />
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card sx={{
              height: '100%',
              background: 'linear-gradient(135deg, #059669 0%, #047857 100%)',
              color: 'white',
              borderRadius: 2,
              boxShadow: '0 10px 30px -5px rgba(5, 150, 105, 0.3)',
              overflow: 'hidden',
              position: 'relative'
            }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, opacity: 0.9 }}>
                      Recebimentos {getFilterLabel()}
                    </Typography>
                    <Typography variant="h2" sx={{ fontWeight: 800, letterSpacing: '-1px' }}>
                      R$ {(metrics.paymentsReceivedInPeriod || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1, opacity: 0.8 }}>
                      valor recebido
                    </Typography>
                  </Box>
                  <Box sx={{ 
                    bgcolor: 'rgba(255,255,255,0.2)', 
                    borderRadius: 2, 
                    p: 2,
                    backdropFilter: 'blur(10px)'
                  }}>
                    <AttachMoneyIcon sx={{ fontSize: 40 }} />
                  </Box>
                </Box>
                
                <Divider sx={{ bgcolor: 'rgba(255,255,255,0.2)', my: 3 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 600 }}>
                      {metrics.collectionRate}%
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
                      taxa de cobrança
                    </Typography>
                  </Box>
                  {metrics.paymentsChange !== 0 && (
                    <Chip
                      icon={metrics.paymentsChange > 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                      label={`${Math.abs(metrics.paymentsChange)}%`}
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        fontWeight: 600,
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  )}
                </Box>
              </CardContent>
              
              {/* Decorative Element */}
              <Box sx={{
                position: 'absolute',
                bottom: -50,
                left: -50,
                width: 200,
                height: 200,
                borderRadius: '50%',
                bgcolor: 'rgba(255,255,255,0.1)',
              }} />
            </Card>
          </Grid>
        </Grid>

        {/* Secondary Metrics */}
        <Grid container spacing={2} sx={{ mb: 4 }}>
          <Grid item xs={6} sm={3}>
            <Card sx={{ 
              p: 2.5,
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ 
                  bgcolor: alpha(COLORS.info, 0.1), 
                  color: COLORS.info,
                  width: 48,
                  height: 48
                }}>
                  <SpeedIcon />
                </Avatar>
                <Box>
                  <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                    Ticket Médio
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                    R$ {(metrics.averageTicket || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card sx={{ 
              p: 2.5,
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ 
                  bgcolor: alpha(COLORS.purple, 0.1), 
                  color: COLORS.purple,
                  width: 48,
                  height: 48
                }}>
                  <TrendingUpIcon />
                </Avatar>
                <Box>
                  <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                    Taxa Conversão
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                    {metrics.conversionRate}%
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card sx={{ 
              p: 2.5,
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ 
                  bgcolor: alpha(COLORS.warning, 0.1), 
                  color: COLORS.warning,
                  width: 48,
                  height: 48
                }}>
                  <AccessTimeIcon />
                </Avatar>
                <Box>
                  <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                    Pendentes
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                    {metrics.pendingOrders}
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card sx={{ 
              p: 2.5,
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ 
                  bgcolor: alpha(COLORS.success, 0.1), 
                  color: COLORS.success,
                  width: 48,
                  height: 48
                }}>
                  <CheckCircleIcon />
                </Avatar>
                <Box>
                  <Typography variant="body2" sx={{ color: '#64748b', fontWeight: 500 }}>
                    Completos
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                    {metrics.completedOrders}
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
        </Grid>


        {/* Charts Row */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* Revenue Trend */}
          <Grid item xs={12} lg={8}>
            <Card sx={{ 
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ p: 3, borderBottom: '1px solid #e5e7eb' }}>
                <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                  Evolução de Vendas e Recebimentos
                </Typography>
                <Typography variant="body2" sx={{ color: '#64748b', mt: 0.5 }}>
                  Acompanhe a progressão diária
                </Typography>
              </Box>
              <Box sx={{ p: 3 }}>
            
                <ResponsiveContainer width="100%" height={350}>
                  <ComposedChart data={revenueTrendData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9ca3af"
                      style={{ fontSize: '0.875rem' }}
                    />
                    <YAxis 
                      yAxisId="left" 
                      stroke="#9ca3af"
                      style={{ fontSize: '0.875rem' }}
                    />
                    <YAxis 
                      yAxisId="right" 
                      orientation="right" 
                      stroke="#9ca3af"
                      style={{ fontSize: '0.875rem' }}
                    />
                    <RechartsTooltip 
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                      formatter={(value: any) => 
                        typeof value === 'number' 
                          ? `R$ ${(value || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                          : value || ''
                      }
                    />
                    <Legend 
                      wrapperStyle={{ paddingTop: '20px' }}
                      iconType="circle"
                    />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      name="Vendas"
                      stroke="#1e40af"
                      fill="#dbeafe"
                      strokeWidth={3}
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="received"
                      name="Recebimentos"
                      stroke="#059669"
                      strokeWidth={3}
                      dot={{ fill: '#059669', r: 5 }}
                      activeDot={{ r: 7 }}
                    />
                    <Bar
                      yAxisId="right"
                      dataKey="orders"
                      name="Quantidade"
                      fill="#e0e7ff"
                      radius={[4, 4, 0, 0]}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </Box>
            </Card>
          </Grid>

          {/* Status Distribution */}
          <Grid item xs={12} lg={4}>
            <Card sx={{ 
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ p: 3, borderBottom: '1px solid #e5e7eb' }}>
                <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                  Status dos Pedidos
                </Typography>
                <Typography variant="body2" sx={{ color: '#64748b', mt: 0.5 }}>
                  Distribuição atual
                </Typography>
              </Box>
              <Box sx={{ p: 3 }}>
                <ResponsiveContainer width="100%" height={350}>
                  <PieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="45%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name}: ${percentage}%`}
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip 
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Legend 
                      verticalAlign="bottom"
                      iconType="circle"
                      wrapperStyle={{ paddingTop: '20px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Card>
          </Grid>
        </Grid>

        {/* Lists Row */}
        <Grid container spacing={3}>
          {/* Top Sellers */}
          <Grid item xs={12} md={6}>
            <Card sx={{ 
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ p: 3, borderBottom: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Avatar sx={{ bgcolor: '#fef3c7', width: 40, height: 40 }}>
                    <EmojiEventsIcon sx={{ color: '#f59e0b', fontSize: 24 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                      Ranking de Vendedores
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      Período: {getFilterLabel()}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box sx={{ p: 2 }}>
            
                <List sx={{ p: 0 }}>
                  {topSellers.slice(0, 5).map((seller, index) => (
                    <ListItem 
                      key={seller.name}
                      sx={{ 
                        px: 2,
                        py: 2,
                        borderBottom: index < topSellers.slice(0, 5).length - 1 ? '1px solid #f3f4f6' : 'none',
                        '&:hover': {
                          bgcolor: '#f8fafc',
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                        <Box sx={{ 
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontWeight: 700,
                          fontSize: '0.875rem',
                          bgcolor: index === 0 ? '#fef3c7' : index === 1 ? '#e0e7ff' : index === 2 ? '#fed7aa' : '#f3f4f6',
                          color: index === 0 ? '#d97706' : index === 1 ? '#4338ca' : index === 2 ? '#ea580c' : '#6b7280',
                        }}>
                          {index + 1}
                        </Box>
                        
                        <Box sx={{ flex: 1 }}>
                          <Typography sx={{ fontWeight: 600, fontSize: '0.875rem', color: '#1e293b' }}>
                            {seller.name}
                          </Typography>
                          <Typography sx={{ fontSize: '0.75rem', color: '#64748b' }}>
                            {seller.orders} vendas • Ticket: R$ {(seller.averageTicket || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                          </Typography>
                        </Box>
                        
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography sx={{ fontWeight: 700, fontSize: '0.875rem', color: '#059669' }}>
                            R$ {(seller.revenue || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                            <Box sx={{ 
                              flex: 1,
                              height: 4,
                              bgcolor: '#e5e7eb',
                              borderRadius: 2,
                              overflow: 'hidden'
                            }}>
                              <Box sx={{ 
                                width: `${(seller.revenue / topSellers[0].revenue) * 100}%`,
                                height: '100%',
                                bgcolor: '#10b981',
                                borderRadius: 2
                              }} />
                            </Box>
                            <Typography sx={{ fontSize: '0.625rem', color: '#9ca3af', minWidth: 35 }}>
                              {Math.round((seller.revenue / topSellers[0].revenue) * 100)}%
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </ListItem>
                  ))}
                </List>
              </Box>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12} md={6}>
            <Card sx={{ 
              height: '100%',
              background: 'white',
              borderRadius: 2,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
            }}>
              <Box sx={{ p: 3, borderBottom: '1px solid #e5e7eb', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Avatar sx={{ bgcolor: '#dbeafe', width: 40, height: 40 }}>
                    <TimelineIcon sx={{ color: '#1e40af', fontSize: 24 }} />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: '#1e293b' }}>
                      Atividades Recentes
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#64748b' }}>
                      Últimas {recentActivities.length} transações
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box sx={{ p: 0 }}>
            
                <List sx={{ 
                  p: 0, 
                  maxHeight: 400, 
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: 6,
                  },
                  '&::-webkit-scrollbar-track': {
                    bgcolor: '#f3f4f6',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    bgcolor: '#d1d5db',
                    borderRadius: 3,
                    '&:hover': {
                      bgcolor: '#9ca3af',
                    },
                  },
                }}>
                  {recentActivities.slice(0, 10).map((activity, index) => (
                    <ListItem 
                      key={activity.id}
                      sx={{ 
                        px: 2,
                        py: 2,
                        borderBottom: '1px solid #f3f4f6',
                        '&:hover': {
                          bgcolor: '#f8fafc',
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                        <Avatar sx={{ 
                          width: 36,
                          height: 36,
                          bgcolor: '#e0e7ff',
                          color: '#4338ca',
                          fontSize: '0.875rem',
                          fontWeight: 600
                        }}>
                          {activity.customer.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
                        </Avatar>
                        
                        <Box sx={{ flex: 1 }}>
                          <Typography sx={{ fontWeight: 600, fontSize: '0.875rem', color: '#1e293b' }}>
                            {activity.customer}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.25 }}>
                            <Typography sx={{ fontSize: '0.75rem', color: '#64748b' }}>
                              {activity.date} • {activity.seller}
                            </Typography>
                            <Chip
                              label={activity.status || 'Pendente'}
                              size="small"
                              sx={{ 
                                height: 18,
                                fontSize: '0.625rem',
                                fontWeight: 600,
                                bgcolor: activity.status?.toLowerCase() === 'completo' ? '#d1fae5' : 
                                        activity.status?.toLowerCase() === 'pendente' ? '#fef3c7' : '#f3f4f6',
                                color: activity.status?.toLowerCase() === 'completo' ? '#065f46' : 
                                       activity.status?.toLowerCase() === 'pendente' ? '#92400e' : '#4b5563',
                                '& .MuiChip-label': { px: 1 }
                              }}
                            />
                          </Box>
                        </Box>
                        
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography sx={{ fontWeight: 700, fontSize: '0.875rem', color: '#1e293b' }}>
                            R$ {(activity.value || 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                          </Typography>
                          <Typography sx={{ fontSize: '0.625rem', color: '#9ca3af' }}>
                            #{activity.id}
                          </Typography>
                        </Box>
                      </Box>
                    </ListItem>
                  ))}
                </List>
              </Box>
            </Card>
          </Grid>

        </Grid>
      </Box>

      {/* Custom Date Range Dialog */}
      <Dialog 
        open={customDateDialogOpen} 
        onClose={() => setCustomDateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.2)',
          }
        }}
      >
        <DialogTitle sx={{ 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          fontWeight: 700
        }}>
          Selecionar Período Personalizado
        </DialogTitle>
        <DialogContent sx={{ mt: 3 }}>
          <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
            <Stack spacing={3}>
              <DatePicker
                label="Data inicial"
                value={customDateRange.start}
                onChange={(date) => setCustomDateRange(prev => ({ ...prev, start: date }))}
                format="dd/MM/yyyy"
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: "outlined"
                  }
                }}
              />
              <DatePicker
                label="Data final"
                value={customDateRange.end}
                onChange={(date) => setCustomDateRange(prev => ({ ...prev, end: date }))}
                format="dd/MM/yyyy"
                minDate={customDateRange.start || undefined}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: "outlined"
                  }
                }}
              />
            </Stack>
          </LocalizationProvider>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button 
            onClick={() => setCustomDateDialogOpen(false)}
            sx={{ textTransform: 'none' }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleCustomDateApply}
            variant="contained"
            disabled={!customDateRange.start || !customDateRange.end}
            sx={{ 
              textTransform: 'none',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)',
              }
            }}
          >
            Aplicar Filtro
          </Button>
        </DialogActions>
      </Dialog>

      {/* Smart Import Dialog */}
      <SmartImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImportSuccess={handleImportSuccess}
      />
    </Box>
  );
  } catch (error: any) {
    console.error('[AdminAnalyticsDashboard] Render error:', error);
    // Fallback to simple dashboard on error
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>Dashboard</Typography>
        <Typography color="error">An error occurred while rendering the dashboard.</Typography>
        <Typography variant="body2" sx={{ mt: 2 }}>
          Orders loaded: {orders.length}
        </Typography>
        <Button 
          variant="contained" 
          sx={{ mt: 2 }}
          onClick={() => window.location.reload()}
        >
          Reload Page
        </Button>
      </Box>
    );
  }
};

export default AdminAnalyticsDashboard;