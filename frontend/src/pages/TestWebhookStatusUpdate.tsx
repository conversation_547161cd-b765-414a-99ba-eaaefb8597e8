import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import api from '../services/api';
import { useOrderData } from '../contexts/OrderDataContext';

export default function TestWebhookStatusUpdate() {
  const { orders, fetchOrders } = useOrderData();
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [selectedEvent, setSelectedEvent] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState('');

  const events = [
    'DELIVERED',
    'IN_TRANSIT',
    'INCORRECT_ADDRESS',
    'DELIVERY_FAILED',
    'FRUSTRATED',
    'IN_PREPARATION',
    'CANCELLED',
    'RETURNED',
    'CLIENT_RECEIVE_CONFIRMATION',
  ];

  const handleTestUpdate = async () => {
    if (!selectedOrderId || !selectedEvent) {
      setError('Por favor, selecione um pedido e um evento');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await api.post('/webhooks/test-status-update', {
        orderId: selectedOrderId,
        event: selectedEvent,
        description: description || undefined,
      });

      setResult(response.data);
      
      // Refresh orders after 2 seconds to show the update
      setTimeout(() => {
        fetchOrders();
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao atualizar status');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Teste de Atualização de Status via Webhook
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Esta página permite testar a atualização automática de status via webhook.
        Selecione um pedido e um evento para simular uma atualização de webhook.
        Observe como o status é atualizado automaticamente na barra lateral e na lista de pedidos.
      </Alert>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <FormControl fullWidth>
            <InputLabel>Pedido</InputLabel>
            <Select
              value={selectedOrderId}
              onChange={(e) => setSelectedOrderId(e.target.value)}
              label="Pedido"
            >
              {orders.slice(0, 20).map((order) => (
                <MenuItem key={order.id} value={order.id}>
                  {order.customerName} - {order.situacao} (ID: {order.id.slice(0, 8)}...)
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Evento</InputLabel>
            <Select
              value={selectedEvent}
              onChange={(e) => setSelectedEvent(e.target.value)}
              label="Evento"
            >
              {events.map((event) => (
                <MenuItem key={event} value={event}>
                  {event}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            label="Descrição (opcional)"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            fullWidth
            helperText="Ex: Objeto não entregue - endereço incorreto"
          />

          <Button
            variant="contained"
            onClick={handleTestUpdate}
            disabled={loading || !selectedOrderId || !selectedEvent}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Testar Atualização'}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}

        {result && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="subtitle2">Atualização bem-sucedida!</Typography>
            <Typography variant="body2">
              Status anterior: {result.previousStatus} → Novo status: {result.newStatus}
            </Typography>
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Aguarde alguns segundos para ver a atualização na interface...
            </Typography>
          </Alert>
        )}
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Como funciona:
        </Typography>
        <Typography variant="body2" paragraph>
          1. Quando um webhook é recebido, o sistema mapeia o evento para um status interno usando o mapeamento configurado
        </Typography>
        <Typography variant="body2" paragraph>
          2. O pedido é atualizado no banco de dados
        </Typography>
        <Typography variant="body2" paragraph>
          3. A página de pedidos atualiza automaticamente a cada 10 segundos
        </Typography>
        <Typography variant="body2" paragraph>
          4. A barra lateral reflete as mudanças nos contadores de status
        </Typography>
      </Paper>
    </Box>
  );
}