import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Typography,
  Alert,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  Card,
  CardContent,
  Divider,
  Grid,
  InputAdornment,
  LinearProgress,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import WebhookIcon from '@mui/icons-material/Webhook';
import LinkIcon from '@mui/icons-material/Link';
import InfoIcon from '@mui/icons-material/Info';
import SearchIcon from '@mui/icons-material/Search';
import CleaningServicesIcon from '@mui/icons-material/CleaningServices';
import DeleteIcon from '@mui/icons-material/Delete';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import Checkbox from '@mui/material/Checkbox';
import api from '../services/api';

interface WebhookMapping {
  id: string;
  payloadKey: string;
  entityColumn: string | null;
  entityType: string;
  dataType: string;
  sampleValue: string;
  isActive: boolean;
  description?: string;
}

export default function WebhookMappings() {
  const [mappings, setMappings] = useState<WebhookMapping[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState({ entityColumn: '', description: '' });
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [availableColumns, setAvailableColumns] = useState<any>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'mapped' | 'unmapped'>('all');
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);

  useEffect(() => {
    fetchMappings();
    fetchAvailableColumns();
  }, []);

  const fetchMappings = async () => {
    try {
      const response = await api.get('/webhooks/mappings');
      console.log('Fetched mappings:', response.data.length, 'items');
      setMappings(response.data);
    } catch (error: any) {
      console.error('Error fetching mappings:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableColumns = async () => {
    try {
      const response = await api.get('/webhooks/mappings/available-columns');
      console.log('Available columns:', response.data);
      setAvailableColumns(response.data);
    } catch (error: any) {
      console.error('Error fetching available columns:', error);
    }
  };

  const handleEdit = (mapping: WebhookMapping) => {
    setEditingId(mapping.id);
    // Suggest a mapping if not already mapped
    const suggestion = !mapping.entityColumn ? suggestMapping(mapping.payloadKey) : mapping.entityColumn;
    setEditForm({
      entityColumn: suggestion || mapping.entityColumn || '',
      description: mapping.description || '',
    });
  };

  const handleSave = async () => {
    if (!editingId) return;

    try {
      console.log('Saving mapping:', { id: editingId, data: editForm });
      await api.put(`/webhooks/mappings/${editingId}`, editForm);
      await fetchMappings();
      setEditingId(null);
      setEditForm({ entityColumn: '', description: '' });
      console.log('Mapping saved successfully');
    } catch (error: any) {
      console.error('Error updating mapping:', error);
      alert('Erro ao salvar mapeamento. Verifique o console para mais detalhes.');
    }
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditForm({ entityColumn: '', description: '' });
  };

  const handleCleanup = async () => {
    if (!window.confirm('Isso vai limpar campos de exemplo que contêm objetos JSON complexos. Deseja continuar?')) {
      return;
    }
    
    setCleanupLoading(true);
    try {
      const response = await api.post('/webhooks/mappings/cleanup-json-values');
      const { updated, deleted } = response.data;
      
      alert(`Limpeza concluída!\n${updated} campos atualizados\n${deleted} campos removidos`);
      
      // Reload mappings
      await fetchMappings();
    } catch (error: any) {
      console.error('Error cleaning up mappings:', error);
      alert('Erro ao limpar mapeamentos');
    } finally {
      setCleanupLoading(false);
    }
  };

  const handleDelete = async (id: string, fieldName: string) => {
    if (!window.confirm(`Tem certeza que deseja deletar o campo "${fieldName}"?`)) {
      return;
    }
    
    try {
      await api.delete(`/webhooks/mappings/${id}`);
      await fetchMappings();
    } catch (error: any) {
      console.error('Error deleting mapping:', error);
      alert('Erro ao deletar mapeamento');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedIds.length === 0) {
      alert('Selecione pelo menos um campo para deletar');
      return;
    }

    const selectedMappings = mappings.filter(m => selectedIds.includes(m.id));
    const fieldNames = selectedMappings.map(m => m.payloadKey).join(', ');
    
    if (!window.confirm(`Tem certeza que deseja deletar ${selectedIds.length} campos?\n\nCampos: ${fieldNames}`)) {
      return;
    }

    setBulkDeleteLoading(true);
    try {
      await api.post('/webhooks/mappings/bulk-delete', { ids: selectedIds });
      
      alert(`${selectedIds.length} campos deletados com sucesso!`);
      setSelectedIds([]);
      await fetchMappings();
    } catch (error: any) {
      console.error('Error bulk deleting mappings:', error);
      alert('Erro ao deletar mapeamentos em massa');
    } finally {
      setBulkDeleteLoading(false);
    }
  };

  const handleSelectAll = () => {
    if (selectedIds.length === filteredMappings.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredMappings.map(m => m.id));
    }
  };

  const handleSelectOne = (id: string) => {
    if (selectedIds.includes(id)) {
      setSelectedIds(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      setSelectedIds([...selectedIds, id]);
    }
  };

  const getDataTypeColor = (dataType: string) => {
    const colors: Record<string, any> = {
      string: 'default',
      number: 'primary',
      date: 'secondary',
      boolean: 'success',
      json: 'warning',
    };
    return colors[dataType] || 'default';
  };

  const getDataTypeIcon = (dataType: string) => {
    const icons: Record<string, string> = {
      string: 'Abc',
      number: '123',
      date: '📅',
      boolean: '✓/✗',
      json: '{ }',
    };
    return icons[dataType] || '?';
  };

  const formatFieldName = (fieldName: string) => {
    // Format nested field names for better readability
    const parts = fieldName.split('.');
    if (parts.length === 1) return fieldName;
    
    const lastPart = parts[parts.length - 1];
    const parentParts = parts.slice(0, -1).join(' › ');
    return (
      <Box>
        <Typography variant="body2" component="span" sx={{ color: 'text.secondary', fontSize: '0.85rem' }}>
          {parentParts} ›
        </Typography>
        <Typography variant="body2" component="span" sx={{ fontWeight: 600, ml: 0.5 }}>
          {lastPart}
        </Typography>
      </Box>
    );
  };

  const commonMappings: Record<string, string> = {
    // Tracking fields
    codigo: 'tracking.code',
    codigoRastreio: 'tracking.code',
    tracking: 'tracking.code',
    trackingCode: 'tracking.code',
    'tracking.code': 'tracking.code',
    'shipment.tracking': 'tracking.code',
    
    // Status fields
    situacao: 'status',
    status: 'status',
    'order.status': 'status',
    'sale.status': 'status',
    estadoPedido: 'status',
    
    // Customer name fields
    cliente: 'customerName',
    nomeCliente: 'customerName',
    nome: 'customerName',
    'customer.name': 'customerName',
    'cliente.nome': 'customerName',
    customerName: 'customerName',
    
    // Phone fields
    telefone: 'customerPhone',
    fone: 'customerPhone',
    celular: 'customerPhone',
    'customer.phone': 'customerPhone',
    'cliente.telefone': 'customerPhone',
    'customer.mobile': 'customerPhone',
    
    // CPF fields
    cpf: 'customerCPF',
    documento: 'customerCPF',
    'customer.cpf': 'customerCPF',
    'cliente.cpf': 'customerCPF',
    'customer.document': 'customerCPF',
    
    // Value/Total fields
    valor: 'total',
    valorTotal: 'total',
    total: 'total',
    'order.total': 'total',
    'sale.amount': 'total',
    'sale.total': 'total',
    
    // Order number fields
    pedido: 'orderNumber',
    numeroPedido: 'orderNumber',
    'order.number': 'orderNumber',
    'sale.id': 'orderNumber',
    orderId: 'orderNumber',
    
    // Address fields
    endereco: 'fullAddress',
    'address.full': 'fullAddress',
    'endereco.completo': 'fullAddress',
    
    // Specific address components
    'address.street': 'address.street',
    'endereco.rua': 'address.street',
    'address.number': 'address.streetNumber',
    'endereco.numero': 'address.streetNumber',
    'address.neighborhood': 'address.neighborhood',
    'endereco.bairro': 'address.neighborhood',
    'address.city': 'address.city',
    'endereco.cidade': 'address.city',
    'address.state': 'address.state',
    'endereco.estado': 'address.state',
    'address.zipcode': 'address.zipCode',
    'address.cep': 'address.zipCode',
    'endereco.cep': 'address.zipCode',
  };

  const suggestMapping = (payloadKey: string): string => {
    // Try exact match first
    if (commonMappings[payloadKey]) {
      return commonMappings[payloadKey];
    }
    
    // Try lowercase match
    const lowerKey = payloadKey.toLowerCase();
    for (const [key, value] of Object.entries(commonMappings)) {
      if (key.toLowerCase() === lowerKey) {
        return value;
      }
    }
    
    // Try partial match
    for (const [key, value] of Object.entries(commonMappings)) {
      if (lowerKey.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerKey)) {
        return value;
      }
    }
    
    return '';
  };

  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(label);
    setTimeout(() => setCopiedText(null), 2000);
  };

  const filteredMappings = mappings.filter((mapping) => {
    const matchesSearch = searchTerm === '' || 
      mapping.payloadKey.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (mapping.entityColumn && mapping.entityColumn.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (mapping.description && mapping.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = filterStatus === 'all' ||
      (filterStatus === 'mapped' && mapping.entityColumn) ||
      (filterStatus === 'unmapped' && !mapping.entityColumn);
    
    return matchesSearch && matchesFilter;
  });

  // Debug log
  console.log('Filter status:', filterStatus, 'Search term:', searchTerm, 'Total:', mappings.length, 'Filtered:', filteredMappings.length);

  const mappedCount = mappings.filter(m => m.entityColumn).length;
  const unmappedCount = mappings.filter(m => !m.entityColumn).length;

  if (loading) return (
    <Box sx={{ p: 3 }}>
      <LinearProgress />
      <Typography sx={{ mt: 2 }}>Carregando mapeamentos...</Typography>
    </Box>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <WebhookIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
        <Box>
          <Typography variant="h4">
            Mapeamento de Webhooks
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Configure como os campos do webhook são mapeados para seu banco de dados
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary">
                {mappings.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Campos Descobertos
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="success.main">
                {mappedCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Campos Mapeados
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="warning.main">
                {unmappedCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pendentes de Mapeamento
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
          📧 Mapeamento de Vendedor por Email
        </Typography>
        <Typography variant="body2">
          Ao mapear o campo "Email do Vendedor", o sistema automaticamente buscará um vendedor cadastrado com o email exato.
          Se nenhum vendedor for encontrado com aquele email, o campo ficará em branco.
          Certifique-se de que os emails no webhook correspondam exatamente aos emails dos vendedores cadastrados no sistema.
        </Typography>
      </Alert>

      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
          💡 Mapeamento Multi-Campo Suportado!
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Você pode mapear múltiplos campos do webhook para o mesmo campo do sistema. 
          O sistema usará o primeiro valor não vazio encontrado. Por exemplo, se o webhook 
          envia "cliente" em alguns pedidos e "nomeCliente" em outros, mapeie ambos para "Nome do Cliente".
        </Typography>
      </Alert>

      {mappedCount > 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Campos já mapeados:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {mappings
              .filter(m => m.entityColumn)
              .map(m => {
                const allFields = [
                  ...(availableColumns.orderFields || []),
                  ...(availableColumns.sellerFields || []),
                  ...(availableColumns.addressFields || []),
                  ...(availableColumns.trackingFields || []),
                  ...(availableColumns.productFields || []),
                  ...(availableColumns.kitFields || []),
                  ...(availableColumns.customFields || [])
                ];
                const field = allFields.find(f => f.value === m.entityColumn);
                return (
                  <Chip
                    key={m.id}
                    label={`${m.payloadKey} → ${field?.label || m.entityColumn}`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                );
              })}
          </Box>
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3, bgcolor: 'background.default', border: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <LinkIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">
            URLs do Webhook
          </Typography>
        </Box>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>✅ URL do Backend:</strong> https://zencash-production.up.railway.app
        </Alert>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            URL Principal (Produção):
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body1" sx={{ fontFamily: 'monospace', bgcolor: 'white', p: 1, borderRadius: 1, flex: 1 }}>
              https://zencash-production.up.railway.app/api/v1/webhooks/shipments
            </Typography>
            <Tooltip title={copiedText === 'production' ? 'Copiado!' : 'Copiar URL'}>
              <IconButton 
                size="small" 
                onClick={() => handleCopy('https://zencash-production.up.railway.app/api/v1/webhooks/shipments', 'production')}
              >
                <ContentCopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            URL de Teste (descobre campos automaticamente):
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body1" sx={{ fontFamily: 'monospace', bgcolor: 'white', p: 1, borderRadius: 1, flex: 1 }}>
              https://zencash-production.up.railway.app/api/v1/webhooks/test
            </Typography>
            <Tooltip title={copiedText === 'test' ? 'Copiado!' : 'Copiar URL'}>
              <IconButton 
                size="small" 
                onClick={() => handleCopy('https://zencash-production.up.railway.app/api/v1/webhooks/test', 'test')}
              >
                <ContentCopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            URL de Validação Simples (retorna "ok"):
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body1" sx={{ fontFamily: 'monospace', bgcolor: 'white', p: 1, borderRadius: 1, flex: 1 }}>
              https://zencash-production.up.railway.app/api/v1/webhooks/validate
            </Typography>
            <Tooltip title={copiedText === 'validate' ? 'Copiado!' : 'Copiar URL'}>
              <IconButton 
                size="small" 
                onClick={() => handleCopy('https://zencash-production.up.railway.app/api/v1/webhooks/validate', 'validate')}
              >
                <ContentCopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">
            Secret Key (Opcional - apenas se solicitado):
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body1" sx={{ fontFamily: 'monospace', bgcolor: 'white', p: 1, borderRadius: 1, flex: 1 }}>
              98405ec1ac97c083ff144b1d15f09d42a0fe2c1f8e98ebec73c26b648ce3e899
            </Typography>
            <Tooltip title={copiedText === 'secret' ? 'Copiado!' : 'Copiar Secret'}>
              <IconButton 
                size="small" 
                onClick={() => handleCopy('98405ec1ac97c083ff144b1d15f09d42a0fe2c1f8e98ebec73c26b648ce3e899', 'secret')}
              >
                <ContentCopyIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            ⚠️ A secret key NÃO é necessária por padrão. Use apenas se a plataforma externa exigir autenticação HMAC-SHA256.
          </Typography>
        </Box>
        <Alert severity="info" sx={{ mt: 2 }}>
          <strong>Como funciona:</strong><br />
          1. Use a URL de teste primeiro para descobrir os campos<br />
          2. Configure o mapeamento dos campos nesta página<br />
          3. Depois use a URL de produção para salvar os dados<br />
          4. Os dados aparecerão em Pedidos/Orders
        </Alert>
      </Paper>

      <Paper sx={{ mb: 3 }}>
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                size="small"
                placeholder="Buscar por campo, mapeamento ou descrição..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <Chip
                  label="Todos"
                  color={filterStatus === 'all' ? 'primary' : 'default'}
                  onClick={() => setFilterStatus('all')}
                  clickable
                />
                <Chip
                  label={`Mapeados (${mappedCount})`}
                  color={filterStatus === 'mapped' ? 'success' : 'default'}
                  onClick={() => setFilterStatus('mapped')}
                  clickable
                />
                <Chip
                  label={`Não Mapeados (${unmappedCount})`}
                  color={filterStatus === 'unmapped' ? 'warning' : 'default'}
                  onClick={() => setFilterStatus('unmapped')}
                  clickable
                />
                <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
                {selectedIds.length > 0 && (
                  <>
                    <Tooltip title="Deletar campos selecionados">
                      <Button
                        size="small"
                        variant="contained"
                        color="error"
                        startIcon={<DeleteSweepIcon />}
                        onClick={handleBulkDelete}
                        disabled={bulkDeleteLoading}
                      >
                        {bulkDeleteLoading ? 'Deletando...' : `Deletar (${selectedIds.length})`}
                      </Button>
                    </Tooltip>
                    <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
                  </>
                )}
                <Tooltip title="Limpar campos com JSON complexo">
                  <Button
                    size="small"
                    variant="outlined"
                    color="warning"
                    startIcon={<CleaningServicesIcon />}
                    onClick={handleCleanup}
                    disabled={cleanupLoading}
                  >
                    {cleanupLoading ? 'Limpando...' : 'Limpar JSON'}
                  </Button>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      <TableContainer component={Paper} sx={{ boxShadow: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: 'primary.dark' }}>
              <TableCell padding="checkbox" sx={{ color: 'white' }}>
                <Checkbox
                  color="default"
                  indeterminate={selectedIds.length > 0 && selectedIds.length < filteredMappings.length}
                  checked={filteredMappings.length > 0 && selectedIds.length === filteredMappings.length}
                  onChange={handleSelectAll}
                  sx={{ color: 'white' }}
                />
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Campo do Webhook</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Tipo</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Exemplo</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Mapeado para</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Descrição</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem' }}>Status</TableCell>
              <TableCell sx={{ fontWeight: 600, color: 'white', fontSize: '0.95rem', textAlign: 'center' }}>Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredMappings.map((mapping) => (
              <TableRow 
                key={mapping.id} 
                sx={{ 
                  '&:hover': { 
                    bgcolor: selectedIds.includes(mapping.id) ? 'action.selected' : (mapping.entityColumn ? 'success.100' : 'grey.50'),
                    transition: 'background-color 0.2s ease'
                  },
                  bgcolor: selectedIds.includes(mapping.id) ? 'action.hover' : (mapping.entityColumn ? 'success.50' : 'transparent'),
                  opacity: mapping.entityColumn ? 1 : 0.85,
                  transition: 'all 0.2s ease',
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    checked={selectedIds.includes(mapping.id)}
                    onChange={() => handleSelectOne(mapping.id)}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    <Chip
                      label={getDataTypeIcon(mapping.dataType)}
                      size="small"
                      sx={{ 
                        bgcolor: getDataTypeColor(mapping.dataType) === 'default' ? 'grey.200' : `${getDataTypeColor(mapping.dataType)}.100`,
                        color: getDataTypeColor(mapping.dataType) === 'default' ? 'text.primary' : `${getDataTypeColor(mapping.dataType)}.dark`,
                        fontFamily: 'monospace',
                        fontSize: '0.8rem',
                        fontWeight: 600,
                        height: 24,
                      }}
                    />
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      {formatFieldName(mapping.payloadKey)}
                      {mapping.payloadKey.includes('.') && (
                        <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.7rem', mt: 0.25 }}>
                          Campo aninhado
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={mapping.dataType}
                    size="small"
                    color={getDataTypeColor(mapping.dataType)}
                    sx={{ fontWeight: 500 }}
                  />
                </TableCell>
                <TableCell>
                  <Tooltip title={mapping.sampleValue} arrow placement="left">
                    <Box 
                      sx={{ 
                        fontFamily: 'monospace',
                        fontSize: '0.85rem',
                        display: 'block',
                        maxWidth: 200,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        bgcolor: 'grey.50',
                        border: '1px solid',
                        borderColor: 'grey.300',
                        px: 1.5,
                        py: 0.75,
                        borderRadius: 1,
                        cursor: 'help',
                        '&:hover': {
                          bgcolor: 'grey.100',
                          borderColor: 'grey.400',
                        }
                      }}
                    >
                      {mapping.sampleValue}
                    </Box>
                  </Tooltip>
                </TableCell>
                <TableCell>
                  {editingId === mapping.id ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <select
                        value={editForm.entityColumn || ''}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setEditForm(prev => ({ ...prev, entityColumn: newValue }));
                        }}
                        style={{
                          flex: 1,
                          padding: '10px 12px',
                          fontSize: '14px',
                          border: '2px solid #e0e0e0',
                          borderRadius: '8px',
                          backgroundColor: 'white',
                          color: '#333',
                          fontFamily: 'inherit',
                          outline: 'none',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = '#1976d2';
                          e.target.style.boxShadow = '0 0 0 3px rgba(25, 118, 210, 0.1)';
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = '#e0e0e0';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        <option value="" style={{ color: '#999' }}>Selecione uma coluna...</option>
                        
                        {availableColumns.orderFields && availableColumns.orderFields.length > 0 && (
                          <>
                            <optgroup label="───── 📋 Campos do Pedido ─────" style={{ fontWeight: 'bold', color: '#1976d2' }}>
                              {availableColumns.orderFields.map((field: any) => {
                                // Check if this field is already mapped by another webhook field
                                const mappingCount = mappings.filter(m => 
                                  m.entityColumn === field.value
                                ).length;
                                const isThisFieldMapped = mappings.some(m => 
                                  m.entityColumn === field.value && 
                                  m.id === editingId
                                );
                                return (
                                  <option 
                                    key={field.value} 
                                    value={field.value} 
                                    style={{ 
                                      padding: '8px',
                                      color: '#333',
                                      fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                    }}
                                  >
                                    {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                  </option>
                                );
                              })}
                            </optgroup>
                          </>
                        )}
                        
                        {availableColumns.sellerFields && availableColumns.sellerFields.length > 0 && (
                          <optgroup label="───── 👤 Campos de Vendedor ─────" style={{ fontWeight: 'bold', color: '#00796b' }}>
                            {availableColumns.sellerFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                        
                        {availableColumns.addressFields && availableColumns.addressFields.length > 0 && (
                          <optgroup label="───── 📍 Campos de Endereço ─────" style={{ fontWeight: 'bold', color: '#2e7d32' }}>
                            {availableColumns.addressFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                        
                        {availableColumns.trackingFields && availableColumns.trackingFields.length > 0 && (
                          <optgroup label="───── 🚚 Campos de Rastreamento ─────" style={{ fontWeight: 'bold', color: '#ed6c02' }}>
                            {availableColumns.trackingFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                        
                        {availableColumns.productFields && availableColumns.productFields.length > 0 && (
                          <optgroup label="───── 🛍️ Campos de Produto ─────" style={{ fontWeight: 'bold', color: '#9c27b0' }}>
                            {console.log('Rendering product fields:', availableColumns.productFields)}
                            {availableColumns.productFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                        
                        {availableColumns.kitFields && availableColumns.kitFields.length > 0 && (
                          <optgroup label="───── 🎁 Campos de Kit/Oferta ─────" style={{ fontWeight: 'bold', color: '#ff5722' }}>
                            {availableColumns.kitFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                        
                        {availableColumns.customFields && availableColumns.customFields.length > 0 && (
                          <optgroup label="───── ⚙️ Opções Especiais ─────" style={{ fontWeight: 'bold', color: '#9c27b0' }}>
                            {availableColumns.customFields.map((field: any) => {
                              const mappingCount = mappings.filter(m => 
                                m.entityColumn === field.value
                              ).length;
                              const isThisFieldMapped = mappings.some(m => 
                                m.entityColumn === field.value && 
                                m.id === editingId
                              );
                              return (
                                <option 
                                  key={field.value} 
                                  value={field.value} 
                                  style={{ 
                                    padding: '8px',
                                    color: '#333',
                                    fontWeight: mappingCount > 0 && !isThisFieldMapped ? 'bold' : 'normal'
                                  }}
                                >
                                  {field.label} {mappingCount > 0 && !isThisFieldMapped ? `(${mappingCount} campo${mappingCount > 1 ? 's' : ''} mapeado${mappingCount > 1 ? 's' : ''})` : ''}
                                </option>
                              );
                            })}
                          </optgroup>
                        )}
                      </select>
                      
                      {/* Show suggested mapping if available */}
                      {suggestMapping(mapping.payloadKey) && editForm.entityColumn !== suggestMapping(mapping.payloadKey) && (
                        <Tooltip title={`Sugestão: ${suggestMapping(mapping.payloadKey)}`}>
                          <Chip
                            label="Sugestão"
                            size="small"
                            color="info"
                            variant="outlined"
                            onClick={() => setEditForm({ ...editForm, entityColumn: suggestMapping(mapping.payloadKey) })}
                            sx={{ cursor: 'pointer', fontSize: '0.75rem' }}
                          />
                        </Tooltip>
                      )}
                    </Box>
                  ) : (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {mapping.entityColumn ? (
                        <>
                          <CheckIcon sx={{ color: 'success.main', fontSize: 18 }} />
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.dark' }}>
                              {(() => {
                                const allFields = [
                                  ...(availableColumns.orderFields || []),
                                  ...(availableColumns.sellerFields || []),
                                  ...(availableColumns.addressFields || []),
                                  ...(availableColumns.trackingFields || []),
                                  ...(availableColumns.productFields || []),
                                  ...(availableColumns.kitFields || []),
                                  ...(availableColumns.customFields || [])
                                ];
                                const field = allFields.find(f => f.value === mapping.entityColumn);
                                return field?.label || mapping.entityColumn;
                              })()}
                            </Typography>
                            <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.7rem' }}>
                              {mapping.entityColumn}
                            </Typography>
                            {(() => {
                              const otherMappings = mappings.filter(m => 
                                m.entityColumn === mapping.entityColumn && 
                                m.id !== mapping.id
                              );
                              if (otherMappings.length > 0) {
                                return (
                                  <Chip
                                    label={`+${otherMappings.length} campo${otherMappings.length > 1 ? 's' : ''}`}
                                    size="small"
                                    sx={{ 
                                      height: 16, 
                                      fontSize: '0.65rem',
                                      bgcolor: 'info.light',
                                      color: 'info.dark',
                                      ml: 0.5
                                    }}
                                  />
                                );
                              }
                              return null;
                            })()}
                          </Box>
                        </>
                      ) : (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <InfoIcon sx={{ color: 'warning.main', fontSize: 16 }} />
                          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                            Clique em editar para mapear
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  )}
                </TableCell>
                <TableCell>
                  {editingId === mapping.id ? (
                    <TextField
                      value={editForm.description}
                      onChange={(e) => setEditForm({ ...editForm, description: e.target.value })}
                      size="small"
                      placeholder="Descrição opcional"
                    />
                  ) : (
                    <Typography variant="caption">
                      {mapping.description || '-'}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    icon={mapping.entityColumn ? <CheckIcon /> : <InfoIcon />}
                    label={mapping.entityColumn ? 'Mapeado' : 'Pendente'}
                    size="small"
                    color={mapping.entityColumn ? 'success' : 'warning'}
                    variant={mapping.entityColumn ? 'filled' : 'outlined'}
                  />
                </TableCell>
                <TableCell align="center">
                  {editingId === mapping.id ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                      <Tooltip title="Salvar">
                        <IconButton size="small" onClick={handleSave} color="primary">
                          <CheckIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Cancelar">
                        <IconButton size="small" onClick={handleCancel} color="error">
                          <CloseIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                      <Tooltip title="Editar mapeamento">
                        <IconButton size="small" onClick={() => handleEdit(mapping)} color="primary">
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Deletar campo">
                        <IconButton 
                          size="small" 
                          onClick={() => handleDelete(mapping.id, mapping.payloadKey)} 
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {mappings.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 5 }}>
          <Typography variant="h6" color="textSecondary">
            Nenhum campo descoberto ainda
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Os campos aparecerão aqui quando o primeiro webhook for recebido
          </Typography>
        </Box>
      )}

      {filteredMappings.length === 0 && mappings.length > 0 && (
        <Box sx={{ textAlign: 'center', py: 5 }}>
          <Typography variant="h6" color="textSecondary">
            Nenhum campo encontrado
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Tente ajustar os filtros ou termo de busca
          </Typography>
        </Box>
      )}

      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%', bgcolor: 'info.50', border: '2px solid', borderColor: 'info.200', borderRadius: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
              <InfoIcon sx={{ color: 'info.main', mt: 0.5, fontSize: 28 }} />
              <Box>
                <Typography variant="h6" color="info.main" gutterBottom>
                  Como funciona o mapeamento?
                </Typography>
                <Typography variant="body2" paragraph>
                  <CheckIcon sx={{ fontSize: 16, color: 'success.main', mr: 1, verticalAlign: 'text-bottom' }} />
                  <strong>Campos Aninhados:</strong> Todos os campos do webhook são extraídos automaticamente
                </Typography>
                <Typography variant="body2" paragraph>
                  <CheckIcon sx={{ fontSize: 16, color: 'success.main', mr: 1, verticalAlign: 'text-bottom' }} />
                  <strong>Mapeamento Flexível:</strong> Mapeie qualquer campo para qualquer coluna
                </Typography>
                <Typography variant="body2" paragraph>
                  <CheckIcon sx={{ fontSize: 16, color: 'success.main', mr: 1, verticalAlign: 'text-bottom' }} />
                  <strong>Sugestões Inteligentes:</strong> Sistema sugere mapeamentos automaticamente
                </Typography>
                <Typography variant="body2">
                  <CheckIcon sx={{ fontSize: 16, color: 'success.main', mr: 1, verticalAlign: 'text-bottom' }} />
                  <strong>Tempo Real:</strong> Dados sincronizados instantaneamente
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%', bgcolor: 'warning.50', border: '2px solid', borderColor: 'warning.200', borderRadius: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
              <WebhookIcon sx={{ color: 'warning.main', mt: 0.5, fontSize: 28 }} />
              <Box>
                <Typography variant="h6" color="warning.main" gutterBottom>
                  Próximos passos
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>1.</strong> Configure os mapeamentos dos campos recebidos
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>2.</strong> Teste enviando um webhook de exemplo
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>3.</strong> Verifique os dados em Pedidos/Orders
                </Typography>
                <Typography variant="body2">
                  <strong>4.</strong> Ajuste os mapeamentos conforme necessário
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}