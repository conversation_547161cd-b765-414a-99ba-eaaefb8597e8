import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import RefreshIcon from '@mui/icons-material/Refresh';
import VisibilityIcon from '@mui/icons-material/Visibility';
import ErrorIcon from '@mui/icons-material/Error';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import ReplayIcon from '@mui/icons-material/Replay';
import api from '../services/api';

interface WebhookLog {
  id: string;
  endpoint: string;
  method: string;
  status: string;
  error?: string;
  identifierField?: string;
  identifierValue?: string;
  orderId?: string;
  isNewOrder?: boolean;
  mappingsUsed?: number;
  receivedAt: string;
  processedAt?: string;
  processingTimeMs?: number;
  responseStatus?: number;
  payload?: any;
  mappedData?: any;
  headers?: any;
  responseData?: any;
}

export default function WebhookLogs() {
  const navigate = useNavigate();
  const [logs, setLogs] = useState<WebhookLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLog, setSelectedLog] = useState<WebhookLog | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [reprocessing, setReprocessing] = useState<string | null>(null);

  const fetchLogs = async () => {
    setLoading(true);
    try {
      const response = await api.get('/webhooks/logs?limit=50');
      setLogs(response.data);
    } catch (error: any) {
      console.error('Error fetching webhook logs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
    // Auto-refresh every 5 seconds
    const interval = setInterval(fetchLogs, 5000);
    return () => clearInterval(interval);
  }, []);

  const getStatusChip = (log: WebhookLog) => {
    switch (log.status) {
      case 'success':
        return <Chip icon={<CheckCircleIcon />} label="Sucesso" color="success" size="small" />;
      case 'error':
        return <Chip icon={<ErrorIcon />} label="Erro" color="error" size="small" />;
      case 'processing':
        return <Chip icon={<WarningIcon />} label="Processando" color="warning" size="small" />;
      default:
        return <Chip label={log.status} size="small" />;
    }
  };

  const handleViewDetails = (log: WebhookLog) => {
    setSelectedLog(log);
    setDetailsOpen(true);
  };

  const handleReprocess = async (log: WebhookLog) => {
    setReprocessing(log.id);
    try {
      const response = await api.post(`/webhooks/logs/${log.id}/reprocess`);
      
      if (response.data.success) {
        // Refresh logs to show updated status
        await fetchLogs();
        
        // Show success message
        alert(`Webhook reprocessado com sucesso! ${response.data.orderId ? `Pedido: ${response.data.orderId}` : ''}`);
      } else {
        alert(`Erro ao reprocessar webhook: ${response.data.message || 'Erro desconhecido'}`);
      }
    } catch (error: any) {
      console.error('Error reprocessing webhook:', error);
      alert(`Erro ao reprocessar webhook: ${error.response?.data?.message || error.message}`);
    } finally {
      setReprocessing(null);
    }
  };

  const formatJson = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  if (loading && logs.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Logs de Webhook</Typography>
        <Button startIcon={<RefreshIcon />} onClick={fetchLogs} variant="outlined">
          Atualizar
        </Button>
      </Box>

      {logs.length === 0 ? (
        <Alert severity="info">Nenhum webhook recebido ainda.</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Data/Hora</TableCell>
                <TableCell>Endpoint</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Identificador</TableCell>
                <TableCell>Pedido</TableCell>
                <TableCell>Novo?</TableCell>
                <TableCell>Tempo</TableCell>
                <TableCell>Erro</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {logs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    {new Date(log.receivedAt).toLocaleString('pt-BR')}
                  </TableCell>
                  <TableCell>{log.endpoint}</TableCell>
                  <TableCell>{getStatusChip(log)}</TableCell>
                  <TableCell>
                    {log.identifierField && log.identifierValue ? (
                      <Typography variant="caption">
                        {log.identifierField}: {log.identifierValue}
                      </Typography>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    {log.orderId ? (
                      <Button
                        size="small"
                        onClick={() => navigate('/dashboard/pedidos')}
                        title={`Order ID: ${log.orderId}`}
                      >
                        {log.orderId.slice(0, 8)}...
                      </Button>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    {log.isNewOrder !== undefined ? (
                      log.isNewOrder ? (
                        <Chip label="Novo" color="primary" size="small" />
                      ) : (
                        <Chip label="Atualização" color="default" size="small" />
                      )
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    {log.processingTimeMs ? `${log.processingTimeMs}ms` : '-'}
                  </TableCell>
                  <TableCell>
                    {log.error ? (
                      <Tooltip title={log.error}>
                        <ErrorIcon color="error" />
                      </Tooltip>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton onClick={() => handleViewDetails(log)} title="Ver detalhes">
                      <VisibilityIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleReprocess(log)} 
                      disabled={reprocessing === log.id || log.status === 'processing'}
                      title="Reprocessar webhook"
                    >
                      {reprocessing === log.id ? <CircularProgress size={20} /> : <ReplayIcon />}
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>Detalhes do Webhook</DialogTitle>
        <DialogContent>
          {selectedLog && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Informações Gerais
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Typography><strong>ID:</strong> {selectedLog.id}</Typography>
                <Typography><strong>Endpoint:</strong> {selectedLog.endpoint}</Typography>
                <Typography><strong>Status:</strong> {selectedLog.status}</Typography>
                {selectedLog.error && (
                  <Typography color="error"><strong>Erro:</strong> {selectedLog.error}</Typography>
                )}
                {selectedLog.orderId && (
                  <Typography><strong>Pedido:</strong> {selectedLog.orderId}</Typography>
                )}
                <Typography>
                  <strong>Mapeamentos usados:</strong> {selectedLog.mappingsUsed || 0}
                </Typography>
              </Box>

              <Typography variant="h6" gutterBottom>
                Payload Recebido
              </Typography>
              <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.100', overflow: 'auto' }}>
                <pre style={{ margin: 0 }}>
                  {formatJson(selectedLog.payload)}
                </pre>
              </Paper>

              {selectedLog.mappedData && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Dados Mapeados
                  </Typography>
                  <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.100', overflow: 'auto' }}>
                    <pre style={{ margin: 0 }}>
                      {formatJson(selectedLog.mappedData)}
                    </pre>
                  </Paper>
                </>
              )}

              {selectedLog.responseData && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Resposta Enviada
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.100', overflow: 'auto' }}>
                    <pre style={{ margin: 0 }}>
                      {formatJson(selectedLog.responseData)}
                    </pre>
                  </Paper>
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Fechar</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}