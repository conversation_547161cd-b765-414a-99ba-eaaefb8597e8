import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  IconButton,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Chip,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import api from '../services/api';

interface EventMapping {
  id: string;
  event: string;
  correiosDescription?: string;
  internalStatus: string;
  priority: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface UnmappedEvent {
  id: string;
  event: string;
  correiosDescription?: string;
  firstSeenAt: string;
  lastSeenAt: string;
  occurrenceCount: number;
  samplePayload?: any;
}

const statusTranslations: Record<string, string> = {
  Analise: 'An<PERSON><PERSON><PERSON>',
  PagamentoPendente: 'Pagamento Pendente',
  <PERSON><PERSON><PERSON>: 'Comple<PERSON>',
  Parcial: 'Parcial',
  Cancelado: 'Cancelado',
  Transito: 'Em Trânsito',
  Separacao: 'Separação',
  Frustrado: 'Frustrado',
  Recuperacao: 'Recuperação',
  Negociacao: 'Negociação',
  Promessa: 'Promessa',
  RetirarCorreios: 'Retirar Correios',
  EntregaFalha: 'Entrega Falha',
  ConfirmarEntrega: 'Confirmar Entrega',
  DevolvidoCorreios: 'Devolvido Correios',
};

// Get the correct status options from the sidebar
const internalStatusOptions = [
  'Analise',
  'PagamentoPendente',
  'Completo',
  'Parcial',
  'Cancelado',
  'Transito',
  'Separacao',
  'Frustrado',
  'Recuperacao',
  'Negociacao',
  'Promessa',
  'RetirarCorreios',
  'EntregaFalha',
  'ConfirmarEntrega',
  'DevolvidoCorreios',
];

export default function EventStatusMappings() {
  const [mappings, setMappings] = useState<EventMapping[]>([]);
  const [unmappedEvents, setUnmappedEvents] = useState<UnmappedEvent[]>([]);
  const [statusOptions, setStatusOptions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [unmappedDialogOpen, setUnmappedDialogOpen] = useState(false);
  const [selectedUnmapped, setSelectedUnmapped] = useState<UnmappedEvent | null>(null);
  const [editingMapping, setEditingMapping] = useState<EventMapping | null>(null);
  const [formData, setFormData] = useState({
    event: '',
    correiosDescription: '',
    internalStatus: '',
    priority: 0,
  });

  const fetchMappings = async () => {
    setLoading(true);
    try {
      const response = await api.get('/webhooks/event-mappings');
      console.log('Event mappings response:', response.data);
      setMappings(response.data.mappings || []);
      setUnmappedEvents(response.data.unmappedEvents || []);
      // Use our predefined status options instead of the ones from backend
      setStatusOptions(internalStatusOptions);
    } catch (error: any) {
      console.error('Error fetching event mappings:', error);
      alert('Erro ao carregar mapeamentos. Verifique o console para mais detalhes.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMappings();
  }, []);

  const handleOpen = (mapping?: EventMapping) => {
    if (mapping) {
      setEditingMapping(mapping);
      setFormData({
        event: mapping.event,
        correiosDescription: mapping.correiosDescription || '',
        internalStatus: mapping.internalStatus,
        priority: mapping.priority,
      });
    } else {
      setEditingMapping(null);
      setFormData({
        event: '',
        correiosDescription: '',
        internalStatus: '',
        priority: 0,
      });
    }
    setDialogOpen(true);
  };

  const handleClose = () => {
    setDialogOpen(false);
    setEditingMapping(null);
    setFormData({
      event: '',
      correiosDescription: '',
      internalStatus: '',
      priority: 0,
    });
  };

  const handleSave = async () => {
    try {
      const data = {
        ...formData,
        correiosDescription: formData.correiosDescription || undefined,
      };
      
      await api.post('/webhooks/event-mappings', data);
      await fetchMappings();
      handleClose();
    } catch (error: any) {
      console.error('Error saving event mapping:', error);
      alert('Erro ao salvar mapeamento');
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Deseja realmente excluir este mapeamento?')) return;
    
    try {
      console.log('Deleting mapping with ID:', id);
      await api.delete(`/webhooks/event-mappings/${id}`);
      console.log('Delete successful, refreshing mappings...');
      await fetchMappings();
    } catch (error: any) {
      console.error('Error deleting event mapping:', error);
      alert('Erro ao excluir mapeamento. Verifique o console para mais detalhes.');
    }
  };

  const handleToggle = async (id: string) => {
    try {
      await api.put(`/webhooks/event-mappings/${id}/toggle`);
      await fetchMappings();
    } catch (error: any) {
      console.error('Error toggling event mapping:', error);
    }
  };

  const seedDefaults = async () => {
    try {
      await api.post('/webhooks/event-mappings/seed');
      await fetchMappings();
      alert('Mapeamentos padrão criados com sucesso!');
    } catch (error: any) {
      console.error('Error seeding defaults:', error);
      alert('Erro ao criar mapeamentos padrão');
    }
  };

  const reactivateAll = async () => {
    try {
      const response = await api.post('/webhooks/event-mappings/reactivate-all');
      await fetchMappings();
      alert(`${response.data.count} mapeamentos foram reativados!`);
    } catch (error: any) {
      console.error('Error reactivating mappings:', error);
      alert('Erro ao reativar mapeamentos');
    }
  };

  const handleUnmappedClick = (unmapped: UnmappedEvent) => {
    setSelectedUnmapped(unmapped);
    setFormData({
      event: unmapped.event,
      correiosDescription: unmapped.correiosDescription || '',
      internalStatus: '',
      priority: 0,
    });
    setUnmappedDialogOpen(true);
  };

  const handleCreateFromUnmapped = async () => {
    if (!selectedUnmapped) return;
    
    try {
      await api.post(`/webhooks/event-mappings/from-unmapped/${selectedUnmapped.id}`, {
        internalStatus: formData.internalStatus,
        priority: formData.priority,
      });
      await fetchMappings();
      setUnmappedDialogOpen(false);
      setSelectedUnmapped(null);
      setFormData({
        event: '',
        correiosDescription: '',
        internalStatus: '',
        priority: 0,
      });
    } catch (error: any) {
      console.error('Error creating mapping from unmapped:', error);
      alert('Erro ao criar mapeamento');
    }
  };

  const handleDeleteUnmapped = async (id: string) => {
    if (!window.confirm('Deseja realmente excluir este evento não mapeado?')) return;
    
    try {
      await api.delete(`/webhooks/unmapped-events/${id}`);
      await fetchMappings();
    } catch (error: any) {
      console.error('Error deleting unmapped event:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Mapeamento de Status por Evento</Typography>
        <Box>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchMappings}
            variant="outlined"
            sx={{ mr: 2 }}
          >
            Atualizar
          </Button>
          {mappings.length === 0 && (
            <Button
              startIcon={<AddIcon />}
              variant="contained"
              color="secondary"
              onClick={seedDefaults}
              sx={{ mr: 2 }}
            >
              Criar Padrões
            </Button>
          )}
          <Button
            startIcon={<AddIcon />}
            variant="contained"
            color="primary"
            onClick={() => handleOpen()}
          >
            Novo Mapeamento
          </Button>
        </Box>
      </Box>

      {/* Configured Mappings Section */}
      <Typography variant="h5" sx={{ mb: 2, mt: 4 }}>
        Mapeamentos configurados
      </Typography>
      <Alert severity="info" sx={{ mb: 3 }}>
        Configure como os eventos de webhook devem atualizar o status dos pedidos. 
        Você pode mapear combinações de evento + descrição dos Correios para status internos específicos.
      </Alert>

      {mappings.some(m => !m.isActive) && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Existem mapeamentos inativos. Clique no botão de toggle para ativá-los individualmente ou use o botão "Reativar Todos".
          <Button
            size="small"
            onClick={reactivateAll}
            sx={{ ml: 2 }}
            variant="outlined"
          >
            Reativar Todos
          </Button>
        </Alert>
      )}

      <TableContainer component={Paper} sx={{ mb: 6 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Evento</TableCell>
              <TableCell>Descrição Correios</TableCell>
              <TableCell>Status Interno</TableCell>
              <TableCell align="center">Prioridade</TableCell>
              <TableCell align="center">Ativo</TableCell>
              <TableCell align="center">Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {mappings.map((mapping) => (
              <TableRow key={mapping.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {mapping.event}
                  </Typography>
                </TableCell>
                <TableCell>
                  {mapping.correiosDescription || (
                    <Typography variant="body2" color="text.secondary">
                      Qualquer descrição
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Chip
                    label={statusTranslations[mapping.internalStatus] || mapping.internalStatus}
                    color="primary"
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">{mapping.priority}</TableCell>
                <TableCell align="center">
                  <Switch
                    checked={mapping.isActive}
                    onChange={() => handleToggle(mapping.id)}
                    color="primary"
                  />
                </TableCell>
                <TableCell align="center">
                  <IconButton onClick={() => handleOpen(mapping)} size="small">
                    <EditIcon />
                  </IconButton>
                  <IconButton onClick={() => handleDelete(mapping.id)} size="small" color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {mappings.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Typography variant="body2" color="text.secondary" sx={{ py: 3 }}>
                    Nenhum mapeamento configurado. Clique em "Criar Padrões" para começar.
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Unmapped Events Section */}
      <Typography variant="h5" sx={{ mb: 2, mt: 4 }}>
        Status não mapeados
      </Typography>
      <Alert severity="warning" sx={{ mb: 3 }}>
        Estes são combinações de evento + descrição que foram recebidos via webhook mas ainda não possuem mapeamento configurado.
      </Alert>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Evento</TableCell>
              <TableCell>Descrição Correios</TableCell>
              <TableCell align="center">Ocorrências</TableCell>
              <TableCell>Primeira vez</TableCell>
              <TableCell>Última vez</TableCell>
              <TableCell align="center">Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {unmappedEvents.map((unmapped) => (
              <TableRow key={unmapped.id}>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {unmapped.event}
                  </Typography>
                </TableCell>
                <TableCell>
                  {unmapped.correiosDescription || (
                    <Typography variant="body2" color="text.secondary">
                      Sem descrição
                    </Typography>
                  )}
                </TableCell>
                <TableCell align="center">
                  <Chip
                    label={unmapped.occurrenceCount}
                    color="warning"
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(unmapped.firstSeenAt).toLocaleDateString('pt-BR')}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(unmapped.lastSeenAt).toLocaleDateString('pt-BR')}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Button
                    size="small"
                    variant="contained"
                    color="primary"
                    onClick={() => handleUnmappedClick(unmapped)}
                    sx={{ mr: 1 }}
                  >
                    Mapear
                  </Button>
                  <IconButton 
                    onClick={() => handleDeleteUnmapped(unmapped.id)} 
                    size="small" 
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {unmappedEvents.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Typography variant="body2" color="text.secondary" sx={{ py: 3 }}>
                    Nenhum evento não mapeado encontrado.
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingMapping ? 'Editar Mapeamento' : 'Novo Mapeamento'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Evento"
              value={formData.event}
              onChange={(e) => setFormData({ ...formData, event: e.target.value })}
              fullWidth
              required
              helperText="Ex: INCORRECT_ADDRESS, IN_TRANSIT, DELIVERED"
            />
            
            <TextField
              label="Descrição Correios (opcional)"
              value={formData.correiosDescription}
              onChange={(e) => setFormData({ ...formData, correiosDescription: e.target.value })}
              fullWidth
              helperText='Ex: "Objeto não entregue - endereço incorreto"'
            />
            
            <FormControl fullWidth required>
              <InputLabel>Status Interno</InputLabel>
              <Select
                value={formData.internalStatus}
                onChange={(e) => setFormData({ ...formData, internalStatus: e.target.value })}
                label="Status Interno"
              >
                {statusOptions.map((status) => (
                  <MenuItem key={status} value={status}>
                    {statusTranslations[status] || status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <TextField
              label="Prioridade"
              type="number"
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
              fullWidth
              helperText="Maior prioridade tem precedência (0-100)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancelar</Button>
          <Button
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={!formData.event || !formData.internalStatus}
          >
            Salvar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create from Unmapped Dialog */}
      <Dialog open={unmappedDialogOpen} onClose={() => setUnmappedDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Criar Mapeamento
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Criando mapeamento para evento não mapeado
          </Alert>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Evento"
              value={formData.event}
              fullWidth
              disabled
              InputProps={{ readOnly: true }}
            />
            
            <TextField
              label="Descrição Correios"
              value={formData.correiosDescription}
              fullWidth
              disabled
              InputProps={{ readOnly: true }}
              helperText={!formData.correiosDescription ? 'Este evento não tem descrição específica' : ''}
            />
            
            <FormControl fullWidth required>
              <InputLabel>Status Interno</InputLabel>
              <Select
                value={formData.internalStatus}
                onChange={(e) => setFormData({ ...formData, internalStatus: e.target.value })}
                label="Status Interno"
              >
                {statusOptions.map((status) => (
                  <MenuItem key={status} value={status}>
                    {statusTranslations[status] || status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <TextField
              label="Prioridade"
              type="number"
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
              fullWidth
              helperText="Maior prioridade tem precedência (0-100)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUnmappedDialogOpen(false)}>Cancelar</Button>
          <Button
            onClick={handleCreateFromUnmapped}
            variant="contained"
            color="primary"
            disabled={!formData.internalStatus}
          >
            Criar Mapeamento
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}