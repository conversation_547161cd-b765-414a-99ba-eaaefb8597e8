import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Chip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  Visibility,
  VisibilityOff,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const steps = ['Escolha o Plano', 'Informações da Empresa', 'Criar Conta Admin'];

interface Plan {
  id: string;
  name: string;
  priceMonthly: number;
  priceYearly: number;
  features: string[];
  recommended?: boolean;
}

const plans: Plan[] = [
  {
    id: 'FREE',
    name: '<PERSON>r<PERSON><PERSON>',
    priceMonthly: 0,
    priceYearly: 0,
    features: [
      'Até 2 usuários',
      'Até 100 pedidos/mês',
      'Até 10 produtos',
      'Suporte por email',
    ],
  },
  {
    id: 'BASIC',
    name: 'Básico',
    priceMonthly: 49.90,
    priceYearly: 499.00,
    features: [
      'Até 5 usuários',
      'Até 1.000 pedidos/mês',
      'Até 100 produtos',
      'Relatórios básicos',
      'Suporte prioritário',
      'Integração WhatsApp',
    ],
    recommended: true,
  },
  {
    id: 'PRO',
    name: 'Profissional',
    priceMonthly: 99.90,
    priceYearly: 999.00,
    features: [
      'Até 20 usuários',
      'Até 10.000 pedidos/mês',
      'Até 1.000 produtos',
      'Relatórios avançados',
      'API access',
      'Múltiplas integrações',
      'Suporte 24/7',
      'Domínio personalizado',
    ],
  },
];

export default function TenantRegisterPage() {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [domainAvailable, setDomainAvailable] = useState<boolean | null>(null);
  const [checkingDomain, setCheckingDomain] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  const [formData, setFormData] = useState({
    plan: 'BASIC',
    name: '',
    domain: '',
    adminEmail: '',
    adminName: '',
    adminPassword: '',
    confirmPassword: '',
  });

  const handleNext = () => {
    setError('');
    if (validateStep()) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const validateStep = () => {
    switch (activeStep) {
      case 0:
        return true; // Plan selection always valid
      case 1:
        if (!formData.name || !formData.domain) {
          setError('Preencha todos os campos');
          return false;
        }
        if (domainAvailable === false) {
          setError('Este subdomínio não está disponível');
          return false;
        }
        return true;
      case 2:
        if (!formData.adminEmail || !formData.adminName || !formData.adminPassword) {
          setError('Preencha todos os campos');
          return false;
        }
        if (formData.adminPassword !== formData.confirmPassword) {
          setError('As senhas não coincidem');
          return false;
        }
        if (formData.adminPassword.length < 8) {
          setError('A senha deve ter pelo menos 8 caracteres');
          return false;
        }
        return true;
      default:
        return true;
    }
  };

  const checkDomainAvailability = async (domain: string) => {
    if (!domain || domain.length < 3) return;
    
    setCheckingDomain(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/tenant-management/check-domain/${domain}`
      );
      setDomainAvailable(response.data.available);
    } catch (error: any) {
      setDomainAvailable(false);
    } finally {
      setCheckingDomain(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;

    setLoading(true);
    setError('');

    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/tenant-management/register`,
        {
          plan: formData.plan,
          name: formData.name,
          domain: formData.domain,
          adminEmail: formData.adminEmail,
          adminName: formData.adminName,
          adminPassword: formData.adminPassword,
        }
      );

      // Success! Redirect to login with the new subdomain
      const newUrl = `https://${formData.domain}.zencash.com.br`;
      window.location.href = newUrl;
    } catch (error: any) {
      setError(error.response?.data?.message || 'Erro ao criar conta');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              Escolha o plano ideal para sua empresa
            </Typography>
            <Grid container spacing={3}>
              {plans.map((plan) => (
                <Grid item xs={12} md={4} key={plan.id}>
                  <Card
                    elevation={formData.plan === plan.id ? 8 : 1}
                    sx={{
                      height: '100%',
                      position: 'relative',
                      border: formData.plan === plan.id ? '2px solid #1976d2' : 'none',
                      cursor: 'pointer',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4,
                      },
                    }}
                    onClick={() => setFormData({ ...formData, plan: plan.id })}
                  >
                    {plan.recommended && (
                      <Chip
                        label="Recomendado"
                        color="primary"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 10,
                          right: 10,
                        }}
                      />
                    )}
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        {plan.name}
                      </Typography>
                      <Typography variant="h3" component="div" gutterBottom>
                        R$ {plan.priceMonthly}
                        <Typography variant="body2" component="span" color="text.secondary">
                          /mês
                        </Typography>
                      </Typography>
                      <Box sx={{ mt: 3 }}>
                        {plan.features.map((feature, index) => (
                          <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CheckCircleIcon color="success" sx={{ mr: 1, fontSize: 20 }} />
                            <Typography variant="body2">{feature}</Typography>
                          </Box>
                        ))}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              Informações da Empresa
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome da Empresa"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <BusinessIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Subdomínio"
                  value={formData.domain}
                  onChange={(e) => {
                    const domain = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
                    setFormData({ ...formData, domain });
                    setDomainAvailable(null);
                  }}
                  onBlur={() => checkDomainAvailability(formData.domain)}
                  helperText={`Sua URL será: ${formData.domain || 'suaempresa'}.zencash.com.br`}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        {checkingDomain && <CircularProgress size={20} />}
                        {domainAvailable === true && (
                          <CheckCircleIcon color="success" />
                        )}
                        {domainAvailable === false && (
                          <Typography color="error" variant="caption">
                            Indisponível
                          </Typography>
                        )}
                      </InputAdornment>
                    ),
                  }}
                  error={domainAvailable === false}
                />
              </Grid>
            </Grid>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              Criar Conta de Administrador
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome Completo"
                  value={formData.adminName}
                  onChange={(e) => setFormData({ ...formData, adminName: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  type="email"
                  label="Email do Administrador"
                  value={formData.adminEmail}
                  onChange={(e) => setFormData({ ...formData, adminEmail: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type={showPassword ? 'text' : 'password'}
                  label="Senha"
                  value={formData.adminPassword}
                  onChange={(e) => setFormData({ ...formData, adminPassword: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <LockIcon />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type={showPassword ? 'text' : 'password'}
                  label="Confirmar Senha"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                  error={formData.confirmPassword !== '' && formData.adminPassword !== formData.confirmPassword}
                  helperText={
                    formData.confirmPassword !== '' && formData.adminPassword !== formData.confirmPassword
                      ? 'As senhas não coincidem'
                      : ''
                  }
                />
              </Grid>
            </Grid>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', py: 4 }}>
      <Box sx={{ maxWidth: 800, mx: 'auto', px: 2 }}>
        <Paper sx={{ p: 4 }}>
          <Typography variant="h4" align="center" gutterBottom>
            Criar Nova Conta ZenCash
          </Typography>
          
          <Stepper activeStep={activeStep} sx={{ my: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mt: 3 }}>
            {renderStepContent(activeStep)}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
            >
              Voltar
            </Button>
            
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={loading}
                startIcon={loading && <CircularProgress size={20} />}
              >
                {loading ? 'Criando conta...' : 'Criar Conta'}
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
              >
                Próximo
              </Button>
            )}
          </Box>
        </Paper>
      </Box>
    </Box>
  );
}