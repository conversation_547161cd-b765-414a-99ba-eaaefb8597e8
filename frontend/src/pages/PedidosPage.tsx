import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useOutletContext, useLocation } from 'react-router-dom';
import OrdersTableWithPagination from '../components/OrdersTableWithPagination';
import OrderCreationDialog from '../components/OrderCreationDialog';
import OrderImportDialogEnhanced from '../components/OrderImportDialogEnhanced';
import OrderService from '../services/OrderService';
import { Order } from '../types/Order';
import { StatusFilter } from '../components/Sidebar';
import useUserPermissions from '../hooks/useUserPermissions';
import { useOrderData } from '../contexts/OrderDataContext';

const PedidosPage: React.FC = () => {
  // Get context from Layout
  const context = useOutletContext<{
    orders: Order[];
    setOrders: (orders: Order[]) => void;
    selectedStatus: StatusFilter | null;
    setSelectedStatus: (filter: StatusFilter | null) => void;
  }>();
  
  // Use location to detect navigation
  const location = useLocation();
  
  // Use orders from context instead of local state
  const { orders, loading, error, fetchOrders, updateOrder, addOrder, getFilteredOrders } = useOrderData();
  const selectedStatus = context?.selectedStatus || null;
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [selectedOrderIds, setSelectedOrderIds] = useState<string[]>([]);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [isPermanentDelete, setIsPermanentDelete] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const { canViewDeletedOrders, isAdmin, isSeller } = useUserPermissions();

  // Debug logging
  console.log('[PedidosPage] Render:', {
    ordersLength: orders.length,
    loading,
    fetchOrdersExists: !!fetchOrders,
    timestamp: new Date().toISOString()
  });

  // Log when component mounts/unmounts for debugging
  useEffect(() => {
    console.log('[PedidosPage] Component mounted');
    console.log('[PedidosPage] Current orders:', orders.length);
    console.log('[PedidosPage] Loading:', loading);
    
    return () => {
      console.log('[PedidosPage] Component unmounting');
    };
  }, []); // Empty deps - just for logging

  // PedidosPage no longer fetches orders - OrderDataContext handles everything
  useEffect(() => {
    console.log('[PedidosPage] Component mounted, orders from context:', orders.length);
    // Trigger fetch if no orders and not loading
    if (!loading && orders.length === 0 && !error) {
      console.log('[PedidosPage] No orders available, triggering fetch...');
      fetchOrders();
    }
  }, [orders.length, loading, error]);

  // Removed auto-refresh - was causing performance issues
  // Orders will be refreshed manually or through context updates

  // Filter orders based on selected status using the context's filter logic
  const filteredOrders = useMemo(() => {
    console.log('[PedidosPage] Filtering orders:', {
      totalOrders: orders.length,
      selectedStatus,
      canViewDeletedOrders: canViewDeletedOrders(),
      userRole: { isAdmin }
    });
    
    // Use the context's getFilteredOrders which has the correct logic for Receber Hoje
    const filtered = getFilteredOrders(selectedStatus);
    
    console.log('[PedidosPage] Filtered orders:', {
      filterField: selectedStatus?.field,
      filterValue: selectedStatus?.value,
      ordersBeforeFilter: orders.length,
      ordersAfterFilter: filtered.length
    });
    
    return filtered;
  }, [orders, selectedStatus, getFilteredOrders, canViewDeletedOrders, isAdmin]);

  const handleOrderUpdate = async (updatedOrder: Order) => {
    console.log('=== PedidosPage.handleOrderUpdate CALLED ===');
    console.log('Updated order:', updatedOrder);
    console.log('Updated order observation:', updatedOrder.observation);
    
    try {
      // The OrdersTable has already made the API call and saved the order
      // We just need to update our local state without triggering a full refresh
      // Extract the changes from the updated order
      const changes: Partial<Order> = {
        ...updatedOrder
      };
      
      // Update only this specific order in the context
      await updateOrder(updatedOrder.id, changes);
      console.log('Order updated in local state successfully');
    } catch (error: any) {
      console.error('Error in PedidosPage.handleOrderUpdate:', error);
      // Don't throw - the update already succeeded in OrdersTable
    }
  };

  const refreshOrders = async () => {
    console.log('[PedidosPage] Manual refresh triggered');
    await fetchOrders();
  };

  const handleCreateOrder = async (orderData: any) => {
    try {
      console.log('Creating order with data:', orderData);
      
      // Use addOrder from context which handles the API call and state update
      await addOrder(orderData);
      
      // Close dialog
      setOrderDialogOpen(false);
      
      // Show success message
      setSnackbarMessage('Pedido criado com sucesso!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Optionally reset filter to show all orders including the new one
      if (context?.setSelectedStatus) {
        context.setSelectedStatus(null);
      }
      
    } catch (error: any) {
      console.error('Error creating order:', error);
      // The error message from our service already includes the user-friendly message
      const errorMessage = error.message || 'Erro ao criar pedido';
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      
      // Don't close dialog on error so user can retry
    }
  };

  const handleBulkDelete = () => {
    if (selectedOrderIds.length === 0) {
      setSnackbarMessage('Nenhum pedido selecionado');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    setBulkDeleteDialogOpen(true);
  };

  const confirmBulkDelete = async () => {
    try {
      setBulkDeleteDialogOpen(false);
      
      // Show progress dialog for large batches
      if (selectedOrderIds.length > 50) {
        setSnackbarMessage(`Excluindo ${selectedOrderIds.length} pedidos. Isso pode levar alguns momentos...`);
        setSnackbarSeverity('info');
        setSnackbarOpen(true);
      }
      
      const result = await OrderService.bulkDeleteOrders(selectedOrderIds, isPermanentDelete);
      
      // Clear selection
      setSelectedOrderIds([]);
      
      // Show success message
      setSnackbarMessage(
        `${result.success} pedido(s) ${isPermanentDelete ? 'excluído(s) permanentemente' : 'excluído(s)'} com sucesso${
          result.failed > 0 ? `. ${result.failed} falharam.` : ''
        }`
      );
      setSnackbarSeverity(result.failed > 0 ? 'error' : 'success');
      setSnackbarOpen(true);
      
      // Refresh orders
      await refreshOrders();
    } catch (error: any) {
      console.error('Error bulk deleting orders:', error);
      setSnackbarMessage('Erro ao excluir pedidos. Tente novamente com menos pedidos por vez.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            Pedidos ({filteredOrders.length} de {orders.length})
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="text"
              onClick={async () => {
                console.log('Debug info:');
                console.log('Orders in context:', orders.length);
                console.log('Filtered orders:', filteredOrders.length);
                console.log('Selected status:', selectedStatus);
                console.log('Is admin:', isAdmin);
                console.log('Can view deleted:', canViewDeletedOrders());
                console.log('First 3 orders:', orders.slice(0, 3));
                
                // Force a fresh fetch
                await fetchOrders();
              }}
            >
              Debug ({orders.length})
            </Button>
            {isAdmin && selectedOrderIds.length > 0 && (
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleBulkDelete}
              >
                Excluir ({selectedOrderIds.length})
              </Button>
            )}
            <Tooltip title="Atualizar pedidos">
              <IconButton onClick={refreshOrders} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            {!isSeller && (
              <>
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  onClick={() => setImportDialogOpen(true)}
                >
                  Importar CSV
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setOrderDialogOpen(true)}
                >
                  Criar Pedido
                </Button>
              </>
            )}
          </Box>
        </Box>
        <Typography variant="body1" color="text.secondary">
          {selectedStatus 
            ? `Filtrando por: ${selectedStatus.field === 'special' ? 'Receber Hoje' : selectedStatus.value}`
            : 'Todos os pedidos'
          } ({filteredOrders.length} pedidos)
        </Typography>
      </Box>

      <Paper elevation={0} sx={{ backgroundColor: 'transparent' }}>
        <OrdersTableWithPagination
          orders={filteredOrders}
          selectedOrderIds={selectedOrderIds}
          onSelectionChange={setSelectedOrderIds}
          showSelection={isAdmin}
          onOrderUpdate={handleOrderUpdate}
        />
      </Paper>

      <OrderCreationDialog
        open={orderDialogOpen}
        onClose={() => setOrderDialogOpen(false)}
        onSave={handleCreateOrder}
      />
      
      <OrderImportDialogEnhanced
        open={importDialogOpen}
        onClose={() => {
          setImportDialogOpen(false);
          // Don't refresh here - the dialog handles it internally after successful import
        }}
      />

      {/* Bulk Delete Confirmation Dialog */}
      <Dialog
        open={bulkDeleteDialogOpen}
        onClose={() => setBulkDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirmar Exclusão em Massa</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Você está prestes a excluir {selectedOrderIds.length} pedido(s).
            {isPermanentDelete
              ? ' Esta ação é permanente e não pode ser desfeita.'
              : ' Os pedidos serão marcados como excluídos mas podem ser recuperados.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkDeleteDialogOpen(false)}>
            Cancelar
          </Button>
          <Button
            onClick={() => setIsPermanentDelete(!isPermanentDelete)}
            color="warning"
          >
            {isPermanentDelete ? 'Exclusão Suave' : 'Exclusão Permanente'}
          </Button>
          <Button
            onClick={confirmBulkDelete}
            color="error"
            variant="contained"
          >
            Confirmar Exclusão
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default PedidosPage;