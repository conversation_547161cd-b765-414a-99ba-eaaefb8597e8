import React, { ReactNode } from 'react';
import { Navigate, RouteObject, Outlet } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import UnifiedAuthService from '../services/UnifiedAuthService';
import { UserRole } from '../types/User';

// Import Layout component
import Layout from '../components/Layout';

// Import pages directly for now (we'll implement lazy loading later)
import LandingPage from '../pages/LandingPage';
import UnifiedLoginPage from '../pages/UnifiedLoginPage';
import PasswordResetPage from '../pages/PasswordResetPage';
import DashboardPage from '../pages/DashboardPage';
import AdminDashboardPage from '../pages/AdminDashboardPage';
import AdminAnalyticsDashboard from '../pages/AdminAnalyticsDashboard';
import ReportsPage from '../pages/ReportsPage';
import AdvancedReportsPage from '../pages/AdvancedReportsPage';
import TrackingPage from '../pages/TrackingPage';
import DuplicateOrdersPage from '../pages/DuplicateOrdersPage';
import SettingsPage from '../pages/SettingsPage';
import UnifiedUsersPage from '../pages/UnifiedUsersPage';
import UnifiedNewUserPage from '../pages/UnifiedNewUserPage';
import UnifiedEditUserPage from '../pages/UnifiedEditUserPage';
import RestoreDataPage from '../pages/RestoreDataPage';
import VendedorRankingPage from '../pages/VendedorRankingPage';
import OperadorRankingPage from '../pages/OperadorRankingPage';
import ZapConfigPage from '../pages/ZapConfigPage';
import ZapsPage from '../pages/ZapsPage';
import ZapReportsPage from '../pages/ZapReportsPage';
import ProfilePage from '../pages/ProfilePage';
import PedidosPage from '../pages/PedidosPage';
import ProdutosV2Page from '../pages/ProdutosV2Page';
import ProductCreationDebugPage from '../pages/ProductCreationDebugPage';
import ProductDetailV2Page from '../pages/ProductDetailV2Page';
import TenantRegisterPage from '../pages/TenantRegisterPage';
import TenantDashboardPage from '../pages/TenantDashboardPage';
import AntifraudDashboard from '../pages/AntifraudDashboard';
import DebugOrders from '../components/DebugOrders';
import WebhookMappings from '../pages/WebhookMappings';
import WebhookLogs from '../pages/WebhookLogs';
import EventStatusMappings from '../pages/EventStatusMappings';
import TestDropdown from '../pages/TestDropdown';
import TestWebhookStatusUpdate from '../pages/TestWebhookStatusUpdate';
import PaymentMethodsPage from '../pages/PaymentMethodsPage';
import FinancialReportsPage from '../pages/FinancialReportsPage';
import PaymentConfirmations from '../pages/PaymentConfirmations';

// Auth guard component
interface AuthGuardProps {
  children?: ReactNode;
  requiredRole?: UserRole | UserRole[];
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, requiredRole }) => {
  const isAuthenticated = UnifiedAuthService.isAuthenticated();

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (requiredRole) {
    const userInfo = UnifiedAuthService.getUserInfo();
    const userRole = userInfo?.role || '';
    
    const roleArray = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const hasRequiredRole = roleArray.includes(userRole as UserRole);
    if (!hasRequiredRole) {
      // Don't redirect to dashboard, as that can cause loops
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h5">Unauthorized</Typography>
          <Typography>You don't have permission to access this page.</Typography>
        </Box>
      );
    }
  }

  return <>{children || <Outlet />}</>;
};

// Define routes
export const routes: RouteObject[] = [
  // Landing page - no auth required
  {
    path: '/',
    element: <LandingPage />,
  },
  // Routes without layout (login and password reset)
  {
    path: '/login',
    element: <UnifiedLoginPage />,
  },
  {
    path: '/reset-password',
    element: <PasswordResetPage />,
  },
  // Tenant registration - no auth required
  {
    path: '/register-tenant',
    element: <TenantRegisterPage />,
  },
  // Protected routes with layout
  {
    path: '/dashboard',
    element: (
      <AuthGuard>
        <Layout />
      </AuthGuard>
    ),
    children: [
      {
        index: true,
        element: <AdminAnalyticsDashboard />,
      },
      {
        path: 'pedidos',
        element: <PedidosPage />,
      },
      {
        path: 'produtos',
        element: (
          <AuthGuard requiredRole="admin">
            <ProdutosV2Page />
          </AuthGuard>
        ),
      },
      {
        path: 'produtos/debug',
        element: (
          <AuthGuard requiredRole="admin">
            <ProductCreationDebugPage />
          </AuthGuard>
        ),
      },
      {
        path: 'produtos/:id',
        element: (
          <AuthGuard requiredRole="admin">
            <ProductDetailV2Page />
          </AuthGuard>
        ),
      },
      {
        path: 'admin',
        element: (
          <AuthGuard requiredRole="admin">
            <AdminDashboardPage />
          </AuthGuard>
        ),
      },
      {
        path: 'reports',
        element: <ReportsPage />,
      },
      {
        path: 'tracking',
        element: <TrackingPage />,
      },
      {
        path: 'duplicates',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <DuplicateOrdersPage />
          </AuthGuard>
        ),
      },
      {
        path: 'antifraud',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <AntifraudDashboard />
          </AuthGuard>
        ),
      },
      {
        path: 'settings',
        element: (
          <AuthGuard requiredRole="admin">
            <SettingsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'advanced-reports',
        element: (
          <AuthGuard requiredRole="admin">
            <AdvancedReportsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedUsersPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users/new',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedNewUserPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users/:id/edit',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedEditUserPage />
          </AuthGuard>
        ),
      },
      {
        path: 'restore-data',
        element: (
          <AuthGuard requiredRole="admin">
            <RestoreDataPage />
          </AuthGuard>
        ),
      },
      {
        path: 'webhook-mappings',
        element: (
          <AuthGuard requiredRole="admin">
            <WebhookMappings />
          </AuthGuard>
        ),
      },
      {
        path: 'webhook-logs',
        element: (
          <AuthGuard requiredRole="admin">
            <WebhookLogs />
          </AuthGuard>
        ),
      },
      {
        path: 'event-mappings',
        element: (
          <AuthGuard requiredRole="admin">
            <EventStatusMappings />
          </AuthGuard>
        ),
      },
      {
        path: 'test-dropdown',
        element: (
          <AuthGuard requiredRole="admin">
            <TestDropdown />
          </AuthGuard>
        ),
      },
      {
        path: 'test-webhook-status',
        element: (
          <AuthGuard requiredRole="admin">
            <TestWebhookStatusUpdate />
          </AuthGuard>
        ),
      },
      {
        path: 'ranking',
        element: <VendedorRankingPage />,
      },
      {
        path: 'ranking-operador',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <OperadorRankingPage />
          </AuthGuard>
        ),
      },
      {
        path: 'zap-config',
        element: (
          <AuthGuard requiredRole="admin">
            <ZapConfigPage />
          </AuthGuard>
        ),
      },
      {
        path: 'zaps',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <ZapsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'zap-reports/:zapId?',
        element: (
          <AuthGuard requiredRole="admin">
            <ZapReportsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'payment-methods',
        element: (
          <AuthGuard requiredRole="admin">
            <PaymentMethodsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'financial-reports',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <FinancialReportsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'payment-confirmations',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <PaymentConfirmations />
          </AuthGuard>
        ),
      },
      {
        path: 'profile',
        element: <ProfilePage />,
      },
      {
        path: 'tenant-management',
        element: (
          <AuthGuard requiredRole="admin">
            <TenantDashboardPage />
          </AuthGuard>
        ),
      },
      {
        path: 'debug-orders',
        element: (
          <AuthGuard>
            <DebugOrders />
          </AuthGuard>
        ),
      },
    ],
  },
  // Catch-all redirect
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
];

// Preload critical routes (placeholder for now)
export const preloadCriticalRoutes = () => {
  // We'll implement this later with proper lazy loading
  console.log('Preloading critical routes...');
};
