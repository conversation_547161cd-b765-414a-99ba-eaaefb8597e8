import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { DuplicateReviewItem, ReviewDecision } from '../services/AntifraudService';
import AntifraudService from '../services/AntifraudService';

export interface FilterState {
  scoreRange: [number, number];
  dateRange: [Date | null, Date | null];
  searchTerm: string;
  statusFilter: 'all' | 'pending' | 'reviewed';
}

export interface SortState {
  column: 'createdAt' | 'matchScore' | 'total' | 'customerName';
  direction: 'asc' | 'desc';
}

interface OptimisticUpdate {
  orderId: string;
  decision: ReviewDecision['decision'];
  timestamp: number;
}

interface AntifraudStore {
  // State
  queue: DuplicateReviewItem[];
  filters: FilterState;
  sorting: SortState;
  selection: Set<string>;
  loading: boolean;
  error: string | null;
  page: number;
  totalPages: number;
  optimisticUpdates: OptimisticUpdate[];
  lastSync: number | null;
  
  // Actions
  setQueue: (items: DuplicateReviewItem[]) => void;
  setFilters: (filters: Partial<FilterState>) => void;
  setSorting: (sorting: SortState) => void;
  toggleSelection: (orderId: string) => void;
  selectAll: () => void;
  clearSelection: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setPage: (page: number) => void;
  
  // Async actions
  fetchQueue: () => Promise<void>;
  refreshQueue: () => Promise<void>;
  
  // Optimistic updates
  approveOrderOptimistic: (orderId: string) => void;
  denyOrderOptimistic: (orderId: string) => void;
  confirmOptimisticUpdate: (orderId: string) => void;
  revertOptimisticUpdate: (orderId: string) => void;
  
  // Computed
  getFilteredQueue: () => DuplicateReviewItem[];
  getSortedQueue: () => DuplicateReviewItem[];
  getSelectedItems: () => DuplicateReviewItem[];
  
  // Persistence
  clearPersistedState: () => void;
  syncWithBackend: () => Promise<void>;
}

const defaultFilters: FilterState = {
  scoreRange: [0, 100],
  dateRange: [null, null],
  searchTerm: '',
  statusFilter: 'pending',
};

const defaultSorting: SortState = {
  column: 'createdAt',
  direction: 'desc',
};

export const useAntifraudStore = create<AntifraudStore>()(
  devtools(
    subscribeWithSelector(
      persist(
        immer((set, get) => ({
          // Initial state
          queue: [],
          filters: defaultFilters,
          sorting: defaultSorting,
          selection: new Set(),
          loading: false,
          error: null,
          page: 1,
          totalPages: 1,
          optimisticUpdates: [],
          lastSync: null,
          
          // Basic setters
          setQueue: (items) => set((state) => {
            state.queue = items;
          }),
          
          setFilters: (filters) => set((state) => {
            state.filters = { ...state.filters, ...filters };
            state.page = 1; // Reset to first page on filter change
          }),
          
          setSorting: (sorting) => set((state) => {
            state.sorting = sorting;
          }),
          
          toggleSelection: (orderId) => set((state) => {
            const newSelection = new Set(state.selection);
            if (newSelection.has(orderId)) {
              newSelection.delete(orderId);
            } else {
              newSelection.add(orderId);
            }
            state.selection = newSelection;
          }),
          
          selectAll: () => set((state) => {
            const filtered = get().getFilteredQueue();
            state.selection = new Set(filtered.map(item => item.id));
          }),
          
          clearSelection: () => set((state) => {
            state.selection = new Set();
          }),
          
          setLoading: (loading) => set((state) => {
            state.loading = loading;
          }),
          
          setError: (error) => set((state) => {
            state.error = error;
          }),
          
          setPage: (page) => set((state) => {
            state.page = page;
          }),
          
          // Async actions
          fetchQueue: async () => {
            const { setLoading, setError, setQueue, page } = get();
            
            try {
              setLoading(true);
              setError(null);
              
              const response = await AntifraudService.getDuplicateReviewQueue(page, 20);
              
              set((state) => {
                state.queue = response.items;
                state.totalPages = response.pages;
                state.lastSync = Date.now();
              });
            } catch (error: any) {
              setError(error instanceof Error ? error.message : 'Failed to fetch queue');
            } finally {
              setLoading(false);
            }
          },
          
          refreshQueue: async () => {
            const { fetchQueue } = get();
            await fetchQueue();
          },
          
          // Optimistic updates
          approveOrderOptimistic: (orderId) => set((state) => {
            // Add to optimistic updates
            state.optimisticUpdates.push({
              orderId,
              decision: 'APPROVE_ORDER',
              timestamp: Date.now(),
            });
            
            // Update item in queue
            const itemIndex = state.queue.findIndex(item => item.id === orderId);
            if (itemIndex !== -1) {
              state.queue[itemIndex] = {
                ...state.queue[itemIndex],
                // Add visual indicator that this is being processed
              };
            }
          }),
          
          denyOrderOptimistic: (orderId) => set((state) => {
            state.optimisticUpdates.push({
              orderId,
              decision: 'DENY_ORDER',
              timestamp: Date.now(),
            });
            
            const itemIndex = state.queue.findIndex(item => item.id === orderId);
            if (itemIndex !== -1) {
              state.queue[itemIndex] = {
                ...state.queue[itemIndex],
              };
            }
          }),
          
          confirmOptimisticUpdate: (orderId) => set((state) => {
            // Remove from optimistic updates
            state.optimisticUpdates = state.optimisticUpdates.filter(
              update => update.orderId !== orderId
            );
            
            // Remove from queue (it's been reviewed)
            state.queue = state.queue.filter(item => item.id !== orderId);
          }),
          
          revertOptimisticUpdate: (orderId) => set((state) => {
            // Remove from optimistic updates
            state.optimisticUpdates = state.optimisticUpdates.filter(
              update => update.orderId !== orderId
            );
            
            // Could refresh the specific item from backend here
          }),
          
          // Computed getters
          getFilteredQueue: () => {
            const { queue, filters } = get();
            
            return queue.filter(item => {
              // Score filter
              if (item.duplicateMatchScore < filters.scoreRange[0] || 
                  item.duplicateMatchScore > filters.scoreRange[1]) {
                return false;
              }
              
              // Date filter
              if (filters.dateRange[0] && new Date(item.createdAt) < filters.dateRange[0]) {
                return false;
              }
              if (filters.dateRange[1] && new Date(item.createdAt) > filters.dateRange[1]) {
                return false;
              }
              
              // Search filter
              if (filters.searchTerm) {
                const search = filters.searchTerm.toLowerCase();
                return (
                  item.customerName.toLowerCase().includes(search) ||
                  item.orderNumber.toLowerCase().includes(search) ||
                  item.customerPhone.includes(search)
                );
              }
              
              return true;
            });
          },
          
          getSortedQueue: () => {
            const { sorting } = get();
            const filtered = get().getFilteredQueue();
            
            return [...filtered].sort((a, b) => {
              let comparison = 0;
              
              switch (sorting.column) {
                case 'createdAt':
                  comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
                  break;
                case 'matchScore':
                  comparison = a.duplicateMatchScore - b.duplicateMatchScore;
                  break;
                case 'total':
                  comparison = a.total - b.total;
                  break;
                case 'customerName':
                  comparison = a.customerName.localeCompare(b.customerName);
                  break;
              }
              
              return sorting.direction === 'asc' ? comparison : -comparison;
            });
          },
          
          getSelectedItems: () => {
            const { queue, selection } = get();
            return queue.filter(item => selection.has(item.id));
          },
          
          // Persistence
          clearPersistedState: () => {
            localStorage.removeItem('antifraud-store');
            set((state) => {
              state.filters = defaultFilters;
              state.sorting = defaultSorting;
              state.selection = new Set();
            });
          },
          
          syncWithBackend: async () => {
            const { optimisticUpdates, fetchQueue } = get();
            
            // If we have pending optimistic updates older than 5 seconds,
            // refresh from backend to ensure consistency
            const now = Date.now();
            const hasStaleUpdates = optimisticUpdates.some(
              update => now - update.timestamp > 5000
            );
            
            if (hasStaleUpdates) {
              await fetchQueue();
            }
          },
        })),
        {
          name: 'antifraud-store',
          partialize: (state) => ({
            filters: state.filters,
            sorting: state.sorting,
          }),
        }
      )
    ),
    {
      name: 'antifraud-store',
    }
  )
);

// Sync with backend periodically
if (typeof window !== 'undefined') {
  setInterval(() => {
    useAntifraudStore.getState().syncWithBackend();
  }, 30000); // Every 30 seconds
}