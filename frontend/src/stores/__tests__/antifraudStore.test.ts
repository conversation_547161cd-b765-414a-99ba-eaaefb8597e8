import { act, renderHook } from '@testing-library/react';
import { useAntifraudStore } from '../antifraudStore';
import AntifraudService from '../../services/AntifraudService';

// Mock the AntifraudService
jest.mock('../../services/AntifraudService');

describe('antifraudStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAntifraudStore.setState({
      orders: [],
      selectedOrderIds: [],
      filters: {
        search: '',
        minScore: 70,
        maxScore: 100,
        dateRange: { start: null, end: null },
      },
      sortBy: 'score',
      sortDirection: 'desc',
      isLoading: false,
      error: null,
      lastSync: null,
      pendingReviews: [],
    });
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Order Management', () => {
    it('should set orders', () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockOrders = [
        {
          id: '1',
          orderNumber: 'ORD-001',
          customerName: '<PERSON>',
          customerCPF: '123.456.789-00',
          duplicateMatchScore: 85,
          createdAt: new Date().toISOString(),
          status: 'PENDING' as const,
        },
      ];

      act(() => {
        result.current.setOrders(mockOrders);
      });

      expect(result.current.orders).toEqual(mockOrders);
    });

    it('should add a single order', () => {
      const { result } = renderHook(() => useAntifraudStore());
      const newOrder = {
        id: '2',
        orderNumber: 'ORD-002',
        customerName: 'Jane Smith',
        customerCPF: '987.654.321-00',
        duplicateMatchScore: 90,
        createdAt: new Date().toISOString(),
        status: 'PENDING' as const,
      };

      act(() => {
        result.current.addOrder(newOrder);
      });

      expect(result.current.orders).toHaveLength(1);
      expect(result.current.orders[0]).toEqual(newOrder);
    });

    it('should update an existing order', () => {
      const { result } = renderHook(() => useAntifraudStore());
      const initialOrder = {
        id: '1',
        orderNumber: 'ORD-001',
        customerName: 'John Doe',
        customerCPF: '123.456.789-00',
        duplicateMatchScore: 85,
        createdAt: new Date().toISOString(),
        status: 'PENDING' as const,
      };

      act(() => {
        result.current.setOrders([initialOrder]);
        result.current.updateOrder('1', { status: 'APPROVED' });
      });

      expect(result.current.orders[0].status).toBe('APPROVED');
    });

    it('should remove an order', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', orderNumber: 'ORD-001' } as any,
          { id: '2', orderNumber: 'ORD-002' } as any,
        ]);
        result.current.removeOrder('1');
      });

      expect(result.current.orders).toHaveLength(1);
      expect(result.current.orders[0].id).toBe('2');
    });
  });

  describe('Selection Management', () => {
    it('should toggle order selection', () => {
      const { result } = renderHook(() => useAntifraudStore());

      act(() => {
        result.current.toggleOrderSelection('1');
      });

      expect(result.current.selectedOrderIds).toContain('1');

      act(() => {
        result.current.toggleOrderSelection('1');
      });

      expect(result.current.selectedOrderIds).not.toContain('1');
    });

    it('should select all orders', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1' } as any,
          { id: '2' } as any,
          { id: '3' } as any,
        ]);
        result.current.selectAllOrders();
      });

      expect(result.current.selectedOrderIds).toEqual(['1', '2', '3']);
    });

    it('should clear all selections', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setSelectedOrderIds(['1', '2', '3']);
        result.current.clearSelection();
      });

      expect(result.current.selectedOrderIds).toEqual([]);
    });
  });

  describe('Filtering', () => {
    it('should update search filter', () => {
      const { result } = renderHook(() => useAntifraudStore());

      act(() => {
        result.current.setFilters({ search: 'John' });
      });

      expect(result.current.filters.search).toBe('John');
    });

    it('should update score range filter', () => {
      const { result } = renderHook(() => useAntifraudStore());

      act(() => {
        result.current.setFilters({ minScore: 80, maxScore: 95 });
      });

      expect(result.current.filters.minScore).toBe(80);
      expect(result.current.filters.maxScore).toBe(95);
    });

    it('should filter orders by search term', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', customerName: 'John Doe', customerCPF: '123', duplicateMatchScore: 85 } as any,
          { id: '2', customerName: 'Jane Smith', customerCPF: '456', duplicateMatchScore: 90 } as any,
        ]);
        result.current.setFilters({ search: 'John' });
      });

      const filtered = result.current.getFilteredOrders();
      expect(filtered).toHaveLength(1);
      expect(filtered[0].customerName).toBe('John Doe');
    });

    it('should filter orders by score range', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', duplicateMatchScore: 75 } as any,
          { id: '2', duplicateMatchScore: 85 } as any,
          { id: '3', duplicateMatchScore: 95 } as any,
        ]);
        result.current.setFilters({ minScore: 80, maxScore: 90 });
      });

      const filtered = result.current.getFilteredOrders();
      expect(filtered).toHaveLength(1);
      expect(filtered[0].duplicateMatchScore).toBe(85);
    });
  });

  describe('Sorting', () => {
    it('should sort orders by score', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', duplicateMatchScore: 75 } as any,
          { id: '2', duplicateMatchScore: 95 } as any,
          { id: '3', duplicateMatchScore: 85 } as any,
        ]);
        result.current.setSortBy('score');
        result.current.setSortDirection('desc');
      });

      const sorted = result.current.getSortedOrders();
      expect(sorted[0].duplicateMatchScore).toBe(95);
      expect(sorted[1].duplicateMatchScore).toBe(85);
      expect(sorted[2].duplicateMatchScore).toBe(75);
    });

    it('should sort orders by date', () => {
      const { result } = renderHook(() => useAntifraudStore());
      const date1 = '2024-01-01T00:00:00Z';
      const date2 = '2024-01-02T00:00:00Z';
      const date3 = '2024-01-03T00:00:00Z';
      
      act(() => {
        result.current.setOrders([
          { id: '1', createdAt: date2 } as any,
          { id: '2', createdAt: date3 } as any,
          { id: '3', createdAt: date1 } as any,
        ]);
        result.current.setSortBy('date');
        result.current.setSortDirection('asc');
      });

      const sorted = result.current.getSortedOrders();
      expect(sorted[0].createdAt).toBe(date1);
      expect(sorted[1].createdAt).toBe(date2);
      expect(sorted[2].createdAt).toBe(date3);
    });
  });

  describe('Review Actions', () => {
    it('should add pending review with optimistic update', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockReview = jest.fn().mockResolvedValue({});
      (AntifraudService.reviewDuplicate as jest.Mock) = mockReview;

      const order = {
        id: '1',
        status: 'PENDING' as const,
      };

      act(() => {
        result.current.setOrders([order as any]);
      });

      await act(async () => {
        await result.current.reviewOrder('1', 'APPROVED', 'Test reason');
      });

      // Check optimistic update
      expect(result.current.orders[0].status).toBe('APPROVED');
      
      // Check API was called
      expect(mockReview).toHaveBeenCalledWith('1', {
        decision: 'APPROVED',
        reason: 'Test reason',
      });
    });

    it('should handle review error and rollback', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockError = new Error('API Error');
      const mockReview = jest.fn().mockRejectedValue(mockError);
      (AntifraudService.reviewDuplicate as jest.Mock) = mockReview;

      const order = {
        id: '1',
        status: 'PENDING' as const,
      };

      act(() => {
        result.current.setOrders([order as any]);
      });

      await act(async () => {
        await result.current.reviewOrder('1', 'APPROVED', 'Test reason');
      });

      // Check rollback occurred
      expect(result.current.orders[0].status).toBe('PENDING');
      expect(result.current.error).toBe('API Error');
    });

    it('should process bulk reviews', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockReview = jest.fn().mockResolvedValue({});
      (AntifraudService.reviewDuplicate as jest.Mock) = mockReview;

      act(() => {
        result.current.setOrders([
          { id: '1', status: 'PENDING' } as any,
          { id: '2', status: 'PENDING' } as any,
          { id: '3', status: 'PENDING' } as any,
        ]);
        result.current.setSelectedOrderIds(['1', '2', '3']);
      });

      await act(async () => {
        await result.current.bulkReview('DENIED', 'Bulk deny');
      });

      // Check all orders were updated
      result.current.orders.forEach(order => {
        expect(order.status).toBe('DENIED');
      });

      // Check API was called for each order
      expect(mockReview).toHaveBeenCalledTimes(3);
    });
  });

  describe('Sync and Persistence', () => {
    it('should refresh queue from API', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockData = {
        items: [
          { id: '1', orderNumber: 'ORD-001' },
          { id: '2', orderNumber: 'ORD-002' },
        ],
      };
      const mockGetQueue = jest.fn().mockResolvedValue(mockData);
      (AntifraudService.getDuplicateReviewQueue as jest.Mock) = mockGetQueue;

      await act(async () => {
        await result.current.refreshQueue();
      });

      expect(result.current.orders).toEqual(mockData.items);
      expect(result.current.lastSync).toBeTruthy();
      expect(result.current.isLoading).toBe(false);
    });

    it('should handle sync errors', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockError = new Error('Network error');
      const mockGetQueue = jest.fn().mockRejectedValue(mockError);
      (AntifraudService.getDuplicateReviewQueue as jest.Mock) = mockGetQueue;

      await act(async () => {
        await result.current.refreshQueue();
      });

      expect(result.current.error).toBe('Network error');
      expect(result.current.isLoading).toBe(false);
    });

    it('should sync pending reviews on reconnect', async () => {
      const { result } = renderHook(() => useAntifraudStore());
      const mockReview = jest.fn().mockResolvedValue({});
      (AntifraudService.reviewDuplicate as jest.Mock) = mockReview;

      // Add pending reviews
      act(() => {
        result.current.pendingReviews = [
          { orderId: '1', decision: 'APPROVED', reason: 'Test', timestamp: Date.now() },
          { orderId: '2', decision: 'DENIED', reason: 'Test 2', timestamp: Date.now() },
        ];
      });

      await act(async () => {
        await result.current.syncPendingReviews();
      });

      expect(mockReview).toHaveBeenCalledTimes(2);
      expect(result.current.pendingReviews).toHaveLength(0);
    });
  });

  describe('Getters and Computed Values', () => {
    it('should get filtered and sorted orders', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', customerName: 'Alice', duplicateMatchScore: 85, createdAt: '2024-01-01' } as any,
          { id: '2', customerName: 'Bob', duplicateMatchScore: 75, createdAt: '2024-01-02' } as any,
          { id: '3', customerName: 'Charlie', duplicateMatchScore: 95, createdAt: '2024-01-03' } as any,
        ]);
        result.current.setFilters({ minScore: 80 });
        result.current.setSortBy('score');
        result.current.setSortDirection('desc');
      });

      const filtered = result.current.getFilteredAndSortedOrders();
      expect(filtered).toHaveLength(2);
      expect(filtered[0].duplicateMatchScore).toBe(95);
      expect(filtered[1].duplicateMatchScore).toBe(85);
    });

    it('should check if has pending reviews', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      expect(result.current.hasPendingReviews()).toBe(false);

      act(() => {
        result.current.pendingReviews = [
          { orderId: '1', decision: 'APPROVED', reason: 'Test', timestamp: Date.now() },
        ];
      });

      expect(result.current.hasPendingReviews()).toBe(true);
    });

    it('should check if all orders are selected', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1' } as any,
          { id: '2' } as any,
        ]);
      });

      expect(result.current.isAllSelected()).toBe(false);

      act(() => {
        result.current.selectAllOrders();
      });

      expect(result.current.isAllSelected()).toBe(true);
    });

    it('should get selected orders', () => {
      const { result } = renderHook(() => useAntifraudStore());
      
      act(() => {
        result.current.setOrders([
          { id: '1', orderNumber: 'ORD-001' } as any,
          { id: '2', orderNumber: 'ORD-002' } as any,
          { id: '3', orderNumber: 'ORD-003' } as any,
        ]);
        result.current.setSelectedOrderIds(['1', '3']);
      });

      const selected = result.current.getSelectedOrders();
      expect(selected).toHaveLength(2);
      expect(selected[0].orderNumber).toBe('ORD-001');
      expect(selected[1].orderNumber).toBe('ORD-003');
    });
  });
});