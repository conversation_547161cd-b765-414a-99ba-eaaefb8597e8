import api from './api';
import { Zap, ZapStats, CreateZapDto, UpdateZapDto } from '../types/Zap';

class ZapService {
  /**
   * Get all Zaps
   */
  async getAll(): Promise<Zap[]> {
    try {
      const response = await api.get('/zaps');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching zaps:', error);
      throw error;
    }
  }

  /**
   * Get a single Zap
   */
  async getOne(id: string): Promise<Zap> {
    try {
      const response = await api.get(`/zaps/${id}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching zap:', error);
      throw error;
    }
  }

  /**
   * Get Zap statistics
   */
  async getStats(id: string, startDate?: Date, endDate?: Date): Promise<ZapStats> {
    try {
      const params: any = {};
      if (startDate) params.startDate = startDate.toISOString();
      if (endDate) params.endDate = endDate.toISOString();
      
      const response = await api.get(`/zaps/${id}/stats`, { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching zap stats:', error);
      throw error;
    }
  }

  /**
   * Create a new Zap
   */
  async create(data: CreateZapDto): Promise<Zap> {
    try {
      const response = await api.post('/zaps', data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating zap:', error);
      throw error;
    }
  }

  /**
   * Update a Zap
   */
  async update(id: string, data: UpdateZapDto): Promise<Zap> {
    try {
      const response = await api.patch(`/zaps/${id}`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating zap:', error);
      throw error;
    }
  }

  /**
   * Delete a Zap
   */
  async delete(id: string): Promise<void> {
    try {
      await api.delete(`/zaps/${id}`);
    } catch (error: any) {
      console.error('Error deleting zap:', error);
      throw error;
    }
  }

  /**
   * Update orders retroactively with Zap information
   */
  async updateRetroactively(): Promise<{ processed: number; updated: number; failed: number }> {
    try {
      const response = await api.post('/zaps/update-retroactively');
      return response.data;
    } catch (error: any) {
      console.error('Error updating orders retroactively:', error);
      throw error;
    }
  }
}

export default new ZapService();