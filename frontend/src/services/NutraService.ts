import {
  Product, ProductCreate, ProductUpdate,
  ProductVariation, ProductVariationCreate,
  Kit, KitCreate, KitUpdate,
  Distributor, DistributorCreate, DistributorUpdate,
  Order, OrderCreate, OrderUpdate,
  StockHistory, StockHistoryCreate,
  KitSale, KitSaleCreate,
  ProductStockStatus, SalesAnalytics, InventorySummary
} from '../types/NutraTypes';
import api from './api';

const NUTRA_API = ''; // Products are at root level, not under /nutra

// Product API calls
export const getProducts = async (activeOnly: boolean = true): Promise<Product[]> => {
  const response = await api.get(`/products?active_only=${activeOnly}`);
  return response.data;
};

export const getProduct = async (id: number): Promise<Product> => {
  const response = await api.get(`/products/${id}`);
  return response.data;
};

export const createProduct = async (product: ProductCreate): Promise<Product> => {
  console.log('API call to create product with data:', JSON.stringify(product));
  console.log('API URL:', `${NUTRA_API}/products`);
  try {
    const response = await api.post(`/products`, product);
    console.log('API response:', response);
    return response.data;
  } catch (error: any) {
    console.error('API error details:', error);
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    } else {
      console.error('Error message:', error.message);
    }
    throw error;
  }
};

export const updateProduct = async (id: number, product: ProductUpdate): Promise<Product> => {
  const response = await api.put(`/products/${id}`, product);
  return response.data;
};

export const deleteProduct = async (id: number): Promise<void> => {
  await api.delete(`/products/${id}`);
};

export const adjustStock = async (productId: number, stockChange: StockHistoryCreate): Promise<StockHistory> => {
  const response = await api.post(
    `/products/${productId}/adjust-stock`,
    stockChange
  );
  return response.data;
};

export const getStockHistory = async (productId: number): Promise<StockHistory[]> => {
  const response = await api.get(
    `/products/${productId}/stock-history`
  );
  return response.data;
};

export const addProductVariation = async (productId: number, variation: ProductVariationCreate): Promise<ProductVariation> => {
  const response = await api.post(
    `/products/${productId}/variations`,
    variation
  );
  return response.data;
};

// Kit API calls
export const getKits = async (activeOnly: boolean = true): Promise<Kit[]> => {
  const response = await api.get(`/kits?active_only=${activeOnly}`);
  return response.data;
};

export const getKit = async (id: number): Promise<Kit> => {
  const response = await api.get(`/kits/${id}`);
  return response.data;
};

export const createKit = async (kit: KitCreate): Promise<Kit> => {
  const response = await api.post(`/kits`, kit);
  return response.data;
};

export const updateKit = async (id: number, kit: KitUpdate): Promise<Kit> => {
  const response = await api.put(`/kits/${id}`, kit);
  return response.data;
};

export const deleteKit = async (id: number): Promise<void> => {
  await api.delete(`${NUTRA_API}/kits/${id}`);
};

// Kit sales API calls
export const createKitSale = async (kitSale: KitSaleCreate): Promise<KitSale> => {
  const response = await api.post(`${NUTRA_API}/kit-sales`, kitSale);
  return response.data;
};

export const getKitSales = async (startDate?: Date, endDate?: Date): Promise<KitSale[]> => {
  let url = `${NUTRA_API}/kit-sales`;

  if (startDate || endDate) {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate.toISOString());
    if (endDate) params.append('end_date', endDate.toISOString());
    url += `?${params.toString()}`;
  }

  const response = await api.get(url);
  return response.data;
};

// Distributor API calls
export const getDistributors = async (activeOnly: boolean = true): Promise<Distributor[]> => {
  const response = await api.get(`${NUTRA_API}/distributors?active_only=${activeOnly}`);
  return response.data;
};

export const getDistributor = async (id: number): Promise<Distributor> => {
  const response = await api.get(`${NUTRA_API}/distributors/${id}`);
  return response.data;
};

export const createDistributor = async (distributor: DistributorCreate): Promise<Distributor> => {
  const response = await api.post(`${NUTRA_API}/distributors`, distributor);
  return response.data;
};

export const updateDistributor = async (id: number, distributor: DistributorUpdate): Promise<Distributor> => {
  const response = await api.put(`${NUTRA_API}/distributors/${id}`, distributor);
  return response.data;
};

export const deleteDistributor = async (id: number): Promise<void> => {
  await api.delete(`${NUTRA_API}/distributors/${id}`);
};

// Order API calls
export const getOrders = async (status?: string): Promise<Order[]> => {
  let url = `${NUTRA_API}/orders`;
  if (status) url += `?status=${status}`;

  const response = await api.get(url);
  return response.data;
};

export const getOrder = async (id: number): Promise<Order> => {
  const response = await api.get(`${NUTRA_API}/orders/${id}`);
  return response.data;
};

export const createOrder = async (order: OrderCreate): Promise<Order> => {
  const response = await api.post(`${NUTRA_API}/orders`, order);
  return response.data;
};

export const updateOrder = async (id: number, order: OrderUpdate): Promise<Order> => {
  const response = await api.put(`${NUTRA_API}/orders/${id}`, order);
  return response.data;
};

export const completeOrder = async (id: number): Promise<Order> => {
  const response = await api.post(`${NUTRA_API}/orders/${id}/complete`, {});
  return response.data;
};

// Analytics API calls
export const getLowStockProducts = async (thresholdPercentage: number = 100): Promise<ProductStockStatus[]> => {
  const response = await api.get(
    `${NUTRA_API}/analytics/low-stock?threshold_percentage=${thresholdPercentage}`
  );
  return response.data;
};

export const getSalesAnalytics = async (days: number = 30): Promise<SalesAnalytics> => {
  const response = await api.get(
    `${NUTRA_API}/analytics/sales?days=${days}`
  );
  return response.data;
};

export const getInventorySummary = async (): Promise<InventorySummary> => {
  const response = await api.get(`${NUTRA_API}/analytics/inventory`);
  return response.data;
};

const NutraService = {
  // Products
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  adjustStock,
  getStockHistory,
  addProductVariation,

  // Kits
  getKits,
  getKit,
  createKit,
  updateKit,
  deleteKit,

  // Kit Sales
  createKitSale,
  getKitSales,

  // Distributors
  getDistributors,
  getDistributor,
  createDistributor,
  updateDistributor,
  deleteDistributor,

  // Orders
  getOrders,
  getOrder,
  createOrder,
  updateOrder,
  completeOrder,

  // Analytics
  getLowStockProducts,
  getSalesAnalytics,
  getInventorySummary
};

export default NutraService;