import axios, { AxiosInstance } from 'axios';

class KitService {
  public api: AxiosInstance;

  constructor() {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
    
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });

    // Add request interceptor for auth and tenant ID
    this.api.interceptors.request.use((config) => {
      // Get auth token
      const authTokens = localStorage.getItem('unified_auth_tokens');
      const token = authTokens ? JSON.parse(authTokens).access_token : null;
      
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Get tenant ID
      const userInfo = localStorage.getItem('unified_user_info');
      console.log('User info from localStorage:', userInfo);
      
      const tenantId = userInfo ? JSON.parse(userInfo).tenantId : null;
      console.log('Tenant ID extracted:', tenantId);
      
      if (tenantId) {
        config.headers['x-tenant-id'] = tenantId;
      } else {
        console.error('No tenant ID found in user info!');
      }

      console.log('Request headers:', config.headers);
      return config;
    });

    // Add response interceptor for better error logging
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        console.error('Error status:', error.response?.status);
        console.error('Error headers:', error.response?.headers);
        return Promise.reject(error);
      }
    );
  }

  async getAllKits(): Promise<any[]> {
    try {
      const response = await this.api.get('/kits');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching kits:', error);
      throw error;
    }
  }

  async getKitsByProductId(productId: string): Promise<any[]> {
    try {
      console.log('Fetching kits for product:', productId);
      
      // Use the backend endpoint with productId parameter
      const response = await this.api.get('/kits', {
        params: { productId }
      });
      
      const kits = response.data;
      console.log(`Found ${kits.length} kits for product ${productId}`);
      
      return kits;
    } catch (error: any) {
      console.error('Error fetching kits for product:', error);
      throw error;
    }
  }

  async updateKit(kitId: string, data: any): Promise<any> {
    try {
      const response = await this.api.patch(`/kits/${kitId}`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating kit:', error);
      throw error;
    }
  }

  async createKit(data: any): Promise<any> {
    try {
      const response = await this.api.post('/kits', data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating kit:', error);
      throw error;
    }
  }

  async deleteKit(kitId: string): Promise<void> {
    try {
      await this.api.delete(`/kits/${kitId}`);
    } catch (error: any) {
      console.error('Error deleting kit:', error);
      throw error;
    }
  }
}

export const kitService = new KitService();
export default KitService;