import UnifiedAuthService from './UnifiedAuthService';

export interface DuplicateReviewItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: string;
  duplicateMatchScore: number;
  matchedOrders: Array<{
    orderId: string;
    orderNumber: string;
    createdAt: string;
  }>;
}

export interface ReviewDecision {
  decision: 'APPROVE_ORDER' | 'DENY_ORDER' | 'MERGE_ORDERS' | 'INVESTIGATE_FURTHER';
  notes?: string;
}

export interface AuditLogEntry {
  id: string;
  orderId: string;
  action: string;
  performedBy: string;
  performedByName: string;
  performedByRole: string;
  performedAt: string;
  previousData?: any;
  newData?: any;
  metadata?: any;
  signatureValid: boolean;
}

export interface OrderReviewItem {
  id: string;
  orderNumber: string | null;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: string;
  riskScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  riskFactors: string[];
  status: string;
  isDuplicate: boolean;
  duplicateMatchScore?: number | null;
  matchedOrders?: Array<{
    id: string;
    orderNumber: string;
    createdAt: string;
  }>;
}

class AntifraudService {
  private static instance: AntifraudService;

  private constructor() {}

  static getInstance(): AntifraudService {
    if (!AntifraudService.instance) {
      AntifraudService.instance = new AntifraudService();
    }
    return AntifraudService.instance;
  }

  private async makeRequest(
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const userInfo = UnifiedAuthService.getUserInfo();
    const tenantId = userInfo?.tenantId || process.env.REACT_APP_TENANT_ID || '28a833c0-c2a1-4498-85ca-b028f982ffb2';
    const authTokens = UnifiedAuthService.getAuthTokens();
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
    const fullUrl = `${apiUrl}${endpoint}`;
    
    console.log('AntifraudService.makeRequest:', {
      method,
      fullUrl,
      endpoint,
      tenantId,
      hasAuth: !!authTokens?.access_token,
      data
    });

    const response = await fetch(fullUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authTokens?.access_token || ''}`,
        'x-tenant-id': tenantId,
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    console.log('AntifraudService response status:', response.status);

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Request failed' }));
      console.error('AntifraudService request failed:', {
        status: response.status,
        error,
        url: fullUrl
      });
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    // Handle nested data structure from backend
    return responseData.data || responseData;
  }

  async getDuplicateReviewQueue(page: number = 1, limit: number = 20): Promise<{
    items: DuplicateReviewItem[];
    total: number;
    pages: number;
  }> {
    return this.makeRequest(
      'GET',
      `/antifraud/duplicates/review-queue?page=${page}&limit=${limit}`
    );
  }

  async reviewDuplicate(
    orderId: string,
    review: ReviewDecision
  ): Promise<{ success: boolean; message: string }> {
    console.log('AntifraudService.reviewDuplicate called with:', {
      orderId,
      orderIdType: typeof orderId,
      review
    });
    
    try {
      const result = await this.makeRequest(
        'POST',
        `/antifraud/duplicates/${orderId}/review`,
        review
      );
      console.log('Review duplicate result:', result);
      return result;
    } catch (error: any) {
      console.error('Review duplicate error:', {
        error,
        message: error?.message,
        response: error?.response,
        data: error?.response?.data
      });
      throw error;
    }
  }

  async getOrderAuditTrail(orderId: string): Promise<AuditLogEntry[]> {
    return this.makeRequest(
      'GET',
      `/antifraud/orders/${orderId}/audit-trail`
    );
  }

  static formatMatchScore(score: number): string {
    if (score >= 90) return 'Muito Alta';
    if (score >= 70) return 'Alta';
    if (score >= 50) return 'Média';
    if (score >= 30) return 'Baixa';
    return 'Muito Baixa';
  }

  static getMatchScoreColor(score: number): string {
    if (score >= 90) return '#f44336'; // Red
    if (score >= 70) return '#ff9800'; // Orange
    if (score >= 50) return '#ffc107'; // Amber
    if (score >= 30) return '#4caf50'; // Green
    return '#2196f3'; // Blue
  }

  async getOrdersForReview(
    riskLevel?: string | null,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    items: OrderReviewItem[];
    total: number;
    pages: number;
    byRiskLevel: Record<string, number>;
  }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    
    if (riskLevel) {
      params.append('riskLevel', riskLevel);
    }

    return this.makeRequest(
      'GET',
      `/antifraud/orders/review-queue?${params.toString()}`
    );
  }

  async assessOrderRisk(orderId: string): Promise<{ success: boolean; message: string }> {
    return this.makeRequest(
      'POST',
      `/antifraud/orders/${orderId}/assess-risk`
    );
  }

  async reviewOrder(
    orderId: string,
    review: ReviewDecision
  ): Promise<{ success: boolean; message: string }> {
    console.log('AntifraudService.reviewOrder called with:', {
      orderId,
      orderIdType: typeof orderId,
      review
    });
    
    try {
      const result = await this.makeRequest(
        'POST',
        `/antifraud/orders/${orderId}/review`,
        review
      );
      console.log('Review order result:', result);
      return result;
    } catch (error: any) {
      console.error('Review order error:', {
        error,
        message: error?.message,
        response: error?.response,
        data: error?.response?.data
      });
      throw error;
    }
  }
}

export default AntifraudService.getInstance();