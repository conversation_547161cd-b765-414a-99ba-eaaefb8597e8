import api from './api';
import { Order, BillingHistoryEntry } from '../types/Order';
import { ApiOrder, ApiOrderUpdate, ApiBillingHistoryCreate, ApiOrderCreate, ApiBillingHistory, OrderStatus } from '../types/api';
import { formatDate, parseAddress } from '../utils/formatters';

// Helper function to format date strings
const formatDateString = (dateString: string | null): string => {
  if (!dateString) return '';
  try {
    return formatDate(new Date(dateString));
  } catch (e) {
    return '';
  }
};

// Helper function to format billing history
const formatBillingHistory = (billingHistory: ApiBillingHistory[]): string => {
  return billingHistory
    .map(bh => {
      const date = formatDateString(bh.createdAt);
      const amount = `R$ ${bh.amount.toFixed(2)}`;
      const notes = bh.notes ? ` - ${bh.notes}` : '';
      const createdBy = bh.createdByName ? ` (por ${bh.createdByName})` : '';
      return `${date}: ${amount}${notes}${createdBy}`;
    })
    .join('\n');
};

// Helper function to parse billing history from string
const parseBillingHistoryFromString = (historyString: string): BillingHistoryEntry[] => {
  if (!historyString) return [];

  return historyString.split('\n')
    .filter(line => line.trim() !== '')
    .map(line => {
      // Expected format: "DD/MM/YYYY: R$ 100.00 - Notes (por User)"
      const dateMatch = line.match(/^([0-9]{2}\/[0-9]{2}\/[0-9]{4})/);
      const amountMatch = line.match(/R\$ ([0-9]+(?:\.[0-9]{2})?)/);
      const notesMatch = line.match(/- ([^(]*)/);
      const createdByMatch = line.match(/\(por (.*)\)/);

      return {
        date: dateMatch ? dateMatch[1] : '',
        amount: amountMatch ? parseFloat(amountMatch[1]) : 0,
        notes: notesMatch ? notesMatch[1].trim() : undefined,
        createdBy: createdByMatch ? createdByMatch[1].trim() : undefined
      };
    });
};

interface CreateOrderPayload {
  customerName: string;
  customerPhone: string;
  customerCPF: string;
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
  }>;
  address?: {
    cep: string;
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
  };
  observation?: string;
  customerId?: string;
  collectorId?: string;
  zapId?: string;
  // Note: vendedorName is not accepted by backend
  // The backend determines the seller from the authenticated user
}

class OrderService {
  /**
   * Check if order exists by order number
   */
  async checkOrderExists(orderNumber: string): Promise<boolean> {
    try {
      const response = await api.get(`/orders/check/${orderNumber}`);
      return response.data?.exists || false;
    } catch (error: any) {
      console.error('Check order exists error:', error);
      // If we get a 404, the order doesn't exist
      if (error.response?.status === 404) {
        return false;
      }
      // For other errors, we'll assume the order doesn't exist to allow import
      return false;
    }
  }

  /**
   * Get a single order by ID
   */
  async getOrder(orderId: string): Promise<Order> {
    try {
      console.log('Getting order:', orderId);
      const response = await api.get(`/orders/${orderId}`);
      console.log('Get order response:', response.data);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error('Get order error:', error);
      throw new Error(error.response?.data?.message || error.message || 'Erro ao buscar pedido');
    }
  }

  /**
   * Import a single order from CSV data
   */
  async importOrder(orderData: any): Promise<Order> {
    try {
      console.log('Importing order with data:', orderData);
      const response = await api.post('/orders/import', orderData);
      console.log('Import response:', response.data);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error('Import error:', error);
      console.error('Error response:', error.response);
      throw new Error(error.response?.data?.message || error.message || 'Erro ao importar pedido');
    }
  }

  /**
   * Map backend status enum to frontend Portuguese status
   */
  private mapBackendStatusToFrontend(backendStatus: string): string {
    // REMOVED ALERT - This function is called too frequently
    const statusMap: Record<string, string> = {
      // Mapping from backend enum values to frontend display names
      'PagamentoPendente': 'Pagamento Pendente',
      'Completo': 'Completo',
      'Parcial': 'Parcial',
      'Cancelado': 'Cancelado',
      'Transito': 'Trânsito',
      'Analise': 'Análise',
      'Separacao': 'Separação',
      'Frustrado': 'Frustrado',
      'Recuperacao': 'Recuperação',
      'Negociacao': 'Negociação',
      'Promessa': 'Promessa',
      'RetirarCorreios': 'Retirar Correios',
      'EntregaFalha': 'Entrega Falha',
      'ConfirmarEntrega': 'Confirmar Entrega',
      'DevolvidoCorreios': 'Devolvido Correios',
      // Legacy mappings
      'PENDING': 'Pagamento Pendente',
      'IN_PROGRESS': 'Em Andamento',
      'PAID': 'Completo',
      'PARTIALLY_PAID': 'Parcial',
      'NEGOTIATING': 'Negociação',
      'CANCELLED': 'Cancelado',
      'DELIVERED': 'Entregue',
      'DELETED': 'Deletado',
      'LIBERACAO': 'Liberação',
    };
    
    const result = statusMap[backendStatus] || backendStatus;
    // REMOVED ALERT - This function is called too frequently
    return result;
  }

  /**
   * No transformation needed - backend now returns data in the correct format
   */
  private processOrderData(order: any): Order {
    // Backend already returns the data in the correct format
    // Just ensure proper typing and add any computed display fields
    return {
      ...order,
      // Convert numeric fields to ensure proper types
      total: Number(order.total || 0),
      paymentReceivedAmount: Number(order.paymentReceivedAmount || 0),
      // The backend addComputedFields already adds sellerName and collectorName
    } as Order;
  }

  // Removed conversion methods - no longer needed since we use English field names everywhere

  /**
   * Get all orders with optional filters
   */
  async getOrders(filters?: { 
    includeDeleted?: boolean;
    status?: string;
    sellerId?: string;
    collectorId?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
  }): Promise<Order[]> {
    try {
      // For development with mock data, use localStorage
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        // Mock data from localStorage
        const ordersData = localStorage.getItem('orders');
        let orders: Order[] = ordersData ? JSON.parse(ordersData) : [];

        // Log first order to check structure
        if (orders.length > 0) {
          console.log('First order from localStorage:', {
            id: orders[0].id,
            idVenda: orders[0].idVenda,
            hasId: !!orders[0].id,
            idType: typeof orders[0].id
          });
        }

        return orders.map((order, index) => {
          // Generate ID if missing (for mock data compatibility)
          if (!order.id) {
            order.id = `mock-${order.idVenda || index}`;
          }
          
          // Ensure all orders have valid numeric fields
          return {
            ...order,
            id: order.id,
            valorVenda: order.valorVenda !== undefined ? order.valorVenda : 0,
            valorRecebido: order.valorRecebido !== undefined ? order.valorRecebido : 0,
            // Ensure situation is a string
            situacaoVenda: order.situacaoVenda || 'Pendente'
          };
        });
      }

      // For production with real API
      const params = filters ? new URLSearchParams(filters as any).toString() : '';
      const url = `/orders${params ? `?${params}` : ''}`;
      
      console.log('[OrderService.getOrders] Making API request to:', url);
      console.log('[OrderService.getOrders] With filters:', filters);
      
      const response = await api.get(url);
      
      console.log('[OrderService.getOrders] API Response:', {
        status: response.status,
        dataLength: Array.isArray(response.data) ? response.data.length : 'not array'
      });
      
      // Backend returns orders array directly
      const orders = Array.isArray(response.data) ? response.data : [];
      
      console.log('OrderService.getOrders - Raw orders from API:', orders.length, 'orders');
      if (orders.length > 0) {
        console.log('OrderService.getOrders - First order structure:', {
          id: orders[0].id,
          orderNumber: orders[0].orderNumber,
          idVenda: orders[0].idVenda,
          customerName: orders[0].customerName,
          cliente: orders[0].cliente,
          status: orders[0].status,
          hasIdVenda: !!orders[0].idVenda,
          hasCliente: !!orders[0].cliente,
          hasCustomerName: !!orders[0].customerName
        });
      }
      
      // For deleted orders, we need to mark them with status "Deletado"
      const mappedOrders = orders.map(order => {
        const frontendOrder = this.processOrderData(order);
        // If order has deletedAt, mark it as deleted
        if (order.deletedAt) {
          frontendOrder.situacaoVenda = 'Deletado';
          frontendOrder.situacao = 'Deletado';
          frontendOrder.status = 'Deletado';
        }
        return frontendOrder;
      });
      
      console.log(`OrderService.getOrders - Returning ${mappedOrders.length} orders`);
      return mappedOrders;
    } catch (error: any) {
      console.error('Error fetching orders:', error);
      console.error('Error details:', {
        message: error?.message,
        response: error?.response?.data,
        status: error?.response?.status
      });
      
      // Don't return empty array on error - throw to let caller handle
      throw error;
    }
  }

  /**
   * Get a specific order by ID
   */
  async getOrder(orderId: string): Promise<Order> {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];
        const order = orders.find((o: Order) => o.idVenda === orderId);

        if (!order) {
          throw new Error('Order not found');
        }

        return order;
      }

      // For production with real API
      const response = await api.get<ApiOrder>(`/orders/${orderId}`);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error(`Error fetching order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Create a new order
   */
  async createOrder(orderData: Order): Promise<Order> {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];

        // Check if order already exists
        const existingOrder = orders.find((o: Order) => o.idVenda === orderData.idVenda);
        if (existingOrder) {
          throw new Error('Order with this ID already exists');
        }

        // Add created_at and updated_at
        const newOrder = {
          ...orderData,
          dataVenda: formatDate(new Date()),
          ultimaAtualizacao: formatDate(new Date())
        };

        // Add to orders array
        const updatedOrders = [...orders, newOrder];
        localStorage.setItem('orders', JSON.stringify(updatedOrders));

        return newOrder;
      }

      // For production with real API
      const response = await api.post<ApiOrder>('/orders', orderData);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  /**
   * Import order using the special import endpoint
   * This endpoint has less strict validation than createOrder
   */
  async importOrder(orderData: any): Promise<Order> {
    try {
      // The import endpoint expects different field names (Portuguese)
      const importData: any = {
        idVenda: orderData.orderNumber || orderData.idVenda,
        cliente: orderData.customerName || orderData.cliente,
        telefone: orderData.customerPhone || orderData.telefone,
        valorVenda: orderData.total || orderData.valorVenda || 0,
        dataVenda: orderData.createdAt || orderData.dataVenda,
        situacaoVenda: orderData.status || orderData.situacaoVenda || 'PagamentoPendente',
        vendedor: orderData.sellerName || orderData.seller?.name || orderData.vendedor,
        operador: orderData.collectorName || orderData.collector?.name || orderData.operador,
        documentoCliente: orderData.customerCPF || orderData.documentoCliente,
        valorRecebido: orderData.paymentReceivedAmount || orderData.valorRecebido || 0,
        codigoRastreio: orderData.trackingCode || orderData.codigoRastreio,
        observacao: orderData.observation || orderData.observacao,
        historico: orderData.observation || orderData.historico,
        oferta: orderData.oferta,
        zap: orderData.zapId || orderData.zap,
        statusCorreios: orderData.statusCorreios,
        dataRecebimento: orderData.paymentReceivedDate || orderData.dataRecebimento,
        dataNegociacao: orderData.lastContactDate || orderData.dataNegociacao,
        dataEstimadaChegada: orderData.dataEstimadaChegada,
        ultimaAtualizacao: orderData.updatedAt || orderData.ultimaAtualizacao,
      };

      // Handle address components if they exist
      if (orderData.addressComponents) {
        importData.estadoDestinatario = orderData.addressComponents.state;
        importData.cidadeDestinatario = orderData.addressComponents.city;
        importData.ruaDestinatario = orderData.addressComponents.street;
        importData.cepDestinatario = orderData.addressComponents.zipCode;
        importData.complementoDestinatario = orderData.addressComponents.complement;
        importData.bairroDestinatario = orderData.addressComponents.neighborhood;
        importData.numeroEnderecoDestinatario = orderData.addressComponents.streetNumber;
      }

      // Also check for individual address fields
      if (orderData.state) importData.estadoDestinatario = orderData.state;
      if (orderData.city) importData.cidadeDestinatario = orderData.city;
      if (orderData.street) importData.ruaDestinatario = orderData.street;
      if (orderData.zipCode) importData.cepDestinatario = orderData.zipCode;
      if (orderData.complement) importData.complementoDestinatario = orderData.complement;
      if (orderData.neighborhood) importData.bairroDestinatario = orderData.neighborhood;
      if (orderData.streetNumber) importData.numeroEnderecoDestinatario = orderData.streetNumber;

      // Remove undefined values
      Object.keys(importData).forEach(key => {
        if (importData[key] === undefined || importData[key] === null || importData[key] === '') {
          delete importData[key];
        }
      });

      console.log('Sending import data:', importData);
      const response = await api.post<ApiOrder>('/orders/import', importData);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error('Error importing order:', error);
      throw error;
    }
  }

  /**
   * Update order with payment information
   * This uses different endpoints based on what needs to be updated
   * to respect role-based permissions
   */
  async updateOrderWithPayment(orderId: string, paymentData: {
    status?: string;
    paymentReceivedAmount?: number;
    observation?: string;
    paymentReceivedDate?: string;
    nextPaymentDate?: string;
    formaPagamento?: string;
  }): Promise<Order> {
    try {
      console.log('[OrderService.updateOrderWithPayment] Called with:', { orderId, paymentData });
      
      // For collectors (COBRADOR), we need to use specific endpoints
      // 1. Update status via /orders/:id/status
      // 2. Update observation via /orders/:id/observation
      // 3. Other fields require ADMIN/SUPERVISOR role
      
      let updatedOrder: Order | null = null;
      
      // Step 1: Update status if provided
      if (paymentData.status) {
        console.log('[OrderService] Updating status via /status endpoint');
        const statusResponse = await api.patch<ApiOrder>(`/orders/${orderId}/status`, {
          status: paymentData.status,
          observation: paymentData.observation
        });
        updatedOrder = this.processOrderData(statusResponse.data);
      }
      
      // Step 2: Update observation if provided and no status update
      if (paymentData.observation && !paymentData.status) {
        console.log('[OrderService] Updating observation via /observation endpoint');
        const obsResponse = await api.patch<ApiOrder>(`/orders/${orderId}/observation`, {
          observation: paymentData.observation
        });
        updatedOrder = this.processOrderData(obsResponse.data);
      }
      
      // Step 3: Try to update other fields (requires ADMIN/SUPERVISOR)
      const otherFields: any = {};
      if (paymentData.paymentReceivedAmount !== undefined) otherFields.paymentReceivedAmount = paymentData.paymentReceivedAmount;
      if (paymentData.paymentReceivedDate) otherFields.paymentReceivedDate = paymentData.paymentReceivedDate;
      if (paymentData.nextPaymentDate) otherFields.nextPaymentDate = paymentData.nextPaymentDate;
      if (paymentData.formaPagamento) otherFields.formaPagamento = paymentData.formaPagamento;
      
      if (Object.keys(otherFields).length > 0) {
        try {
          console.log('[OrderService] Updating other fields via general endpoint');
          const response = await api.patch<ApiOrder>(`/orders/${orderId}`, otherFields);
          updatedOrder = this.processOrderData(response.data);
        } catch (error: any) {
          if (error.response?.status === 403) {
            console.warn('[OrderService] User does not have permission to update payment fields. This is expected for COBRADOR role.');
            // Return the last successful update or fetch current state
            if (!updatedOrder) {
              const currentOrder = await this.getOrder(orderId);
              updatedOrder = currentOrder;
            }
          } else {
            throw error;
          }
        }
      }
      
      // If no update was made, fetch current state
      if (!updatedOrder) {
        updatedOrder = await this.getOrder(orderId);
      }
      
      return updatedOrder;
    } catch (error: any) {
      console.error('[OrderService.updateOrderWithPayment] Error:', error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, status: string): Promise<Order> {
    try {
      console.log('OrderService.updateOrderStatus called with:', { orderId, status });
      
      if (!orderId) {
        throw new Error('Order ID is required');
      }
      
      // Map Portuguese status to backend enum
      const statusMap: Record<string, string> = {
        'Pagamento Pendente': 'PagamentoPendente',
        'PagamentoPendente': 'PagamentoPendente',
        'Completo': 'Completo',
        'Parcial': 'Parcial',
        'Negociação': 'Negociacao',
        'Promessa': 'Promessa',
        'Cancelado': 'Cancelado',
        'Frustrado': 'Frustrado',
        'Recuperação': 'Recuperacao',
        'Análise': 'Analise',
        'Separação': 'Separacao',
        'Trânsito': 'Transito',
        'Retirar Correios': 'RetirarCorreios',
        'Entrega Falha': 'EntregaFalha',
        'Confirmar Entrega': 'ConfirmarEntrega',
        'Devolvido Correios': 'DevolvidoCorreios',
      };
      
      const mappedStatus = statusMap[status] || status;
      console.log('Mapped status:', { original: status, mapped: mappedStatus });
      
      // Use the specific status update endpoint
      const response = await api.patch<ApiOrder>(`/orders/${orderId}/status`, {
        status: mappedStatus
      });
      
      console.log('API Response:', response.data);
      const convertedOrder = this.processOrderData(response.data);
      console.log('Converted order:', { 
        backendStatus: response.data.status,
        frontendSituacao: convertedOrder.situacao,
        frontendSituacaoVenda: convertedOrder.situacaoVenda 
      });
      return convertedOrder;
    } catch (error: any) {
      console.error('Error updating order status:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        url: error.config?.url,
        method: error.config?.method,
        payload: error.config?.data
      });
      throw error;
    }
  }

  /**
   * Update an order
   */
  async updateOrder(orderId: string, orderData: Partial<Order>): Promise<Order> {
    console.log('OrderService.updateOrder:', orderId);
    
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';
      console.log('Mock mode:', mockMode);

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        let orders = savedOrders ? JSON.parse(savedOrders) : [];

        // Find and update the order
        orders = orders.map((o: Order) => {
          if (o.id === orderId) {
            return {
              ...o,
              ...orderData,
              ultimaAtualizacao: formatDate(new Date()),
              // Update status field to match situacaoVenda if it was updated
              status: orderData.situacaoVenda || o.status,
              situacao: orderData.situacaoVenda || o.situacao
            };
          }
          return o;
        });

        // Save back to localStorage
        localStorage.setItem('orders', JSON.stringify(orders));

        // Return the updated order
        const updatedOrder = orders.find((o: Order) => o.id === orderId);
        console.log('Mock mode - Updated order:', updatedOrder);
        return updatedOrder;
      }

      // For production with real API
      console.log('Making API call to:', `/orders/${orderId}`);
      console.log('Update data:', orderData);
      const response = await api.patch<ApiOrder>(`/orders/${orderId}`, orderData);
      console.log('Backend response:', response.data);
      
      const frontendOrder = this.processOrderData(response.data);
      console.log('Converted to frontend format:', frontendOrder);
      return frontendOrder;
    } catch (error: any) {
      console.error(`Error updating order ${orderId}:`, error);
      console.error('Error details:', error.response?.data);
      throw error;
    }
  }

  /**
   * Add billing history to an order
   */
  async addBillingHistory(orderId: string, amount: number, notes?: string): Promise<Order> {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        let orders = savedOrders ? JSON.parse(savedOrders) : [];

        // Find the order
        const orderIndex = orders.findIndex((o: Order) => o.idVenda === orderId);
        if (orderIndex === -1) {
          throw new Error('Order not found');
        }

        const order = orders[orderIndex];

        // Calculate new values
        const newValorRecebido = (order.valorRecebido || 0) + amount;
        const date = formatDate(new Date());
        const newHistoryEntry = `${date}: R$ ${amount.toFixed(2)}${notes ? ` - ${notes}` : ''}`;
        const newHistorico = order.historico ? `${order.historico}\n${newHistoryEntry}` : newHistoryEntry;

        // Update payment status
        let newSituacaoVenda = order.situacaoVenda;
        if (newValorRecebido >= order.valorVenda) {
          newSituacaoVenda = OrderStatus.PAID;
        } else if (newValorRecebido > 0) {
          newSituacaoVenda = OrderStatus.PARTIALLY_PAID;
        }

        // Create new billing history entry
        const newBillingEntry: BillingHistoryEntry = {
          amount,
          notes,
          date,
          createdBy: 'Current User' // In a real app, this would be the current user's name
        };

        // Update the order
        const updatedOrder: Order = {
          ...order,
          valorRecebido: newValorRecebido,
          historico: newHistorico,
          situacaoVenda: newSituacaoVenda,
          status: newSituacaoVenda, // Update status to match situacaoVenda
          ultimaAtualizacao: date,
          billingHistory: [...(order.billingHistory || []), newBillingEntry]
        };

        // Update the orders array
        orders[orderIndex] = updatedOrder;
        localStorage.setItem('orders', JSON.stringify(orders));

        return updatedOrder;
      }

      // For production with real API
      const billingData: ApiBillingHistoryCreate = {
        orderId: orderId,
        amount,
        notes: notes || null
      };

      const response = await api.post<ApiOrder>(`/orders/${orderId}/billing`, billingData);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error(`Error adding billing history to order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Update tracking code for an order
   */
  async updateTrackingCode(orderId: string, trackingCode: string): Promise<Order> {
    return this.updateOrder(orderId, { codigoRastreio: trackingCode });
  }

  /**
   * Get duplicate orders
   */
  async getDuplicateOrders(): Promise<Order[]> {
    try {
      console.log('OrderService: getDuplicateOrders called');
      
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';
      console.log('OrderService: Mock mode is', mockMode);

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];
        console.log(`OrderService: Found ${orders.length} orders in localStorage`);

        // Method 1: Find orders marked as duplicates
        const markedDuplicates = orders.filter((order: Order) => 
          order.situacaoVenda === 'Possíveis Duplicados'
        );
        console.log(`OrderService: Found ${markedDuplicates.length} orders marked as duplicates`);
        
        // Method 2: Find orders with the same phone
        const duplicates: Order[] = [];
        const phoneMap = new Map<string, Order[]>();

        orders.forEach((order: Order) => {
          if (order.telefone) {
            if (!phoneMap.has(order.telefone)) {
              phoneMap.set(order.telefone, []);
            }
            phoneMap.get(order.telefone)?.push(order);
          }
        });

        // Add orders to duplicates if there are multiple with the same phone
        phoneMap.forEach((orderList, phone) => {
          if (orderList.length > 1) {
            console.log(`OrderService: Found ${orderList.length} orders with the same phone: ${phone}`);
            duplicates.push(...orderList);
          }
        });
        
        // Combine both methods and remove duplicates
        const combinedDuplicates = [...markedDuplicates];
        
        // Add duplicates from Method 2 that aren't already in the list
        for (const order of duplicates) {
          if (!combinedDuplicates.some(o => o.idVenda === order.idVenda)) {
            combinedDuplicates.push(order);
          }
        }
        
        console.log(`OrderService: Returning ${combinedDuplicates.length} total duplicate orders`);
        return combinedDuplicates;
      }

      // For production with real API
      const response = await api.get<ApiOrder[]>('/orders/duplicates');
      console.log(`OrderService: API returned ${response.data.length} duplicate orders`);
      return response.data.map(order => this.processOrderData(order));
    } catch (error: any) {
      console.error('Error fetching duplicate orders:', error);
      throw error;
    }
  }

  /**
   * Run duplicate detection algorithm
   */
  async detectDuplicates(): Promise<Order[]> {
    try {
      console.log('OrderService: detectDuplicates called');
      
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';
      console.log('OrderService: Mock mode is', mockMode);

      if (mockMode) {
        console.log('OrderService: Running duplicate detection in mock mode');
        
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];
        console.log(`OrderService: Analyzing ${orders.length} orders for duplicates`);
        
        // Find potential duplicates based on phone number and order amount
        const duplicates: Order[] = [];
        const processedIds = new Set<string>(); // Track already processed orders
        
        for (let i = 0; i < orders.length; i++) {
          const order = orders[i];
          
          // Skip if already processed
          if (processedIds.has(order.idVenda)) continue;
          
          let isDuplicate = false;
          const potentialDuplicates: Order[] = [order];
          
          for (let j = 0; j < orders.length; j++) {
            if (i === j) continue; // Skip self
            
            const otherOrder = orders[j];
            
            // Skip if already processed
            if (processedIds.has(otherOrder.idVenda)) continue;
            
            // Check if orders have the same phone
            if (order.telefone === otherOrder.telefone) {
              // Check if values are within 5% threshold
              const valueDiff = Math.abs(order.valorVenda - otherOrder.valorVenda);
              const maxValue = Math.max(order.valorVenda, otherOrder.valorVenda);
              const percentDiff = maxValue > 0 ? (valueDiff / maxValue) : 0;
              
              if (percentDiff <= 0.05) { // 5% threshold
                isDuplicate = true;
                potentialDuplicates.push(otherOrder);
                processedIds.add(otherOrder.idVenda);
              }
            }
          }
          
          if (isDuplicate) {
            // Mark all as duplicates and add to the result
            potentialDuplicates.forEach(dupOrder => {
              // Create a copy to update the status
              const updatedOrder = {
                ...dupOrder,
                situacaoVenda: 'Possíveis Duplicados',
                status: 'PENDING'
              };
              duplicates.push(updatedOrder);
              processedIds.add(dupOrder.idVenda);
            });
            
            console.log(`OrderService: Found duplicate set with ${potentialDuplicates.length} orders (phone: ${order.telefone})`);
          }
        }
        
        // Update orders in localStorage
        const updatedOrders = orders.map((order: Order) => {
          const duplicate = duplicates.find(d => d.idVenda === order.idVenda);
          if (duplicate) {
            return {
              ...order,
              situacaoVenda: 'Possíveis Duplicados',
              status: 'PENDING'
            };
          }
          return order;
        });
        
        localStorage.setItem('orders', JSON.stringify(updatedOrders));
        console.log(`OrderService: Updated localStorage with ${duplicates.length} marked duplicates`);
        
        return duplicates;
      }

      // For production with real API
      const response = await api.post<ApiOrder[]>('/orders/detect-duplicates');
      console.log(`OrderService: API returned ${response.data.length} detected duplicates`);
      return response.data.map(order => this.processOrderData(order));
    } catch (error: any) {
      console.error('Error detecting duplicate orders:', error);
      throw error;
    }
  }

  /**
   * Search orders
   */
  async searchOrders(query: string): Promise<Order[]> {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];

        // Search in various fields
        return orders.filter((order: Order) => {
          const searchFields = [
            order.idVenda,
            order.cliente,
            order.telefone,
            order.codigoRastreio
          ];

          return searchFields.some(field =>
            field && field.toLowerCase().includes(query.toLowerCase())
          );
        });
      }

      // For production with real API
      const response = await api.get<ApiOrder[]>(`/orders/search?q=${encodeURIComponent(query)}`);
      return response.data.map(order => this.processOrderData(order));
    } catch (error: any) {
      console.error(`Error searching orders with query "${query}":`, error);
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics() {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        const savedOrders = localStorage.getItem('orders');
        const orders = savedOrders ? JSON.parse(savedOrders) : [];

        // Calculate statistics
        const totalOrders = orders.length;
        const totalAmount = orders.reduce((sum: number, order: Order) => sum + (order.valorVenda || 0), 0);
        const totalPaid = orders.reduce((sum: number, order: Order) => sum + (order.valorRecebido || 0), 0);

        // Count orders by status
        const statusCounts: Record<string, number> = {};
        orders.forEach((order: Order) => {
          const status = order.situacaoVenda || 'unknown';
          statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        return {
          total_orders: totalOrders,
          total_amount: totalAmount,
          total_paid: totalPaid,
          payment_rate: totalAmount > 0 ? totalPaid / totalAmount : 0,
          status_counts: statusCounts
        };
      }

      // For production with real API
      const response = await api.get('/orders/statistics');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching order statistics:', error);
      throw error;
    }
  }

  /**
   * Helper function to generate a unique client reference
   */
  private generateClientReference(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 9);
    return `${timestamp}-${random}`;
  }

  /**
   * Generate a suggested order number to help prevent collisions
   */
  private generateSuggestedOrderNumber(): string {
    // Get current date components
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    
    // Add milliseconds and random component for uniqueness
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    // Format: YYYYMMDD-HHMMSS-MS-RND (e.g., 20240117-143052-123-456)
    return `${year}${month}${day}-${hour}${minute}${second}-${milliseconds}-${random}`;
  }

  /**
   * Sleep function for retry delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a new order with the backend API structure
   */
  async createNewOrder(orderData: CreateOrderPayload, retryCount: number = 0): Promise<Order> {
    const MAX_RETRIES = 3;
    const BASE_DELAY = 1000; // 1 second
    
    try {
      // Calculate total amount from items
      const totalAmount = orderData.items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
      
      // Validate items before sending
      const validatedItems = orderData.items.map((item, index) => {
        if (!item.productId || item.productId.trim() === '') {
          throw new Error(`Item ${index + 1} is missing productId`);
        }
        if (!item.productName || item.productName.trim() === '') {
          throw new Error(`Item ${index + 1} is missing productName`);
        }
        return {
          productId: item.productId.trim(),
          productName: item.productName.trim(),
          quantity: item.quantity,
          unitPrice: item.unitPrice
        };
      });

      // Create the backend payload with the expected structure
      const backendPayload: any = {
        customerName: orderData.customerName,
        customerPhone: orderData.customerPhone,
        customerCPF: orderData.customerCPF,
        items: validatedItems,
        collectorId: orderData.collectorId
      };

      // Add optional fields only if they have values
      if (orderData.observation) {
        backendPayload.observation = orderData.observation;
      }
      
      // Add address back - it was in the original payload
      if (orderData.address) {
        backendPayload.address = orderData.address;
      }
      
      console.log(`Creating order (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`, backendPayload);
      console.log('Exact payload being sent to backend:', JSON.stringify(backendPayload, null, 2));
      
      const response = await api.post('/orders', backendPayload);
      console.log('Backend response:', response.data);
      
      // The backend returns the created order, convert it to frontend format
      const frontendOrder = this.processOrderData(response.data);
      console.log('Converted to frontend order:', frontendOrder);
      
      return frontendOrder;
    } catch (error: any) {
      console.error(`Error creating new order (attempt ${retryCount + 1}):`, error);
      console.error('Error response:', error.response);
      console.error('Error response data:', error.response?.data);
      console.error('Error response status:', error.response?.status);
      
      // Check for specific database constraint errors
      if (error.response?.status === 500) {
        const errorMessage = error.response?.data?.message || '';
        
        // Check for duplicate order number error - retry with backoff
        if (errorMessage.includes('Unique constraint failed') && errorMessage.includes('orderNumber')) {
          if (retryCount < MAX_RETRIES) {
            const delay = BASE_DELAY * Math.pow(2, retryCount); // Exponential backoff
            console.log(`Order number conflict detected. Retrying in ${delay}ms...`);
            await this.sleep(delay);
            return this.createNewOrder(orderData, retryCount + 1);
          }
          
          throw new Error('Erro ao gerar número do pedido após múltiplas tentativas. Por favor, aguarde alguns segundos e tente novamente.');
        }
        
        // Generic internal server error
        throw new Error('Erro interno do servidor. Por favor, tente novamente mais tarde.');
      }
      
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      
      throw error;
    }
  }

  /**
   * Delete an order (soft delete - changes status to Cancelado)
   */
  async deleteOrder(orderId: string): Promise<Order> {
    try {
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';

      if (mockMode) {
        // Update order status to 'Deletado' in localStorage
        const savedOrders = localStorage.getItem('orders');
        let orders = savedOrders ? JSON.parse(savedOrders) : [];

        orders = orders.map((o: Order) => {
          if (o.id === orderId) {
            return {
              ...o,
              situacaoVenda: 'Deletado',
              status: 'Deletado',
              ultimaAtualizacao: formatDate(new Date())
            };
          }
          return o;
        });

        localStorage.setItem('orders', JSON.stringify(orders));
        return orders.find((o: Order) => o.id === orderId);
      }

      // For production with real API
      const response = await api.delete<ApiOrder>(`/orders/${orderId}`);
      return this.processOrderData(response.data);
    } catch (error: any) {
      console.error(`Error deleting order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk delete multiple orders (admin only)
   * Process in smaller chunks to avoid timeout issues
   */
  async bulkDeleteOrders(orderIds: string[], permanent: boolean = false): Promise<{
    success: number;
    failed: number;
    details: any[];
  }> {
    try {
      // If less than 50 orders, process normally
      if (orderIds.length <= 50) {
        const response = await api.post('/orders/bulk-delete', {
          orderIds,
          permanent
        }, {
          timeout: 60000 // 60 second timeout
        });
        return response.data;
      }

      // For larger batches, process in chunks
      const chunkSize = 30;
      const results = {
        success: 0,
        failed: 0,
        details: [] as any[]
      };

      console.log(`Processing ${orderIds.length} orders in chunks of ${chunkSize}`);

      for (let i = 0; i < orderIds.length; i += chunkSize) {
        const chunk = orderIds.slice(i, Math.min(i + chunkSize, orderIds.length));
        console.log(`Processing chunk ${Math.floor(i / chunkSize) + 1}/${Math.ceil(orderIds.length / chunkSize)}`);
        
        try {
          const response = await api.post('/orders/bulk-delete', {
            orderIds: chunk,
            permanent
          }, {
            timeout: 30000 // 30 second timeout per chunk
          });
          
          results.success += response.data.success;
          results.failed += response.data.failed;
          results.details.push(...response.data.details);
          
          // Small delay between chunks to avoid overwhelming the server
          if (i + chunkSize < orderIds.length) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error: any) {
          console.error(`Error processing chunk ${Math.floor(i / chunkSize) + 1}:`, error);
          // Add failed items from this chunk
          chunk.forEach(orderId => {
            results.failed++;
            results.details.push({
              orderId,
              status: 'failed',
              error: error.message || 'Erro ao processar chunk'
            });
          });
        }
      }

      return results;
    } catch (error: any) {
      console.error('Error bulk deleting orders:', error);
      throw error;
    }
  }

  /**
   * Permanently delete an order (admin only)
   */
  async permanentDeleteOrder(orderId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('OrderService.permanentDeleteOrder called with:', {
        orderId,
        orderIdType: typeof orderId,
        hasOrderId: !!orderId
      });
      
      // For development with mock data
      const mockMode = process.env.REACT_APP_MOCK_API === 'true';
      console.log('Mock mode:', mockMode);

      if (mockMode) {
        // Remove order from localStorage
        const savedOrders = localStorage.getItem('orders');
        let orders = savedOrders ? JSON.parse(savedOrders) : [];

        const orderToDelete = orders.find((o: Order) => o.id === orderId);
        if (!orderToDelete) {
          console.error('Order not found in localStorage:', { orderId, orders });
          throw new Error('Order not found');
        }

        orders = orders.filter((o: Order) => o.id !== orderId);
        localStorage.setItem('orders', JSON.stringify(orders));

        return {
          success: true,
          message: `Pedido ${orderToDelete.idVenda} foi permanentemente excluído`
        };
      }

      // For production with real API
      const endpoint = `/orders/${orderId}/permanent`;
      console.log('Calling API endpoint:', endpoint);
      
      const response = await api.delete(endpoint);
      console.log('API response:', response);
      
      return response.data;
    } catch (error: any) {
      console.error(`Error permanently deleting order ${orderId}:`, {
        error,
        message: error?.message,
        response: error?.response,
        data: error?.response?.data,
        status: error?.response?.status
      });
      throw error;
    }
  }

  /**
   * Rerun the latest webhook for a specific order
   */
  async rerunWebhook(orderNumber: string): Promise<{
    success: boolean;
    message: string;
    webhookLogId?: string;
    originalLogId?: string;
    orderId?: string;
    processingTimeMs?: number;
  }> {
    try {
      console.log(`Rerunning webhook for order: ${orderNumber}`);
      
      const endpoint = `/webhooks/rerun/${orderNumber}`;
      const response = await api.post(endpoint);
      
      console.log('Webhook rerun response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error(`Error rerunning webhook for order ${orderNumber}:`, {
        error,
        message: error?.message,
        response: error?.response,
        data: error?.response?.data,
        status: error?.response?.status
      });
      
      // If it's a 404, throw a more user-friendly error
      if (error?.response?.status === 404) {
        throw new Error(`Nenhum webhook encontrado para o pedido ${orderNumber}`);
      }
      
      throw error;
    }
  }

  /**
   * Get pending payment confirmations
   */
  async getPendingPaymentConfirmations(): Promise<Order[]> {
    try {
      console.log('[OrderService] Getting pending payment confirmations');
      const response = await api.get('/orders/payment-confirmations/pending');
      const orders = response.data.map((order: any) => this.convertApiOrderToOrder(order));
      return orders;
    } catch (error: any) {
      console.error('Error getting pending payment confirmations:', error);
      throw error;
    }
  }

  /**
   * Confirm or deny a payment
   */
  async confirmPayment(orderId: string, approve: boolean, reason?: string): Promise<Order> {
    try {
      console.log('[OrderService] Confirming payment:', { orderId, approve, reason });
      const response = await api.post(`/orders/${orderId}/confirm-payment`, {
        approve,
        reason
      });
      const convertedOrder = this.convertApiOrderToOrder(response.data);
      return convertedOrder;
    } catch (error: any) {
      console.error('Error confirming payment:', error);
      throw error;
    }
  }
}

export default new OrderService();