import api from './api';

export interface ShippingLabelRequest {
  serviceCode: string;
  weight: number;
  dimensions: {
    height: number;
    width: number;
    length: number;
  };
  declaredValue?: number;
}

export interface ShippingLabel {
  id: string;
  trackingCode: string;
  labelNumber: string;
  serviceType: string;
  weight: number;
  dimensions: {
    height: number;
    width: number;
    length: number;
  };
  declaredValue?: number;
  orderId: string;
  createdAt: string;
  updatedAt: string;
}

export interface TrackingInfo {
  id: string;
  code: string;
  status: string;
  lastUpdate: string;
  events: TrackingEvent[];
  lastSync: string | null;
  isDelivered: boolean;
  hasAlert: boolean;
  alertReason: string | null;
}

export interface TrackingEvent {
  tipo: string;
  status: number;
  data: string;
  hora: string;
  descricao: string;
  local?: string;
  codigo?: string;
  cidade?: string;
  uf?: string;
}

export interface ImportResult {
  message: string;
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    orderNumber: string;
    error: string;
  }>;
}

export interface BulkGenerateRequest {
  orders: Array<{
    orderId: string;
    serviceCode: string;
    weight: number;
    dimensions: {
      height: number;
      width: number;
      length: number;
    };
    declaredValue?: number;
  }>;
}

class ShippingService {
  /**
   * Generate shipping label for an order
   */
  async generateLabel(orderId: string, data: ShippingLabelRequest): Promise<{
    success: boolean;
    trackingCode: string;
    label: ShippingLabel;
  }> {
    try {
      const response = await api.post(`/shipments/generate/${orderId}`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error generating shipping label:', error);
      throw new Error(error.response?.data?.message || 'Failed to generate shipping label');
    }
  }

  /**
   * Refresh tracking information for an order
   */
  async refreshTracking(orderId: string): Promise<{
    success: boolean;
    tracking: TrackingInfo;
    message?: string;
  }> {
    try {
      const response = await api.get(`/shipments/${orderId}/refresh`);
      return response.data;
    } catch (error: any) {
      console.error('Error refreshing tracking:', error);
      throw new Error(error.response?.data?.message || 'Failed to refresh tracking');
    }
  }

  /**
   * Import shipments from file
   */
  async importShipments(file: File): Promise<ImportResult> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post('/shipments/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      console.error('Error importing shipments:', error);
      throw new Error(error.response?.data?.message || 'Failed to import shipments');
    }
  }

  /**
   * Bulk generate shipping labels
   */
  async bulkGenerateLabels(data: BulkGenerateRequest): Promise<{
    message: string;
    totalProcessed: number;
    successful: number;
    failed: number;
    results: {
      successful: Array<{ orderId: string; trackingCode: string }>;
      failed: Array<{ orderId: string; error: string }>;
    };
  }> {
    try {
      const response = await api.post('/shipments/bulk-generate', data);
      return response.data;
    } catch (error: any) {
      console.error('Error bulk generating labels:', error);
      throw new Error(error.response?.data?.message || 'Failed to bulk generate labels');
    }
  }

  /**
   * Get service types for shipping
   */
  getServiceTypes() {
    return [
      { code: '04014', name: 'SEDEX', estimatedDays: 1 },
      { code: '04510', name: 'PAC', estimatedDays: 5 },
      { code: '04162', name: 'SEDEX Hoje', estimatedDays: 0 },
      { code: '04669', name: 'PAC Econômico', estimatedDays: 8 },
      { code: '03220', name: 'SEDEX 10', estimatedDays: 1 },
      { code: '03298', name: 'PAC GF', estimatedDays: 5 },
    ];
  }

  /**
   * Format tracking status to Portuguese
   */
  formatTrackingStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'Postado': 'Postado',
      'Em trânsito': 'Em Trânsito',
      'Saiu para entrega': 'Saiu para Entrega',
      'Entregue': 'Entregue',
      'Aguardando retirada': 'Aguardando Retirada',
      'Devolvido': 'Devolvido',
      'Extraviado': 'Extraviado',
    };
    return statusMap[status] || status;
  }

  /**
   * Get status color for UI
   */
  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' | 'default' {
    const colorMap: Record<string, 'success' | 'warning' | 'error' | 'info' | 'default'> = {
      'Entregue': 'success',
      'Em trânsito': 'info',
      'Saiu para entrega': 'info',
      'Postado': 'default',
      'Aguardando retirada': 'warning',
      'Devolvido': 'error',
      'Extraviado': 'error',
    };
    return colorMap[status] || 'default';
  }
}

export const shippingService = new ShippingService();