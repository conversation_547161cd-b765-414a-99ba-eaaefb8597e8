import api from './api';

class TrackingService {
  /**
   * Manually trigger tracking update for all orders
   * Admin only
   */
  async triggerPollAll(): Promise<any> {
    try {
      const response = await api.post('/correios/poll-all');
      return response.data;
    } catch (error: any) {
      console.error('Error triggering tracking poll:', error);
      throw new Error(
        error.response?.data?.message || 
        'Erro ao atualizar rastreamentos'
      );
    }
  }
}

export default new TrackingService();