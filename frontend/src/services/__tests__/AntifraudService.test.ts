import AntifraudService, { DuplicateReviewItem, ReviewDecision } from '../AntifraudService';
import api from '../api';

// Mock the api module
jest.mock('../api');

describe('AntifraudService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getDuplicateReviewQueue', () => {
    it('should fetch duplicate review queue with default parameters', async () => {
      const mockResponse = {
        data: {
          items: [
            {
              id: '1',
              orderNumber: 'ORD-001',
              customerName: '<PERSON> Do<PERSON>',
              customerCPF: '123.456.789-00',
              status: 'PENDING',
            },
          ],
          total: 1,
          page: 1,
          totalPages: 1,
        },
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AntifraudService.getDuplicateReviewQueue();

      expect(api.get).toHaveBeenCalledWith('/antifraud/duplicates/review-queue', {
        params: { page: 1, limit: 20 },
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should fetch with custom page and limit', async () => {
      const mockResponse = {
        data: { items: [], total: 0, page: 2, totalPages: 0 },
      };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      await AntifraudService.getDuplicateReviewQueue(2, 50);

      expect(api.get).toHaveBeenCalledWith('/antifraud/duplicates/review-queue', {
        params: { page: 2, limit: 50 },
      });
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Network error');
      (api.get as jest.Mock).mockRejectedValue(mockError);

      await expect(AntifraudService.getDuplicateReviewQueue()).rejects.toThrow('Network error');
    });
  });

  describe('reviewDuplicate', () => {
    it('should submit a review decision', async () => {
      const mockResponse = { data: { success: true } };
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const orderId = '123';
      const review: ReviewDecision = {
        decision: 'APPROVED',
        reason: 'Valid order',
      };

      const result = await AntifraudService.reviewDuplicate(orderId, review);

      expect(api.post).toHaveBeenCalledWith(
        `/antifraud/duplicates/${orderId}/review`,
        review
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle review with empty reason', async () => {
      const mockResponse = { data: { success: true } };
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const review: ReviewDecision = {
        decision: 'DENIED',
        reason: '',
      };

      await AntifraudService.reviewDuplicate('456', review);

      expect(api.post).toHaveBeenCalledWith(
        '/antifraud/duplicates/456/review',
        review
      );
    });

    it('should handle review errors', async () => {
      const mockError = new Error('Unauthorized');
      (api.post as jest.Mock).mockRejectedValue(mockError);

      await expect(
        AntifraudService.reviewDuplicate('789', {
          decision: 'APPROVED',
          reason: 'Test',
        })
      ).rejects.toThrow('Unauthorized');
    });
  });

  describe('getOrderById', () => {
    it('should fetch order details by ID', async () => {
      const mockOrder: DuplicateReviewItem = {
        id: '1',
        orderNumber: 'ORD-001',
        customerName: 'John Doe',
        customerCPF: '123.456.789-00',
        customerEmail: '<EMAIL>',
        customerPhone: '(11) 98765-4321',
        fullAddress: 'Rua A, 123, São Paulo - SP',
        total: 150.00,
        paymentMethod: 'COD',
        status: 'PENDING',
        duplicateMatchScore: 85,
        matchedComponents: ['street', 'number'],
        createdAt: '2024-01-01T10:00:00Z',
        updatedAt: '2024-01-01T10:00:00Z',
      };

      const mockResponse = { data: mockOrder };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AntifraudService.getOrderById('1');

      expect(api.get).toHaveBeenCalledWith('/antifraud/orders/1');
      expect(result).toEqual(mockOrder);
    });

    it('should handle order not found', async () => {
      const mockError = { response: { status: 404, data: { message: 'Order not found' } } };
      (api.get as jest.Mock).mockRejectedValue(mockError);

      await expect(AntifraudService.getOrderById('999')).rejects.toEqual(mockError);
    });
  });

  describe('getOrderAuditTrail', () => {
    it('should fetch audit trail for an order', async () => {
      const mockAuditTrail = [
        {
          id: '1',
          orderId: '123',
          action: 'ORDER_CREATED',
          performedBy: 'System',
          performedByRole: 'SYSTEM',
          timestamp: '2024-01-01T10:00:00Z',
          details: { source: 'API' },
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0',
        },
        {
          id: '2',
          orderId: '123',
          action: 'DUPLICATE_DETECTED',
          performedBy: 'Anti-fraud System',
          performedByRole: 'SYSTEM',
          timestamp: '2024-01-01T10:00:05Z',
          details: { matchScore: 85, matchedWith: 'ORD-002' },
        },
      ];

      const mockResponse = { data: mockAuditTrail };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AntifraudService.getOrderAuditTrail('123');

      expect(api.get).toHaveBeenCalledWith('/antifraud/orders/123/audit-trail');
      expect(result).toEqual(mockAuditTrail);
    });

    it('should return empty array for orders without audit trail', async () => {
      const mockResponse = { data: [] };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AntifraudService.getOrderAuditTrail('456');

      expect(result).toEqual([]);
    });
  });

  describe('bulkReview', () => {
    it('should submit bulk review for multiple orders', async () => {
      const mockResponse = { data: { success: true, processed: 3 } };
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const orderIds = ['1', '2', '3'];
      const review: ReviewDecision = {
        decision: 'DENIED',
        reason: 'Bulk fraud detection',
      };

      const result = await AntifraudService.bulkReview(orderIds, review);

      expect(api.post).toHaveBeenCalledWith('/antifraud/duplicates/bulk-review', {
        orderIds,
        review,
      });
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle empty order IDs array', async () => {
      const mockResponse = { data: { success: true, processed: 0 } };
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      await AntifraudService.bulkReview([], {
        decision: 'APPROVED',
        reason: '',
      });

      expect(api.post).toHaveBeenCalled();
    });
  });

  describe('getStatistics', () => {
    it('should fetch antifraud statistics', async () => {
      const mockStats = {
        pendingReviews: 10,
        approvedToday: 25,
        deniedToday: 5,
        duplicateRate: 15.5,
        totalProcessed: 1000,
        averageReviewTime: 300, // seconds
      };

      const mockResponse = { data: mockStats };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AntifraudService.getStatistics();

      expect(api.get).toHaveBeenCalledWith('/antifraud/statistics');
      expect(result).toEqual(mockStats);
    });
  });

  describe('exportReviewQueue', () => {
    it('should export review queue to CSV', async () => {
      const mockCsvData = 'Order,Customer,CPF,Score\nORD-001,John Doe,123.456.789-00,85';
      const mockBlob = new Blob([mockCsvData], { type: 'text/csv' });
      const mockResponse = { data: mockBlob };

      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      const filters = {
        minScore: 80,
        maxScore: 100,
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
      };

      const result = await AntifraudService.exportReviewQueue(filters);

      expect(api.get).toHaveBeenCalledWith('/antifraud/duplicates/export', {
        params: filters,
        responseType: 'blob',
      });
      expect(result).toEqual(mockBlob);
    });

    it('should export without filters', async () => {
      const mockResponse = { data: new Blob([''], { type: 'text/csv' }) };
      (api.get as jest.Mock).mockResolvedValue(mockResponse);

      await AntifraudService.exportReviewQueue();

      expect(api.get).toHaveBeenCalledWith('/antifraud/duplicates/export', {
        params: undefined,
        responseType: 'blob',
      });
    });
  });

  describe('Error Handling', () => {
    it('should preserve error response structure', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid request',
            errors: [{ field: 'decision', message: 'Decision is required' }],
          },
        },
      };

      (api.post as jest.Mock).mockRejectedValue(mockError);

      await expect(
        AntifraudService.reviewDuplicate('123', { decision: '' as any, reason: '' })
      ).rejects.toEqual(mockError);
    });

    it('should handle network errors without response', async () => {
      const networkError = new Error('Network Error');
      (networkError as any).code = 'ECONNREFUSED';

      (api.get as jest.Mock).mockRejectedValue(networkError);

      await expect(AntifraudService.getDuplicateReviewQueue()).rejects.toThrow('Network Error');
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for review decisions', () => {
      const validReview: ReviewDecision = {
        decision: 'APPROVED',
        reason: 'Valid',
      };

      // TypeScript should catch invalid decision values at compile time
      // @ts-expect-error - Testing invalid decision value
      const invalidReview: ReviewDecision = {
        decision: 'MAYBE',
        reason: 'Invalid',
      };

      expect(validReview.decision).toBe('APPROVED');
      expect(invalidReview.decision).toBe('MAYBE');
    });
  });
});