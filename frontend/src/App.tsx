import React, { useState, useEffect } from 'react';
import { createB<PERSON>erRouter, RouterProvider } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { ptBR } from 'date-fns/locale';
import './utils/testConnection'; // Import test utility
import './utils/windowDebug'; // Import debug utilities
import './utils/debugOrders'; // Import order debug utilities

// Import custom contexts
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { UnifiedUserProvider } from './contexts/UnifiedUserContext';
import { AuthProvider } from './contexts/AuthContext';
import { OrderDataProvider } from './contexts/OrderDataContext';
import { ConversionProvider } from './contexts/ConversionContext';
import { FeatureFlagProvider } from './contexts/FeatureFlagContext';
import { UsuariosProvider } from './contexts/UsuariosContext';
import ErrorBoundary from './components/ErrorBoundary';
import globalStyles from './components/GlobalStyles';

// Import routes
import { routes } from './routes/routes';

// Services
import UnifiedAuthService from './services/UnifiedAuthService';
import { Order } from './types/Order';

// Development helpers
import SetupTestData from './components/SetupTestData';

function App() {
  const [orders, setOrders] = useState<Order[]>([]);

  // Check authentication on app start
  useEffect(() => {
    try {
      const isAuth = UnifiedAuthService.isAuthenticated();
      console.log(`Verificação de autenticação: ${isAuth ? 'Autenticado' : 'Não autenticado'}`);

      if (isAuth) {
        const userInfo = UnifiedAuthService.getUserInfo();
        console.log(`Usuário autenticado: ${userInfo?.fullName} (${userInfo?.email})`);
      }
    } catch (error: any) {
      console.error("Erro ao verificar autenticação:", error);
    }
  }, []);

  // Load orders from localStorage
  useEffect(() => {
    const storedOrders = localStorage.getItem('orders');
    if (storedOrders) {
      try {
        const parsedOrders = JSON.parse(storedOrders);
        setOrders(parsedOrders);
      } catch (error: any) {
        console.error('Error parsing stored orders:', error);
      }
    }
  }, []);

  // Check for tracking updates periodically
  useEffect(() => {
    if (!UnifiedAuthService.isAuthenticated() || orders.length === 0) {
      return;
    }


  }, [orders]);

  // Create router with routes
  const router = createBrowserRouter(routes);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <NotificationProvider>
          <FeatureFlagProvider>
            <UnifiedUserProvider>
              <AuthProvider>
                <UsuariosProvider>
                  <OrderDataProvider>
                    <ConversionProvider>
                    <CssBaseline />
                    {globalStyles}
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                      <RouterProvider router={router} />
                      {process.env.NODE_ENV === 'development' && <SetupTestData />}
                    </LocalizationProvider>
                    </ConversionProvider>
                  </OrderDataProvider>
                </UsuariosProvider>
              </AuthProvider>
            </UnifiedUserProvider>
          </FeatureFlagProvider>
        </NotificationProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;// Force rebuild - Fix error reference issue - 2025-01-12T18:45:00Z
