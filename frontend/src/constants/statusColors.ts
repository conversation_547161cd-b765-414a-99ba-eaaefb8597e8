/**
 * Centralized status colors for consistency across the application
 */
export const STATUS_COLORS: Record<string, string> = {
  // Order Status Colors - Darker versions for better visibility
  'PAGAMENTO PENDENTE': '#FFA726',      // Darker Amber - waiting for payment
  'PAGAMENTOPENDENTE': '#FFA726',
  'COMPLETO': '#66BB6A',                // Darker Green - success/completed
  'CANCELADO': '#EF5350',               // Darker Red - cancelled/failed
  'SEPARAÇÃO': '#42A5F5',               // Darker Blue - in progress
  'SEPARACAO': '#42A5F5',
  'FRUSTRADO': '#EF5350',               // Darker Red - failed attempt
  'RECUPERAÇÃO': '#AB47BC',             // Darker Purple - recovery process
  'RECUPERACAO': '#AB47BC',
  'NEGOCIAÇÃO': '#7E57C2',              // Darker Deep Purple - negotiation
  'NEGOCIACAO': '#7E57C2',
  'RETIRAR CORREIOS': '#FF7043',        // Darker Orange - action needed
  'ENTREGA FALHA': '#FF5722',           // Darker Deep Orange - delivery issue
  'CONFIRMAR ENTREGA': '#9C27B0',       // Medium Purple - action required
  'DEVOLVIDO CORREIOS': '#EF5350',      // Darker Red - returned/failed
  'LIBERAÇÃO': '#29B6F6',               // Darker Cyan - release/approval
  'LIBERACAO': '#29B6F6',
  'RECEBER HOJE': '#26C6DA',            // Darker Cyan - today's delivery
  'POSSÍVEIS DUPLICADOS': '#FF7043',    // Darker Orange - attention/warning
  'POSSIVEIS DUPLICADOS': '#FF7043',
  'DELETADO': '#9E9E9E',                // Darker Grey - deleted/inactive
  'ANÁLISE': '#FFCA28',                 // Darker Yellow - under analysis
  'ANALISE': '#FFCA28',
  'TRÂNSITO': '#5C9DE5',                // Darker Blue - in transit
  'TRANSITO': '#5C9DE5',
  'PARCIAL': '#FFB74D',                 // Darker Orange - partial
  'PROMESSA': '#BA68C8',               // Darker Purple - promise/commitment
  'CONFIRMAR PAGAMENTO': '#FF9800',     // Orange - payment confirmation needed
  'CONFIRMARPAGAMENTO': '#FF9800',
  
  // Default
  'DEFAULT': '#BDBDBD'                  // Light Grey
};

/**
 * Get color for a status
 */
export const getStatusColor = (status: string): string => {
  if (!status) return STATUS_COLORS.DEFAULT;
  
  const normalizedStatus = status.toUpperCase().replace(/[_-]/g, ' ');
  return STATUS_COLORS[normalizedStatus] || STATUS_COLORS.DEFAULT;
};

/**
 * Get display name for a status
 */
export const getStatusDisplayName = (status: string): string => {
  if (!status) return '';
  
  // Convert from backend format to display format
  const statusMap: Record<string, string> = {
    'PagamentoPendente': 'Pagamento Pendente',
    'Completo': 'Completo',
    'Parcial': 'Parcial',
    'Cancelado': 'Cancelado',
    'Transito': 'Trânsito',
    'Analise': 'Análise',
    'Separacao': 'Separação',
    'Frustrado': 'Frustrado',
    'Recuperacao': 'Recuperação',
    'Negociacao': 'Negociação',
    'RetiraCorreio': 'Retirar Correios',
    'ConfirmarEntrega': 'Confirmar Entrega',
    'Deletado': 'Deletado',
    'Liberacao': 'Liberação',
    'Promessa': 'Promessa',
    'ConfirmarPagamento': 'Confirmar Pagamento'
  };
  
  return statusMap[status] || status;
};