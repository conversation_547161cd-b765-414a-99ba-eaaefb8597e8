import React, { ReactElement } from 'react';
import {
  PaidOutlined as PaidOutlinedIcon,
  CheckCircleOutlined as CheckCircleOutlinedIcon,
  CancelOutlined as CancelOutlinedIcon,
  HourglassEmptyOutlined as HourglassEmptyOutlinedIcon,
  ErrorOutlineOutlined as ErrorOutlineOutlinedIcon,
  ReplayOutlined as ReplayOutlinedIcon,
  HandshakeOutlined as HandshakeOutlinedIcon,
  LocalShippingOutlined as LocalShippingOutlinedIcon,
  WarningAmberOutlined as WarningAmberOutlinedIcon,
  ThumbUpAltOutlined as ThumbUpAltOutlinedIcon,
  KeyboardReturnOutlined as KeyboardReturnOutlinedIcon,
  AccessTimeOutlined as AccessTimeOutlinedIcon,
  DirectionsBus as TransitIcon,
  MoneyOff as PartialIcon,
} from '@mui/icons-material';

// Map backend status values to frontend display
export interface StatusConfig {
  backendValue: string;
  displayName: string;
  filterValue: string;
  icon: ReactElement;
  color: string;
}

// All possible statuses from backend OrderStatus enum
export const ORDER_STATUSES: StatusConfig[] = [
  {
    backendValue: 'Analise',
    displayName: 'Análise',
    filterValue: 'analise',
    icon: <HourglassEmptyOutlinedIcon sx={{ color: '#FFD54F' }} />,
    color: '#FFD54F'
  },
  {
    backendValue: 'PagamentoPendente',
    displayName: 'Pagamento Pendente',
    filterValue: 'pagamentopendente',
    icon: <PaidOutlinedIcon sx={{ color: '#FFE082' }} />,
    color: '#FFE082'
  },
  {
    backendValue: 'Completo',
    displayName: 'Completo',
    filterValue: 'completo',
    icon: <CheckCircleOutlinedIcon sx={{ color: '#81C784' }} />,
    color: '#81C784'
  },
  {
    backendValue: 'Parcial',
    displayName: 'Parcial',
    filterValue: 'parcial',
    icon: <PartialIcon sx={{ color: '#FFCC80' }} />,
    color: '#FFCC80'
  },
  {
    backendValue: 'Cancelado',
    displayName: 'Cancelado',
    filterValue: 'cancelado',
    icon: <CancelOutlinedIcon sx={{ color: '#E57373' }} />,
    color: '#E57373'
  },
  {
    backendValue: 'Transito',
    displayName: 'Trânsito',
    filterValue: 'transito',
    icon: <TransitIcon sx={{ color: '#90CAF9' }} />,
    color: '#90CAF9'
  },
  {
    backendValue: 'Separacao',
    displayName: 'Separação',
    filterValue: 'separacao',
    icon: <HourglassEmptyOutlinedIcon sx={{ color: '#64B5F6' }} />,
    color: '#64B5F6'
  },
  {
    backendValue: 'Frustrado',
    displayName: 'Frustrado',
    filterValue: 'frustrado',
    icon: <ErrorOutlineOutlinedIcon sx={{ color: '#E57373' }} />,
    color: '#E57373'
  },
  {
    backendValue: 'Recuperacao',
    displayName: 'Recuperação',
    filterValue: 'recuperacao',
    icon: <ReplayOutlinedIcon sx={{ color: '#BA68C8' }} />,
    color: '#BA68C8'
  },
  {
    backendValue: 'Negociacao',
    displayName: 'Negociação',
    filterValue: 'negociacao',
    icon: <HandshakeOutlinedIcon sx={{ color: '#9575CD' }} />,
    color: '#9575CD'
  },
  {
    backendValue: 'RetirarCorreios',
    displayName: 'Retirar Correios',
    filterValue: 'retirarcorreios',
    icon: <LocalShippingOutlinedIcon sx={{ color: '#EF5350' }} />,
    color: '#EF5350'
  },
  {
    backendValue: 'EntregaFalha',
    displayName: 'Entrega Falha',
    filterValue: 'entregafalha',
    icon: <WarningAmberOutlinedIcon sx={{ color: '#FF8A65' }} />,
    color: '#FF8A65'
  },
  {
    backendValue: 'ConfirmarEntrega',
    displayName: 'Confirmar Entrega',
    filterValue: 'confirmarentrega',
    icon: <ThumbUpAltOutlinedIcon sx={{ color: '#FF8A80' }} />,
    color: '#FF8A80'
  },
  {
    backendValue: 'DevolvidoCorreios',
    displayName: 'Devolvido Correios',
    filterValue: 'devolvidocorreios',
    icon: <KeyboardReturnOutlinedIcon sx={{ color: '#E57373' }} />,
    color: '#E57373'
  }
];

// Helper function to get status config by backend value
export const getStatusConfig = (backendValue: string): StatusConfig | undefined => {
  return ORDER_STATUSES.find(status => 
    status.backendValue.toLowerCase() === backendValue.toLowerCase()
  );
};

// Helper function to get display name from backend value
export const getStatusDisplayName = (backendValue: string): string => {
  const config = getStatusConfig(backendValue);
  return config ? config.displayName : backendValue;
};