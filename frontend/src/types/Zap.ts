export enum ZapStatus {
  Ativo = 'Ativo',
  Aquecendo = 'Aquecendo',
  Pronto = 'Pronto',
  Bloqueado = 'Bloqueado',
  EmAnalise = 'EmAnalise',
  Recuperado = 'Recuperado',
  StandBy = 'StandBy',
}

export interface Zap {
  id: string;
  name: string;
  phoneNumber?: string | null;
  status: ZapStatus;
  createdAt: string;
  updatedAt: string;
  _count?: {
    orders: number;
  };
}

export interface ZapStats {
  totalOrders: number;
  totalRevenue: number;
  ordersByStatus: {
    status: string;
    count: number;
  }[];
}

export interface CreateZapDto {
  name: string;
  phoneNumber?: string;
  status?: ZapStatus;
}

export interface UpdateZapDto {
  name?: string;
  phoneNumber?: string;
  status?: ZapStatus;
}

// Legacy interfaces for backward compatibility
export interface ZapConfig {
  id: string;
  name: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ZapConfigInput {
  name: string;
  isActive: boolean;
} 