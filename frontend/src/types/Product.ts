// Unified Product types for the entire application

export enum VariationType {
  CAPSULAS = 'CAPSULAS',
  GOTAS = 'GOTAS',
  GEL = 'GEL',
  SPRAY = 'SPRAY',
  CREME = 'CREME',
  CUSTOM = 'CUSTOM',
}

export interface Inventory {
  id: string;
  quantity: number;
  minAlert: number;
  productVariationId: string;
  updatedAt: Date;
}

export interface ProductVariation {
  id: string;
  productId: string;
  variation: string;  // Display name
  type: VariationType;
  customName?: string;
  sku: string;
  price: number;
  costPrice: number;  // Same as price, for UI compatibility
  active: boolean;
  inventory?: Inventory;
  createdAt: Date;
  updatedAt: Date;
}

export interface KitItem {
  id: string;
  kitId: string;
  variationId: string;
  quantity: number;
  variation: ProductVariation;  // Required: always present by design
}

export interface Kit {
  id: string;
  productId: string;
  sku: string;
  name: string;
  description?: string;
  price: number;
  active: boolean;
  items: KitItem[];
  product?: Product;
  createdAt: Date;
  updatedAt: Date;
  _count?: {
    orders: number;
  };
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  active: boolean;
  tenantId: string;
  variations: ProductVariation[];
  kits: Kit[];
  createdAt: Date;
  updatedAt: Date;
  _count: {
    variations: number;
    kits: number;
  };
}

// DTOs for API calls
export interface CreateProductDto {
  name: string;
  description?: string;
  imageUrl?: string;
  price?: number;
  active?: boolean;
  variations?: CreateVariationDto[];
}

export interface CreateVariationDto {
  variation: string;
  sku: string;
  price: number;
  active?: boolean;
}

export interface UpdateProductDto {
  name?: string;
  description?: string;
  imageUrl?: string;
  active?: boolean;
}

export interface UpdateVariationDto {
  variation?: string;
  sku?: string;
  price?: number;
  active?: boolean;
}

export interface CreateKitDto {
  name: string;
  description?: string;
  price: number;
  active?: boolean; // Changed from isActive to active to match backend
  items: Array<{
    productVariationId: string; // Changed from variationId to match backend
    quantity: number;
  }>;
}

// Helper functions
export function generateSku(productName: string, variationType: string): string {
  const prefix = productName.substring(0, 3).toUpperCase();
  const typePrefix = variationType.substring(0, 3).toUpperCase();
  const timestamp = Date.now().toString().slice(-4);
  return `${prefix}-${typePrefix}-${timestamp}`;
}

export function getVariationTypeLabel(type: VariationType): string {
  const labels: Record<VariationType, string> = {
    [VariationType.CAPSULAS]: 'Cápsulas',
    [VariationType.GOTAS]: 'Gotas',
    [VariationType.GEL]: 'Gel',
    [VariationType.SPRAY]: 'Spray',
    [VariationType.CREME]: 'Creme',
    [VariationType.CUSTOM]: 'Personalizado',
  };
  return labels[type] || type;
}