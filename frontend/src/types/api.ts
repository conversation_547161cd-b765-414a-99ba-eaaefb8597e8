// API response types that match the backend models

// User types
export interface ApiUser {
  id: string;
  email: string;
  fullName: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'COLLECTOR' | 'SELLER';
  isActive: boolean;
  tenantId: string;
}

// Order status enum - matching backend
export enum OrderStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  PAID = 'PAID',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  NEGOTIATING = 'NEGOTIATING',
  CANCELLED = 'CANCELLED',
  DELIVERED = 'DELIVERED',
  DELETED = 'DELETED',
  LIBERACAO = 'LIBERACAO'
}

// Order types
export interface ApiBillingHistory {
  id: string;
  orderId: string;
  amount: number;
  notes: string | null;
  createdAt: string;
  createdBy: string;
  createdByName?: string; // This might be populated by the backend
}

export interface ApiOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  fullAddress: string;
  customerCPF?: string | null;
  total: number;
  paymentReceivedAmount: number;
  status: OrderStatus;
  trackingCode: string | null;
  isDuplicate: boolean;
  tenantId: string;
  sellerId: string;
  collectorId: string | null;
  offerId: string | null;
  zapId?: string | null;
  paymentReceivedDate?: string | null;
  lastContactDate?: string | null;
  nextPaymentDate?: string | null;
  createdAt: string;
  updatedAt: string | null;
  deletedAt?: string | null;
  deletedBy?: string | null;
  observation: string | null;
  lastWebhookEvent?: string | null;
  tracking?: {
    id: string;
    code: string;
    status: string;
    lastUpdate: string;
    events: any[];
  };
  billingHistory?: ApiBillingHistory[];
  statusHistory?: Array<{
    id: string;
    previousStatus: string;
    newStatus: string;
    changedById: string;
    changedByName?: string;
    createdAt: string;
  }>;
  addressComponents?: {
    street: string;
    streetNumber: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // These fields might be populated by the backend
  sellerName?: string;
  collectorName?: string;
  // Nested relations from backend
  seller?: {
    id: string;
    name: string;
    email?: string;
  };
  collector?: {
    id: string;
    name: string;
    email?: string;
  };
}

export interface ApiOrderCreate {
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  fullAddress: string;
  total: number;
  trackingCode?: string | null;
  sellerId: string;
}

export interface ApiOrderUpdate {
  customerName?: string;
  customerPhone?: string;
  fullAddress?: string;
  total?: number;
  paymentReceivedAmount?: number;
  status?: OrderStatus;
  // trackingCode removed - tracking is handled via separate Tracking model
  collectorId?: string | null;
  sellerId?: string | null;
  isDuplicate?: boolean;
  customerCPF?: string | null;
  paymentReceivedDate?: string | null;
  observation?: string | null;
  nextPaymentDate?: string | null;
}

export interface ApiBillingHistoryCreate {
  orderId: string;
  amount: number;
  notes?: string | null;
}

// Webhook types
export interface ApiWebhookOrder {
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_address: string;
  total_amount: number;
  seller_id: number;
  tracking_code?: string | null;
}

// Statistics types
export interface ApiOrderStatistics {
  total_orders: number;
  total_amount: number;
  total_paid: number;
  payment_rate: number;
  status_counts: Record<OrderStatus, number>;
}

// Tracking types
export interface ApiTrackingUpdate {
  order_id: string;  // Changed to string to match Order.idVenda
  tracking_code: string;
  status: string;
  last_update: string;
  location?: string;
  estimated_delivery?: string;
  is_critical?: boolean;
}
