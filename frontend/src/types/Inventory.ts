import { VariationType } from './Product';

// Product types for inventory
export enum ProductType {
  GEL = 'GEL',
  CAPSULES = 'CAPSULES'
}

// Transaction types
export enum TransactionType {
  SALE = 'SALE',           // Deduct from inventory when a sale is made
  PURCHASE = 'PURCHASE',   // Add to inventory when purchasing from supplier
  ADJUSTMENT = 'ADJUSTMENT', // Manual inventory adjustment
  RETURN = 'RETURN'        // Add to inventory when a customer returns an item
}

// Keep the old enum for backward compatibility
export enum InventoryTransactionType {
  SALE = 'sale',           // Deduct from inventory when a sale is made
  PURCHASE = 'purchase',   // Add to inventory when purchasing from supplier
  ADJUSTMENT = 'adjustment', // Manual inventory adjustment
  RETURN = 'return'        // Add to inventory when a customer returns an item
}

// Inventory item interface representing base stock for each product type
export interface InventoryItem {
  productType: ProductType;
  currentStock: number;
  minimumStock: number;    // For reorder alerts
  unitCost: number;        // Cost price of the item
  transactions?: InventoryTransaction[];
}

// Inventory transaction interface for tracking inventory changes
export interface InventoryTransaction {
  id: string;
  productType: ProductType;
  type: TransactionType;
  quantity: number;        // Positive for additions, negative for deductions
  unitCost?: number;
  totalCost?: number;
  reference?: string;      // Related order ID if applicable
  notes?: string;
  timestamp: string;
  performedBy: string;
}

// Interface for inventory levels by variation
export interface InventoryLevels {
  gel: number;
  capsulas: number;
}

// Interface for inventory statistics
export interface InventoryStats {
  totalItems: number;
  lowStockItems: number;
  totalValue: number;
  mostSoldVariation: VariationType | null;
}

// Generate a unique ID for inventory items and transactions
export const generateInventoryId = (): string => {
  return `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Initial inventory data
export const initialInventory: InventoryItem[] = [
  {
    productType: ProductType.GEL,
    currentStock: 500,
    minimumStock: 100,
    unitCost: 25.5,
    transactions: []
  },
  {
    productType: ProductType.CAPSULES,
    currentStock: 750,
    minimumStock: 150,
    unitCost: 18.2,
    transactions: []
  }
]; 