import { OrderStatus } from './api';

// Order interface that exactly matches backend schema
export interface Order {
  // Core fields from backend
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  status: OrderStatus | string;
  total: number;
  sellerId: string;
  tenantId?: string;
  createdAt: string;
  updatedAt: string;
  
  // Payment fields
  paymentReceivedAmount: number;
  paymentReceivedDate?: string;
  commissionApproved?: boolean;
  
  // Relations
  customerId?: string;
  collectorId?: string;
  
  // Contact/Promise fields
  nextPaymentDate?: string;
  lastContactDate?: string;
  
  // Additional fields
  zapId?: string;
  zapSourceId?: string;
  customerCPF?: string;
  fullAddress?: string;
  observation?: string;
  
  // Tracking fields
  trackingCode?: string;
  trackingStatus?: string;
  statusCorreios?: string;
  lastWebhookEvent?: string;
  
  // Anti-fraud fields
  isDuplicate?: boolean;
  duplicateStatus?: string;
  riskScore?: number;
  riskLevel?: string;
  requiresReview?: boolean;
  
  // Soft delete fields
  deletedAt?: string;
  deletedBy?: string;
  
  // Nested relations (populated by backend)
  seller?: {
    id: string;
    name: string;
    email?: string;
  };
  
  collector?: {
    id: string;
    name: string;
    email?: string;
  };
  
  zapSource?: {
    id: string;
    name: string;
    status: string;
  };
  
  tracking?: {
    id: string;
    code: string;
    status: string;
    lastUpdate: string;
    events?: any[];
    isDelivered?: boolean;
  };
  
  addressComponents?: {
    street: string;
    streetNumber: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  
  items?: Array<{
    id: string;
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
  }>;
  
  billingHistory?: Array<{
    id?: string;
    amount: number;
    notes?: string;
    date: string;
    createdBy?: string;
  }>;
  
  statusHistory?: Array<{
    id: string;
    previousStatus: string;
    newStatus: string;
    changedById: string;
    changedByName?: string;
    createdAt: string;
  }>;
  
  // Computed/Display fields (will be calculated in components)
  // These are NOT stored in backend but computed for display
  sellerName?: string;
  collectorName?: string;
  displayStatus?: string; // Portuguese status for display
}

// Helper type for CSV import - maps CSV columns to Order fields
export interface CSVImportMapping {
  'Data Venda'?: string;
  'ID Venda': string;
  'Cliente': string;
  'Telefone': string;
  'Oferta'?: string;
  'Valor Venda': string;
  'Status'?: string;
  'Situação Venda': string;
  'Valor Recebido'?: string;
  'Historico'?: string;
  'Ultima Atualização'?: string;
  'Código de Rastreio'?: string;
  'Status Correios'?: string;
  'Vendedor'?: string;
  'Operador'?: string;
  'ESTADO DO DESTINATÁRIO'?: string;
  'CIDADE DO DESTINATÁRIO'?: string;
  'RUA DO DESTINATÁRIO'?: string;
  'CEP DO DESTINATÁRIO'?: string;
  'COMPLEMENTO DO DESTINATÁRIO'?: string;
  'BAIRRO DO DESTINATÁRIO'?: string;
  'NÚMERO DO ENDEREÇO DO DESTINATÁRIO'?: string;
  'DOCUMENTO CLIENTE'?: string;
}

// Status display mapping (English to Portuguese)
export const STATUS_DISPLAY_MAP: Record<string, string> = {
  'Analise': 'Análise',
  'Separacao': 'Separação',
  'Transito': 'Trânsito',
  'PagamentoPendente': 'Pagamento Pendente',
  'Completo': 'Completo',
  'Parcial': 'Parcial',
  'Negociacao': 'Negociação',
  'Promessa': 'Promessa',
  'Frustrado': 'Frustrado',
  'Recuperacao': 'Recuperação',
  'Cancelado': 'Cancelado',
  'RetirarCorreios': 'Retirar Correios',
  'EntregaFalha': 'Entrega Falha',
  'ConfirmarEntrega': 'Confirmar Entrega',
  'DevolvidoCorreios': 'Devolvido Correios',
};

// Column header labels for tables
export const COLUMN_LABELS = {
  orderNumber: 'ID Venda',
  createdAt: 'Data Venda',
  customerName: 'Cliente',
  customerPhone: 'Telefone',
  status: 'Status',
  total: 'Valor Venda',
  paymentReceivedAmount: 'Valor Recebido',
  paymentReceivedDate: 'Pgto Recebido',
  trackingCode: 'Código Rastreio',
  updatedAt: 'Última Atualização',
  sellerName: 'Vendedor',
  collectorName: 'Operador',
  statusCorreios: 'Status Correios',
  nextPaymentDate: 'Próx. Pagamento',
  lastContactDate: 'Última Negociação',
};