import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AntifraudService, { DuplicateReviewItem, ReviewDecision } from '../services/AntifraudService';

// Query keys
export const antifraudKeys = {
  all: ['antifraud'] as const,
  queue: (page: number = 1) => [...antifraudKeys.all, 'queue', page] as const,
  auditTrail: (orderId: string) => [...antifraudKeys.all, 'audit', orderId] as const,
  statistics: () => [...antifraudKeys.all, 'statistics'] as const,
};

// Hooks
export const useAntifraudQueue = (page: number = 1, enabled: boolean = true) => {
  return useQuery({
    queryKey: antifraudKeys.queue(page),
    queryFn: () => AntifraudService.getDuplicateReviewQueue(page, 20),
    staleTime: 30 * 1000, // Consider data stale after 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchIntervalInBackground: true,
    enabled,
  });
};

export const useReviewDuplicate = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderId, review }: { orderId: string; review: ReviewDecision }) =>
      AntifraudService.reviewDuplicate(orderId, review),
    onSuccess: (_, variables) => {
      // Invalidate and refetch queue
      queryClient.invalidateQueries({ queryKey: antifraudKeys.all });
      
      // Optimistically update the cache
      queryClient.setQueriesData(
        { queryKey: antifraudKeys.all },
        (old: any) => {
          if (!old?.items) return old;
          
          return {
            ...old,
            items: old.items.filter((item: DuplicateReviewItem) => item.id !== variables.orderId),
          };
        }
      );
    },
  });
};

export const useAuditTrail = (orderId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: antifraudKeys.auditTrail(orderId),
    queryFn: () => AntifraudService.getOrderAuditTrail(orderId),
    staleTime: 60 * 1000, // 1 minute
    enabled: enabled && !!orderId,
  });
};

// Prefetch helper
export const prefetchAntifraudData = async (queryClient: any) => {
  // Prefetch first page of queue
  await queryClient.prefetchQuery({
    queryKey: antifraudKeys.queue(1),
    queryFn: () => AntifraudService.getDuplicateReviewQueue(1, 20),
  });
  
  // Could also prefetch statistics, etc.
};