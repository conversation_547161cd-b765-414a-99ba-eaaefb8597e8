import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  useAntifraudQueue,
  useReviewDuplicate,
  useAuditTrail,
  prefetchAntifraudData,
} from '../useAntifraudQuery';
import AntifraudService from '../../services/AntifraudService';

// Mock AntifraudService
jest.mock('../../services/AntifraudService');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClientProvider, { client: queryClient }, children)
  );
};

describe('useAntifraudQuery hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useAntifraudQueue', () => {
    it('should fetch review queue data', async () => {
      const mockData = {
        items: [
          { id: '1', orderNumber: 'ORD-001' },
          { id: '2', orderNumber: 'ORD-002' },
        ],
        total: 2,
        page: 1,
        totalPages: 1,
      };

      (AntifraudService.getDuplicateReviewQueue as jest.Mock).mockResolvedValue(mockData);

      const { result } = renderHook(() => useAntifraudQueue(1), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(true);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockData);
      expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledWith(1, 20);
    });

    it('should handle errors', async () => {
      const mockError = new Error('Failed to fetch');
      (AntifraudService.getDuplicateReviewQueue as jest.Mock).mockRejectedValue(mockError);

      const { result } = renderHook(() => useAntifraudQueue(1), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
    });

    it('should not fetch when disabled', () => {
      const { result } = renderHook(() => useAntifraudQueue(1, false), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.data).toBeUndefined();
      expect(AntifraudService.getDuplicateReviewQueue).not.toHaveBeenCalled();
    });

    it('should refetch at intervals', async () => {
      jest.useFakeTimers();
      const mockData = { items: [], total: 0, page: 1, totalPages: 0 };
      (AntifraudService.getDuplicateReviewQueue as jest.Mock).mockResolvedValue(mockData);

      renderHook(() => useAntifraudQueue(1), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledTimes(1);
      });

      // Fast forward 60 seconds
      jest.advanceTimersByTime(60 * 1000);

      await waitFor(() => {
        expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledTimes(2);
      });

      jest.useRealTimers();
    });
  });

  describe('useReviewDuplicate', () => {
    it('should successfully review a duplicate', async () => {
      const mockResponse = { success: true };
      (AntifraudService.reviewDuplicate as jest.Mock).mockResolvedValue(mockResponse);

      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        React.createElement(QueryClientProvider, { client: queryClient }, children)
      );

      const { result } = renderHook(() => useReviewDuplicate(), { wrapper });

      await result.current.mutateAsync({
        orderId: '1',
        review: { decision: 'APPROVED', reason: 'Valid order' },
      });

      expect(AntifraudService.reviewDuplicate).toHaveBeenCalledWith('1', {
        decision: 'APPROVED',
        reason: 'Valid order',
      });
    });

    it('should optimistically update cache on success', async () => {
      const mockResponse = { success: true };
      (AntifraudService.reviewDuplicate as jest.Mock).mockResolvedValue(mockResponse);

      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      // Set initial cache data
      queryClient.setQueryData(['antifraud', 'queue', 1], {
        items: [
          { id: '1', orderNumber: 'ORD-001' },
          { id: '2', orderNumber: 'ORD-002' },
        ],
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        React.createElement(QueryClientProvider, { client: queryClient }, children)
      );

      const { result } = renderHook(() => useReviewDuplicate(), { wrapper });

      await result.current.mutateAsync({
        orderId: '1',
        review: { decision: 'APPROVED', reason: '' },
      });

      // Check cache was updated
      const cacheData = queryClient.getQueryData(['antifraud', 'queue', 1]) as any;
      expect(cacheData.items).toHaveLength(1);
      expect(cacheData.items[0].id).toBe('2');
    });

    it('should handle mutation errors', async () => {
      const mockError = new Error('Review failed');
      (AntifraudService.reviewDuplicate as jest.Mock).mockRejectedValue(mockError);

      const { result } = renderHook(() => useReviewDuplicate(), {
        wrapper: createWrapper(),
      });

      await expect(
        result.current.mutateAsync({
          orderId: '1',
          review: { decision: 'DENIED', reason: 'Fraud' },
        })
      ).rejects.toThrow('Review failed');
    });
  });

  describe('useAuditTrail', () => {
    it('should fetch audit trail for an order', async () => {
      const mockAuditTrail = [
        {
          id: '1',
          action: 'ORDER_CREATED',
          performedBy: 'System',
          timestamp: '2024-01-01T10:00:00Z',
        },
        {
          id: '2',
          action: 'DUPLICATE_DETECTED',
          performedBy: 'Anti-fraud',
          timestamp: '2024-01-01T10:00:05Z',
        },
      ];

      (AntifraudService.getOrderAuditTrail as jest.Mock).mockResolvedValue(mockAuditTrail);

      const { result } = renderHook(() => useAuditTrail('order-1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockAuditTrail);
      expect(AntifraudService.getOrderAuditTrail).toHaveBeenCalledWith('order-1');
    });

    it('should not fetch when orderId is empty', () => {
      const { result } = renderHook(() => useAuditTrail(''), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.data).toBeUndefined();
      expect(AntifraudService.getOrderAuditTrail).not.toHaveBeenCalled();
    });

    it('should not fetch when disabled', () => {
      const { result } = renderHook(() => useAuditTrail('order-1', false), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(false);
      expect(AntifraudService.getOrderAuditTrail).not.toHaveBeenCalled();
    });
  });

  describe('prefetchAntifraudData', () => {
    it('should prefetch the first page of queue data', async () => {
      const mockData = {
        items: [{ id: '1', orderNumber: 'ORD-001' }],
        total: 1,
        page: 1,
        totalPages: 1,
      };

      (AntifraudService.getDuplicateReviewQueue as jest.Mock).mockResolvedValue(mockData);

      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
        },
      });

      await prefetchAntifraudData(queryClient);

      // Check that data was prefetched
      const cachedData = queryClient.getQueryData(['antifraud', 'queue', 1]);
      expect(cachedData).toEqual(mockData);
      expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledWith(1, 20);
    });
  });

  describe('Query Key Management', () => {
    it('should generate correct query keys', () => {
      const { antifraudKeys } = require('../useAntifraudQuery');

      expect(antifraudKeys.all).toEqual(['antifraud']);
      expect(antifraudKeys.queue(2)).toEqual(['antifraud', 'queue', 2]);
      expect(antifraudKeys.auditTrail('123')).toEqual(['antifraud', 'audit', '123']);
      expect(antifraudKeys.statistics()).toEqual(['antifraud', 'statistics']);
    });
  });

  describe('Stale Time and Cache Configuration', () => {
    it('should respect stale time configuration', async () => {
      jest.useFakeTimers();
      const mockData = { items: [], total: 0, page: 1, totalPages: 0 };
      (AntifraudService.getDuplicateReviewQueue as jest.Mock).mockResolvedValue(mockData);

      const { result, rerender } = renderHook(() => useAntifraudQueue(1), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledTimes(1);

      // Fast forward 29 seconds - data should still be fresh
      jest.advanceTimersByTime(29 * 1000);
      rerender();

      expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledTimes(1);

      // Fast forward 2 more seconds (31 total) - data should be stale
      jest.advanceTimersByTime(2 * 1000);
      rerender();

      await waitFor(() => {
        expect(AntifraudService.getDuplicateReviewQueue).toHaveBeenCalledTimes(2);
      });

      jest.useRealTimers();
    });
  });
});