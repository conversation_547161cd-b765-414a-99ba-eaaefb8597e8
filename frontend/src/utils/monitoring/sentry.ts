import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';
import { CaptureConsole } from '@sentry/integrations';

export function initSentry() {
  const dsn = process.env.REACT_APP_SENTRY_DSN;
  const environment = process.env.REACT_APP_ENVIRONMENT || 'development';
  
  if (!dsn) {
    console.warn('Sentry DSN not configured. Error tracking disabled.');
    return;
  }

  Sentry.init({
    dsn,
    environment,
    integrations: [
      new BrowserTracing({
        // Set sampling to 100% for development, 10% for production
        tracingOrigins: ['localhost', /^https:\/\/yourapp\.com\/api/],
        routingInstrumentation: Sentry.reactRouterV6Instrumentation(
          React.useEffect,
          useLocation,
          useNavigationType,
          createRoutesFromChildren,
          matchRoutes,
        ),
      }),
      new CaptureConsole({
        levels: ['error', 'warn'],
      }),
    ],
    
    // Performance monitoring
    tracesSampleRate: environment === 'production' ? 0.1 : 1.0,
    
    // Session replay
    replaysSessionSampleRate: environment === 'production' ? 0.1 : 1.0,
    replaysOnErrorSampleRate: 1.0,
    
    // Release tracking
    release: process.env.REACT_APP_VERSION || 'development',
    
    // Before sending error to Sentry
    beforeSend: (event, hint) => {
      // Add user context
      const user = getUserFromLocalStorage();
      if (user) {
        event.user = {
          id: user.id,
          email: user.email,
          username: user.username,
        };
        event.tags = {
          ...event.tags,
          tenant_id: user.tenantId,
        };
      }

      // Add anti-fraud context if available
      const antifraudContext = getAntifraudContext();
      if (antifraudContext) {
        event.contexts = {
          ...event.contexts,
          antifraud: antifraudContext,
        };
      }

      // Remove sensitive data
      if (event.request) {
        delete event.request.cookies;
        
        // Mask sensitive fields in request
        if (event.request.data) {
          maskSensitiveData(event.request.data);
        }
      }

      // Don't send events in development unless explicitly enabled
      if (environment === 'development' && !process.env.REACT_APP_SENTRY_DEBUG) {
        return null;
      }

      return event;
    },
    
    // Ignore certain errors
    ignoreErrors: [
      'Network request failed',
      'NetworkError',
      'Non-Error promise rejection captured',
      /Failed to fetch/,
      /Load failed/,
    ],
  });

  // Set initial user context
  const user = getUserFromLocalStorage();
  if (user) {
    Sentry.setUser({
      id: user.id,
      email: user.email,
      username: user.username,
    });
    Sentry.setTag('tenant_id', user.tenantId);
  }
}

// Anti-fraud specific error capture
export function captureAntifraudError(
  error: Error,
  context: {
    action: 'review' | 'bulk_review' | 'filter' | 'export';
    orderId?: string;
    orderIds?: string[];
    metadata?: any;
  },
) {
  Sentry.captureException(error, {
    contexts: {
      antifraud: {
        action: context.action,
        orderId: context.orderId,
        orderCount: context.orderIds?.length,
        ...context.metadata,
      },
    },
    tags: {
      'antifraud.action': context.action,
      'antifraud.has_order': context.orderId ? 'true' : 'false',
    },
  });
}

// Performance monitoring for anti-fraud operations
export function measureAntifraudOperation<T>(
  operation: string,
  callback: () => Promise<T>,
): Promise<T> {
  const transaction = Sentry.startTransaction({
    name: `antifraud.${operation}`,
    op: 'antifraud',
  });

  Sentry.getCurrentHub().configureScope(scope => scope.setSpan(transaction));

  return callback()
    .then((result) => {
      transaction.setStatus('ok');
      return result;
    })
    .catch((error) => {
      transaction.setStatus('internal_error');
      throw error;
    })
    .finally(() => {
      transaction.finish();
    });
}

// Add breadcrumb for user actions
export function addAntifraudBreadcrumb(
  message: string,
  data?: Record<string, any>,
) {
  Sentry.addBreadcrumb({
    category: 'antifraud',
    message,
    level: 'info',
    data,
  });
}

// Track user interactions
export function trackUserAction(
  action: string,
  category: string,
  data?: Record<string, any>,
) {
  Sentry.addBreadcrumb({
    category: `user.${category}`,
    message: action,
    level: 'info',
    data,
  });

  // Also send to analytics if configured
  if (window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      ...data,
    });
  }
}

// Helper functions
function getUserFromLocalStorage() {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch {
    return null;
  }
}

function getAntifraudContext() {
  // Get current anti-fraud context from store
  const store = window.__ZUSTAND_STORE__;
  if (store) {
    const state = store.getState();
    return {
      queueSize: state.orders?.length || 0,
      selectedCount: state.selectedOrderIds?.length || 0,
      filters: state.filters,
      lastAction: state.lastAction,
    };
  }
  return null;
}

function maskSensitiveData(data: any): void {
  if (typeof data !== 'object' || !data) return;

  const sensitiveFields = ['customerCPF', 'cpf', 'password', 'token'];
  
  Object.keys(data).forEach((key) => {
    if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
      data[key] = '[REDACTED]';
    } else if (typeof data[key] === 'object') {
      maskSensitiveData(data[key]);
    }
  });
}

// Error boundary component
export const SentryErrorBoundary = Sentry.ErrorBoundary;