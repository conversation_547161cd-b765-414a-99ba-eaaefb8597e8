// Performance monitoring utilities for the frontend

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  tags?: Record<string, string>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: Map<string, PerformanceObserver> = new Map();

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // Observe Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric('lcp', lastEntry.startTime, 'ms', {
            element: (lastEntry as any).element?.tagName,
          });
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // Observe First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            this.recordMetric('fid', entry.processingStart - entry.startTime, 'ms', {
              eventType: entry.name,
            });
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Observe Cumulative Layout Shift
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          this.recordMetric('cls', clsValue, 'score');
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('cls', clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }
  }

  // Record a custom metric
  recordMetric(name: string, value: number, unit: string, tags?: Record<string, string>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      tags,
    };

    this.metrics.push(metric);

    // Send to monitoring service
    this.sendMetric(metric);

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${value}${unit}`, tags);
    }
  }

  // Measure operation duration
  async measureOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    tags?: Record<string, string>,
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.recordMetric(`operation.${operationName}`, duration, 'ms', {
        ...tags,
        status: 'success',
      });
      
      return result;
    } catch (error: any) {
      const duration = performance.now() - startTime;
      
      this.recordMetric(`operation.${operationName}`, duration, 'ms', {
        ...tags,
        status: 'error',
        error: error.message,
      });
      
      throw error;
    }
  }

  // Anti-fraud specific measurements
  measureReviewQueueLoad(itemCount: number, loadTime: number) {
    this.recordMetric('antifraud.queue.load_time', loadTime, 'ms', {
      item_count: itemCount.toString(),
    });

    // Calculate items per second
    const itemsPerSecond = itemCount / (loadTime / 1000);
    this.recordMetric('antifraud.queue.items_per_second', itemsPerSecond, 'items/s');
  }

  measureVirtualScrollPerformance(
    visibleItems: number,
    totalItems: number,
    renderTime: number,
  ) {
    this.recordMetric('antifraud.virtual_scroll.render_time', renderTime, 'ms', {
      visible_items: visibleItems.toString(),
      total_items: totalItems.toString(),
    });

    // Calculate efficiency
    const efficiency = (visibleItems / totalItems) * 100;
    this.recordMetric('antifraud.virtual_scroll.efficiency', efficiency, '%');
  }

  measureFilterPerformance(filterType: string, itemCount: number, duration: number) {
    this.recordMetric('antifraud.filter.duration', duration, 'ms', {
      filter_type: filterType,
      result_count: itemCount.toString(),
    });
  }

  measureReviewDecisionTime(decision: string, timeToDecision: number) {
    this.recordMetric('antifraud.review.time_to_decision', timeToDecision, 's', {
      decision,
    });
  }

  // Resource timing
  measureResourceLoading() {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const resources = performance.getEntriesByType('resource');
      
      // Group by resource type
      const resourcesByType: Record<string, number[]> = {};
      
      resources.forEach((resource) => {
        const url = resource.name;
        let type = 'other';
        
        if (url.includes('.js')) type = 'script';
        else if (url.includes('.css')) type = 'stylesheet';
        else if (url.includes('.json')) type = 'json';
        else if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)/)) type = 'image';
        
        if (!resourcesByType[type]) {
          resourcesByType[type] = [];
        }
        
        resourcesByType[type].push(resource.duration);
      });

      // Calculate averages
      Object.entries(resourcesByType).forEach(([type, durations]) => {
        const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
        this.recordMetric(`resource.${type}.avg_duration`, avg, 'ms', {
          count: durations.length.toString(),
        });
      });
    }
  }

  // Memory usage monitoring
  monitorMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      
      this.recordMetric('memory.used', memory.usedJSHeapSize / 1048576, 'MB');
      this.recordMetric('memory.total', memory.totalJSHeapSize / 1048576, 'MB');
      this.recordMetric('memory.limit', memory.jsHeapSizeLimit / 1048576, 'MB');
      
      // Calculate usage percentage
      const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
      this.recordMetric('memory.usage_percent', usagePercent, '%');
      
      // Warn if memory usage is high
      if (usagePercent > 80) {
        console.warn(`High memory usage: ${usagePercent.toFixed(2)}%`);
      }
    }
  }

  // Send metrics to backend
  private async sendMetric(metric: PerformanceMetric) {
    // In production, send to monitoring endpoint
    if (process.env.NODE_ENV === 'production') {
      try {
        await fetch('/api/metrics/frontend', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-tenant-id': getTenantId(),
          },
          body: JSON.stringify({
            timestamp: new Date().toISOString(),
            metric,
            userAgent: navigator.userAgent,
            url: window.location.href,
          }),
        });
      } catch (error: any) {
        console.error('Failed to send metric:', error);
      }
    }
  }

  // Get all collected metrics
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Clear metrics
  clearMetrics() {
    this.metrics = [];
  }

  // Cleanup observers
  cleanup() {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers.clear();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = performance.now();

  React.useEffect(() => {
    const renderTime = performance.now() - renderStartTime;
    performanceMonitor.recordMetric(
      `component.${componentName}.render_time`,
      renderTime,
      'ms',
    );

    return () => {
      // Cleanup if needed
    };
  }, [componentName, renderStartTime]);

  return performanceMonitor;
}

// Helper function to get tenant ID
function getTenantId(): string {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user.tenantId || 'unknown';
  } catch {
    return 'unknown';
  }
}