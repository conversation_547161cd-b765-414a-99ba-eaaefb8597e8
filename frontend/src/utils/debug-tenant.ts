export const debugTenant = () => {
  console.log('=== TENANT DEBUG ===');
  
  // Check localStorage
  const userInfo = localStorage.getItem('unified_user_info');
  const authTokens = localStorage.getItem('unified_auth_tokens');
  
  console.log('User Info:', userInfo);
  console.log('Auth Tokens:', authTokens);
  
  if (userInfo) {
    try {
      const parsed = JSON.parse(userInfo);
      console.log('Parsed User Info:', parsed);
      console.log('Tenant ID from user:', parsed.tenantId);
    } catch (e) {
      console.error('Failed to parse user info');
    }
  }
  
  console.log('ENV Tenant ID:', process.env.REACT_APP_TENANT_ID);
  console.log('=== END TENANT DEBUG ===');
};

// Auto-expose to window
if (typeof window !== 'undefined') {
  (window as any).debugTenant = debugTenant;
}