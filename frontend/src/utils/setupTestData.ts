import axios from 'axios';

const API_URL = 'http://localhost:3000/api/v1';

export async function setupTestData() {
  try {
    console.log('Setting up test data...');
    
    // Step 1: Create tenant
    let tenant;
    try {
      const tenantResponse = await axios.post(`${API_URL}/tenants`, {
        name: 'ACME Corporation',
        domain: 'localhost'
      });
      tenant = tenantResponse.data;
      console.log('Created tenant:', tenant);
    } catch (error: any) {
      if (error.response?.status === 409) {
        // Tenant already exists, try to get it
        const tenantsResponse = await axios.get(`${API_URL}/tenants`);
        tenant = tenantsResponse.data.find((t: any) => t.domain === 'localhost');
        console.log('Using existing tenant:', tenant);
      } else {
        throw error;
      }
    }
    
    // Step 2: Create admin user
    let adminUser;
    try {
      const userResponse = await axios.post(`${API_URL}/users/first-admin`, {
        email: '<EMAIL>',
        password: 'Admin123!@#',
        fullName: 'Admin User',
        tenantId: tenant.id
      });
      adminUser = userResponse.data;
      console.log('Created admin user:', adminUser);
    } catch (error: any) {
      if (error.response?.status === 409) {
        console.log('Admin user already exists');
      } else {
        throw error;
      }
    }
    
    // Step 3: Login
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123!@#',
      tenantId: tenant.id
    });
    
    const { access_token, user } = loginResponse.data;
    console.log('Login successful!');
    
    // Save to localStorage for the app to use
    localStorage.setItem('unified_auth_tokens', JSON.stringify({
      access_token,
      refresh_token: null,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    }));
    
    localStorage.setItem('unified_user_info', JSON.stringify({
      ...user,
      tenantId: tenant.id
    }));
    
    console.log('Test data setup complete!');
    console.log('Token saved to localStorage');
    
    return { tenant, user, token: access_token };
    
  } catch (error: any) {
    console.error('Error setting up test data:', error.response?.data || error.message);
    throw error;
  }
}

// Auto-run if called directly
if (typeof window !== 'undefined') {
  (window as any).setupTestData = setupTestData;
}