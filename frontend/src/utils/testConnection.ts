// Test connectivity to backend
export async function testBackendConnection() {
  const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
  
  console.log('🧪 Testing backend connection...');
  console.log('   Base URL:', baseURL);
  
  try {
    // Test 1: Simple fetch without credentials
    console.log('\n📡 Test 1: Simple fetch to /test-cors');
    const response1 = await fetch(`${baseURL}/test-cors`);
    const data1 = await response1.json();
    console.log('   Response:', data1);
    
    // Test 2: Fetch with credentials
    console.log('\n📡 Test 2: Fetch with credentials to /test-cors');
    const response2 = await fetch(`${baseURL}/test-cors`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const data2 = await response2.json();
    console.log('   Response:', data2);
    
    // Test 3: Test products endpoint
    console.log('\n📡 Test 3: Fetch products');
    const response3 = await fetch(`${baseURL}/products`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': 'default-tenant',
      },
    });
    
    if (!response3.ok) {
      console.error('   Products request failed:', response3.status, response3.statusText);
      const text = await response3.text();
      console.error('   Response body:', text);
    } else {
      const data3 = await response3.json();
      console.log('   Products response:', data3);
    }
    
  } catch (error: any) {
    console.error('❌ Connection test failed:', error);
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      console.error('   This usually means CORS is blocking the request or the backend is unreachable');
    }
  }
}

// Test product creation
export async function testProductCreation() {
  const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
  
  console.log('🧪 Testing product creation...');
  console.log('   Base URL:', baseURL);
  
  // Get auth token
  const authTokens = localStorage.getItem('unified_auth_tokens');
  const token = authTokens ? JSON.parse(authTokens).access_token : null;
  const tenantId = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
  
  if (!token) {
    console.error('❌ No auth token found. Please log in first.');
    return;
  }
  
  const testPayload = {
    name: 'Test Product Debug',
    variations: [
      {
        variation: 'Cápsulas',
        sku: 'TEST-CAP-' + Date.now(),
        price: 0
      }
    ]
  };
  
  console.log('📤 Sending payload:', JSON.stringify(testPayload, null, 2));
  
  try {
    const response = await fetch(`${baseURL}/products`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'x-tenant-id': tenantId,
      },
      body: JSON.stringify(testPayload)
    });
    
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    if (!response.ok) {
      console.error(`❌ Failed with status ${response.status}`);
      console.error('📥 Response:', responseData);
      
      if (responseData && responseData.message) {
        console.error('🔍 Validation Errors:');
        if (Array.isArray(responseData.message)) {
          responseData.message.forEach((msg: string) => {
            console.error(`   - ${msg}`);
          });
        } else {
          console.error(`   - ${responseData.message}`);
        }
      }
    } else {
      console.log('✅ Success! Product created:', responseData);
    }
  } catch (error: any) {
    console.error('❌ Network error:', error);
  }
}

// Add to window for easy testing in console
if (typeof window !== 'undefined') {
  (window as any).testBackendConnection = testBackendConnection;
  (window as any).testProductCreation = testProductCreation;
}