import { Order } from '../types/Order';

/**
 * Normalizes order data to ensure both English and Portuguese field names are available
 * This is used by report components to handle the transition from Portuguese to English field names
 */
export function normalizeOrderForReports(order: Order): Order {
  return {
    ...order,
    // Ensure Portuguese fields exist for backward compatibility
    dataVenda: order.dataVenda || order.createdAt,
    valorVenda: order.valorVenda ?? order.total ?? 0,
    valorRecebido: order.valorRecebido ?? order.paymentReceivedAmount ?? 0,
    situacaoVenda: order.situacaoVenda || order.status,
    vendedor: order.vendedor || order.seller?.name || 'Sem Vendedor',
    operador: order.operador || order.collector?.name || 'Sem Operador',
    cliente: order.cliente || order.customerName,
    telefone: order.telefone || order.customerPhone,
    // Ensure English fields also exist
    createdAt: order.createdAt || order.dataVenda,
    total: order.total ?? order.valorVenda ?? 0,
    paymentReceivedAmount: order.paymentReceivedAmount ?? order.valorRecebido ?? 0,
    status: order.status || order.situacaoVenda,
    customerName: order.customerName || order.cliente,
    customerPhone: order.customerPhone || order.telefone,
  };
}

/**
 * Normalizes an array of orders
 */
export function normalizeOrdersForReports(orders: Order[]): Order[] {
  return orders.map(normalizeOrderForReports);
}

/**
 * Parses a date string that could be in DD/MM/YYYY or ISO format
 */
export function parseOrderDate(dateStr: string | undefined | null): Date | null {
  if (!dateStr) return null;
  
  try {
    // Try DD/MM/YYYY format first
    if (dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts.map(Number);
        if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
          return new Date(year, month - 1, day);
        }
      }
    }
    
    // Try ISO format
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date;
    }
    
    return null;
  } catch (e) {
    console.error('Error parsing date:', dateStr, e);
    return null;
  }
}