// Window debug utilities
import { Product } from '../types/Product';

declare global {
  interface Window {
    debugProductCreation: () => Promise<Product | void>;
    testProductPayload: () => void;
    lastProductError: any;
  }
}

export function setupWindowDebug() {
  // Debug function to test product creation
  window.debugProductCreation = async function() {
    console.log('🔍 Starting product creation debug...');
    
    // Test payload that should work
    const testPayload = {
      name: 'Debug Test Product',
      description: 'Testing product creation',
      variations: [
        {
          variation: 'Cápsulas',
          sku: 'TEST-CAP-' + Date.now(),
          price: 99.99
        }
      ]
    };
    
    console.log('📦 Test payload:', testPayload);
    
    try {
      // Get the service
      const productServiceModule = await import('../services/ProductService');
      const productService = productServiceModule.default;
      
      const response = await productService.createProduct(testPayload);
      console.log('✅ Success! Product created:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Product creation failed!');
      console.error('Full error object:', error);
      
      if (error.response?.data) {
        console.error('🔴 Backend error response:');
        console.error(JSON.stringify(error.response.data, null, 2));
        
        // Check for validation messages
        if (error.response.data.message) {
          console.error('📋 Validation messages:');
          if (Array.isArray(error.response.data.message)) {
            error.response.data.message.forEach((msg: string, i: number) => {
              console.error(`   ${i + 1}. ${msg}`);
            });
          } else {
            console.error(`   ${error.response.data.message}`);
          }
        }
      }
      
      console.error('\n💡 To see the last error details, type: window.lastProductError');
      throw error;
    }
  };

  // Function to test payload transformation
  window.testProductPayload = function() {
    const testCases = [
      {
        name: 'Minimal valid payload',
        payload: {
          name: 'Test Product',
          variations: [
            { variation: 'Cápsulas', sku: 'TEST-001', price: 100 }
          ]
        }
      },
      {
        name: 'Payload with extra fields',
        payload: {
          name: 'Test Product',
          tenantId: 'should-be-removed',
          kits: [],
          variations: [
            {
              variation: 'Cápsulas',
              sku: 'TEST-002',
              price: 100,
              type: 'CAPSULAS',
              costPrice: 80,
              productId: 'temp-id',
              createdAt: new Date().toISOString()
            }
          ]
        }
      }
    ];

    import('../utils/productTransformers').then(({ transformProductForBackend }) => {
      testCases.forEach(test => {
        console.log(`\n📋 ${test.name}:`);
        console.log('Input:', test.payload);
        const transformed = transformProductForBackend(test.payload);
        console.log('Output:', transformed);
      });
    });
  };

  console.log('🔧 Debug functions loaded!');
  console.log('   - debugProductCreation(): Test product creation with a valid payload');
  console.log('   - testProductPayload(): Test payload transformation');
  console.log('   - window.lastProductError: View last product creation error');
}

// Auto-setup on import
setupWindowDebug();