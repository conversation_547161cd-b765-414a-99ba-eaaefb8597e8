/**
 * Utility function to clear all order data from localStorage
 */
export function clearAllOrderData() {
  // Clear orders from localStorage
  localStorage.removeItem('orders');
  
  // Clear any other order-related data
  localStorage.removeItem('selectedOrderIds');
  localStorage.removeItem('orderFilters');
  
  // No need for event - context will handle state updates
  
  console.log('✅ All order data cleared from localStorage');
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).clearAllOrderData = clearAllOrderData;
}