import { resizeImage, getImageDimensions, formatFileSize, isImageFile } from '../imageResizer';

// Mock HTMLCanvasElement
const mockToBlob = jest.fn();
const mockToDataURL = jest.fn();
const mockGetContext = jest.fn();
const mockDrawImage = jest.fn();

beforeAll(() => {
  // Mock canvas methods
  HTMLCanvasElement.prototype.toBlob = mockToBlob;
  HTMLCanvasElement.prototype.toDataURL = mockToDataURL;
  HTMLCanvasElement.prototype.getContext = mockGetContext;
  
  // Mock 2D context
  mockGetContext.mockReturnValue({
    drawImage: mockDrawImage,
    imageSmoothingEnabled: true,
    imageSmoothingQuality: 'high',
  });
});

// Mock FileReader
const mockReadAsDataURL = jest.fn();
const mockFileReader = {
  readAsDataURL: mockReadAsDataURL,
  onload: null as any,
  onerror: null as any,
  result: 'data:image/jpeg;base64,mockbase64data',
};

(global as any).FileReader = jest.fn(() => mockFileReader);

// Mock Image
const mockImage = {
  onload: null as any,
  onerror: null as any,
  src: '',
  width: 2400,
  height: 1800,
};

(global as any).Image = jest.fn(() => mockImage);

describe('imageResizer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock behaviors
    mockReadAsDataURL.mockImplementation(() => {
      setTimeout(() => {
        if (mockFileReader.onload) {
          mockFileReader.onload({ target: { result: mockFileReader.result } });
        }
      }, 0);
    });
    
    mockToBlob.mockImplementation((callback, format, quality) => {
      setTimeout(() => {
        const blob = new Blob(['mock blob data'], { type: format });
        callback(blob);
      }, 0);
    });
    
    mockToDataURL.mockReturnValue('data:image/jpeg;base64,resizedmockdata');
  });

  describe('resizeImage', () => {
    it('should resize large image to maximum dimensions', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      // Trigger image load after src is set
      Object.defineProperty(mockImage, 'src', {
        set(value) {
          setTimeout(() => {
            if (mockImage.onload) mockImage.onload();
          }, 0);
        },
      });
      
      const result = await resizeImage(mockFile);
      
      expect(mockDrawImage).toHaveBeenCalled();
      expect(result.blob).toBeInstanceOf(Blob);
      expect(result.dataUrl).toBe('data:image/jpeg;base64,resizedmockdata');
    });
    
    it('should not upscale small images', async () => {
      mockImage.width = 800;
      mockImage.height = 600;
      
      const mockFile = new File(['test'], 'small.jpg', { type: 'image/jpeg' });
      
      Object.defineProperty(mockImage, 'src', {
        set(value) {
          setTimeout(() => {
            if (mockImage.onload) mockImage.onload();
          }, 0);
        },
      });
      
      await resizeImage(mockFile);
      
      // Check that canvas was set to original dimensions
      expect(mockDrawImage).toHaveBeenCalledWith(mockImage, 0, 0, 800, 600);
    });
    
    it('should use custom options when provided', async () => {
      const mockFile = new File(['test'], 'test.png', { type: 'image/png' });
      
      Object.defineProperty(mockImage, 'src', {
        set(value) {
          setTimeout(() => {
            if (mockImage.onload) mockImage.onload();
          }, 0);
        },
      });
      
      await resizeImage(mockFile, {
        maxWidth: 800,
        maxHeight: 800,
        quality: 0.9,
        outputFormat: 'image/webp',
      });
      
      expect(mockToBlob).toHaveBeenCalledWith(
        expect.any(Function),
        'image/webp',
        0.9
      );
    });
  });

  describe('getImageDimensions', () => {
    it('should return image dimensions', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      Object.defineProperty(mockImage, 'src', {
        set(value) {
          setTimeout(() => {
            if (mockImage.onload) mockImage.onload();
          }, 0);
        },
      });
      
      const dimensions = await getImageDimensions(mockFile);
      
      expect(dimensions).toEqual({ width: 2400, height: 1800 });
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1536 * 1024)).toBe('1.5 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });
  });

  describe('isImageFile', () => {
    it('should identify image files correctly', () => {
      expect(isImageFile(new File([''], 'test.jpg', { type: 'image/jpeg' }))).toBe(true);
      expect(isImageFile(new File([''], 'test.png', { type: 'image/png' }))).toBe(true);
      expect(isImageFile(new File([''], 'test.gif', { type: 'image/gif' }))).toBe(true);
      expect(isImageFile(new File([''], 'test.webp', { type: 'image/webp' }))).toBe(true);
      expect(isImageFile(new File([''], 'test.pdf', { type: 'application/pdf' }))).toBe(false);
      expect(isImageFile(new File([''], 'test.doc', { type: 'application/msword' }))).toBe(false);
    });
  });
});