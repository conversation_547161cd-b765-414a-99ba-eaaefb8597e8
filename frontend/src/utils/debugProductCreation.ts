import productService from '../services/ProductService';

// Test function to debug product creation
export async function debugProductCreation() {
  console.log('🔍 Starting product creation debug test...');
  
  // Create a simple test payload - exactly what should be sent
  const testPayload = {
    name: 'Test Product Debug',
    description: 'Testing product creation',
    variations: [
      {
        variation: 'Cápsulas',
        sku: 'TEST-CAP-001',
        price: 50
      }
    ]
  };
  
  console.log('📦 Test payload:', JSON.stringify(testPayload, null, 2));
  
  try {
    // Call the service directly
    console.log('🚀 Calling productService.createProduct...');
    const result = await productService.createProduct(testPayload);
    console.log('✅ Success! Created product:', result);
    return result;
  } catch (error: any) {
    console.error('❌ Error occurred:', error);
    
    if (error.response) {
      console.error('📋 Full error response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers
      });
      
      // Log validation errors in detail
      if (error.response.data?.message) {
        console.error('🚨 Backend validation errors:');
        if (Array.isArray(error.response.data.message)) {
          error.response.data.message.forEach((msg: string, idx: number) => {
            console.error(`   ${idx + 1}. ${msg}`);
          });
        } else {
          console.error(`   ${error.response.data.message}`);
        }
      }
      
      // Log the actual request that was sent
      if (error.config?.data) {
        console.error('📤 Request data that was sent:');
        try {
          const sentData = JSON.parse(error.config.data);
          console.error(JSON.stringify(sentData, null, 2));
        } catch {
          console.error(error.config.data);
        }
      }
    }
    
    throw error;
  }
}

// Function to check what transformProductForBackend does
export function testTransform() {
  console.log('🔍 Testing transform function...');
  
  const testInputs = [
    {
      name: 'Test Product',
      description: 'Test description',
      variations: [
        {
          type: 'CAPSULAS',
          variation: 'Cápsulas',
          sku: 'TEST-001',
          price: 100,
          costPrice: 50,
          active: true,
          productId: 'some-id',
          tenantId: 'tenant-123',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]
    },
    {
      name: 'Test Product 2',
      variations: [
        {
          variation: 'Gotas',
          sku: 'TEST-002',
          price: 75
        }
      ]
    }
  ];
  
  testInputs.forEach((input, idx) => {
    console.log(`\n📥 Test input ${idx + 1}:`, JSON.stringify(input, null, 2));
    
    // Import and test the transformer
    import('../utils/productTransformers').then(({ transformProductForBackend }) => {
      const output = transformProductForBackend(input as any);
      console.log(`📤 Transform output ${idx + 1}:`, JSON.stringify(output, null, 2));
    });
  });
}

// Export a function to run from console
(window as any).debugProductCreation = debugProductCreation;
(window as any).testTransform = testTransform;

console.log('🔧 Debug functions loaded. Run debugProductCreation() or testTransform() in console.');