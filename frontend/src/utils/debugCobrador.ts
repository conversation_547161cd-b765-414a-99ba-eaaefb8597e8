// Debug utility to help identify cobrador filtering issues
export const debugCobradorIssue = (
  orders: any[],
  currentUserId: string,
  currentUserName: string,
  currentUserEmail: string,
  selectedStatus: any
) => {
  console.log('=== COBRADOR DEBUG START ===');
  console.log('Total orders from backend:', orders.length);
  console.log('Current user:', { currentUserId, currentUserName, currentUserEmail });
  console.log('Selected status filter:', selectedStatus);
  
  // Check what orders the cobrador has
  const cobradorOrders = orders.filter(order => {
    const hasOperadorName = order.operador && (
      order.operador.toLowerCase().trim().includes(currentUserName?.toLowerCase().trim() || '') ||
      currentUserName?.toLowerCase().trim().includes(order.operador.toLowerCase().trim())
    );
    const matchesUserId = currentUserId && order.collectorId === currentUserId;
    return hasOperadorName || matchesUserId;
  });
  
  console.log(`Cobrador has ${cobradorOrders.length} orders assigned`);
  
  // Show sample of cobrador orders
  if (cobradorOrders.length > 0) {
    console.log('Sample cobrador orders:');
    cobradorOrders.slice(0, 3).forEach(order => {
      console.log({
        id: order.id,
        idVenda: order.idVenda,
        operador: order.operador,
        collectorId: order.collectorId,
        situacaoVenda: order.situacaoVenda,
        situacao: order.situacao,
        status: order.status
      });
    });
    
    // Get unique statuses for cobrador orders
    const uniqueStatuses = [...new Set(cobradorOrders.map(o => o.situacaoVenda))];
    console.log('Unique statuses in cobrador orders:', uniqueStatuses);
    
    // Check if any match the selected status
    if (selectedStatus && selectedStatus.field === 'situacaoVenda') {
      const matchingOrders = cobradorOrders.filter(order => {
        const orderStatus = (order.situacaoVenda || '').toLowerCase();
        const filterValue = selectedStatus.value.toLowerCase();
        return orderStatus === filterValue;
      });
      
      console.log(`Orders matching status filter "${selectedStatus.value}": ${matchingOrders.length}`);
      
      if (matchingOrders.length === 0) {
        console.log('NO ORDERS MATCH THE STATUS FILTER!');
        console.log('Available statuses:', uniqueStatuses);
        console.log('Looking for:', selectedStatus.value);
        
        // Check for case sensitivity issues
        const caseInsensitiveMatch = cobradorOrders.find(order => 
          (order.situacaoVenda || '').toLowerCase() === selectedStatus.value.toLowerCase()
        );
        
        if (caseInsensitiveMatch) {
          console.log('Found case-insensitive match:', {
            orderStatus: caseInsensitiveMatch.situacaoVenda,
            filterValue: selectedStatus.value
          });
        }
      }
    }
  } else {
    console.log('NO ORDERS ASSIGNED TO THIS COBRADOR!');
  }
  
  console.log('=== COBRADOR DEBUG END ===');
};