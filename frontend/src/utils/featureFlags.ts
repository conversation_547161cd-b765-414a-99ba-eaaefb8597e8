/**
 * Simple feature flag system for frontend
 * In production, this would integrate with LaunchDarkly, Unleash, or similar
 */

export interface FeatureFlags {
  DUPLICATE_CHECK_ENABLED: boolean;
  FUZZY_MATCHING_ENABLED: boolean;
  AUDIT_TRAIL_ENABLED: boolean;
  REAL_TIME_UPDATES_ENABLED: boolean;
  BULK_REVIEW_ENABLED: boolean;
  ML_SCORING_ENABLED: boolean;
}

class FeatureFlagService {
  private flags: FeatureFlags;
  private defaultFlags: FeatureFlags = {
    DUPLICATE_CHECK_ENABLED: true,
    FUZZY_MATCHING_ENABLED: true,
    AUDIT_TRAIL_ENABLED: true,
    REAL_TIME_UPDATES_ENABLED: false,
    BULK_REVIEW_ENABLED: false,
    ML_SCORING_ENABLED: false,
  };

  constructor() {
    this.flags = this.loadFlags();
  }

  private loadFlags(): FeatureFlags {
    // First, try to load from environment variables
    const envFlags = process.env.NEXT_PUBLIC_FEATURE_FLAGS;
    if (envFlags) {
      try {
        return { ...this.defaultFlags, ...JSON.parse(envFlags) };
      } catch (e) {
        console.warn('Failed to parse feature flags from env:', e);
      }
    }

    // Then, try to load from localStorage (for development/testing)
    if (typeof window !== 'undefined') {
      const storedFlags = localStorage.getItem('featureFlags');
      if (storedFlags) {
        try {
          return { ...this.defaultFlags, ...JSON.parse(storedFlags) };
        } catch (e) {
          console.warn('Failed to parse feature flags from localStorage:', e);
        }
      }
    }

    return this.defaultFlags;
  }

  /**
   * Check if a feature is enabled
   */
  isEnabled(flag: keyof FeatureFlags): boolean {
    return this.flags[flag] ?? false;
  }

  /**
   * Get all feature flags
   */
  getAllFlags(): FeatureFlags {
    return { ...this.flags };
  }

  /**
   * Update feature flags (development only)
   */
  setFlag(flag: keyof FeatureFlags, enabled: boolean): void {
    if (process.env.NODE_ENV !== 'production') {
      this.flags[flag] = enabled;
      if (typeof window !== 'undefined') {
        localStorage.setItem('featureFlags', JSON.stringify(this.flags));
      }
    }
  }

  /**
   * Reset all flags to defaults (development only)
   */
  resetFlags(): void {
    if (process.env.NODE_ENV !== 'production') {
      this.flags = { ...this.defaultFlags };
      if (typeof window !== 'undefined') {
        localStorage.removeItem('featureFlags');
      }
    }
  }
}

// Export singleton instance
export const featureFlags = new FeatureFlagService();

// Export hook for React components
export function useFeatureFlag(flag: keyof FeatureFlags): boolean {
  return featureFlags.isEnabled(flag);
}

// Export all flag names for type safety
export const FEATURE_FLAGS = {
  DUPLICATE_CHECK_ENABLED: 'DUPLICATE_CHECK_ENABLED',
  FUZZY_MATCHING_ENABLED: 'FUZZY_MATCHING_ENABLED',
  AUDIT_TRAIL_ENABLED: 'AUDIT_TRAIL_ENABLED',
  REAL_TIME_UPDATES_ENABLED: 'REAL_TIME_UPDATES_ENABLED',
  BULK_REVIEW_ENABLED: 'BULK_REVIEW_ENABLED',
  ML_SCORING_ENABLED: 'ML_SCORING_ENABLED',
} as const;