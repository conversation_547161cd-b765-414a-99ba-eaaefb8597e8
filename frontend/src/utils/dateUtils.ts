import { format, parse, parseISO, isValid } from 'date-fns';

/**
 * Parses a date string in various formats and returns a Date object
 * Handles DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD, ISO strings, etc.
 */
export function parseDate(dateStr: string | Date | null | undefined): Date | null {
  if (!dateStr) return null;
  
  // If already a Date object
  if (dateStr instanceof Date) {
    return isValid(dateStr) ? dateStr : null;
  }
  
  try {
    // First, try to parse as DD/MM/YYYY (our standard format)
    const ddmmyyyy = parse(dateStr, 'dd/MM/yyyy', new Date());
    if (isValid(ddmmyyyy)) {
      return ddmmyyyy;
    }
    
    // Try other common formats
    const formats = [
      'dd-MM-yyyy',    // Alternative with dashes
      'dd.MM.yyyy',    // Alternative with dots
      'yyyy-MM-dd',    // ISO format
      'MM/dd/yyyy',    // US format
      'yyyy/MM/dd',    // Alternative ISO
    ];
    
    for (const fmt of formats) {
      const parsed = parse(dateStr, fmt, new Date());
      if (isValid(parsed)) {
        return parsed;
      }
    }
    
    // Try ISO parse
    const isoParsed = parseISO(dateStr);
    if (isValid(isoParsed)) {
      return isoParsed;
    }
    
    // Last resort: native Date constructor
    const nativeParsed = new Date(dateStr);
    if (isValid(nativeParsed) && !isNaN(nativeParsed.getTime())) {
      return nativeParsed;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Formats a date to DD/MM/YYYY format
 * This is our standard display format
 */
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return '';
  
  const parsedDate = typeof date === 'string' ? parseDate(date) : date;
  if (!parsedDate || !isValid(parsedDate)) return '';
  
  return format(parsedDate, 'dd/MM/yyyy');
}

/**
 * Formats a date with time to DD/MM/YYYY HH:mm format
 */
export function formatDateTime(date: Date | string | null | undefined): string {
  if (!date) return '';
  
  const parsedDate = typeof date === 'string' ? parseDate(date) : date;
  if (!parsedDate || !isValid(parsedDate)) return '';
  
  return format(parsedDate, 'dd/MM/yyyy HH:mm');
}

/**
 * Gets today's date at start of day
 */
export function getToday(): Date {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
}

/**
 * Gets yesterday's date at start of day
 */
export function getYesterday(): Date {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);
  return yesterday;
}

/**
 * Checks if a date is today
 */
export function isToday(date: Date | string | null | undefined): boolean {
  if (!date) return false;
  
  const parsedDate = typeof date === 'string' ? parseDate(date) : date;
  if (!parsedDate) return false;
  
  const today = getToday();
  const dateToCheck = new Date(parsedDate);
  dateToCheck.setHours(0, 0, 0, 0);
  
  return dateToCheck.getTime() === today.getTime();
}

/**
 * Checks if a date is within a range
 */
export function isDateInRange(
  date: Date | string | null | undefined,
  startDate: Date,
  endDate: Date
): boolean {
  if (!date) return false;
  
  const parsedDate = typeof date === 'string' ? parseDate(date) : date;
  if (!parsedDate) return false;
  
  return parsedDate >= startDate && parsedDate <= endDate;
}