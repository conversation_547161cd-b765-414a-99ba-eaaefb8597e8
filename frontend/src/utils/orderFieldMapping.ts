/**
 * Maps between English (new) and Portuguese (legacy) field names
 * The API now returns English field names, but some components still use Portuguese names
 */

import { Order } from '../types/Order';

export interface LegacyOrder {
  // Portuguese field names (legacy)
  dataVenda?: string;
  valorVenda?: number;
  situacaoVenda?: string;
  vendedorName?: string;
  operadorName?: string;
  valorRecebido?: number;
  dataRecebimento?: string;
  // ... other legacy fields
}

/**
 * Normalizes an order object to ensure it has both English and Portuguese field names
 * This allows legacy code to work with the new data model
 */
export function normalizeOrder(order: Order & Partial<LegacyOrder>): Order & LegacyOrder {
  return {
    ...order,
    // Map English to Portuguese field names for backward compatibility
    dataVenda: order.dataVenda || order.createdAt,
    valorVenda: order.valorVenda || order.total,
    situacaoVenda: order.situacaoVenda || order.status,
    vendedorName: order.vendedorName || order.seller?.name || order.sellerName,
    operadorName: order.operadorName || order.collector?.name || order.collectorName,
    valorRecebido: order.valorRecebido || order.paymentReceivedAmount,
    dataRecebimento: order.dataRecebimento || order.paymentReceivedDate,
    
    // Ensure English fields also exist
    total: order.total || order.valorVenda || 0,
    createdAt: order.createdAt || order.dataVenda || new Date().toISOString(),
    status: order.status || order.situacaoVenda || 'Unknown',
  };
}

/**
 * Normalizes an array of orders
 */
export function normalizeOrders(orders: (Order & Partial<LegacyOrder>)[]): (Order & LegacyOrder)[] {
  return orders.map(normalizeOrder);
}