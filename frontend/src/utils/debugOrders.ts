// Debug helper for order loading issues
export const debugOrderLoading = () => {
  console.group('[DEBUG] Order Loading State');
  
  // Check auth
  const authTokens = localStorage.getItem('unified_auth_tokens');
  const userInfo = localStorage.getItem('unified_user_info');
  console.log('Auth tokens exist:', !!authTokens);
  console.log('User info:', userInfo ? JSON.parse(userInfo) : null);
  
  // Check API URL
  console.log('API URL:', process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1');
  console.log('Mock mode:', process.env.REACT_APP_MOCK_API);
  
  // Check localStorage for mock data
  const mockOrders = localStorage.getItem('orders');
  console.log('Mock orders in localStorage:', mockOrders ? JSON.parse(mockOrders).length : 0);
  
  console.groupEnd();
};

// Attach to window for easy access
if (typeof window !== 'undefined') {
  (window as any).debugOrderLoading = debugOrderLoading;
}