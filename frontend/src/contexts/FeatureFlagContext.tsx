import React, { createContext, useContext, useEffect, useState } from 'react';
import { featureFlags, FeatureFlags, FEATURE_FLAGS } from '../utils/featureFlags';

interface FeatureFlagContextType {
  flags: FeatureFlags;
  isEnabled: (flag: keyof FeatureFlags) => boolean;
  setFlag: (flag: keyof FeatureFlags, enabled: boolean) => void;
  resetFlags: () => void;
}

const FeatureFlagContext = createContext<FeatureFlagContextType | null>(null);

export const FeatureFlagProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [flags, setFlags] = useState<FeatureFlags>(featureFlags.getAllFlags());

  useEffect(() => {
    // Re-sync flags when they change (for development)
    const interval = setInterval(() => {
      setFlags(featureFlags.getAllFlags());
    }, 5000); // Check every 5 seconds in development

    return () => clearInterval(interval);
  }, []);

  const isEnabled = (flag: keyof FeatureFlags): boolean => {
    return featureFlags.isEnabled(flag);
  };

  const setFlagHandler = (flag: keyof FeatureFlags, enabled: boolean) => {
    featureFlags.setFlag(flag, enabled);
    setFlags(featureFlags.getAllFlags());
  };

  const resetFlagsHandler = () => {
    featureFlags.resetFlags();
    setFlags(featureFlags.getAllFlags());
  };

  return (
    <FeatureFlagContext.Provider
      value={{
        flags,
        isEnabled,
        setFlag: setFlagHandler,
        resetFlags: resetFlagsHandler,
      }}
    >
      {children}
    </FeatureFlagContext.Provider>
  );
};

export const useFeatureFlags = () => {
  const context = useContext(FeatureFlagContext);
  if (!context) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider');
  }
  return context;
};

// Export typed flag names for convenience
export { FEATURE_FLAGS };