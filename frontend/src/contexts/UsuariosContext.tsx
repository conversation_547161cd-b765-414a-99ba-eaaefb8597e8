import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import api from '../services/api';

export interface Usuario {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'supervisor' | 'vendedor' | 'operador' | 'collector';
  active: boolean;
}

interface UsuariosContextType {
  usuarios: Usuario[];
  loading: boolean;
  error: string | null;
  fetchUsuarios: () => Promise<void>;
  lastOperatorIndex: number;
  setLastOperatorIndex: (index: number) => void;
}

const UsuariosContext = createContext<UsuariosContextType | undefined>(undefined);

interface UsuariosProviderProps {
  children: ReactNode;
}

// Helper function to normalize roles from backend to frontend format
const normalizeRole = (role: string): 'admin' | 'supervisor' | 'vendedor' | 'operador' | 'collector' => {
  const roleMap: Record<string, 'admin' | 'supervisor' | 'vendedor' | 'operador' | 'collector'> = {
    'ADMIN': 'admin',
    'SUPERVISOR': 'supervisor',
    'VENDEDOR': 'vendedor',
    'COBRADOR': 'operador',
    'collector': 'operador',
    'operador': 'operador',
    'admin': 'admin',
    'supervisor': 'supervisor',
    'vendedor': 'vendedor'
  };
  
  return roleMap[role] || role as any;
};

export const UsuariosProvider: React.FC<UsuariosProviderProps> = ({ children }) => {
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [loading, setLoading] = useState(true); // Start with loading true
  const [error, setError] = useState<string | null>(null);
  const [lastOperatorIndex, setLastOperatorIndex] = useState(-1);

  const fetchUsuarios = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get('/users');
      
      // Handle backend response format with TransformInterceptor
      // Backend wraps response in { data: ..., statusCode: ..., message: ..., timestamp: ... }
      let usersArray: Usuario[] = [];
      
      // Check if response is wrapped by TransformInterceptor
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        // Response is wrapped, extract the actual data
        const actualData = response.data.data;
        if (Array.isArray(actualData)) {
          usersArray = actualData;
        } else {
          usersArray = [];
        }
      } else if (Array.isArray(response.data)) {
        // Direct array response (no wrapper)
        usersArray = response.data;
      } else {
        usersArray = [];
      }
      
      // Filter for active users only
      const activeUsers = usersArray.filter((user: Usuario) => user.active !== false);
      
      // Map backend roles to frontend format
      const mappedUsers = activeUsers.map((user: Usuario) => ({
        ...user,
        role: normalizeRole(user.role)
      }));
      
      setUsuarios(mappedUsers);
      setError(null);
    } catch (err: any) {
      console.error('UsuariosContext: Error fetching usuarios:', err);
      // Get a readable error message
      let errorMessage = 'Erro ao carregar usuários';
      if (err.response) {
        // The request was made and the server responded with a status code
        errorMessage = err.response.data?.message || `Erro do servidor: ${err.response.status}`;
      } else if (err.request) {
        // The request was made but no response was received
        errorMessage = 'Servidor não respondeu';
      } else if (err.message) {
        // Something happened in setting up the request
        errorMessage = err.message;
      }
      setError(errorMessage);
      
      // Fallback to mock data for development if API fails
      if (process.env.NODE_ENV === 'development') {
        console.log('UsuariosContext: Using mock data in development due to API error');
        setUsuarios([
          { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'admin', active: true },
          { id: '2', name: 'João Silva', email: '<EMAIL>', role: 'vendedor', active: true },
          { id: '3', name: 'Maria Santos', email: '<EMAIL>', role: 'vendedor', active: true },
          { id: '4', name: 'Pedro Oliveira', email: '<EMAIL>', role: 'operador', active: true },
          { id: '5', name: 'Ana Costa', email: '<EMAIL>', role: 'operador', active: true },
          { id: '6', name: 'Carlos Ferreira', email: '<EMAIL>', role: 'operador', active: true },
        ]);
        setError(null); // Clear error when using mock data
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch usuarios on mount
  useEffect(() => {
    fetchUsuarios();
  }, [fetchUsuarios]);

  const value = {
    usuarios,
    loading,
    error,
    fetchUsuarios,
    lastOperatorIndex,
    setLastOperatorIndex,
  };

  return (
    <UsuariosContext.Provider value={value}>
      {children}
    </UsuariosContext.Provider>
  );
};

export const useUsuarios = () => {
  const context = useContext(UsuariosContext);
  if (!context) {
    throw new Error('useUsuarios must be used within a UsuariosProvider');
  }
  return context;
};