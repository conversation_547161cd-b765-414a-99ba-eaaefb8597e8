import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '../types/User';
import api from '../services/api';
import UnifiedAuthService from '../services/UnifiedAuthService';

// Interface do contexto
interface UserContextType {
  users: User[];
  loading: boolean;
  error: string | null;
  getUserById: (id: string) => User | undefined;
  getUserByEmail: (email: string) => User | undefined;
  addUser: (user: Partial<User>) => Promise<User | undefined>;
  updateUser: (id: string, user: Partial<User>) => Promise<User | undefined>;
  deleteUser: (userId: string) => Promise<boolean>;
  refreshUsers: () => Promise<void>;
}

// Criação do contexto
const UnifiedUserContext = createContext<UserContextType | undefined>(undefined);

// Provider props
interface UnifiedUserProviderProps {
  children: ReactNode;
}

// Map frontend role names to backend enum values
const mapRoleToBackend = (role: string): string => {
  const roleMap: { [key: string]: string } = {
    'admin': 'ADMIN',
    'supervisor': 'SUPERVISOR',
    'vendedor': 'VENDEDOR',
    'operador': 'COBRADOR', // Frontend uses 'operador', backend uses 'COBRADOR'
    'cobrador': 'COBRADOR',
    'collector': 'COBRADOR'
  };
  return roleMap[role.toLowerCase()] || role.toUpperCase();
};

// Provider component
export const UnifiedUserProvider: React.FC<UnifiedUserProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load users from backend
  const loadUsers = async () => {
    if (!UnifiedAuthService.isAuthenticated()) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/users');
      
      // With TransformInterceptor, the response should be wrapped in { data: ..., statusCode: ..., message: ..., timestamp: ... }
      const backendUsers = response.data?.data || response.data || [];
      
      // Transform backend user format to match frontend expectations
      const transformedUsers = backendUsers.map((user: any) => ({
        ...user,
        // Map backend fields to frontend expectations
        nome: user.name || user.email, // Backend uses 'name' not 'fullName'
        ativo: user.active !== undefined ? user.active : true, // Backend uses 'active' not 'isActive'
        papeis: [user.role.toLowerCase()], // Convert single role to array and lowercase
        full_name: user.name || '', // Backend uses 'name' not 'fullName'
        is_active: user.active !== undefined ? user.active : true, // Backend uses 'active' not 'isActive'
        created_at: user.createdAt,
        updated_at: user.updatedAt,
      }));
      
      setUsers(transformedUsers);
    } catch (err: any) {
      console.error('Error loading users:', err);
      setError(err.response?.data?.message || 'Erro ao carregar usuários');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const getUserById = (id: string): User | undefined => {
    return users.find(user => user.id === id);
  };

  const getUserByEmail = (email: string): User | undefined => {
    return users.find(user => user.email === email);
  };

  const addUser = async (userData: Partial<User>): Promise<User | undefined> => {
    try {
      setError(null);
      
      // Transform frontend User format to backend CreateUserDto format
      const backendData = {
        name: userData.full_name || userData.nome || '',
        email: userData.email,
        password: userData.password,
        role: mapRoleToBackend(Array.isArray(userData.papeis) ? userData.papeis[0] : userData.role || ''),
        active: userData.is_active !== undefined ? userData.is_active : userData.ativo
      };
      
      console.log('Creating user with data:', backendData);
      
      const response = await api.post('/users', backendData);
      const newUser = response.data.data;
      
      // Transform the new user to match frontend format
      const transformedUser = {
        ...newUser,
        nome: newUser.name || newUser.email,
        ativo: newUser.active !== undefined ? newUser.active : true,
        papeis: [newUser.role.toLowerCase()], // Convert to lowercase
        full_name: newUser.name || '',
        is_active: newUser.active !== undefined ? newUser.active : true,
        created_at: newUser.createdAt,
        updated_at: newUser.updatedAt,
      };
      
      // Update local state
      setUsers(prevUsers => [...prevUsers, transformedUser]);
      
      return newUser;
    } catch (err: any) {
      console.error('Error adding user:', err);
      console.error('Error response:', err.response);
      console.error('Error data:', err.response?.data);
      const errorMessage = err.response?.data?.message || err.response?.data?.error || 'Erro ao adicionar usuário';
      setError(errorMessage);
      return undefined;
    }
  };

  const updateUser = async (id: string, userData: Partial<User>): Promise<User | undefined> => {
    try {
      setError(null);
      
      // Transform frontend User format to backend UpdateUserDto format
      const backendData: any = {};
      if (userData.full_name !== undefined || userData.nome !== undefined) {
        backendData.name = userData.full_name || userData.nome;
      }
      if (userData.email !== undefined) {
        backendData.email = userData.email;
      }
      if (userData.papeis !== undefined || userData.role !== undefined) {
        const roleToMap = Array.isArray(userData.papeis) ? userData.papeis[0] : userData.role || '';
        console.log('Role before mapping:', roleToMap);
        backendData.role = mapRoleToBackend(roleToMap);
        console.log('Role after mapping:', backendData.role);
      }
      if (userData.is_active !== undefined || userData.ativo !== undefined) {
        backendData.active = userData.is_active !== undefined ? userData.is_active : userData.ativo;
      }
      
      // Add commission_rate if provided
      if (userData.commission_rate !== undefined) {
        backendData.commissionRate = userData.commission_rate;
      }
      
      console.log('Updating user with backend data:', backendData);
      console.log('Making PATCH request to:', `/users/${id}`);
      
      try {
        const response = await api.patch(`/users/${id}`, backendData);
        console.log('Update response:', response);
        const updatedUser = response.data.data || response.data;
      
      // Transform the updated user to match frontend format
      const transformedUser = {
        ...updatedUser,
        nome: updatedUser.name || updatedUser.email,
        ativo: updatedUser.active !== undefined ? updatedUser.active : true,
        papeis: [updatedUser.role.toLowerCase()], // Convert to lowercase
        full_name: updatedUser.name || '',
        is_active: updatedUser.active !== undefined ? updatedUser.active : true,
        created_at: updatedUser.createdAt,
        updated_at: updatedUser.updatedAt,
        commission_rate: updatedUser.commissionRate || 0,
      };
      
      // Update local state
      setUsers(prevUsers => 
        prevUsers.map(user => user.id === id ? transformedUser : user)
      );
      
        return updatedUser;
      } catch (innerErr: any) {
        console.error('Inner error during update:', innerErr);
        throw innerErr;
      }
    } catch (err: any) {
      console.error('Error updating user:', err);
      console.error('Error response:', err.response?.data);
      console.error('Full error object:', err);
      setError(err.response?.data?.message || 'Erro ao atualizar usuário');
      return undefined;
    }
  };

  const deleteUser = async (userId: string): Promise<boolean> => {
    try {
      setError(null);
      await api.delete(`/users/${userId}`);
      
      // Update local state
      setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      
      return true;
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.response?.data?.message || 'Erro ao deletar usuário');
      return false;
    }
  };

  const refreshUsers = async (): Promise<void> => {
    await loadUsers();
  };

  const contextValue: UserContextType = {
    users,
    loading,
    error,
    getUserById,
    getUserByEmail,
    addUser,
    updateUser,
    deleteUser,
    refreshUsers,
  };

  return (
    <UnifiedUserContext.Provider value={contextValue}>
      {children}
    </UnifiedUserContext.Provider>
  );
};

// Hook para usar o contexto
export const useUnifiedUsers = (): UserContextType => {
  const context = useContext(UnifiedUserContext);
  if (context === undefined) {
    throw new Error('useUnifiedUsers must be used within a UnifiedUserProvider');
  }
  return context;
};

export default UnifiedUserContext;