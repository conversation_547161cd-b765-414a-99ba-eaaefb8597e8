import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback, useRef } from 'react';
import { Order } from '../types/Order';
import OrderService from '../services/OrderService';
import { useAuth } from './AuthContext';

interface OrderDataContextType {
  orders: Order[];
  loading: boolean;
  error: string | null;
  fetchOrders: () => Promise<void>;
  updateOrderStatus: (orderId: string, newStatus: string) => Promise<void>;
  updateOrder: (orderId: string, changes: Partial<Order>) => Promise<void>;
  addOrder: (orderData: any) => Promise<void>;
  deleteOrder: (orderId: string, isPermanent?: boolean) => Promise<void>;
  bulkDeleteOrders: (orderIds: string[], isPermanent?: boolean) => Promise<void>;
  clearAllOrders: () => void;
  // Computed values
  getFilteredOrders: (filter: { field: string; value: string } | null) => Order[];
  getStatusCounts: () => Record<string, number>;
}

const OrderDataContext = createContext<OrderDataContextType | undefined>(undefined);

interface OrderDataProviderProps {
  children: ReactNode;
}

export const OrderDataProvider: React.FC<OrderDataProviderProps> = ({ children }) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Get auth state from AuthContext
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();
  
  
  // Refs to prevent duplicate fetches and state updates after unmount
  const fetchingRef = useRef(false);
  const mountedRef = useRef(true);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Create a ref to hold the latest auth state to avoid stale closures
  const authRef = useRef({ isAuthenticated, user });
  useEffect(() => {
    authRef.current = { isAuthenticated, user };
  }, [isAuthenticated, user]);

  // Stable fetchOrders function with no dependencies
  const fetchOrders = useCallback(async () => {
    // Prevent concurrent fetches
    if (fetchingRef.current) {
      console.debug('[OrderDataContext] Fetch already in progress, skipping');
      return;
    }
    
    fetchingRef.current = true;
    
    try {
      setLoading(true);
      setError(null);
      
      // Get user info from ref to avoid stale closures
      const currentUser = authRef.current.user;
      const isAdmin = currentUser?.role === 'admin' || currentUser?.role === 'ADMIN';
      
      console.log('[OrderDataContext] Fetching orders for tenant:', currentUser?.tenantId || 'default');
      console.log('[OrderDataContext] User role:', currentUser?.role);
      console.log('[OrderDataContext] Is admin:', isAdmin);
      console.log('[OrderDataContext] User object:', currentUser);
      
      // Include deleted orders for admins
      console.log('[OrderDataContext] Calling getOrders with includeDeleted:', isAdmin);
      const data = await OrderService.getOrders(isAdmin ? { includeDeleted: true } : undefined);
      
      console.log('[OrderDataContext] Fetched', data?.length || 0, 'orders');
      if (data && data.length > 0) {
        console.log('[OrderDataContext] First order:', data[0]);
        console.log('[OrderDataContext] First order status:', data[0].status);
        console.log('[OrderDataContext] First order total:', data[0].total);
      } else {
        console.log('[OrderDataContext] No orders returned from API');
      }
      
      // Only update state if component is still mounted
      if (mountedRef.current && data && Array.isArray(data)) {
        setOrders(data);
      }
    } catch (err: any) {
      console.error('[OrderDataContext] Error fetching orders:', err);
      console.error('[OrderDataContext] Error details:', {
        message: err?.message,
        response: err?.response,
        status: err?.response?.status,
        data: err?.response?.data
      });
      if (mountedRef.current) {
        setError(err?.message || 'Erro ao carregar pedidos');
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
      fetchingRef.current = false;
    }
  }, []);

  // Track if we've fetched once
  const hasFetchedRef = useRef(false);
  
  // Reset fetch flag when user changes
  useEffect(() => {
    if (!user) {
      hasFetchedRef.current = false;
    } else {
      // When user is set, we should allow fetching
      console.log('[OrderDataContext] User changed, allowing fetch for:', user.email);
    }
  }, [user]);
  
  // Single useEffect that only fires after auth is ready
  useEffect(() => {
    console.log('[OrderDataContext] Auth effect triggered:', {
      authLoading,
      isAuthenticated,
      userExists: !!user,
      hasFetched: hasFetchedRef.current
    });
    
    // Wait for auth to finish loading
    if (authLoading) {
      console.log('[OrderDataContext] Still waiting for auth...');
      return;
    }
    
    // Only fetch if authenticated
    if (!isAuthenticated || !user) {
      console.log('[OrderDataContext] Not authenticated or no user');
      // Clear orders when not authenticated
      setOrders([]);
      hasFetchedRef.current = false;
      return;
    }
    
    // Only fetch once per user session
    if (hasFetchedRef.current) {
      console.log('[OrderDataContext] Already fetched, skipping');
      return;
    }
    
    // Fetch orders once auth is ready
    console.log('[OrderDataContext] All conditions met, fetching orders...');
    hasFetchedRef.current = true;
    
    // Fetch immediately without delay for better UX
    console.log('[OrderDataContext] Fetching orders immediately...');
    fetchOrders();
  }, [authLoading, isAuthenticated, user]); // Don't include fetchOrders to avoid loops

  // Update order status with optimistic update
  const updateOrderStatus = useCallback(async (orderId: string, newStatus: string) => {
    if (!orderId) {
      throw new Error('ID do pedido não fornecido');
    }

    // Find the order to update
    const orderToUpdate = orders.find(o => o.id === orderId);
    if (!orderToUpdate) {
      throw new Error('Pedido não encontrado');
    }

    // Optimistic update
    const optimisticOrder = { ...orderToUpdate, situacao: newStatus, situacaoVenda: newStatus };
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.id === orderId ? optimisticOrder : order
      )
    );

    try {
      // Make API call
      const updatedOrder = await OrderService.updateOrderStatus(orderId, newStatus);
      
      // Update with server response
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === updatedOrder.id ? updatedOrder : order
        )
      );
    } catch (err: any) {
      console.error('Error updating order status:', err);
      
      // Rollback on error
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId ? orderToUpdate : order
        )
      );
      
      setError(err?.message || 'Erro ao atualizar status do pedido');
      throw err;
    }
  }, [orders]);

  // Update entire order
  const updateOrder = useCallback(async (orderId: string, changes: Partial<Order>) => {
    if (!orderId) {
      throw new Error('ID do pedido não fornecido');
    }

    const orderToUpdate = orders.find(o => o.id === orderId);
    if (!orderToUpdate) {
      throw new Error('Pedido não encontrado');
    }

    // Optimistic update
    const optimisticOrder = { ...orderToUpdate, ...changes };
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.id === orderId ? optimisticOrder : order
      )
    );

    try {
      // Make API call with only the changes
      const updatedOrder = await OrderService.updateOrder(orderId, changes);
      
      // Update with server response
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === updatedOrder.id ? updatedOrder : order
        )
      );
    } catch (err: any) {
      console.error('Error updating order:', err);
      
      // Rollback on error
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId ? orderToUpdate : order
        )
      );
      
      setError(err?.message || 'Erro ao atualizar pedido');
      throw err;
    }
  }, [orders]);

  // Add new order
  const addOrder = useCallback(async (orderData: any) => {
    try {
      setLoading(true);
      const createdOrder = await OrderService.createNewOrder(orderData);
      
      // Add the new order to state
      setOrders(prevOrders => [...prevOrders, createdOrder]);
    } catch (err: any) {
      console.error('Error adding order:', err);
      // Pass through the specific error message from the service
      const errorMessage = err?.message || 'Erro ao adicionar pedido';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete order
  const deleteOrder = useCallback(async (orderId: string, isPermanent: boolean = false) => {
    try {
      await OrderService.deleteOrder(orderId, isPermanent);
      
      if (isPermanent) {
        // Remove from state
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));
      } else {
        // Update status to deleted
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === orderId 
              ? { ...order, status: 'Deletado' }
              : order
          )
        );
      }
    } catch (err: any) {
      console.error('Error deleting order:', err);
      setError(err?.message || 'Erro ao deletar pedido');
      throw err;
    }
  }, []);

  // Bulk delete orders
  const bulkDeleteOrders = useCallback(async (orderIds: string[], isPermanent: boolean = false) => {
    try {
      const result = await OrderService.bulkDeleteOrders(orderIds, isPermanent);
      
      if (isPermanent) {
        // Remove from state
        setOrders(prevOrders => 
          prevOrders.filter(order => !orderIds.includes(order.id))
        );
      } else {
        // Update status to deleted
        setOrders(prevOrders => 
          prevOrders.map(order => 
            orderIds.includes(order.id)
              ? { ...order, status: 'Deletado' }
              : order
          )
        );
      }
      
      return result;
    } catch (err: any) {
      console.error('Error bulk deleting orders:', err);
      setError(err?.message || 'Erro ao deletar pedidos');
      throw err;
    }
  }, []);

  // Clear all orders (local only)
  const clearAllOrders = useCallback(() => {
    localStorage.removeItem('orders');
    setOrders([]);
  }, []);

  // Get filtered orders
  const getFilteredOrders = useCallback((filter: { field: string; value: string } | null) => {
    const isAdmin = authRef.current.user?.role === 'admin';
    
    if (!filter) {
      // Show all orders except deleted ones (unless user is admin)
      return orders.filter(order => {
        const isDeleted = order.status?.toLowerCase() === 'deletado';
        return isAdmin || !isDeleted;
      });
    }

    // Special filter for "Receber Hoje"
    if (filter.field === 'special' && filter.value === 'dataRecebimento') {
      const today = new Date().toLocaleDateString('pt-BR');
      return orders.filter(order => {
        // Only show Negociação and Promessa orders
        const status = order.status?.toLowerCase() || '';
        if (status !== 'negociação' && status !== 'promessa') {
          return false;
        }
        
        // Check if nextPaymentDate is today or overdue
        if (order.nextPaymentDate) {
          const paymentDate = new Date(order.nextPaymentDate).toLocaleDateString('pt-BR');
          return paymentDate <= today;
        }
        
        return order.dataRecebimento === today;
      });
    }

    // Normal status filter
    return orders.filter(order => {
      const fieldValue = (order as any)[filter.field] || '';
      
      return fieldValue?.toLowerCase() === filter.value.toLowerCase();
    });
  }, [orders]);

  // Memoized function to get status counts
  const getStatusCounts = useCallback(() => {
    const counts: Record<string, number> = {};
    
    const normalizeString = (str: string) => {
      return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase().replace(/\s+/g, '');
    };
    
    // Count all statuses
    orders.forEach(order => {
      const status = order.status || '';
      if (status) {
        const normalizedStatus = normalizeString(status);
        counts[normalizedStatus] = (counts[normalizedStatus] || 0) + 1;
      }
    });
    
    // Override 'analise' count to include orders that require review
    const analiseOrders = orders.filter(order => {
      const normalizedStatus = normalizeString(order.status || '');
      // Count orders with status 'Análise' OR requiresReview = true (not yet reviewed)
      return normalizedStatus === 'analise' || (order.requiresReview && !order.reviewedAt);
    });
    counts['analise'] = analiseOrders.length;
    
    // Add special counts
    counts['todos'] = orders.filter(order => {
      const orderStatus = order.status || '';
      // Check for orderNumber as valid order identifier
      const hasValidId = !!(order.orderNumber || order.id);
      return hasValidId && (!orderStatus || orderStatus.toLowerCase() !== 'deletado');
    }).length;
    
    const receberHojeOrders = orders.filter(order => {
      // Only count Negociação and Promessa orders
      const status = order.status?.toLowerCase() || '';
      if (status !== 'negociação' && status !== 'promessa') {
        return false;
      }
      
      // Check if nextPaymentDate is today or overdue
      const today = new Date().toLocaleDateString('pt-BR');
      if (order.nextPaymentDate) {
        const paymentDate = new Date(order.nextPaymentDate).toLocaleDateString('pt-BR');
        return paymentDate <= today;
      }
      
      return order.paymentReceivedDate === today;
    });
    
    counts['receberhoje'] = receberHojeOrders.length;
    
    return counts;
  }, [orders]);

  const value = useMemo(() => ({
    orders,
    loading,
    error,
    fetchOrders,
    updateOrderStatus,
    updateOrder,
    addOrder,
    deleteOrder,
    bulkDeleteOrders,
    clearAllOrders,
    getFilteredOrders,
    getStatusCounts,
  }), [
    orders,
    loading,
    error,
    fetchOrders,
    updateOrderStatus,
    updateOrder,
    addOrder,
    deleteOrder,
    bulkDeleteOrders,
    clearAllOrders,
    getFilteredOrders,
    getStatusCounts,
  ]);

  // Add to window for debugging
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).debugOrderContext = {
        fetchOrders,
        orders,
        loading,
        error,
        user: authRef.current.user,
        isAuthenticated: authRef.current.isAuthenticated
      };
    }
  }, [fetchOrders, orders, loading, error]);

  return (
    <OrderDataContext.Provider value={value}>
      {children}
    </OrderDataContext.Provider>
  );
};

export const useOrderData = (): OrderDataContextType => {
  const context = useContext(OrderDataContext);
  if (context === undefined) {
    throw new Error('useOrderData must be used within an OrderDataProvider');
  }
  return context;
};