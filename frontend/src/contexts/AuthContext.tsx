import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import UnifiedAuthService, { UserInfo } from '../services/UnifiedAuthService';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  userInfo: UserInfo | null;
  user: UserInfo | null; // Alias for userInfo
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAdmin: () => boolean;
  isSupervisor: () => boolean;
  isCollector: () => boolean;
  isSeller: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  useEffect(() => {
    // Check authentication status on mount
    const checkAuth = async () => {
      setIsLoading(true);
      const isAuth = UnifiedAuthService.isAuthenticated();
      setIsAuthenticated(isAuth);

      if (isAuth) {
        const user = UnifiedAuthService.getUserInfo();
        console.log('AuthContext: Loading user info on mount:', user);
        setUserInfo(user);
      }
      setIsLoading(false);
    };

    checkAuth();

    // Listen for storage changes (in case user logs in from another tab)
    const handleStorageChange = () => {
      const isAuth = UnifiedAuthService.isAuthenticated();
      setIsAuthenticated(isAuth);
      
      if (isAuth) {
        const user = UnifiedAuthService.getUserInfo();
        console.log('AuthContext: User info updated from storage:', user);
        setUserInfo(user);
      } else {
        setUserInfo(null);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const login = async (email: string, password: string) => {
    await UnifiedAuthService.login({ email, password });
    setIsAuthenticated(true);
    const user = UnifiedAuthService.getUserInfo();
    console.log('AuthContext: User logged in:', user);
    setUserInfo(user);
  };

  const logout = () => {
    UnifiedAuthService.logout();
    setIsAuthenticated(false);
    setUserInfo(null);
  };

  // Helper functions to check user roles
  const isAdmin = () => {
    // Use state userInfo instead of fetching from service
    return userInfo?.role === 'admin';
  };

  const isSupervisor = () => {
    // Use state userInfo instead of fetching from service
    return userInfo?.role === 'supervisor';
  };

  const isCollector = () => {
    // Use state userInfo instead of fetching from service
    // Note: Backend uses 'cobrador', frontend uses 'collector'
    return userInfo?.role === 'collector' || userInfo?.role === 'cobrador';
  };

  const isSeller = () => {
    // Use state userInfo instead of fetching from service
    // Note: Backend uses 'vendedor', frontend uses 'seller'
    return userInfo?.role === 'seller' || userInfo?.role === 'vendedor';
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        userInfo,
        user: userInfo, // Alias for userInfo
        login,
        logout,
        isAdmin,
        isSupervisor,
        isCollector,
        isSeller
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};