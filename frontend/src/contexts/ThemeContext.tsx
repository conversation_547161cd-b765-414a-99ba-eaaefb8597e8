import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme, Theme, PaletteMode } from '@mui/material';
import { ptBR } from '@mui/material/locale';

// Define theme colors
const lightTheme = {
  primary: {
    main: '#1976d2',
    light: '#42a5f5',
    dark: '#1565c0',
    contrastText: '#fff',
  },
  secondary: {
    main: '#9c27b0',
    light: '#ba68c8',
    dark: '#7b1fa2',
    contrastText: '#fff',
  },
  background: {
    default: '#f5f5f5',
    paper: '#fff',
  },
};

const darkTheme = {
  primary: {
    main: '#90caf9',
    light: '#e3f2fd',
    dark: '#42a5f5',
    contrastText: '#000',
  },
  secondary: {
    main: '#ce93d8',
    light: '#f3e5f5',
    dark: '#ab47bc',
    contrastText: '#000',
  },
  background: {
    default: '#121212',
    paper: '#1e1e1e',
  },
};

// Create theme with mode
const createAppTheme = (mode: PaletteMode) => {
  const colors = mode === 'light' ? lightTheme : darkTheme;

  return createTheme(
    {
      palette: {
        mode,
        primary: colors.primary,
        secondary: colors.secondary,
        background: colors.background,
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: 12.6, // 90% of default 14px
        h1: {
          fontSize: '2.16rem', // 90% of 2.4rem
        },
        h2: {
          fontSize: '1.8rem', // 90% of 2rem
        },
        h3: {
          fontSize: '1.44rem', // 90% of 1.6rem
        },
        h4: {
          fontSize: '1.26rem', // 90% of 1.4rem
        },
        h5: {
          fontSize: '1.08rem', // 90% of 1.2rem
        },
        h6: {
          fontSize: '0.9rem', // 90% of 1rem
        },
        body1: {
          fontSize: '0.875rem', // 90% of 1rem (14px)
        },
        body2: {
          fontSize: '0.7875rem', // 90% of 0.875rem
        },
        button: {
          fontSize: '0.7875rem', // 90% of 0.875rem
        },
        caption: {
          fontSize: '0.675rem', // 90% of 0.75rem
        },
        overline: {
          fontSize: '0.675rem', // 90% of 0.75rem
        },
      },
      spacing: 7.2, // 90% of default 8px
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              overflowX: 'hidden',
              width: '100%',
              boxSizing: 'border-box',
            },
            '#root': {
              width: '100%',
              minHeight: '100vh',
              overflowX: 'hidden',
            },
            '*': {
              boxSizing: 'border-box',
            },
          },
        },
        MuiButton: {
          styleOverrides: {
            root: {
              textTransform: 'none',
              borderRadius: 7,
              fontWeight: 500,
              boxShadow: 'none',
              padding: '5px 14px', // 90% of default padding
              minHeight: '32px', // 90% of ~36px
              '&:hover': {
                boxShadow: 'none',
              },
              '&.MuiButton-sizeSmall': {
                padding: '3px 9px',
                fontSize: '0.7rem',
                minHeight: '28px',
              },
              '&.MuiButton-sizeLarge': {
                padding: '7px 20px',
                fontSize: '0.875rem',
                minHeight: '40px',
              },
            },
            contained: {
              boxShadow: 'none',
              '&:hover': {
                boxShadow: 'none',
              },
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 10,
              boxShadow: mode === 'light'
                ? '0px 1px 3px rgba(0, 0, 0, 0.05)'
                : '0px 1px 3px rgba(0, 0, 0, 0.2)',
              border: mode === 'light' ? '1px solid rgba(0, 0, 0, 0.05)' : 'none',
            },
          },
        },
        MuiPaper: {
          styleOverrides: {
            root: {
              borderRadius: 10,
              boxShadow: mode === 'light'
                ? '0px 1px 3px rgba(0, 0, 0, 0.05)'
                : '0px 1px 3px rgba(0, 0, 0, 0.2)',
            },
          },
        },
        MuiTableCell: {
          styleOverrides: {
            root: {
              borderBottom: mode === 'light' ? '1px solid rgba(0, 0, 0, 0.05)' : '1px solid rgba(255, 255, 255, 0.05)',
              padding: '14px 12px', // Reduced padding
              fontSize: '0.8125rem', // 90% of default
            },
            head: {
              fontWeight: 600,
              backgroundColor: mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.05)',
              padding: '12px',
              fontSize: '0.8125rem',
            },
            sizeSmall: {
              padding: '5px 12px',
            },
          },
        },
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: 6,
              height: '24px',
              fontSize: '0.75rem',
            },
            sizeSmall: {
              height: '20px',
              fontSize: '0.7rem',
            },
            labelSmall: {
              paddingLeft: '8px',
              paddingRight: '8px',
            },
          },
        },
        MuiTextField: {
          defaultProps: {
            size: 'small',
          },
          styleOverrides: {
            root: {
              '& .MuiInputBase-root': {
                fontSize: '0.875rem',
              },
              '& .MuiInputLabel-root': {
                fontSize: '0.875rem',
              },
            },
          },
        },
        MuiSelect: {
          defaultProps: {
            size: 'small',
          },
          styleOverrides: {
            root: {
              fontSize: '0.875rem',
            },
          },
        },
        MuiFormControlLabel: {
          styleOverrides: {
            label: {
              fontSize: '0.875rem',
            },
          },
        },
      },
    },
    ptBR // Add Brazilian Portuguese localization
  );
};

// Theme context type
interface ThemeContextType {
  theme: Theme;
  mode: PaletteMode;
  toggleTheme: () => void;
  setMode: (mode: PaletteMode) => void;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme provider
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Get initial theme from localStorage or use light mode
  const [mode, setMode] = useState<PaletteMode>(() => {
    const savedMode = localStorage.getItem('themeMode');
    return (savedMode as PaletteMode) || 'light';
  });

  // Create theme based on mode
  const theme = createAppTheme(mode);

  // Toggle theme
  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  // Save theme mode to localStorage
  useEffect(() => {
    localStorage.setItem('themeMode', mode);
  }, [mode]);

  return (
    <ThemeContext.Provider value={{ theme, mode, toggleTheme, setMode }}>
      <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
};
