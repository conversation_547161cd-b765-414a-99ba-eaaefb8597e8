# Debug - Erro ao Atualizar Pedido

## Fluxo de Atualização de Status

1. **OrdersTable.handleUpdateStatus**
   - Chama `OrderService.updateOrderStatus(selectedOrder.id, newStatus)`
   - Se sucesso, chama `onOrderUpdate(savedOrder)`

2. **OrderService.updateOrderStatus**
   - Valida o orderId
   - Mapeia status PT → EN
   - Faz PATCH para `/orders/{orderId}/status`
   - Converte resposta para formato frontend

3. **PedidosPage.handleOrderUpdate**
   - Recebe o pedido já atualizado
   - Chama `updateOrder` do contexto (apenas atualiza estado local)

4. **OrderDataContext.updateOrder**
   - Atualiza estado local apenas (não chama API)
   - Dispara evento 'orders-updated'

## Possíveis Causas do Erro

1. **ID inválido ou ausente**
   - Verificar se `selectedOrder.id` existe
   - Log adicionado para validar

2. **Status inválido**
   - Backend pode rejeitar transição inválida
   - Verificar mapeamento de status

3. **Problema de autenticação**
   - Token expirado
   - Tenant ID incorreto

4. **Erro no backend**
   - Endpoint incorreto
   - Validação falhou

## Como Debugar

1. Abrir console do navegador
2. Tentar atualizar um pedido
3. Observar logs:
   - "OrderService.updateOrderStatus called with:"
   - "Mapped status:"
   - "API Response:" (se sucesso)
   - "Error details:" (se erro)

4. Verificar Network tab:
   - Ver request para `/orders/{id}/status`
   - Verificar status code
   - Ver response body

## Logs Adicionados

- Validação de orderId
- Detalhes completos do erro
- Payload da request
- URL e método