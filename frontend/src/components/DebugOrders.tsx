import React, { useEffect, useState } from 'react';
import { useOrderData } from '../contexts/OrderDataContext';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip } from '@mui/material';

const DebugOrders: React.FC = () => {
  const { orders, getFilteredOrders, getStatusCounts } = useOrderData();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    // Get today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get orders that should be in Receber Hoje
    const receberHojeOrders = getFilteredOrders({ field: 'special', value: 'dataRecebimento' });
    
    // Get all Negociação and Promessa orders
    const negociacaoPromessaOrders = orders.filter(order => {
      const status = order.situacaoVenda?.toLowerCase() || order.situacao?.toLowerCase() || '';
      return status === 'negociação' || status === 'promessa';
    });

    // Get status counts
    const counts = getStatusCounts();

    setDebugInfo({
      today: today.toISOString(),
      todayFormatted: today.toLocaleDateString('pt-BR'),
      totalOrders: orders.length,
      negociacaoPromessaCount: negociacaoPromessaOrders.length,
      receberHojeCount: receberHojeOrders.length,
      statusCounts: counts,
      negociacaoPromessaOrders: negociacaoPromessaOrders.map(order => ({
        id: order.idVenda,
        status: order.situacaoVenda || order.situacao,
        nextPaymentDate: order.nextPaymentDate,
        nextPaymentDateFormatted: order.nextPaymentDate ? new Date(order.nextPaymentDate).toLocaleDateString('pt-BR') : 'N/A',
        isDue: order.nextPaymentDate ? new Date(order.nextPaymentDate) <= today : false,
        cliente: order.cliente,
        valor: order.valorVenda
      })),
      receberHojeOrders: receberHojeOrders.map(order => ({
        id: order.idVenda,
        status: order.situacaoVenda || order.situacao,
        nextPaymentDate: order.nextPaymentDate,
        cliente: order.cliente
      }))
    });
  }, [orders, getFilteredOrders, getStatusCounts]);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>Debug - Receber Hoje</Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>Informações Gerais</Typography>
        <Typography>Data de Hoje: {debugInfo.todayFormatted} ({debugInfo.today})</Typography>
        <Typography>Total de Pedidos: {debugInfo.totalOrders}</Typography>
        <Typography>Pedidos em Negociação/Promessa: {debugInfo.negociacaoPromessaCount}</Typography>
        <Typography>Pedidos em Receber Hoje: {debugInfo.receberHojeCount}</Typography>
        <Typography sx={{ mt: 2 }}>Status Counts:</Typography>
        <Box sx={{ pl: 2 }}>
          {Object.entries(debugInfo.statusCounts || {}).map(([status, count]) => (
            <Typography key={status}>{status}: {count as number}</Typography>
          ))}
        </Box>
      </Paper>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>Pedidos em Negociação/Promessa</Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Cliente</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Próximo Pagamento</TableCell>
                <TableCell>Vencido?</TableCell>
                <TableCell>Valor</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {debugInfo.negociacaoPromessaOrders?.map((order: any) => (
                <TableRow key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.cliente}</TableCell>
                  <TableCell>
                    <Chip label={order.status} size="small" />
                  </TableCell>
                  <TableCell>{order.nextPaymentDateFormatted}</TableCell>
                  <TableCell>
                    {order.isDue ? (
                      <Chip label="Vencido/Hoje" color="error" size="small" />
                    ) : (
                      <Chip label="Futuro" color="default" size="small" />
                    )}
                  </TableCell>
                  <TableCell>R$ {order.valor?.toFixed(2)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>Pedidos em Receber Hoje (Filtrados)</Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Cliente</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Próximo Pagamento</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {debugInfo.receberHojeOrders?.map((order: any) => (
                <TableRow key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.cliente}</TableCell>
                  <TableCell>{order.status}</TableCell>
                  <TableCell>{order.nextPaymentDate}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default DebugOrders;