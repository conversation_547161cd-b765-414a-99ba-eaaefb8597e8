import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  CircularProgress,
  Alert,
  Pagination,
  Badge,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as DenyIcon,
  History as HistoryIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Warning as WarningIcon,
  VerifiedUser as AuditIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import AntifraudService, { DuplicateReviewItem, ReviewDecision } from '../../services/AntifraudService';
import { useNotificationContext } from '../../contexts/NotificationContext';
import { useFeatureFlags } from '../../contexts/FeatureFlagContext';
import OrderAuditTrail from './OrderAuditTrail';

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

const DuplicateReviewQueue: React.FC = () => {
  const [items, setItems] = useState<DuplicateReviewItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<DuplicateReviewItem | null>(null);
  const [reviewDecision, setReviewDecision] = useState<ReviewDecision['decision']>('APPROVE_ORDER');
  const [reviewNotes, setReviewNotes] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [auditDialogOpen, setAuditDialogOpen] = useState(false);
  const [auditOrderId, setAuditOrderId] = useState<string>('');
  const { showNotification } = useNotificationContext();
  const { isEnabled } = useFeatureFlags();

  useEffect(() => {
    loadDuplicateQueue();
  }, [page]);

  const loadDuplicateQueue = async () => {
    try {
      setLoading(true);
      const response = await AntifraudService.getDuplicateReviewQueue(page, 20);
      console.log('Anti-fraud queue response:', response);
      setItems(response.items || []);
      setTotalPages(response.pages || 1);
    } catch (error: any) {
      showNotification('Erro ao carregar fila de revisão', 'error');
      console.error('Error loading duplicate queue:', error || 'Unknown error');
      // Set empty state on error
      setItems([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleReviewClick = (item: DuplicateReviewItem) => {
    setSelectedOrder(item);
    setReviewDialogOpen(true);
    setReviewDecision('APPROVE_ORDER');
    setReviewNotes('');
  };

  const handleReviewSubmit = async () => {
    if (!selectedOrder) return;

    try {
      await AntifraudService.reviewDuplicate(selectedOrder.id, {
        decision: reviewDecision,
        notes: reviewNotes,
      });

      showNotification('Pedido revisado com sucesso', 'success');
      setReviewDialogOpen(false);
      loadDuplicateQueue();
    } catch (error: any) {
      showNotification('Erro ao revisar pedido', 'error');
      console.error('Error reviewing duplicate:', error || 'Unknown error');
    }
  };

  const toggleRowExpansion = (orderId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedRows(newExpanded);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  if (!isEnabled('DUPLICATE_CHECK_ENABLED')) {
    return (
      <Box p={3}>
        <Alert severity="info">
          O sistema de detecção de duplicatas está desabilitado.
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h5" component="h2">
              Pedidos Duplicados para Revisão
            </Typography>
            <Badge badgeContent={items.length} color="error">
              <WarningIcon />
            </Badge>
          </Box>

          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : items.length === 0 ? (
            <Alert severity="success">
              Nenhum pedido duplicado pendente de revisão.
            </Alert>
          ) : (
            <>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell />
                      <TableCell>Pedido</TableCell>
                      <TableCell>Cliente</TableCell>
                      <TableCell>Telefone</TableCell>
                      <TableCell align="right">Valor</TableCell>
                      <TableCell align="center">Similaridade</TableCell>
                      <TableCell>Data</TableCell>
                      <TableCell align="center">Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {items.map((item) => (
                      <React.Fragment key={item.id}>
                        <TableRow>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => toggleRowExpansion(item.id)}
                            >
                              {expandedRows.has(item.id) ? (
                                <ExpandLessIcon />
                              ) : (
                                <ExpandMoreIcon />
                              )}
                            </IconButton>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              {item.orderNumber}
                            </Typography>
                          </TableCell>
                          <TableCell>{item.customerName}</TableCell>
                          <TableCell>{item.customerPhone}</TableCell>
                          <TableCell align="right">
                            {formatCurrency(item.total)}
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={AntifraudService.formatMatchScore(item.duplicateMatchScore)}
                              size="small"
                              style={{
                                backgroundColor: AntifraudService.getMatchScoreColor(item.duplicateMatchScore),
                                color: 'white',
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            {format(new Date(item.createdAt), 'dd/MM/yyyy HH:mm', {
                              locale: ptBR,
                            })}
                          </TableCell>
                          <TableCell align="center">
                            <Box display="flex" gap={1} justifyContent="center">
                              <Tooltip title="Revisar Duplicata">
                                <Button
                                  variant="contained"
                                  size="small"
                                  onClick={() => handleReviewClick(item)}
                                  startIcon={<HistoryIcon />}
                                >
                                  Revisar
                                </Button>
                              </Tooltip>
                              <Tooltip title="Ver Auditoria">
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setAuditOrderId(item.id);
                                    setAuditDialogOpen(true);
                                  }}
                                >
                                  <AuditIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={8} style={{ paddingBottom: 0, paddingTop: 0 }}>
                            <Collapse in={expandedRows.has(item.id)} timeout="auto" unmountOnExit>
                              <Box margin={1}>
                                <Typography variant="h6" gutterBottom component="div">
                                  Pedidos Similares Encontrados
                                </Typography>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Pedido</TableCell>
                                      <TableCell>Data</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {item.matchedOrders.map((match) => (
                                      <TableRow key={match.orderId}>
                                        <TableCell>{match.orderNumber}</TableCell>
                                        <TableCell>
                                          {format(new Date(match.createdAt), 'dd/MM/yyyy HH:mm', {
                                            locale: ptBR,
                                          })}
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>
                            </Collapse>
                          </TableCell>
                        </TableRow>
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box display="flex" justifyContent="center" mt={3}>
                <Pagination
                  count={totalPages}
                  page={page}
                  onChange={(_, value) => setPage(value)}
                  color="primary"
                />
              </Box>
            </>
          )}
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Revisar Pedido Duplicado
          {selectedOrder && (
            <Typography variant="subtitle2" color="textSecondary">
              Pedido: {selectedOrder.orderNumber} - Similaridade: {AntifraudService.formatMatchScore(selectedOrder.duplicateMatchScore)}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          <Box py={2}>
            <TextField
              select
              fullWidth
              label="Decisão"
              value={reviewDecision}
              onChange={(e) => setReviewDecision(e.target.value as ReviewDecision['decision'])}
              variant="outlined"
              margin="normal"
            >
              <MenuItem value="APPROVE_ORDER">
                <Box display="flex" alignItems="center">
                  <ApproveIcon style={{ marginRight: 8, color: '#4caf50' }} />
                  Aprovar Pedido
                </Box>
              </MenuItem>
              <MenuItem value="DENY_ORDER">
                <Box display="flex" alignItems="center">
                  <DenyIcon style={{ marginRight: 8, color: '#f44336' }} />
                  Negar Pedido
                </Box>
              </MenuItem>
              <MenuItem value="MERGE_ORDERS">Mesclar Pedidos</MenuItem>
              <MenuItem value="INVESTIGATE_FURTHER">Investigar Mais</MenuItem>
            </TextField>

            <TextField
              fullWidth
              multiline
              rows={4}
              label="Observações"
              value={reviewNotes}
              onChange={(e) => setReviewNotes(e.target.value)}
              variant="outlined"
              margin="normal"
              placeholder="Adicione observações sobre sua decisão..."
            />

            {selectedOrder && selectedOrder.matchedOrders.length > 0 && (
              <Alert severity="info" style={{ marginTop: 16 }}>
                Este pedido possui {selectedOrder.matchedOrders.length} pedido(s) similar(es) encontrado(s).
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>Cancelar</Button>
          <Button
            onClick={handleReviewSubmit}
            variant="contained"
            color={reviewDecision === 'APPROVE_ORDER' ? 'primary' : 'error'}
          >
            Confirmar Decisão
          </Button>
        </DialogActions>
      </Dialog>

      {/* Audit Trail Dialog */}
      <OrderAuditTrail
        orderId={auditOrderId}
        open={auditDialogOpen}
        onClose={() => setAuditDialogOpen(false)}
      />
    </Box>
  );
};

export default DuplicateReviewQueue;