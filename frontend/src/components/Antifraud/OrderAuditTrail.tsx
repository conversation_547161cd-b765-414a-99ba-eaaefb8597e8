import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Typography,
  Box,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/lab';
import {
  AddCircle as CreateIcon,
  Update as UpdateIcon,
  Warning as DuplicateIcon,
  CheckCircle as ApproveIcon,
  Cancel as DenyIcon,
  LocationOn as AddressIcon,
  Security as SecurityIcon,
  VerifiedUser as VerifiedIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import AntifraudService, { AuditLogEntry } from '../../services/AntifraudService';

interface OrderAuditTrailProps {
  orderId: string;
  open: boolean;
  onClose: () => void;
}

const OrderAuditTrail: React.FC<OrderAuditTrailProps> = ({ orderId, open, onClose }) => {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open && orderId) {
      loadAuditTrail();
    }
  }, [open, orderId]);

  const loadAuditTrail = async () => {
    try {
      setLoading(true);
      setError(null);
      const logs = await AntifraudService.getOrderAuditTrail(orderId);
      setAuditLogs(logs);
    } catch (err) {
      setError('Erro ao carregar histórico de auditoria');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'ORDER_CREATED':
        return <CreateIcon />;
      case 'ORDER_UPDATED':
        return <UpdateIcon />;
      case 'DUPLICATE_DETECTED':
        return <DuplicateIcon />;
      case 'DUPLICATE_APPROVED':
        return <ApproveIcon />;
      case 'DUPLICATE_DENIED':
        return <DenyIcon />;
      case 'ADDRESS_PARSED':
      case 'ADDRESS_GEOCODED':
        return <AddressIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getActionColor = (action: string): 'primary' | 'secondary' | 'error' | 'warning' | 'success' => {
    switch (action) {
      case 'ORDER_CREATED':
        return 'primary';
      case 'DUPLICATE_DETECTED':
        return 'warning';
      case 'DUPLICATE_APPROVED':
        return 'success';
      case 'DUPLICATE_DENIED':
        return 'error';
      default:
        return 'secondary';
    }
  };

  const formatAction = (action: string): string => {
    const actionMap: { [key: string]: string } = {
      ORDER_CREATED: 'Pedido Criado',
      ORDER_UPDATED: 'Pedido Atualizado',
      DUPLICATE_DETECTED: 'Duplicata Detectada',
      DUPLICATE_REVIEWED: 'Duplicata Revisada',
      DUPLICATE_APPROVED: 'Duplicata Aprovada',
      DUPLICATE_DENIED: 'Duplicata Negada',
      ADDRESS_PARSED: 'Endereço Processado',
      ADDRESS_GEOCODED: 'Endereço Geocodificado',
      MANUAL_OVERRIDE: 'Alteração Manual',
      SYSTEM_OVERRIDE: 'Alteração do Sistema',
    };
    return actionMap[action] || action;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Histórico de Auditoria</Typography>
          <Typography variant="subtitle2" color="textSecondary">
            Pedido: {orderId.substring(0, 8)}...
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        {loading ? (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : auditLogs.length === 0 ? (
          <Alert severity="info">Nenhum registro de auditoria encontrado.</Alert>
        ) : (
          <Timeline position="alternate">
            {auditLogs.map((log, index) => (
              <TimelineItem key={log.id}>
                <TimelineOppositeContent color="textSecondary">
                  <Typography variant="caption">
                    {format(new Date(log.performedAt), 'dd/MM/yyyy')}
                  </Typography>
                  <br />
                  <Typography variant="caption">
                    {format(new Date(log.performedAt), 'HH:mm:ss')}
                  </Typography>
                </TimelineOppositeContent>
                <TimelineSeparator>
                  <TimelineDot color={getActionColor(log.action)}>
                    {getActionIcon(log.action)}
                  </TimelineDot>
                  {index < auditLogs.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Box mb={2}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {formatAction(log.action)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Por: {log.performedByName} ({log.performedByRole})
                    </Typography>
                    
                    {log.metadata?.decision && (
                      <Box mt={1}>
                        <Chip
                          label={`Decisão: ${log.metadata.decision}`}
                          size="small"
                          color={log.metadata.decision === 'APPROVE_ORDER' ? 'success' : 'error'}
                        />
                      </Box>
                    )}
                    
                    {log.metadata?.notes && (
                      <Typography variant="body2" style={{ marginTop: 8 }}>
                        Observações: {log.metadata.notes}
                      </Typography>
                    )}
                    
                    {log.metadata?.duplicateCheckResult && (
                      <Box mt={1}>
                        <Typography variant="caption" color="textSecondary">
                          Score de duplicação: {Math.round(log.metadata.duplicateCheckResult.matchScore * 100)}%
                        </Typography>
                      </Box>
                    )}
                    
                    <Box mt={1} display="flex" alignItems="center">
                      {log.signatureValid ? (
                        <>
                          <VerifiedIcon style={{ fontSize: 16, marginRight: 4, color: '#4caf50' }} />
                          <Typography variant="caption" style={{ color: '#4caf50' }}>
                            Assinatura Verificada
                          </Typography>
                        </>
                      ) : (
                        <>
                          <SecurityIcon style={{ fontSize: 16, marginRight: 4, color: '#f44336' }} />
                          <Typography variant="caption" style={{ color: '#f44336' }}>
                            Assinatura Inválida
                          </Typography>
                        </>
                      )}
                    </Box>
                  </Box>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Fechar</Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderAuditTrail;