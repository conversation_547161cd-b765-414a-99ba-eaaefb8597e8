import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Card,
  CardContent,
  Tabs,
  Tab,
  Button,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  Close as CloseIcon,
  SwapHoriz as SwapIcon,
  Map as MapIcon,
  Timeline as TimelineIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  AttachMoney as MoneyIcon,
  CalendarToday as DateIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import AntifraudService, { DuplicateReviewItem } from '../../services/AntifraudService';

interface OrderComparisonViewProps {
  leftOrder: DuplicateReviewItem;
  rightOrderId: string;
  onClose: () => void;
}

interface OrderDetails {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerCPF?: string;
  fullAddress?: string;
  total: number;
  status: string;
  createdAt: string;
  items?: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  addressComponents?: {
    street: string;
    streetNumber: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
}

const OrderComparisonView: React.FC<OrderComparisonViewProps> = ({
  leftOrder,
  rightOrderId,
  onClose,
}) => {
  const [rightOrder, setRightOrder] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [swapped, setSwapped] = useState(false);

  useEffect(() => {
    // Fetch right order details
    // This would be a real API call
    setLoading(false);
    setRightOrder({
      id: rightOrderId,
      orderNumber: 'ID00124',
      customerName: leftOrder.customerName,
      customerPhone: leftOrder.customerPhone,
      total: leftOrder.total * 0.95,
      status: 'Completo',
      createdAt: new Date(Date.now() - 86400000).toISOString(),
      addressComponents: {
        street: 'RUA DAS FLORES',
        streetNumber: '125',
        neighborhood: 'CENTRO',
        city: 'SAO PAULO',
        state: 'SP',
        zipCode: '01000000',
      },
    } as OrderDetails);
  }, [rightOrderId, leftOrder]);

  const order1 = swapped ? rightOrder : leftOrder;
  const order2 = swapped ? leftOrder : rightOrder;

  const handleSwap = () => setSwapped(!swapped);

  const renderDiff = (value1: any, value2: any, formatter?: (v: any) => string) => {
    const formatted1 = formatter ? formatter(value1) : value1;
    const formatted2 = formatter ? formatter(value2) : value2;
    const isDifferent = value1 !== value2;

    return (
      <Box display="flex" alignItems="center" gap={2}>
        <Typography
          variant="body2"
          sx={{
            color: isDifferent ? 'error.main' : 'text.primary',
            fontWeight: isDifferent ? 'bold' : 'normal',
          }}
        >
          {formatted1}
        </Typography>
        {isDifferent && (
          <>
            <SwapIcon fontSize="small" color="action" />
            <Typography
              variant="body2"
              sx={{
                color: 'error.main',
                fontWeight: 'bold',
              }}
            >
              {formatted2}
            </Typography>
          </>
        )}
      </Box>
    );
  };

  if (loading) {
    return <LinearProgress />;
  }

  if (!rightOrder) {
    return <Typography>Erro ao carregar pedido</Typography>;
  }

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Typography variant="h6" flex={1}>
          Comparação de Pedidos
        </Typography>
        <Tooltip title="Trocar posições">
          <IconButton onClick={handleSwap}>
            <SwapIcon />
          </IconButton>
        </Tooltip>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={(_, v) => setTabValue(v)}>
          <Tab label="Informações Gerais" />
          <Tab label="Endereços" />
          <Tab label="Histórico" />
          <Tab label="Mapa" />
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {tabValue === 0 && (
          <Grid container spacing={2}>
            {/* Order 1 */}
            <Grid item xs={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {order1?.orderNumber}
                  </Typography>
                  
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                          Cliente
                        </TableCell>
                        <TableCell>{order1?.customerName}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <PhoneIcon fontSize="small" sx={{ mr: 1 }} />
                          Telefone
                        </TableCell>
                        <TableCell>{order1?.customerPhone}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <MoneyIcon fontSize="small" sx={{ mr: 1 }} />
                          Valor
                        </TableCell>
                        <TableCell>
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL',
                          }).format(order1?.total || 0)}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <DateIcon fontSize="small" sx={{ mr: 1 }} />
                          Data
                        </TableCell>
                        <TableCell>
                          {format(new Date(order1?.createdAt || ''), 'dd/MM/yyyy HH:mm', {
                            locale: ptBR,
                          })}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </Grid>

            {/* Order 2 */}
            <Grid item xs={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {order2?.orderNumber}
                  </Typography>
                  
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                          Cliente
                        </TableCell>
                        <TableCell>{order2?.customerName}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <PhoneIcon fontSize="small" sx={{ mr: 1 }} />
                          Telefone
                        </TableCell>
                        <TableCell>{order2?.customerPhone}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <MoneyIcon fontSize="small" sx={{ mr: 1 }} />
                          Valor
                        </TableCell>
                        <TableCell>
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL',
                          }).format(order2?.total || 0)}
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>
                          <DateIcon fontSize="small" sx={{ mr: 1 }} />
                          Data
                        </TableCell>
                        <TableCell>
                          {format(new Date(order2?.createdAt || ''), 'dd/MM/yyyy HH:mm', {
                            locale: ptBR,
                          })}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </Grid>

            {/* Comparison Summary */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Resumo da Comparação
                  </Typography>
                  
                  <Box display="flex" gap={2} flexWrap="wrap">
                    <Chip
                      label={`Similaridade: ${leftOrder.duplicateMatchScore}%`}
                      color="error"
                      variant="outlined"
                    />
                    <Chip
                      label={`Diferença de valor: ${Math.abs(order1?.total - order2?.total).toFixed(2)}`}
                      variant="outlined"
                    />
                    <Chip
                      label={`Intervalo: ${Math.abs(
                        new Date(order1?.createdAt).getTime() - new Date(order2?.createdAt).getTime()
                      ) / 3600000} horas`}
                      variant="outlined"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {tabValue === 1 && (
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <LocationIcon sx={{ mr: 1 }} />
                    Endereço - {order1?.orderNumber}
                  </Typography>
                  <Typography variant="body2">
                    {order1?.addressComponents?.street}, {order1?.addressComponents?.streetNumber}
                  </Typography>
                  <Typography variant="body2">
                    {order1?.addressComponents?.neighborhood}
                  </Typography>
                  <Typography variant="body2">
                    {order1?.addressComponents?.city} - {order1?.addressComponents?.state}
                  </Typography>
                  <Typography variant="body2">
                    CEP: {order1?.addressComponents?.zipCode}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <LocationIcon sx={{ mr: 1 }} />
                    Endereço - {order2?.orderNumber}
                  </Typography>
                  <Typography variant="body2">
                    {order2?.addressComponents?.street}, {order2?.addressComponents?.streetNumber}
                  </Typography>
                  <Typography variant="body2">
                    {order2?.addressComponents?.neighborhood}
                  </Typography>
                  <Typography variant="body2">
                    {order2?.addressComponents?.city} - {order2?.addressComponents?.state}
                  </Typography>
                  <Typography variant="body2">
                    CEP: {order2?.addressComponents?.zipCode}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {tabValue === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              <TimelineIcon sx={{ mr: 1 }} />
              Histórico do Cliente
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Funcionalidade em desenvolvimento...
            </Typography>
          </Box>
        )}

        {tabValue === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              <MapIcon sx={{ mr: 1 }} />
              Visualização no Mapa
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Integração com mapas em desenvolvimento...
            </Typography>
          </Box>
        )}
      </Box>

      {/* Actions */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', display: 'flex', gap: 2 }}>
        <Button variant="contained" color="success" flex={1}>
          Aprovar Ambos
        </Button>
        <Button variant="contained" color="error" flex={1}>
          Negar Ambos
        </Button>
        <Button variant="outlined" flex={1}>
          Mesclar Pedidos
        </Button>
      </Box>
    </Paper>
  );
};

export default OrderComparisonView;