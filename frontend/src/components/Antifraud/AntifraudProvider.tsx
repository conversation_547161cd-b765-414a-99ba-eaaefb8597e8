import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { prefetchAntifraudData } from '../../hooks/useAntifraudQuery';

// Create a client with anti-fraud specific configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // 30 seconds
      gcTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
      onError: (error: any) => {
        // Global error handling for mutations
        console.error('Mutation error:', error || 'Unknown error');
        
        // Could add notification here
        if (error?.message) {
          // showNotification(error.message, 'error');
        }
      },
    },
  },
});

interface AntifraudProviderProps {
  children: React.ReactNode;
}

export const AntifraudProvider: React.FC<AntifraudProviderProps> = ({ children }) => {
  useEffect(() => {
    // Register service worker
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      navigator.serviceWorker
        .register('/antifraud-sw.js')
        .then((registration) => {
          console.log('Antifraud SW registered:', registration.scope);
          
          // Check for updates every hour
          setInterval(() => {
            registration.update();
          }, 60 * 60 * 1000);
        })
        .catch((error) => {
          console.error('Antifraud SW registration failed:', error || 'Unknown error');
        });
    }

    // Prefetch initial data
    prefetchAntifraudData(queryClient);

    // Set up periodic background sync
    if ('sync' in navigator && 'serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        return (registration as any).sync.register('review-sync');
      });
    }

    // Clean up on unmount
    return () => {
      queryClient.clear();
    };
  }, []);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      console.log('Back online - refetching data');
      queryClient.refetchQueries();
    };

    const handleOffline = () => {
      console.log('Offline - using cached data');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}
    </QueryClientProvider>
  );
};