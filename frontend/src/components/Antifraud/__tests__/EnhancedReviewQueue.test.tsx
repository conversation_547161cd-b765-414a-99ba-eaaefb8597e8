import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import EnhancedReviewQueue from '../EnhancedReviewQueue';
import { useAntifraudStore } from '../../../stores/antifraudStore';
import { useAntifraudQueue } from '../../../hooks/useAntifraudQuery';

// Mock dependencies
jest.mock('../../../stores/antifraudStore');
jest.mock('../../../hooks/useAntifraudQuery');
jest.mock('react-window', () => ({
  FixedSizeList: ({ children, itemCount, itemSize, height, width }: any) => (
    <div data-testid="virtual-list" style={{ height, width }}>
      {Array.from({ length: Math.min(itemCount, 10) }, (_, index) => (
        <div key={index} style={{ height: itemSize }}>
          {children({ index, style: { height: itemSize } })}
        </div>
      ))}
    </div>
  ),
}));

const createQueryWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('EnhancedReviewQueue', () => {
  const mockOrders = [
    {
      id: '1',
      orderNumber: 'ORD-001',
      customerName: 'John Doe',
      customerCPF: '123.456.789-00',
      fullAddress: 'Rua A, 123',
      duplicateMatchScore: 85,
      matchedComponents: ['street', 'number'],
      createdAt: '2024-01-01T10:00:00Z',
      status: 'PENDING',
    },
    {
      id: '2',
      orderNumber: 'ORD-002',
      customerName: 'Jane Smith',
      customerCPF: '987.654.321-00',
      fullAddress: 'Av B, 456',
      duplicateMatchScore: 92,
      matchedComponents: ['street', 'neighborhood', 'city'],
      createdAt: '2024-01-02T14:30:00Z',
      status: 'PENDING',
    },
  ];

  const mockStore = {
    orders: mockOrders,
    selectedOrderIds: [],
    filters: {
      search: '',
      minScore: 70,
      maxScore: 100,
      dateRange: { start: null, end: null },
    },
    sortBy: 'score',
    sortDirection: 'desc',
    isLoading: false,
    error: null,
    getFilteredAndSortedOrders: jest.fn(() => mockOrders),
    toggleOrderSelection: jest.fn(),
    selectAllOrders: jest.fn(),
    clearSelection: jest.fn(),
    isAllSelected: jest.fn(() => false),
    getSelectedOrders: jest.fn(() => []),
    setFilters: jest.fn(),
    setSortBy: jest.fn(),
    setSortDirection: jest.fn(),
    refreshQueue: jest.fn(),
    reviewOrder: jest.fn(),
    bulkReview: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAntifraudStore as unknown as jest.Mock).mockReturnValue(mockStore);
    (useAntifraudQueue as jest.Mock).mockReturnValue({
      data: { items: mockOrders, total: 2, page: 1, totalPages: 1 },
      isLoading: false,
      error: null,
    });
  });

  describe('Rendering', () => {
    it('should render the review queue with orders', () => {
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByText('Fila de Revisão')).toBeInTheDocument();
      expect(screen.getByTestId('virtual-list')).toBeInTheDocument();
      expect(screen.getByText('ORD-001')).toBeInTheDocument();
      expect(screen.getByText('ORD-002')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
        ...mockStore,
        isLoading: true,
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should show empty state when no orders', () => {
      (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
        ...mockStore,
        getFilteredAndSortedOrders: jest.fn(() => []),
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByText(/Nenhum pedido duplicado pendente/i)).toBeInTheDocument();
    });

    it('should show error state', () => {
      (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
        ...mockStore,
        error: 'Failed to load orders',
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByText(/Failed to load orders/i)).toBeInTheDocument();
    });
  });

  describe('Filtering', () => {
    it('should update search filter', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const searchInput = screen.getByPlaceholderText(/Buscar por nome, CPF ou pedido/i);
      await user.type(searchInput, 'John');

      await waitFor(() => {
        expect(mockStore.setFilters).toHaveBeenCalledWith({ search: 'John' });
      });
    });

    it('should update score range filter', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const minScoreInput = screen.getByLabelText(/Score mínimo/i);
      await user.clear(minScoreInput);
      await user.type(minScoreInput, '80');

      await waitFor(() => {
        expect(mockStore.setFilters).toHaveBeenCalledWith({ minScore: 80 });
      });
    });

    it('should handle keyboard shortcut for search (Ctrl+F)', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      await user.keyboard('{Control>}f{/Control}');

      const searchInput = screen.getByPlaceholderText(/Buscar por nome, CPF ou pedido/i);
      expect(searchInput).toHaveFocus();
    });
  });

  describe('Sorting', () => {
    it('should toggle sort direction when clicking column header', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const scoreHeader = screen.getByText(/Score/i).closest('div');
      await user.click(scoreHeader!);

      expect(mockStore.setSortBy).toHaveBeenCalledWith('score');
      expect(mockStore.setSortDirection).toHaveBeenCalledWith('asc');
    });

    it('should change sort column', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const dateHeader = screen.getByText(/Data/i).closest('div');
      await user.click(dateHeader!);

      expect(mockStore.setSortBy).toHaveBeenCalledWith('date');
    });
  });

  describe('Selection', () => {
    it('should toggle individual order selection', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]); // First order checkbox (0 is select all)

      expect(mockStore.toggleOrderSelection).toHaveBeenCalledWith('1');
    });

    it('should select all orders', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      await user.click(selectAllCheckbox);

      expect(mockStore.selectAllOrders).toHaveBeenCalled();
    });

    it('should handle keyboard shortcut for select all (Ctrl+A)', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      await user.keyboard('{Control>}a{/Control}');

      expect(mockStore.selectAllOrders).toHaveBeenCalled();
    });
  });

  describe('Review Actions', () => {
    it('should approve an order', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const approveButtons = screen.getAllByLabelText(/Aprovar/i);
      await user.click(approveButtons[0]);

      // Should open confirmation dialog
      expect(screen.getByText(/Confirmar Aprovação/i)).toBeInTheDocument();

      // Confirm approval
      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockStore.reviewOrder).toHaveBeenCalledWith('1', 'APPROVED', '');
    });

    it('should deny an order with reason', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const denyButtons = screen.getAllByLabelText(/Negar/i);
      await user.click(denyButtons[0]);

      // Should open confirmation dialog
      expect(screen.getByText(/Confirmar Negação/i)).toBeInTheDocument();

      // Add reason
      const reasonInput = screen.getByLabelText(/Motivo/i);
      await user.type(reasonInput, 'Duplicate order detected');

      // Confirm denial
      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockStore.reviewOrder).toHaveBeenCalledWith('1', 'DENIED', 'Duplicate order detected');
    });

    it('should handle bulk approval', async () => {
      const user = userEvent.setup();
      (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
        ...mockStore,
        selectedOrderIds: ['1', '2'],
        getSelectedOrders: jest.fn(() => mockOrders),
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const bulkButton = screen.getByLabelText(/Ações em massa/i);
      await user.click(bulkButton);

      const approveAllButton = screen.getByText(/Aprovar selecionados/i);
      await user.click(approveAllButton);

      expect(screen.getByText(/Aprovar 2 pedidos/i)).toBeInTheDocument();

      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockStore.bulkReview).toHaveBeenCalledWith('APPROVED', '');
    });
  });

  describe('Export', () => {
    it('should export filtered data to CSV', async () => {
      const user = userEvent.setup();
      // Mock URL.createObjectURL and document.createElement
      global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
      const mockClick = jest.fn();
      const mockCreateElement = jest.spyOn(document, 'createElement');
      mockCreateElement.mockImplementation((tagName) => {
        if (tagName === 'a') {
          return { click: mockClick, setAttribute: jest.fn() } as any;
        }
        return document.createElement(tagName);
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const exportButton = screen.getByLabelText(/Exportar para CSV/i);
      await user.click(exportButton);

      expect(mockClick).toHaveBeenCalled();
      expect(global.URL.createObjectURL).toHaveBeenCalled();

      mockCreateElement.mockRestore();
    });
  });

  describe('Refresh', () => {
    it('should refresh queue on button click', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const refreshButton = screen.getByLabelText(/Atualizar/i);
      await user.click(refreshButton);

      expect(mockStore.refreshQueue).toHaveBeenCalled();
    });

    it('should handle keyboard shortcut for refresh (Ctrl+R)', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      await user.keyboard('{Control>}r{/Control}');

      expect(mockStore.refreshQueue).toHaveBeenCalled();
    });
  });

  describe('Score Visualization', () => {
    it('should display score with appropriate color', () => {
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const scoreChips = screen.getAllByText(/85%|92%/);
      expect(scoreChips[0]).toHaveClass('MuiChip-colorWarning'); // 85%
      expect(scoreChips[1]).toHaveClass('MuiChip-colorError'); // 92%
    });

    it('should show matched components', () => {
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByText(/street, number/i)).toBeInTheDocument();
      expect(screen.getByText(/street, neighborhood, city/i)).toBeInTheDocument();
    });
  });

  describe('Virtual Scrolling', () => {
    it('should render only visible items', () => {
      const manyOrders = Array.from({ length: 100 }, (_, i) => ({
        ...mockOrders[0],
        id: `order-${i}`,
        orderNumber: `ORD-${String(i + 1).padStart(3, '0')}`,
      }));

      (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
        ...mockStore,
        orders: manyOrders,
        getFilteredAndSortedOrders: jest.fn(() => manyOrders),
      });

      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      const virtualList = screen.getByTestId('virtual-list');
      const renderedItems = within(virtualList).getAllByText(/ORD-/);

      // Should only render first 10 items (as mocked)
      expect(renderedItems).toHaveLength(10);
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getAllByRole('checkbox')).toHaveLength(3); // Select all + 2 orders
      expect(screen.getByLabelText(/Buscar/i)).toBeInTheDocument();
    });

    it('should handle keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<EnhancedReviewQueue />, { wrapper: createQueryWrapper() });

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByPlaceholderText(/Buscar/i)).toHaveFocus();

      await user.tab();
      expect(screen.getAllByRole('checkbox')[0]).toHaveFocus();
    });
  });
});