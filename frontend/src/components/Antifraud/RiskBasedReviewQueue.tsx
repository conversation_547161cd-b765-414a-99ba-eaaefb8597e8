import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  Stack,
  LinearProgress,
  Grid,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as DenyIcon,
  History as HistoryIcon,
  Warning as WarningIcon,
  Shield as ShieldIcon,
  Assessment as AssessmentIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import AntifraudService, { ReviewDecision } from '../../services/AntifraudService';
import { useNotificationContext } from '../../contexts/NotificationContext';
import { useOrderData } from '../../contexts/OrderDataContext';

interface OrderReviewItem {
  id: string;
  orderNumber: string | null;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: string;
  riskScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  riskFactors: string[];
  status: string;
  isDuplicate: boolean;
  duplicateMatchScore?: number | null;
  matchedOrders?: Array<{
    id: string;
    orderNumber: string;
    createdAt: string;
  }>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`risk-tabpanel-${index}`}
      aria-labelledby={`risk-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

const getRiskLevelColor = (level: string): string => {
  switch (level) {
    case 'CRITICAL': return '#d32f2f';
    case 'HIGH': return '#f44336';
    case 'MEDIUM': return '#ff9800';
    case 'LOW': return '#4caf50';
    default: return '#757575';
  }
};

const getRiskLevelLabel = (level: string): string => {
  switch (level) {
    case 'CRITICAL': return 'Crítico';
    case 'HIGH': return 'Alto';
    case 'MEDIUM': return 'Médio';
    case 'LOW': return 'Baixo';
    default: return level;
  }
};

const RiskBasedReviewQueue: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [items, setItems] = useState<OrderReviewItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderReviewItem | null>(null);
  const [reviewDecision, setReviewDecision] = useState<ReviewDecision['decision']>('APPROVE_ORDER');
  const [reviewNotes, setReviewNotes] = useState('');
  const [riskLevelCounts, setRiskLevelCounts] = useState<Record<string, number>>({});
  const { showNotification } = useNotificationContext();
  const { fetchOrders } = useOrderData();

  const riskLevels = ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW'];
  const currentRiskLevel = currentTab === 0 ? null : riskLevels[currentTab - 1];

  useEffect(() => {
    loadOrdersForReview();
  }, [currentRiskLevel]);

  const loadOrdersForReview = async () => {
    try {
      setLoading(true);
      const response = await AntifraudService.getOrdersForReview(currentRiskLevel, 1, 50);
      setItems(response.items);
      setRiskLevelCounts(response.byRiskLevel);
    } catch (error: any) {
      showNotification('Erro ao carregar pedidos para revisão', 'error');
      console.error('Error loading orders for review:', error || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleReviewClick = (item: OrderReviewItem) => {
    setSelectedOrder(item);
    setReviewDialogOpen(true);
    setReviewDecision('APPROVE_ORDER');
    setReviewNotes('');
  };

  const handleReviewSubmit = async () => {
    if (!selectedOrder) return;

    try {
      await AntifraudService.reviewOrder(selectedOrder.id, {
        decision: reviewDecision,
        notes: reviewNotes,
      });

      showNotification('Pedido revisado com sucesso', 'success');
      setReviewDialogOpen(false);
      
      // Add a small delay to ensure backend has processed the update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh both anti-fraud queue and orders context (for sidebar)
      await Promise.all([
        loadOrdersForReview(),
        fetchOrders()
      ]);
      
      // Dispatch event to update sidebar
      window.dispatchEvent(new Event('orders-updated'));
    } catch (error: any) {
      showNotification('Erro ao revisar pedido', 'error');
      console.error('Error reviewing order:', error || 'Unknown error');
    }
  };

  const getFilteredItems = () => {
    if (currentTab === 0) return items;
    const level = riskLevels[currentTab - 1];
    return items.filter(item => item.riskLevel === level);
  };

  const filteredItems = getFilteredItems();

  return (
    <Box>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Box display="flex" alignItems="center" gap={2}>
              <ShieldIcon sx={{ fontSize: 32, color: '#2196F3' }} />
              <Box>
                <Typography variant="h5" component="h2">
                  Central Anti-Fraude
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Análise de risco e aprovação de pedidos suspeitos
                </Typography>
              </Box>
            </Box>
            <Tooltip title="Atualizar">
              <IconButton onClick={loadOrdersForReview} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Risk Overview Cards */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Card sx={{ bgcolor: '#ffebee' }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography color="error" variant="h4">
                        {riskLevelCounts.CRITICAL || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Risco Crítico
                      </Typography>
                    </Box>
                    <WarningIcon sx={{ fontSize: 40, color: '#d32f2f' }} />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card sx={{ bgcolor: '#fff3e0' }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography sx={{ color: '#f44336' }} variant="h4">
                        {riskLevelCounts.HIGH || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Risco Alto
                      </Typography>
                    </Box>
                    <WarningIcon sx={{ fontSize: 40, color: '#f44336' }} />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card sx={{ bgcolor: '#fff8e1' }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography sx={{ color: '#ff9800' }} variant="h4">
                        {riskLevelCounts.MEDIUM || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Risco Médio
                      </Typography>
                    </Box>
                    <AssessmentIcon sx={{ fontSize: 40, color: '#ff9800' }} />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card sx={{ bgcolor: '#e8f5e9' }}>
                <CardContent>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography sx={{ color: '#4caf50' }} variant="h4">
                        {riskLevelCounts.LOW || 0}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Risco Baixo
                      </Typography>
                    </Box>
                    <ApproveIcon sx={{ fontSize: 40, color: '#4caf50' }} />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Tabs for risk levels */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={currentTab} onChange={handleTabChange}>
              <Tab label="Todos" />
              <Tab 
                label={
                  <Badge badgeContent={riskLevelCounts.CRITICAL || 0} color="error">
                    Crítico
                  </Badge>
                } 
              />
              <Tab 
                label={
                  <Badge badgeContent={riskLevelCounts.HIGH || 0} color="error">
                    Alto
                  </Badge>
                } 
              />
              <Tab 
                label={
                  <Badge badgeContent={riskLevelCounts.MEDIUM || 0} color="warning">
                    Médio
                  </Badge>
                } 
              />
              <Tab 
                label={
                  <Badge badgeContent={riskLevelCounts.LOW || 0} color="success">
                    Baixo
                  </Badge>
                } 
              />
            </Tabs>
          </Box>

          {/* Table content */}
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : filteredItems.length === 0 ? (
            <Box p={3}>
              <Alert severity="success">
                Nenhum pedido pendente de revisão{currentRiskLevel ? ` para risco ${getRiskLevelLabel(currentRiskLevel)}` : ''}.
              </Alert>
            </Box>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Pedido</TableCell>
                    <TableCell>Cliente</TableCell>
                    <TableCell>Telefone</TableCell>
                    <TableCell align="right">Valor</TableCell>
                    <TableCell align="center">Score</TableCell>
                    <TableCell>Fatores de Risco</TableCell>
                    <TableCell>Data</TableCell>
                    <TableCell align="center">Ações</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredItems.map((item) => (
                    <TableRow key={item.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {item.orderNumber || 'Sem número'}
                        </Typography>
                      </TableCell>
                      <TableCell>{item.customerName}</TableCell>
                      <TableCell>{item.customerPhone}</TableCell>
                      <TableCell align="right">
                        {formatCurrency(item.total)}
                      </TableCell>
                      <TableCell align="center">
                        <Box display="flex" alignItems="center" gap={1}>
                          <LinearProgress
                            variant="determinate"
                            value={item.riskScore}
                            sx={{
                              width: 60,
                              height: 8,
                              borderRadius: 4,
                              bgcolor: 'grey.300',
                              '& .MuiLinearProgress-bar': {
                                bgcolor: getRiskLevelColor(item.riskLevel),
                              },
                            }}
                          />
                          <Typography variant="body2" fontWeight="bold">
                            {item.riskScore}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" flexDirection="column" gap={0.5}>
                          {item.riskFactors.length > 2 ? (
                            <>
                              <Chip
                                label={item.riskFactors[0]}
                                size="small"
                                variant="outlined"
                                sx={{ fontSize: '0.75rem' }}
                              />
                              <Tooltip 
                                title={
                                  <Box>
                                    {item.riskFactors.map((factor, idx) => (
                                      <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                                        • {factor}
                                      </Typography>
                                    ))}
                                    {item.isDuplicate && item.matchedOrders && item.matchedOrders.length > 0 && (
                                      <Box mt={1}>
                                        <Typography variant="body2" fontWeight="bold">Pedidos duplicados:</Typography>
                                        {item.matchedOrders.map((order, idx) => (
                                          <Typography key={idx} variant="body2">
                                            • {order.orderNumber || order.id}
                                          </Typography>
                                        ))}
                                      </Box>
                                    )}
                                  </Box>
                                }
                                arrow
                              >
                                <Chip
                                  label={`+${item.riskFactors.length - 1} mais...`}
                                  size="small"
                                  color="warning"
                                  variant="filled"
                                  sx={{ fontSize: '0.75rem', cursor: 'pointer' }}
                                />
                              </Tooltip>
                            </>
                          ) : (
                            item.riskFactors.map((factor, index) => (
                              <Chip
                                key={index}
                                label={factor}
                                size="small"
                                variant="outlined"
                                sx={{ fontSize: '0.75rem' }}
                              />
                            ))
                          )}
                          {item.isDuplicate && item.matchedOrders && item.matchedOrders.length > 0 && (
                            <Chip
                              label={`Duplicado de: ${item.matchedOrders.map(o => o.orderNumber || 'ID: ' + o.id.slice(0, 8)).join(', ')}`}
                              size="small"
                              color="error"
                              variant="filled"
                              sx={{ fontSize: '0.75rem' }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {format(new Date(item.createdAt), 'dd/MM/yyyy HH:mm', {
                          locale: ptBR,
                        })}
                      </TableCell>
                      <TableCell align="center">
                        <Box display="flex" gap={1} justifyContent="center">
                          <Tooltip title="Revisar Pedido">
                            <Button
                              variant="contained"
                              size="small"
                              onClick={() => handleReviewClick(item)}
                              sx={{
                                bgcolor: getRiskLevelColor(item.riskLevel),
                                '&:hover': {
                                  bgcolor: getRiskLevelColor(item.riskLevel),
                                  opacity: 0.9,
                                },
                              }}
                            >
                              Revisar
                            </Button>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onClose={() => setReviewDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Revisar Pedido</DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Número do Pedido
                  </Typography>
                  <Typography variant="h6">{selectedOrder.orderNumber || 'Sem número'}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Score de Risco
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="h6">{selectedOrder.riskScore}</Typography>
                    <Chip
                      label={getRiskLevelLabel(selectedOrder.riskLevel)}
                      size="small"
                      sx={{
                        bgcolor: getRiskLevelColor(selectedOrder.riskLevel),
                        color: 'white',
                      }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">
                    Cliente
                  </Typography>
                  <Typography>{selectedOrder.customerName}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Telefone
                  </Typography>
                  <Typography>{selectedOrder.customerPhone}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    Valor Total
                  </Typography>
                  <Typography>{formatCurrency(selectedOrder.total)}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Fatores de Risco Identificados
                  </Typography>
                  <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                    {selectedOrder.riskFactors.map((factor, index) => (
                      <Chip
                        key={index}
                        label={factor}
                        size="small"
                        color="error"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                  {selectedOrder.isDuplicate && selectedOrder.matchedOrders && selectedOrder.matchedOrders.length > 0 && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2" fontWeight="bold" gutterBottom>
                        Possível duplicata dos seguintes pedidos:
                      </Typography>
                      {selectedOrder.matchedOrders.map((order, idx) => (
                        <Typography key={idx} variant="body2">
                          • <strong>{order.orderNumber || `ID: ${order.id}`}</strong> - 
                          Criado em {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </Typography>
                      ))}
                    </Alert>
                  )}
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <TextField
                  select
                  fullWidth
                  label="Decisão"
                  value={reviewDecision}
                  onChange={(e) => setReviewDecision(e.target.value as ReviewDecision['decision'])}
                  margin="normal"
                >
                  <MenuItem value="APPROVE_ORDER">Aprovar Pedido</MenuItem>
                  <MenuItem value="DENY_ORDER">Cancelar Pedido</MenuItem>
                  <MenuItem value="INVESTIGATE_FURTHER">Investigar Mais</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Observações"
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  margin="normal"
                  placeholder="Adicione observações sobre a decisão..."
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialogOpen(false)}>Cancelar</Button>
          <Button
            onClick={handleReviewSubmit}
            variant="contained"
            color={reviewDecision === 'DENY_ORDER' ? 'error' : 'primary'}
            startIcon={reviewDecision === 'DENY_ORDER' ? <DenyIcon /> : <ApproveIcon />}
          >
            {reviewDecision === 'DENY_ORDER' ? 'Cancelar Pedido' : 'Aprovar Pedido'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RiskBasedReviewQueue;