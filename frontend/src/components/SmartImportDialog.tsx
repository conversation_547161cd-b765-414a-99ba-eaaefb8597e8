import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Chip,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  TextField,
  Tooltip,
} from '@mui/material';
import {
  UploadFile as UploadFileIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { Order } from '../types/Order';
import { formatDate } from '../utils/dateUtils';
import OrderService from '../services/OrderService';

interface SmartImportDialogProps {
  open: boolean;
  onClose: () => void;
  onImportSuccess: (results: ImportResults) => void;
}

interface ImportResults {
  created: number;
  updated: number;
  skipped: number;
  errors: string[];
}

interface ColumnMapping {
  sourceColumn: string;
  targetField: string;
  enabled: boolean;
}

// Available fields in our Order model (English names)
const availableFields = [
  { value: 'orderNumber', label: 'Sale ID', required: true },
  { value: 'customerName', label: 'Customer Name', required: true },
  { value: 'customerPhone', label: 'Phone', required: false },
  { value: 'customerCPF', label: 'CPF/Document', required: false },
  { value: 'total', label: 'Total Value', required: true },
  { value: 'paymentReceivedAmount', label: 'Amount Received', required: false },
  { value: 'status', label: 'Status', required: false },
  { value: 'trackingCode', label: 'Tracking Code', required: false },
  { value: 'fullAddress', label: 'Address', required: false },
  { value: 'observation', label: 'Observation/Notes', required: false },
  { value: 'createdAt', label: 'Sale Date', required: false },
  { value: 'paymentReceivedDate', label: 'Payment Date', required: false },
  { value: 'nextPaymentDate', label: 'Next Payment Date', required: false },
  { value: 'seller.name', label: 'Seller Name', required: false },
  { value: 'collector.name', label: 'Collector Name', required: false },
];

const SmartImportDialog: React.FC<SmartImportDialogProps> = ({
  open,
  onClose,
  onImportSuccess,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // File data
  const [fileData, setFileData] = useState<{
    headers: string[];
    rows: string[][];
    fileName: string;
  } | null>(null);
  
  // Column mappings
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);
  const [identifierField, setIdentifierField] = useState<string>('orderNumber');
  const [updateExisting, setUpdateExisting] = useState(true);
  
  // Preview data
  const [previewData, setPreviewData] = useState<{
    toCreate: any[];
    toUpdate: any[];
    errors: string[];
  } | null>(null);

  const steps = ['Upload File', 'Map Columns', 'Preview & Import'];

  const resetState = () => {
    setActiveStep(0);
    setFileData(null);
    setMappings([]);
    setPreviewData(null);
    setError(null);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError(null);
    setLoading(true);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const lines = text.split('\n').filter(line => line.trim());
        
        if (lines.length < 2) {
          throw new Error('File must contain at least a header row and one data row');
        }

        // Parse CSV/TSV
        const delimiter = text.includes('\t') ? '\t' : ',';
        const parseRow = (row: string) => {
          const result: string[] = [];
          let current = '';
          let inQuotes = false;
          
          for (let i = 0; i < row.length; i++) {
            const char = row[i];
            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === delimiter && !inQuotes) {
              result.push(current.trim());
              current = '';
            } else {
              current += char;
            }
          }
          result.push(current.trim());
          return result;
        };

        const headers = parseRow(lines[0]);
        const rows = lines.slice(1).map(line => parseRow(line));

        setFileData({
          headers,
          rows,
          fileName: file.name,
        });

        // Initialize mappings with smart detection
        const initialMappings: ColumnMapping[] = headers.map(header => {
          const mapping: ColumnMapping = {
            sourceColumn: header,
            targetField: '',
            enabled: true,
          };

          // Smart field detection based on column names
          const headerLower = header.toLowerCase();
          
          if (headerLower.includes('id') && headerLower.includes('venda')) {
            mapping.targetField = 'orderNumber';
          } else if (headerLower.includes('cliente') || headerLower.includes('customer')) {
            mapping.targetField = 'customerName';
          } else if (headerLower.includes('telefone') || headerLower.includes('phone')) {
            mapping.targetField = 'customerPhone';
          } else if (headerLower.includes('cpf') || headerLower.includes('documento')) {
            mapping.targetField = 'customerCPF';
          } else if (headerLower.includes('valor') && headerLower.includes('venda')) {
            mapping.targetField = 'total';
          } else if (headerLower.includes('valor') && headerLower.includes('recebid')) {
            mapping.targetField = 'paymentReceivedAmount';
          } else if (headerLower.includes('status') || headerLower.includes('situacao')) {
            mapping.targetField = 'status';
          } else if (headerLower.includes('rastreio') || headerLower.includes('tracking')) {
            mapping.targetField = 'trackingCode';
          } else if (headerLower.includes('endereco') || headerLower.includes('address')) {
            mapping.targetField = 'fullAddress';
          } else if (headerLower.includes('vendedor') || headerLower.includes('seller')) {
            mapping.targetField = 'seller.name';
          }

          return mapping;
        });

        setMappings(initialMappings);
        setActiveStep(1);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error reading file');
      } finally {
        setLoading(false);
      }
    };

    reader.readAsText(file);
  };

  const handleMappingChange = (index: number, field: string, value: any) => {
    const newMappings = [...mappings];
    (newMappings[index] as any)[field] = value;
    setMappings(newMappings);
  };

  const handleToggleColumn = (index: number) => {
    const newMappings = [...mappings];
    if (newMappings[index].enabled) {
      // Disable the column
      newMappings[index].enabled = false;
      newMappings[index].targetField = '';
    } else {
      // Re-enable the column
      newMappings[index].enabled = true;
      // Try to auto-detect the field again
      const headerLower = newMappings[index].sourceColumn.toLowerCase();
      if (headerLower.includes('id') && headerLower.includes('venda')) {
        newMappings[index].targetField = 'orderNumber';
      } else if (headerLower.includes('cliente') || headerLower.includes('customer')) {
        newMappings[index].targetField = 'customerName';
      } else if (headerLower.includes('telefone') || headerLower.includes('phone')) {
        newMappings[index].targetField = 'customerPhone';
      } else if (headerLower.includes('cpf') || headerLower.includes('documento')) {
        newMappings[index].targetField = 'customerCPF';
      } else if (headerLower.includes('valor') && headerLower.includes('venda')) {
        newMappings[index].targetField = 'total';
      } else if (headerLower.includes('valor') && headerLower.includes('recebid')) {
        newMappings[index].targetField = 'paymentReceivedAmount';
      } else if (headerLower.includes('status') || headerLower.includes('situacao')) {
        newMappings[index].targetField = 'status';
      } else if (headerLower.includes('rastreio') || headerLower.includes('tracking')) {
        newMappings[index].targetField = 'trackingCode';
      } else if (headerLower.includes('endereco') || headerLower.includes('address')) {
        newMappings[index].targetField = 'fullAddress';
      } else if (headerLower.includes('vendedor') || headerLower.includes('seller')) {
        newMappings[index].targetField = 'seller.name';
      }
    }
    setMappings(newMappings);
  };

  const validateAndPreview = async () => {
    if (!fileData) return;

    setLoading(true);
    setError(null);

    try {
      const toCreate: any[] = [];
      const toUpdate: any[] = [];
      const errors: string[] = [];
      const existingOrders: Record<string, Order> = {};

      // Get identifier mapping
      const idMapping = mappings.find(m => m.targetField === identifierField);
      if (!idMapping) {
        throw new Error('Identifier field must be mapped');
      }

      // Fetch existing orders if updating
      if (updateExisting) {
        const idColumnIndex = fileData.headers.indexOf(idMapping.sourceColumn);
        const orderIds = fileData.rows
          .map(row => row[idColumnIndex])
          .filter(id => id);

        // Batch fetch existing orders
        for (const orderId of orderIds) {
          try {
            const orders = await OrderService.getOrders({ search: orderId });
            const order = orders.find(o => o.orderNumber === orderId);
            if (order) {
              existingOrders[orderId] = order;
            }
          } catch (err) {
            // Order doesn't exist
          }
        }
      }

      // Process each row
      fileData.rows.forEach((row, rowIndex) => {
        try {
          const record: any = {};
          let identifier = '';

          // Map columns to fields
          mappings.forEach(mapping => {
            if (!mapping.enabled || !mapping.targetField) return;

            const columnIndex = fileData.headers.indexOf(mapping.sourceColumn);
            const value = row[columnIndex];

            if (mapping.targetField === identifierField) {
              identifier = value;
            }

            // Handle nested fields (e.g., seller.name)
            if (mapping.targetField.includes('.')) {
              const [parent, child] = mapping.targetField.split('.');
              if (!record[parent]) record[parent] = {};
              record[parent][child] = value;
            } else {
              // Parse numeric values
              if (['total', 'paymentReceivedAmount'].includes(mapping.targetField) && value) {
                record[mapping.targetField] = parseFloat(
                  value.replace('R$', '').replace(/\./g, '').replace(',', '.')
                );
              } else {
                record[mapping.targetField] = value;
              }
            }
          });

          // Check if record exists
          const existingOrder = existingOrders[identifier];
          
          if (existingOrder && updateExisting) {
            // Only include mapped fields for update
            const updateData: any = { id: existingOrder.id };
            Object.keys(record).forEach(key => {
              if (record[key] !== undefined && record[key] !== '') {
                updateData[key] = record[key];
              }
            });
            toUpdate.push(updateData);
          } else if (!existingOrder) {
            // Validate required fields for new records
            const requiredFields = availableFields
              .filter(f => f.required)
              .map(f => f.value);
            
            const missingFields = requiredFields.filter(field => !record[field]);
            
            if (missingFields.length > 0) {
              errors.push(`Row ${rowIndex + 2}: Missing required fields: ${missingFields.join(', ')}`);
            } else {
              toCreate.push(record);
            }
          }
        } catch (err) {
          errors.push(`Row ${rowIndex + 2}: ${err instanceof Error ? err.message : 'Processing error'}`);
        }
      });

      setPreviewData({ toCreate, toUpdate, errors });
      setActiveStep(2);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error processing data');
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (!previewData) return;

    setLoading(true);
    setError(null);

    try {
      const results: ImportResults = {
        created: 0,
        updated: 0,
        skipped: 0,
        errors: [],
      };

      // Create new orders using the import endpoint which has less strict validation
      for (const order of previewData.toCreate) {
        try {
          // Use importOrder instead of createOrder to bypass strict phone validation
          await OrderService.importOrder(order);
          results.created++;
        } catch (err: any) {
          console.error('Error importing order:', err);
          // Extract error message from various error formats
          let errorMessage = 'Unknown error';
          if (err.response?.data?.message) {
            errorMessage = err.response.data.message;
          } else if (err.response?.data?.error) {
            errorMessage = err.response.data.error;
          } else if (err.message) {
            errorMessage = err.message;
          }
          results.errors.push(`Failed to import order: ${errorMessage}`);
          results.skipped++;
        }
      }

      // Update existing orders
      for (const order of previewData.toUpdate) {
        try {
          await OrderService.updateOrder(order.id, order);
          results.updated++;
        } catch (err) {
          results.errors.push(`Failed to update order ${order.id}: ${err instanceof Error ? err.message : 'Unknown error'}`);
          results.skipped++;
        }
      }

      onImportSuccess(results);
      onClose();
      resetState();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        Smart Import
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Step 1: File Upload */}
        {activeStep === 0 && (
          <Box>
            <input
              type="file"
              accept=".csv,.xlsx,.xls,.tsv"
              ref={fileInputRef}
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
            
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 4,
                textAlign: 'center',
                cursor: 'pointer',
                '&:hover': { borderColor: 'primary.main' },
              }}
              onClick={() => fileInputRef.current?.click()}
            >
              <UploadFileIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Click to upload file
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Supports CSV, Excel (XLSX/XLS), and TSV files
              </Typography>
            </Box>

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Your file should contain headers in the first row. The system will help you map columns to the correct fields.
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Step 2: Column Mapping */}
        {activeStep === 1 && fileData && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Map Your Columns
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <FormControl sx={{ minWidth: 200, mr: 2 }}>
                <InputLabel>Identifier Field</InputLabel>
                <Select
                  value={identifierField}
                  onChange={(e) => setIdentifierField(e.target.value)}
                  label="Identifier Field"
                >
                  {availableFields.map(field => (
                    <MenuItem key={field.value} value={field.value}>
                      {field.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <FormControlLabel
                control={
                  <Checkbox
                    checked={updateExisting}
                    onChange={(e) => setUpdateExisting(e.target.checked)}
                  />
                }
                label="Update existing records"
              />
            </Box>

            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Source Column</TableCell>
                    <TableCell>Sample Data</TableCell>
                    <TableCell>Map To</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mappings.map((mapping, index) => (
                    <TableRow
                      key={index}
                      sx={{ 
                        opacity: mapping.enabled ? 1 : 0.4,
                        backgroundColor: mapping.enabled ? 'transparent' : 'action.hover'
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {mapping.sourceColumn}
                          {!mapping.enabled && (
                            <Chip 
                              label="Skipped" 
                              size="small" 
                              color="warning"
                              sx={{ height: 20, fontSize: '0.75rem' }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {fileData.rows[0]?.[index] || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <FormControl fullWidth size="small" disabled={!mapping.enabled}>
                          <Select
                            value={mapping.targetField}
                            onChange={(e) => handleMappingChange(index, 'targetField', e.target.value)}
                            displayEmpty
                          >
                            <MenuItem value="">
                              <em>Skip this column</em>
                            </MenuItem>
                            {availableFields.map(field => (
                              <MenuItem key={field.value} value={field.value}>
                                {field.label}
                                {field.required && ' *'}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </TableCell>
                      <TableCell>
                        <Tooltip title={mapping.enabled ? "Skip this column" : "Re-enable this column"}>
                          <IconButton
                            onClick={() => handleToggleColumn(index)}
                            size="small"
                            color={mapping.enabled ? "default" : "success"}
                          >
                            {mapping.enabled ? <DeleteIcon /> : <CheckCircleIcon />}
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Step 3: Preview & Import */}
        {activeStep === 2 && previewData && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Import Preview
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Chip
                icon={<CheckCircleIcon />}
                label={`${previewData.toCreate.length} New Records`}
                color="success"
                variant="outlined"
              />
              <Chip
                icon={<InfoIcon />}
                label={`${previewData.toUpdate.length} Updates`}
                color="info"
                variant="outlined"
              />
              {previewData.errors.length > 0 && (
                <Chip
                  icon={<WarningIcon />}
                  label={`${previewData.errors.length} Errors`}
                  color="error"
                  variant="outlined"
                />
              )}
            </Box>

            {previewData.errors.length > 0 && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Found {previewData.errors.length} errors:
                </Typography>
                <Box sx={{ maxHeight: 100, overflow: 'auto' }}>
                  {previewData.errors.map((error, index) => (
                    <Typography key={index} variant="body2">
                      • {error}
                    </Typography>
                  ))}
                </Box>
              </Alert>
            )}

            {/* Preview tables */}
            {previewData.toCreate.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  New Records (showing first 5)
                </Typography>
                <TableContainer component={Paper} sx={{ maxHeight: 200 }}>
                  <Table size="small" stickyHeader>
                    <TableHead>
                      <TableRow>
                        {Object.keys(previewData.toCreate[0]).map(key => (
                          <TableCell key={key}>{key}</TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {previewData.toCreate.slice(0, 5).map((record, index) => (
                        <TableRow key={index}>
                          {Object.values(record).map((value: any, i) => (
                            <TableCell key={i}>
                              {typeof value === 'object' ? JSON.stringify(value) : value}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {previewData.toUpdate.length > 0 && (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Updates (showing first 5)
                </Typography>
                <TableContainer component={Paper} sx={{ maxHeight: 200 }}>
                  <Table size="small" stickyHeader>
                    <TableHead>
                      <TableRow>
                        {Object.keys(previewData.toUpdate[0]).map(key => (
                          <TableCell key={key}>{key}</TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {previewData.toUpdate.slice(0, 5).map((record, index) => (
                        <TableRow key={index}>
                          {Object.values(record).map((value: any, i) => (
                            <TableCell key={i}>
                              {typeof value === 'object' ? JSON.stringify(value) : value}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </Box>
        )}

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        
        {activeStep === 1 && (
          <Button
            onClick={validateAndPreview}
            variant="contained"
            disabled={loading || mappings.filter(m => m.enabled && m.targetField).length === 0}
          >
            Preview Import
          </Button>
        )}
        
        {activeStep === 2 && (
          <Button
            onClick={handleImport}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            Import {previewData?.toCreate.length || 0 + previewData?.toUpdate.length || 0} Records
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default SmartImportDialog;