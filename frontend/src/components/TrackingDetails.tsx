import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Paper,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/lab';
import {
  LocalShipping as LocalShippingIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import api from '../services/api';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TrackingEvent {
  descricao: string;
  descricaoFrontEnd?: string;
  internalStatus?: string;
  dtHrCriado: string;
  tipo?: string;
  local?: string;
  cidade?: string;
  uf?: string;
}

interface TrackingDetailsProps {
  orderId: string;
  trackingCode: string;
  statusCorreios?: string;
  ultimaAtualizacaoCorreios?: string;
}

const TrackingDetails: React.FC<TrackingDetailsProps> = ({ orderId, trackingCode, statusCorreios, ultimaAtualizacaoCorreios }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [trackingData, setTrackingData] = useState<any>(null);
  const [expanded, setExpanded] = useState(true);

  const fetchTrackingDetails = async () => {
    // Shipment API has been removed - tracking is now handled through webhooks
    setLoading(false);
    setTrackingData(null);
  };

  useEffect(() => {
    // Disabled - tracking updates come from webhooks only
    setLoading(false);
  }, [orderId, trackingCode]);

  const getStatusIcon = (status: string | undefined | null) => {
    if (!status) return <InfoIcon />;
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('entregue') || lowerStatus.includes('entrega')) {
      return <CheckCircleIcon />;
    }
    if (lowerStatus.includes('erro') || lowerStatus.includes('falha') || lowerStatus.includes('devolvido')) {
      return <ErrorIcon />;
    }
    if (lowerStatus.includes('tentativa') || lowerStatus.includes('aguardando')) {
      return <WarningIcon />;
    }
    return <LocalShippingIcon />;
  };

  const getStatusColor = (status: string | undefined | null): 'success' | 'error' | 'warning' | 'info' | 'default' => {
    if (!status) return 'default';
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes('entregue') || lowerStatus.includes('confirmar entrega')) {
      return 'success';
    }
    if (lowerStatus.includes('erro') || lowerStatus.includes('falha') || lowerStatus.includes('devolvido')) {
      return 'error';
    }
    if (lowerStatus.includes('tentativa') || lowerStatus.includes('aguardando')) {
      return 'warning';
    }
    if (lowerStatus.includes('trânsito') || lowerStatus.includes('postado')) {
      return 'info';
    }
    return 'default';
  };

  const formatEventDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!trackingData && !statusCorreios) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        Nenhuma informação de rastreamento disponível
      </Alert>
    );
  }
  
  // Show status from webhook if no tracking data
  if (!trackingData && statusCorreios) {
    return (
      <Paper elevation={0} sx={{ p: 2, backgroundColor: 'grey.50' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="subtitle2">
            Informações de Rastreamento
          </Typography>
          {trackingCode && (
            <Chip label={trackingCode} size="small" />
          )}
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
          <Chip
            label={statusCorreios}
            sx={{
              backgroundColor: (() => {
                const color = getStatusColor(statusCorreios);
                switch(color) {
                  case 'success': return '#4caf50';
                  case 'error': return '#f44336';
                  case 'warning': return '#ff9800';
                  case 'info': return '#2196f3';
                  default: return '#9e9e9e';
                }
              })(),
              color: 'white'
            }}
            icon={getStatusIcon(statusCorreios)}
          />
        </Box>
        
        {ultimaAtualizacaoCorreios && (
          <Typography variant="caption" color="text.secondary">
            Última atualização: {ultimaAtualizacaoCorreios}
          </Typography>
        )}
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 2, mt: 2, bgcolor: 'background.default' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="subtitle1" fontWeight={600}>
          Status Detalhado do Rastreamento
        </Typography>
        <IconButton size="small" onClick={() => setExpanded(!expanded)}>
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <Chip
          label={trackingData.lastStatus || 'Sem status'}
          sx={{ 
            fontWeight: 600,
            bgcolor: (() => {
              const color = getStatusColor(trackingData.lastStatus);
              switch(color) {
                case 'success': return '#4caf50';
                case 'error': return '#f44336';
                case 'warning': return '#ff9800';
                case 'info': return '#2196f3';
                default: return '#9e9e9e';
              }
            })(),
            color: 'white'
          }}
          icon={getStatusIcon(trackingData.lastStatus)}
        />
        {trackingData.isDelivered && (
          <Chip label="Entregue" color="success" size="small" />
        )}
        {trackingData.hasAlert && (
          <Chip label="Atenção Necessária" color="error" size="small" />
        )}
      </Box>

      {trackingData.alertReason && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          {trackingData.alertReason}
        </Alert>
      )}

      <Collapse in={expanded}>
        {trackingData.events && trackingData.events.length > 0 ? (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Histórico de Movimentação
            </Typography>
            <Timeline sx={{ 
              '& .MuiTimelineItem-root:before': { flex: 0, padding: 0 },
              '& .MuiTimelineOppositeContent-root': { flex: 0.3 }
            }}>
              {trackingData.events.map((event: TrackingEvent, index: number) => (
                <TimelineItem key={index}>
                  <TimelineOppositeContent color="text.secondary" variant="caption">
                    {formatEventDate(event.dtHrCriado)}
                  </TimelineOppositeContent>
                  <TimelineSeparator>
                    <TimelineDot
                      sx={{
                        bgcolor: (() => {
                          const color = getStatusColor(event.descricao || event.tipo);
                          switch(color) {
                            case 'success': return '#4caf50';
                            case 'error': return '#f44336';
                            case 'warning': return '#ff9800';
                            case 'info': return '#2196f3';
                            default: return '#9e9e9e';
                          }
                        })(),
                        color: 'white'
                      }}
                    >
                      {getStatusIcon(event.descricao || event.tipo)}
                    </TimelineDot>
                    {index < trackingData.events.length - 1 && <TimelineConnector />}
                  </TimelineSeparator>
                  <TimelineContent>
                    <Typography variant="body2" fontWeight={500}>
                      {event.descricaoFrontEnd || event.descricao || event.tipo || 'Sem descrição'}
                    </Typography>
                    {event.internalStatus && event.internalStatus !== event.descricao && (
                      <Typography variant="caption" color="primary">
                        Status: {event.internalStatus}
                      </Typography>
                    )}
                    {(event.local || event.cidade) && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {[event.local, event.cidade, event.uf].filter(Boolean).join(' - ')}
                      </Typography>
                    )}
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Aguardando primeira atualização dos Correios
          </Typography>
        )}
      </Collapse>

      {trackingData.lastSync && (
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 2 }}>
          Última sincronização: {formatEventDate(trackingData.lastSync)}
        </Typography>
      )}
    </Paper>
  );
};

export default TrackingDetails;