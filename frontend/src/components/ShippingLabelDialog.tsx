import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  InputAdornment,
  Typography,
  Box,
} from '@mui/material';
import { LocalShipping, Print } from '@mui/icons-material';
import { Order } from '../types/Order';
import { shippingService, ShippingLabelRequest } from '../services/ShippingService';
import { useSnackbar } from 'notistack';

interface ShippingLabelDialogProps {
  open: boolean;
  onClose: () => void;
  order: Order;
  onSuccess?: () => void;
}

export const ShippingLabelDialog: React.FC<ShippingLabelDialogProps> = ({
  open,
  onClose,
  order,
  onSuccess,
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ShippingLabelRequest>({
    serviceCode: '04014', // Default to SEDEX
    weight: 0.5,
    dimensions: {
      height: 10,
      width: 15,
      length: 20,
    },
    declaredValue: Number(order.total) || 0,
  });

  const serviceTypes = shippingService.getServiceTypes();

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev as any)[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validate form
      if (formData.weight <= 0) {
        enqueueSnackbar('Peso deve ser maior que zero', { variant: 'error' });
        return;
      }

      if (formData.dimensions.height <= 0 || formData.dimensions.width <= 0 || formData.dimensions.length <= 0) {
        enqueueSnackbar('Dimensões devem ser maiores que zero', { variant: 'error' });
        return;
      }

      const result = await shippingService.generateLabel(order.id, formData);
      
      enqueueSnackbar(`Etiqueta gerada com sucesso! Código: ${result.trackingCode}`, { 
        variant: 'success',
        autoHideDuration: 5000,
      });

      onSuccess?.();
      onClose();
    } catch (error: any) {
      enqueueSnackbar(error.message || 'Erro ao gerar etiqueta', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <LocalShipping />
          Gerar Etiqueta de Envio
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Pedido: <strong>{order.orderNumber}</strong> - Cliente: <strong>{order.customerName}</strong>
          </Alert>

          <Grid container spacing={2}>
            {/* Service Type */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Tipo de Serviço</InputLabel>
                <Select
                  value={formData.serviceCode}
                  onChange={(e) => handleInputChange('serviceCode', e.target.value)}
                  label="Tipo de Serviço"
                >
                  {serviceTypes.map(service => (
                    <MenuItem key={service.code} value={service.code}>
                      {service.name} - {service.estimatedDays === 0 ? 'Mesmo dia' : `${service.estimatedDays} dia(s) úteis`}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Weight */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Peso"
                type="number"
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                InputProps={{
                  endAdornment: <InputAdornment position="end">kg</InputAdornment>,
                  inputProps: { min: 0.001, step: 0.1 }
                }}
              />
            </Grid>

            {/* Declared Value */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Valor Declarado"
                type="number"
                value={formData.declaredValue}
                onChange={(e) => handleInputChange('declaredValue', parseFloat(e.target.value) || 0)}
                InputProps={{
                  startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                  inputProps: { min: 0, step: 0.01 }
                }}
              />
            </Grid>

            {/* Dimensions */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Dimensões da Embalagem
              </Typography>
            </Grid>

            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Altura"
                type="number"
                value={formData.dimensions.height}
                onChange={(e) => handleInputChange('dimensions.height', parseFloat(e.target.value) || 0)}
                InputProps={{
                  endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                  inputProps: { min: 1, step: 1 }
                }}
              />
            </Grid>

            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Largura"
                type="number"
                value={formData.dimensions.width}
                onChange={(e) => handleInputChange('dimensions.width', parseFloat(e.target.value) || 0)}
                InputProps={{
                  endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                  inputProps: { min: 1, step: 1 }
                }}
              />
            </Grid>

            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Comprimento"
                type="number"
                value={formData.dimensions.length}
                onChange={(e) => handleInputChange('dimensions.length', parseFloat(e.target.value) || 0)}
                InputProps={{
                  endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                  inputProps: { min: 1, step: 1 }
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <Print />}
        >
          {loading ? 'Gerando...' : 'Gerar Etiqueta'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};