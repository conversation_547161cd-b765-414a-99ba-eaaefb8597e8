import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { Kit } from '../types/Product';

interface KitEditDialogProps {
  kit: Kit;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (updatedKit: Partial<Kit>) => void;
}

export function KitEditDialog({
  kit,
  open,
  onOpenChange,
  onSave,
}: KitEditDialogProps) {
  const [name, setName] = useState(kit.name);
  const [active, setActive] = useState(kit.active);

  const handleSave = () => {
    onSave({
      ...kit,
      name: name.trim(),
      active,
    });
    onOpenChange(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset form when closing
      setName(kit.name);
      setActive(kit.active);
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onClose={() => handleOpenChange(false)} maxWidth="sm" fullWidth>
      <DialogTitle>Editar Kit</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Atualize o nome e o status do kit.
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Nome"
            value={name}
            onChange={(e) => setName(e.target.value)}
            fullWidth
            placeholder="Digite o nome do kit"
            variant="outlined"
          />
          <FormControlLabel
            control={
              <Switch
                checked={active}
                onChange={(e) => setActive(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Box>
                <Typography variant="body1">Ativo</Typography>
                <Typography variant="caption" color="text.secondary">
                  {active ? 'Kit está ativo' : 'Kit está inativo'}
                </Typography>
              </Box>
            }
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => handleOpenChange(false)} color="inherit">
          Cancelar
        </Button>
        <Button onClick={handleSave} disabled={!name.trim()} variant="contained" color="primary">
          Salvar alterações
        </Button>
      </DialogActions>
    </Dialog>
  );
}