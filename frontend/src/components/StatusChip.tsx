import React from 'react';
import { Chip } from '@mui/material';
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';
import PendingOutlinedIcon from '@mui/icons-material/PendingOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import HandshakeOutlinedIcon from '@mui/icons-material/HandshakeOutlined';
import ThumbUpAltOutlinedIcon from '@mui/icons-material/ThumbUpAltOutlined';
import HourglassEmptyOutlinedIcon from '@mui/icons-material/HourglassEmptyOutlined';
import ReplayOutlinedIcon from '@mui/icons-material/ReplayOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import KeyboardReturnOutlinedIcon from '@mui/icons-material/KeyboardReturnOutlined';
import ContentCopyOutlinedIcon from '@mui/icons-material/ContentCopyOutlined';
import { getStatusColor } from '../constants/statusColors';

interface StatusChipProps {
  status: string;
}

const StatusChip: React.FC<StatusChipProps> = ({ status }) => {
  const getStatusConfig = (statusText: string) => {
    const lowerStatus = statusText.toLowerCase();
    const color = getStatusColor(statusText);
    
    // Define icon based on status
    let icon = <AccessTimeOutlinedIcon fontSize="small" />;
    
    if (lowerStatus.includes('completo')) {
      icon = <CheckCircleOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('pendente') || lowerStatus.includes('pagamento pendente')) {
      icon = <PendingOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('cancelado')) {
      icon = <CancelOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('falha') || lowerStatus.includes('frustrado')) {
      icon = <ErrorOutlineOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('separação') || lowerStatus.includes('separacao')) {
      icon = <HourglassEmptyOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('retirar correios')) {
      icon = <LocalShippingOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('entrega falha')) {
      icon = <WarningAmberOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('devolvido correios')) {
      icon = <KeyboardReturnOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('negociação') || lowerStatus.includes('negociacao')) {
      icon = <HandshakeOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('confirmar entrega')) {
      icon = <ThumbUpAltOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('recuperação') || lowerStatus.includes('recuperacao')) {
      icon = <ReplayOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('possíveis duplicados') || lowerStatus.includes('possiveis duplicados')) {
      icon = <ContentCopyOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('liberação') || lowerStatus.includes('liberacao')) {
      icon = <AccessTimeOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('promessa')) {
      icon = <HandshakeOutlinedIcon fontSize="small" />;
    } else if (lowerStatus.includes('confirmar pagamento')) {
      icon = <WarningAmberOutlinedIcon fontSize="small" />;
    }
    
    return { color, icon };
  };

  const config = getStatusConfig(status);
  
  return (
    <Chip
      icon={config.icon}
      label={status}
      size="small"
      variant="filled"
      sx={{
        backgroundColor: config.color,
        color: 'white',
        maxWidth: '100%',
        borderRadius: 1,
        fontWeight: 600,
        fontSize: '0.75rem',
        height: '24px',
        '& .MuiChip-label': {
          px: 0.75,
          py: 0,
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '108px',
          color: 'white',
          fontSize: '0.75rem',
        },
        '& .MuiChip-icon': {
          color: 'white',
          fontSize: '1rem',
          marginLeft: '6px',
          marginRight: '-2px',
        },
      }}
    />
  );
};

export default StatusChip; 