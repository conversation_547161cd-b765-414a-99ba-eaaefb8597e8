import { GlobalStyles } from '@mui/material';

const globalStyles = (
  <GlobalStyles
    styles={{
      '*': {
        boxSizing: 'border-box',
      },
      'html, body': {
        margin: 0,
        padding: 0,
        width: '100%',
        height: '100%',
        overflowX: 'hidden',
      },
      '#root': {
        width: '100%',
        height: '100%',
        overflowX: 'hidden',
      },
      '::-webkit-scrollbar': {
        width: '8px',
        height: '8px',
      },
      '::-webkit-scrollbar-track': {
        background: '#f1f1f1',
      },
      '::-webkit-scrollbar-thumb': {
        background: '#888',
        borderRadius: '4px',
      },
      '::-webkit-scrollbar-thumb:hover': {
        background: '#555',
      },
      // Prevent horizontal scroll on tables
      'table': {
        maxWidth: '100%',
        tableLayout: 'fixed',
      },
      // Ensure all containers respect boundaries
      '.MuiContainer-root': {
        maxWidth: '100%',
      },
      // Fix any potential overflow from Material-UI components
      '.MuiDrawer-root': {
        overflowX: 'hidden',
      },
      '.MuiPaper-root': {
        maxWidth: '100%',
      },
    }}
  />
);

export default globalStyles;