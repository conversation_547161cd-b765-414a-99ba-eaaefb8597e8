import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useOrderData } from '../contexts/OrderDataContext';

// Define operator-only statuses
const OPERATOR_STATUSES = [
  'Completo',
  'Negociação',
  'Promessa',
  'Pagamento Pendente',
  'Frustrado'
];

// Define all possible statuses (operator statuses first)
const ALL_STATUSES = [
  // Operator statuses (always at top)
  'Completo',
  'Negociação',
  'Promessa',
  'Pagamento Pendente',
  'Frustrado',
  // Other statuses
  'Pendente',
  'Cancelado',
  'Separação',
  'Recuperação',
  'Devolvido Correios',
  'Liberação',
  'Possíveis Duplicados',
  'Em Trânsito',
  'Entregue',
  'Parcial',
  'Retirar Correios',
  'Entrega Falha',
  'Confirmar Entrega',
  'Análise',
  'Trânsito',
  'Deletado'
];

interface PedidoProps {
  pedido: {
    id: string;
    status: string;
    name?: string;
    details?: string;
  };
}

const Pedido: React.FC<PedidoProps> = ({ pedido }) => {
  const { userInfo } = useAuth(); // Get user role
  const { updateOrderStatus } = useOrderData(); // Get update function from context

  // Determine available statuses based on user role
  const role = userInfo?.role;
  const availableStatuses = role === 'admin' ? ALL_STATUSES : OPERATOR_STATUSES;

  // Handle status change
  async function handleChange(e: React.ChangeEvent<HTMLSelectElement>) {
    const newStatus = e.target.value;
    try {
      await updateOrderStatus(pedido.id, newStatus);
    } catch (error: any) {
      console.error('Status update failed:', error);
      // Optionally show a user-facing error toast here
    }
  }

  return (
    <div className="pedido">
      <h3>{pedido.name || `Pedido ${pedido.id}`}</h3>
      {pedido.details && <p>{pedido.details}</p>}
      
      <div className="status-section">
        <label htmlFor={`status-${pedido.id}`}>Status:</label>
        <select 
          id={`status-${pedido.id}`}
          value={pedido.status} 
          onChange={handleChange}
        >
          {availableStatuses.map(status => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default Pedido;