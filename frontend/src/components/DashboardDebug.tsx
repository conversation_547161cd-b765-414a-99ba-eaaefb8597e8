import React from 'react';
import { Box, Paper, Typography, Button } from '@mui/material';
import { useOrderData } from '../contexts/OrderDataContext';

export const DashboardDebug: React.FC = () => {
  const { orders, loading, error, fetchOrders } = useOrderData();
  
  const handleManualFetch = () => {
    console.log('Manual fetch triggered');
    fetchOrders();
  };
  
  return (
    <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.100' }}>
      <Typography variant="h6" gutterBottom>Dashboard Debug Info</Typography>
      
      <Box sx={{ display: 'grid', gap: 1 }}>
        <Typography variant="body2">
          <strong>Orders from Context:</strong> {orders.length}
        </Typography>
        
        <Typography variant="body2">
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </Typography>
        
        <Typography variant="body2">
          <strong>Error:</strong> {error || 'None'}
        </Typography>
        
        {orders.length > 0 && (
          <>
            <Typography variant="body2">
              <strong>First Order:</strong>
            </Typography>
            <Box sx={{ pl: 2, fontSize: '0.75rem' }}>
              <Typography variant="caption" component="div">
                ID: {orders[0].id}
              </Typography>
              <Typography variant="caption" component="div">
                Number: {orders[0].orderNumber}
              </Typography>
              <Typography variant="caption" component="div">
                Status: {orders[0].status}
              </Typography>
              <Typography variant="caption" component="div">
                Customer: {orders[0].customerName}
              </Typography>
            </Box>
          </>
        )}
        
        <Button 
          variant="contained" 
          size="small"
          onClick={handleManualFetch}
          disabled={loading}
        >
          {loading ? 'Loading...' : 'Manual Fetch Orders'}
        </Button>
        
        <Typography variant="caption" sx={{ mt: 1 }}>
          Check console for detailed logs
        </Typography>
      </Box>
    </Paper>
  );
};