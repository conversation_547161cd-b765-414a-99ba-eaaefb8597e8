import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Box,
  Typography,
  Divider,
  IconButton,
  Grid,
  Alert,
  Chip,
  Stack,
  InputAdornment,
  Tabs,
  Tab,
  Card,
  CardContent,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ContentCopy as ContentCopyIcon,
  LocalOffer as LocalOfferIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Speed as SpeedIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { Product } from '../types/Product';

interface OfferForm {
  name: string;
  price: number;
  gelQuantity: number;
  capsulesQuantity: number;
  isKit: boolean;
  active: boolean;
}

interface ProductForm {
  name: string;
  description: string;
  active: boolean;
}

interface ProductTemplate {
  id: string;
  name: string;
  description: string;
  offers: Omit<OfferForm, 'active'>[];
}

interface ProductCreationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (product: ProductForm, offers: OfferForm[]) => Promise<void>;
  editingProduct?: Product | null;
}

const productTemplates: ProductTemplate[] = [
  {
    id: 'gel-standard',
    name: 'Tratamento Padrão Gel',
    description: 'Ofertas de 30, 60 e 90 dias apenas com gel',
    offers: [
      { name: 'Tratamento 30 dias', price: 197, gelQuantity: 1, capsulesQuantity: 0, isKit: false },
      { name: 'Tratamento 60 dias', price: 297, gelQuantity: 2, capsulesQuantity: 0, isKit: false },
      { name: 'Tratamento 90 dias', price: 397, gelQuantity: 3, capsulesQuantity: 0, isKit: false },
    ],
  },
  {
    id: 'capsules-standard',
    name: 'Tratamento Padrão Cápsulas',
    description: 'Ofertas de 30, 60 e 90 dias apenas com cápsulas',
    offers: [
      { name: 'Tratamento 30 dias', price: 197, gelQuantity: 0, capsulesQuantity: 1, isKit: false },
      { name: 'Tratamento 60 dias', price: 297, gelQuantity: 0, capsulesQuantity: 2, isKit: false },
      { name: 'Tratamento 90 dias', price: 397, gelQuantity: 0, capsulesQuantity: 3, isKit: false },
    ],
  },
  {
    id: 'complete-kit',
    name: 'Kit Completo',
    description: 'Kits com gel e cápsulas combinados',
    offers: [
      { name: 'Kit Completo 30 dias', price: 347, gelQuantity: 1, capsulesQuantity: 1, isKit: true },
      { name: 'Kit Completo 60 dias', price: 547, gelQuantity: 2, capsulesQuantity: 2, isKit: true },
      { name: 'Kit Completo 90 dias', price: 747, gelQuantity: 3, capsulesQuantity: 3, isKit: true },
    ],
  },
  {
    id: 'mixed',
    name: 'Variações Mistas',
    description: 'Gel, cápsulas e kits em um único produto',
    offers: [
      { name: '30 dias Gel', price: 197, gelQuantity: 1, capsulesQuantity: 0, isKit: false },
      { name: '30 dias Cápsulas', price: 197, gelQuantity: 0, capsulesQuantity: 1, isKit: false },
      { name: 'Kit 30 dias', price: 347, gelQuantity: 1, capsulesQuantity: 1, isKit: true },
      { name: '60 dias Gel', price: 297, gelQuantity: 2, capsulesQuantity: 0, isKit: false },
      { name: '60 dias Cápsulas', price: 297, gelQuantity: 0, capsulesQuantity: 2, isKit: false },
      { name: 'Kit 60 dias', price: 547, gelQuantity: 2, capsulesQuantity: 2, isKit: true },
    ],
  },
];

const ProductCreationDialog: React.FC<ProductCreationDialogProps> = ({
  open,
  onClose,
  onSave,
  editingProduct,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [showTemplates, setShowTemplates] = useState(true);
  
  const [productForm, setProductForm] = useState<ProductForm>({
    name: '',
    description: '',
    active: true,
  });

  const [offers, setOffers] = useState<OfferForm[]>([]);
  const [offerForm, setOfferForm] = useState<OfferForm>({
    name: '',
    price: 0,
    gelQuantity: 0,
    capsulesQuantity: 0,
    isKit: false,
    active: true,
  });

  const [errors, setErrors] = useState<{
    product?: string;
    offers?: string;
  }>({});

  // Initialize form when dialog opens
  React.useEffect(() => {
    if (open) {
      if (editingProduct) {
        setProductForm({
          name: editingProduct.name,
          description: editingProduct.description || '',
          active: editingProduct.active,
        });
        setShowTemplates(false);
        setActiveTab(0);
      } else {
        // Reset form for new product
        setProductForm({
          name: '',
          description: '',
          active: true,
        });
        setOffers([]);
        setOfferForm({
          name: '',
          price: 0,
          gelQuantity: 0,
          capsulesQuantity: 0,
          isKit: false,
          active: true,
        });
        setSelectedTemplate('');
        setShowTemplates(true);
        setActiveTab(0);
      }
      setErrors({});
    }
  }, [open, editingProduct]);

  const handleTemplateSelect = (templateId: string) => {
    const template = productTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      const newOffers = template.offers.map(offer => ({
        ...offer,
        active: true,
      }));
      setOffers(newOffers);
      setShowTemplates(false);
      setActiveTab(1);
    }
  };

  const handleAddOffer = () => {
    // Validate offer
    if (!offerForm.name.trim()) {
      alert('Nome da oferta é obrigatório');
      return;
    }
    if (offerForm.price <= 0) {
      alert('Preço deve ser maior que zero');
      return;
    }
    if (offerForm.gelQuantity === 0 && offerForm.capsulesQuantity === 0) {
      alert('A oferta deve ter pelo menos uma quantidade de gel ou cápsulas');
      return;
    }
    if (offerForm.isKit && (offerForm.gelQuantity === 0 || offerForm.capsulesQuantity === 0)) {
      alert('Kits devem ter quantidades de gel E cápsulas');
      return;
    }

    setOffers([...offers, offerForm]);
    setOfferForm({
      name: '',
      price: 0,
      gelQuantity: 0,
      capsulesQuantity: 0,
      isKit: false,
      active: true,
    });
  };

  const handleRemoveOffer = (index: number) => {
    setOffers(offers.filter((_, i) => i !== index));
  };

  const handleDuplicateOffer = (index: number) => {
    const offerToDuplicate = offers[index];
    setOffers([
      ...offers,
      {
        ...offerToDuplicate,
        name: `${offerToDuplicate.name} (Cópia)`,
      },
    ]);
  };

  const validateForm = (): boolean => {
    const newErrors: { product?: string; offers?: string } = {};

    if (!productForm.name.trim()) {
      newErrors.product = 'Nome do produto é obrigatório';
    }

    if (offers.length === 0 && !editingProduct) {
      newErrors.offers = 'Adicione pelo menos uma oferta ao produto';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(productForm, offers);
      onClose();
    } catch (error: any) {
      console.error('Error saving product:', error);
    } finally {
      setLoading(false);
    }
  };

  const getOfferTypeChip = (offer: OfferForm) => {
    if (offer.isKit) {
      return <Chip label="Kit" size="small" color="secondary" icon={<CheckCircleIcon />} />;
    } else if (offer.gelQuantity > 0) {
      return <Chip label="Gel" size="small" color="primary" />;
    } else {
      return <Chip label="Cápsulas" size="small" color="info" />;
    }
  };

  const getTotalOfferValue = () => {
    return offers.reduce((total, offer) => total + offer.price, 0);
  };

  const getActiveOffersCount = () => {
    return offers.filter(offer => offer.active).length;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <InventoryIcon />
          {editingProduct ? 'Editar Produto' : 'Novo Produto'}
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        {/* Tabs */}
        <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v)} sx={{ mb: 3 }}>
          <Tab label="Informações do Produto" />
          <Tab label={`Ofertas (${offers.length})`} />
          {!editingProduct && <Tab label="Resumo" />}
        </Tabs>

        {/* Tab 1: Product Information */}
        {activeTab === 0 && (
          <Box>
            {/* Template Selection for new products */}
            {!editingProduct && showTemplates && (
              <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                  <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Começar com um Template
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Economize tempo usando um template pré-configurado
                </Typography>
                <Grid container spacing={2}>
                  {productTemplates.map((template) => (
                    <Grid item xs={12} sm={6} key={template.id}>
                      <Card
                        sx={{
                          cursor: 'pointer',
                          transition: 'all 0.3s',
                          border: selectedTemplate === template.id ? '2px solid' : '1px solid',
                          borderColor: selectedTemplate === template.id ? 'primary.main' : 'divider',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 3,
                          },
                        }}
                        onClick={() => handleTemplateSelect(template.id)}
                      >
                        <CardContent>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {template.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {template.description}
                          </Typography>
                          <Stack direction="row" spacing={1}>
                            <Chip
                              label={`${template.offers.length} ofertas`}
                              size="small"
                              icon={<LocalOfferIcon />}
                            />
                          </Stack>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                  <Grid item xs={12} sm={6}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s',
                        border: '1px dashed',
                        borderColor: 'divider',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 3,
                        },
                      }}
                      onClick={() => {
                        setShowTemplates(false);
                        setActiveTab(0);
                      }}
                    >
                      <CardContent sx={{ textAlign: 'center', py: 4 }}>
                        <AddIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                        <Typography variant="subtitle1">
                          Criar do Zero
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Configure tudo manualmente
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
                <Divider sx={{ my: 3 }} />
              </Box>
            )}

            {/* Product Form */}
            <TextField
              fullWidth
              label="Nome do Produto"
              value={productForm.name}
              onChange={(e) => setProductForm({ ...productForm, name: e.target.value })}
              error={!!errors.product}
              helperText={errors.product}
              sx={{ mb: 2 }}
              required
            />
            
            <TextField
              fullWidth
              label="Descrição"
              value={productForm.description}
              onChange={(e) => setProductForm({ ...productForm, description: e.target.value })}
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={productForm.active}
                  onChange={(e) => setProductForm({ ...productForm, active: e.target.checked })}
                />
              }
              label="Produto Ativo"
            />
          </Box>
        )}

        {/* Tab 2: Offers */}
        {activeTab === 1 && (
          <Box>
            {/* Add New Offer Form */}
            <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
              <Typography variant="subtitle1" gutterBottom>
                <AddIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Adicionar Nova Oferta
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Nome da Oferta"
                    value={offerForm.name}
                    onChange={(e) => setOfferForm({ ...offerForm, name: e.target.value })}
                    placeholder="Ex: Tratamento 30 dias"
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Preço"
                    type="number"
                    value={offerForm.price}
                    onChange={(e) => setOfferForm({ ...offerForm, price: parseFloat(e.target.value) || 0 })}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                    }}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6} md={3}>
                  <TextField
                    fullWidth
                    label="Qtd. Gel"
                    type="number"
                    value={offerForm.gelQuantity}
                    onChange={(e) => setOfferForm({ ...offerForm, gelQuantity: parseInt(e.target.value) || 0 })}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6} md={3}>
                  <TextField
                    fullWidth
                    label="Qtd. Cápsulas"
                    type="number"
                    value={offerForm.capsulesQuantity}
                    onChange={(e) => setOfferForm({ ...offerForm, capsulesQuantity: parseInt(e.target.value) || 0 })}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={offerForm.isKit}
                        onChange={(e) => {
                          const isKit = e.target.checked;
                          setOfferForm({ 
                            ...offerForm, 
                            isKit,
                            // If switching to kit, ensure both quantities are at least 1
                            gelQuantity: isKit && offerForm.gelQuantity === 0 ? 1 : offerForm.gelQuantity,
                            capsulesQuantity: isKit && offerForm.capsulesQuantity === 0 ? 1 : offerForm.capsulesQuantity,
                          });
                        }}
                        size="small"
                      />
                    }
                    label="É um Kit"
                  />
                </Grid>
                <Grid item xs={6} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={offerForm.active}
                        onChange={(e) => setOfferForm({ ...offerForm, active: e.target.checked })}
                        size="small"
                      />
                    }
                    label="Ativa"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddOffer}
                    disabled={!offerForm.name || offerForm.price <= 0}
                  >
                    Adicionar Oferta
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {/* Offers List */}
            {errors.offers && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {errors.offers}
              </Alert>
            )}

            {offers.length === 0 ? (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <LocalOfferIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  Nenhuma oferta adicionada
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Adicione pelo menos uma oferta para este produto
                </Typography>
              </Paper>
            ) : (
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome</TableCell>
                      <TableCell align="right">Preço</TableCell>
                      <TableCell align="center">Gel</TableCell>
                      <TableCell align="center">Cápsulas</TableCell>
                      <TableCell align="center">Tipo</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="right">Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {offers.map((offer, index) => (
                      <TableRow key={index}>
                        <TableCell>{offer.name}</TableCell>
                        <TableCell align="right">
                          <strong>
                            {offer.price.toLocaleString('pt-BR', {
                              style: 'currency',
                              currency: 'BRL',
                            })}
                          </strong>
                        </TableCell>
                        <TableCell align="center">{offer.gelQuantity}</TableCell>
                        <TableCell align="center">{offer.capsulesQuantity}</TableCell>
                        <TableCell align="center">
                          {getOfferTypeChip(offer)}
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={offer.active ? 'Ativa' : 'Inativa'}
                            color={offer.active ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Duplicar">
                            <IconButton
                              size="small"
                              onClick={() => handleDuplicateOffer(index)}
                            >
                              <ContentCopyIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Remover">
                            <IconButton
                              size="small"
                              onClick={() => handleRemoveOffer(index)}
                              color="error"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        )}

        {/* Tab 3: Summary (only for new products) */}
        {activeTab === 2 && !editingProduct && (
          <Box>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Resumo do Produto
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Nome</Typography>
                    <Typography variant="subtitle1">{productForm.name || 'Não definido'}</Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Descrição</Typography>
                    <Typography variant="body1">{productForm.description || 'Sem descrição'}</Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Status</Typography>
                    <Chip
                      label={productForm.active ? 'Ativo' : 'Inativo'}
                      color={productForm.active ? 'success' : 'default'}
                      size="small"
                    />
                  </Box>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Estatísticas das Ofertas
                  </Typography>
                  <Stack spacing={2}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">Total de Ofertas</Typography>
                      <Typography variant="subtitle1">{offers.length}</Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">Ofertas Ativas</Typography>
                      <Typography variant="subtitle1" color="success.main">
                        {getActiveOffersCount()}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">Kits</Typography>
                      <Typography variant="subtitle1" color="secondary.main">
                        {offers.filter(o => o.isKit).length}
                      </Typography>
                    </Box>
                    <Divider />
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">Valor Total</Typography>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {getTotalOfferValue().toLocaleString('pt-BR', {
                          style: 'currency',
                          currency: 'BRL',
                        })}
                      </Typography>
                    </Box>
                  </Stack>
                </Paper>
              </Grid>

              {/* Offers Preview */}
              <Grid item xs={12}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Preview das Ofertas
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Assim que o produto for criado, estas ofertas estarão disponíveis para vendas
                  </Alert>
                  <Stack spacing={1}>
                    {offers.filter(o => o.active).map((offer, index) => (
                      <Box
                        key={index}
                        sx={{
                          p: 2,
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}
                      >
                        <Box>
                          <Typography variant="subtitle1">
                            {productForm.name} - {offer.name}
                          </Typography>
                          <Stack direction="row" spacing={1} mt={0.5}>
                            {offer.gelQuantity > 0 && (
                              <Chip label={`${offer.gelQuantity} Gel`} size="small" />
                            )}
                            {offer.capsulesQuantity > 0 && (
                              <Chip label={`${offer.capsulesQuantity} Cápsulas`} size="small" />
                            )}
                            {offer.isKit && (
                              <Chip label="Kit" size="small" color="secondary" />
                            )}
                          </Stack>
                        </Box>
                        <Typography variant="h6" color="primary">
                          {offer.price.toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL',
                          })}
                        </Typography>
                      </Box>
                    ))}
                  </Stack>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || !productForm.name.trim()}
        >
          {loading ? 'Salvando...' : editingProduct ? 'Salvar' : 'Criar Produto'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductCreationDialog;