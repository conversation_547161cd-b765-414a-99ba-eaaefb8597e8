import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Typography,
  Box,
} from '@mui/material';
import { Order } from '../types/Order';
import { formatDate, formatDateTime } from '../utils/dateUtils';

interface OrdersTableWithDatesProps {
  orders: Order[];
  showDateTime?: boolean;
}

const OrdersTableWithDates: React.FC<OrdersTableWithDatesProps> = ({ 
  orders, 
  showDateTime = false 
}) => {
  const getStatusColor = (status: string): 'success' | 'warning' | 'error' | 'default' => {
    const normalizedStatus = status?.toLowerCase() || '';
    
    if (normalizedStatus === 'aprovado' || normalizedStatus === 'approved') return 'success';
    if (normalizedStatus === 'pendente' || normalizedStatus === 'pending') return 'warning';
    if (normalizedStatus === 'cancelado' || normalizedStatus === 'cancelled') return 'error';
    return 'default';
  };

  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }}>
        <TableHead>
          <TableRow>
            <TableCell>Order Date</TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Product</TableCell>
            <TableCell align="right">Value</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Seller</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {orders.map((order, index) => (
            <TableRow
              key={order.id || index}
              sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
            >
              <TableCell>
                <Typography variant="body2">
                  {showDateTime 
                    ? formatDateTime(order.dataVenda || order.createdAt)
                    : formatDate(order.dataVenda || order.createdAt)
                  }
                </Typography>
              </TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2">{order.customerName || 'Unknown'}</Typography>
                  {order.customerPhone && (
                    <Typography variant="caption" color="text.secondary">
                      {order.customerPhone}
                    </Typography>
                  )}
                </Box>
              </TableCell>
              <TableCell>
                <Typography variant="body2">{order.productName || 'N/A'}</Typography>
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2" fontWeight="medium">
                  R$ {(order.total || order.valorVenda || 0).toLocaleString('pt-BR', { 
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2 
                  })}
                </Typography>
              </TableCell>
              <TableCell>
                <Chip
                  label={order.status || order.situacaoVenda || 'Unknown'}
                  size="small"
                  color={getStatusColor(order.status || order.situacaoVenda || '')}
                />
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {order.vendedorName || order.sellerName || '-'}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {orders.length === 0 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No orders found
          </Typography>
        </Box>
      )}
    </TableContainer>
  );
};

export default OrdersTableWithDates;