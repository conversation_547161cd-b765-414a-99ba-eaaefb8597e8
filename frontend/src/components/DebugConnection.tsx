import React, { useState } from 'react';
import { Box, Button, Paper, Typography, Alert } from '@mui/material';
import axios from 'axios';

export default function DebugConnection() {
  const [results, setResults] = useState<Array<{ message: string; type: 'info' | 'success' | 'error' }>>([]);
  
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
  const TENANT_ID = process.env.REACT_APP_TENANT_ID || '28a833c0-c2a1-4498-85ca-b028f982ffb2';
  
  const addResult = (message: string, type: 'info' | 'success' | 'error' = 'info') => {
    setResults(prev => [...prev, { message, type }]);
  };
  
  const clearResults = () => {
    setResults([]);
  };
  
  const testSimpleFetch = async () => {
    addResult('Testing simple fetch to /test-cors...');
    try {
      const response = await fetch(`${API_URL}/test-cors`);
      const data = await response.json();
      addResult(`Success! Response: ${JSON.stringify(data)}`, 'success');
    } catch (error: any) {
      addResult(`Error: ${error.message}`, 'error');
      if (error.message === 'Failed to fetch') {
        addResult('This usually indicates a CORS issue or the backend is unreachable', 'error');
      }
    }
  };
  
  const testWithCredentials = async () => {
    addResult('Testing fetch with credentials to /test-cors...');
    try {
      const response = await fetch(`${API_URL}/test-cors`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      addResult(`Success! Response: ${JSON.stringify(data)}`, 'success');
    } catch (error: any) {
      addResult(`Error: ${error.message}`, 'error');
    }
  };
  
  const testAxios = async () => {
    addResult('Testing axios request to /test-cors...');
    try {
      const response = await axios.get(`${API_URL}/test-cors`, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      addResult(`Success! Response: ${JSON.stringify(response.data)}`, 'success');
    } catch (error: any) {
      addResult(`Error: ${error.message}`, 'error');
      if (error.response) {
        addResult(`Response status: ${error.response.status}`, 'error');
        addResult(`Response data: ${JSON.stringify(error.response.data)}`, 'error');
      }
    }
  };
  
  const testProducts = async () => {
    addResult('Testing products endpoint...');
    try {
      const response = await fetch(`${API_URL}/products`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': TENANT_ID,
        },
      });
      
      if (!response.ok) {
        const text = await response.text();
        addResult(`Failed with status ${response.status}: ${text}`, 'error');
      } else {
        const data = await response.json();
        addResult(`Success! Found ${data.length} products`, 'success');
      }
    } catch (error: any) {
      addResult(`Error: ${error.message}`, 'error');
    }
  };
  
  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Backend Connection Debugger
      </Typography>
      
      <Alert severity="info" sx={{ mb: 2 }}>
        <strong>Configuration:</strong><br />
        API URL: {API_URL}<br />
        Tenant ID: {TENANT_ID}<br />
        Environment: {process.env.NODE_ENV}
      </Alert>
      
      <Box sx={{ mb: 2 }}>
        <Button variant="contained" onClick={testSimpleFetch} sx={{ mr: 1 }}>
          Test Simple Fetch
        </Button>
        <Button variant="contained" onClick={testWithCredentials} sx={{ mr: 1 }}>
          Test With Credentials
        </Button>
        <Button variant="contained" onClick={testAxios} sx={{ mr: 1 }}>
          Test Axios
        </Button>
        <Button variant="contained" onClick={testProducts} sx={{ mr: 1 }}>
          Test Products
        </Button>
        <Button variant="outlined" onClick={clearResults}>
          Clear Results
        </Button>
      </Box>
      
      <Box>
        {results.map((result, index) => (
          <Alert key={index} severity={result.type} sx={{ mb: 1 }}>
            {result.message}
          </Alert>
        ))}
      </Box>
    </Paper>
  );
}