import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Alert,
  AlertTitle,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  FormControlLabel,
  Checkbox,
  Chip,
  Autocomplete,
  TextField,
  Grid,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import Papa from 'papaparse';
import OrderService from '../services/OrderService';
import { useOrderData } from '../contexts/OrderDataContext';
import { useNotificationContext } from '../contexts/NotificationContext';
import api from '../services/api';

interface OrderImportDialogEnhancedProps {
  open: boolean;
  onClose: () => void;
}

interface CSVRow {
  [key: string]: string;
}

interface ImportPreview {
  row: CSVRow;
  status: 'valid' | 'warning' | 'error';
  messages: string[];
  mapped: boolean;
}

interface SystemOption {
  id: string;
  name: string;
}

interface FieldMapping {
  csvField: string;
  systemField: string;
  mappingType?: 'text' | 'kit' | 'seller' | 'operator';
  mappedId?: string;
}

const SYSTEM_FIELDS = [
  { value: 'createdAt', label: 'Data da Venda', required: true, type: 'text' },
  { value: 'orderNumber', label: 'ID da Venda', required: true, type: 'text' },
  { value: 'customerName', label: 'Nome do Cliente', required: true, type: 'text' },
  { value: 'customerPhone', label: 'Telefone', required: true, type: 'text' },
  { value: 'oferta', label: 'Produto/Kit', required: false, type: 'kit' },
  { value: 'total', label: 'Valor da Venda', required: true, type: 'text' },
  { value: 'status', label: 'Status', required: false, type: 'text' },
  { value: 'paymentReceivedAmount', label: 'Valor Recebido', required: false, type: 'text' },
  { value: 'observation', label: 'Histórico', required: false, type: 'text' },
  { value: 'updatedAt', label: 'Última Atualização', required: false, type: 'text' },
  { value: 'trackingCode', label: 'Código de Rastreio', required: false, type: 'text' },
  { value: 'statusCorreios', label: 'Status Correios', required: false, type: 'text' },
  { value: 'sellerName', label: 'Vendedor', required: false, type: 'seller' },
  { value: 'collectorName', label: 'Operador', required: false, type: 'operator' },
  { value: 'zapId', label: 'WhatsApp', required: false, type: 'text' },
  { value: 'addressComponents.state', label: 'Estado', required: false, type: 'text' },
  { value: 'addressComponents.city', label: 'Cidade', required: false, type: 'text' },
  { value: 'addressComponents.street', label: 'Rua', required: false, type: 'text' },
  { value: 'addressComponents.zipCode', label: 'CEP', required: false, type: 'text' },
  { value: 'addressComponents.complement', label: 'Complemento', required: false, type: 'text' },
  { value: 'addressComponents.neighborhood', label: 'Bairro', required: false, type: 'text' },
  { value: 'addressComponents.streetNumber', label: 'Número', required: false, type: 'text' },
  { value: 'dataEstimadaChegada', label: 'Data Estimada Chegada', required: false, type: 'text' },
  { value: 'paymentReceivedDate', label: 'Data Recebimento', required: false, type: 'text' },
  { value: 'lastContactDate', label: 'Data Negociação', required: false, type: 'text' },
  { value: 'customerCPF', label: 'CPF Cliente', required: false, type: 'text' },
];

const DEFAULT_MAPPINGS: { [key: string]: string } = {
  'Data Venda': 'createdAt',
  'ID Venda': 'orderNumber',
  'Cliente': 'customerName',
  'Telefone': 'customerPhone',
  'Oferta': 'oferta',
  'Valor Venda': 'total',
  'Status': 'status',
  'Situação Venda': 'status',
  'Valor Recebido': 'paymentReceivedAmount',
  'Historico': 'observation',
  'Ultima Atualização': 'updatedAt',
  'Código de Rastreio': 'trackingCode',
  'Codigo Rastreio': 'trackingCode',
  'Código Rastreio': 'trackingCode',
  'codigo_rastreio': 'trackingCode',
  'CODIGO RASTREIO': 'trackingCode',
  'Rastreio': 'trackingCode',
  'Status Correios': 'statusCorreios',
  'Vendedor': 'sellerName',
  'Operador': 'collectorName',
  'Zap': 'zapId',
  'WhatsApp': 'zapId',
  'ESTADO DO DESTINATÁRIO': 'addressComponents.state',
  'CIDADE DO DESTINATÁRIO': 'addressComponents.city',
  'RUA DO DESTINATÁRIO': 'addressComponents.street',
  'CEP DO DESTINATÁRIO': 'addressComponents.zipCode',
  'COMPLEMENTO DO DESTINATÁRIO': 'addressComponents.complement',
  'BAIRRO DO DESTINATÁRIO': 'addressComponents.neighborhood',
  'NÚMERO DO ENDEREÇO DO DESTINATÁRIO': 'addressComponents.streetNumber',
  'DATA ESTIMADA DE CHEGADA': 'dataEstimadaChegada',
  'DATA DE RECEBIMENTO': 'paymentReceivedDate',
  'Data Recebimento': 'paymentReceivedDate',
  'Data_Negociacao': 'lastContactDate',
  'DOCUMENTO CLIENTE': 'customerCPF',
  'CPF': 'customerCPF',
};

const OrderImportDialogEnhanced: React.FC<OrderImportDialogEnhancedProps> = ({ open, onClose }) => {
  const [activeStep, setActiveStep] = useState(0);
  
  // Override setActiveStep to log all changes
  const originalSetActiveStep = setActiveStep;
  const setActiveStepWithLog = (step: number) => {
    console.log('setActiveStep called:', {
      from: activeStep,
      to: step,
      stack: new Error().stack
    });
    originalSetActiveStep(step);
  };
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVRow[]>([]);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]);
  const [importPreview, setImportPreview] = useState<ImportPreview[]>([]);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importCompleted, setImportCompleted] = useState(false);
  const [importResults, setImportResults] = useState<{
    success: number;
    failed: number;
    errors: string[];
  }>({ success: 0, failed: 0, errors: [] });
  const [skipDuplicates, setSkipDuplicates] = useState(true);
  
  // Use ref to persist results across re-renders
  const importResultsRef = useRef<{ success: number; failed: number; errors: string[] }>({ success: 0, failed: 0, errors: [] });
  const importCompletedRef = useRef(false);
  
  // System data
  const [kits, setKits] = useState<SystemOption[]>([]);
  const [sellers, setSellers] = useState<SystemOption[]>([]);
  const [operators, setOperators] = useState<SystemOption[]>([]);
  
  // User mapping state
  const [uniqueSellers, setUniqueSellers] = useState<string[]>([]);
  const [uniqueOperators, setUniqueOperators] = useState<string[]>([]);
  const [userMappings, setUserMappings] = useState<{
    sellers: { [csvName: string]: string };
    operators: { [csvName: string]: string };
  }>({ sellers: {}, operators: {} });
  
  const { fetchOrders } = useOrderData();
  const { showNotification } = useNotificationContext();

  const steps = ['Upload do Arquivo', 'Mapear Campos', 'Mapear Usuários', 'Revisar e Importar'];

  // Track dialog open/close
  useEffect(() => {
    console.log('Dialog open state changed:', { open, wasOpen: !open ? 'closed' : 'opened' });
  }, [open]);
  
  // Fetch system data on mount
  useEffect(() => {
    if (open) {
      console.log('Dialog opened, checking state:', {
        activeStep,
        importResults,
        importResultsRef: importResultsRef.current,
        importCompleted,
        importCompletedRef: importCompletedRef.current
      });
      
      // Check localStorage for recent results
      const storedData = localStorage.getItem('csvImportResults');
      if (storedData) {
        try {
          const parsed = JSON.parse(storedData);
          // Only use if less than 5 minutes old
          if (Date.now() - parsed.timestamp < 5 * 60 * 1000) {
            if (parsed.results.success > 0 || parsed.results.failed > 0) {
              console.log('Restoring results from localStorage:', parsed);
              setImportResults(parsed.results);
              importResultsRef.current = parsed.results;
              setImportCompleted(true);
              importCompletedRef.current = true;
              setActiveStepWithLog(3);
              // Clear localStorage after restoring
              localStorage.removeItem('csvImportResults');
              return; // Don't fetch system data if we're showing results
            }
          } else {
            // Clear old data
            localStorage.removeItem('csvImportResults');
          }
        } catch (e) {
          console.error('Error parsing stored results:', e);
          localStorage.removeItem('csvImportResults');
        }
      }
      
      // Restore results from ref if they exist but state was lost
      if (importResultsRef.current.success > 0 || importResultsRef.current.failed > 0) {
        if (importResults.success === 0 && importResults.failed === 0) {
          console.log('Restoring results from ref');
          setImportResults(importResultsRef.current);
          setImportCompleted(importCompletedRef.current);
          // Make sure we're on step 3 to show results
          if (activeStep !== 3) {
            console.log('Restoring activeStep to 3 to show results');
            setActiveStepWithLog(3);
          }
        }
      }
      
      // Only reset if we're opening fresh (no results)
      const hasResults = importResults.success > 0 || importResults.failed > 0 || 
                       importResultsRef.current.success > 0 || importResultsRef.current.failed > 0;
      if (!hasResults) {
        fetchSystemData();
      }
    }
  }, [open]);
  
  // Debug effect to track component lifecycle and state
  useEffect(() => {
    console.log('OrderImportDialogEnhanced state:', {
      open,
      activeStep,
      importing,
      importCompleted,
      hasResults: importResults.success > 0 || importResults.failed > 0,
      importResults
    });
  }, [open, activeStep, importing, importCompleted, importResults]);

  const fetchSystemData = async () => {
    try {
      // Fetch kits
      const kitsResponse = await api.get('/kits');
      setKits(kitsResponse.data.map((kit: any) => ({ id: kit.id, name: kit.name })));

      // Fetch users (sellers and operators)
      const usersResponse = await api.get('/users');
      // The backend uses TransformInterceptor which wraps response in data.data
      const users = Array.isArray(usersResponse.data.data) ? usersResponse.data.data : 
                   Array.isArray(usersResponse.data) ? usersResponse.data : 
                   usersResponse.data.users || [];
      
      console.log('Users API response:', usersResponse);
      console.log('Users data structure:', usersResponse.data);
      console.log('Fetched users:', users);
      
      // Debug: Log all users with their roles
      console.log('All users with roles:', users.map((u: any) => ({ name: u.name, role: u.role, email: u.email })));
      
      const mappedSellers = users
        .filter((user: any) => {
          // Include ADMIN, SUPERVISOR, and VENDEDOR roles for sellers dropdown
          const isVendedor = user.role === 'VENDEDOR' || user.role === 'SELLER' || user.role === 'vendedor' ||
                            user.role === 'ADMIN' || user.role === 'SUPERVISOR';
          if (isVendedor) {
            console.log('Found user for VENDEDOR dropdown:', user);
          }
          return isVendedor;
        })
        .map((user: any) => ({ id: user.id, name: user.name || user.nome || user.email }));
      
      const mappedOperators = users
        .filter((user: any) => {
          // Include ADMIN, SUPERVISOR, and COBRADOR roles for operators dropdown
          const isCobrador = user.role === 'COBRADOR' || user.role === 'COLLECTOR' || user.role === 'cobrador' || 
                            user.role === 'operador' || user.role === 'ADMIN' || user.role === 'SUPERVISOR';
          if (isCobrador) {
            console.log('Found user for COBRADOR dropdown:', user);
          }
          return isCobrador;
        })
        .map((user: any) => ({ id: user.id, name: user.name || user.nome || user.email }));
      
      setSellers(mappedSellers);
      setOperators(mappedOperators);
      
      console.log('Final Mapped Sellers:', mappedSellers);
      console.log('Final Mapped Operators:', mappedOperators);
    } catch (error: any) {
      console.error('Error fetching system data:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      parseCSV(selectedFile);
    }
  };

  const parseCSV = (file: File) => {
    Papa.parse(file, {
      complete: (result) => {
        if (result.data && result.data.length > 0) {
          const headers = Object.keys(result.data[0] as any);
          setCsvHeaders(headers);
          setCsvData(result.data as CSVRow[]);
          
          // Auto-map fields based on default mappings
          const autoMappings: FieldMapping[] = [];
          headers.forEach(header => {
            if (DEFAULT_MAPPINGS[header]) {
              const systemField = DEFAULT_MAPPINGS[header];
              const fieldConfig = SYSTEM_FIELDS.find(f => f.value === systemField);
              autoMappings.push({
                csvField: header,
                systemField: systemField,
                mappingType: (fieldConfig?.type || 'text') as 'text' | 'kit' | 'seller' | 'operator'
              });
            }
          });
          setFieldMappings(autoMappings);
          
          // Move to next step
          setActiveStepWithLog(1);
        }
      },
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8'
    });
  };

  const handleMappingChange = (csvField: string, systemField: string) => {
    const fieldConfig = SYSTEM_FIELDS.find(f => f.value === systemField);
    const newMappings = fieldMappings.filter(m => m.csvField !== csvField);
    
    if (systemField) {
      newMappings.push({
        csvField,
        systemField,
        mappingType: (fieldConfig?.type || 'text') as 'text' | 'kit' | 'seller' | 'operator'
      });
    }
    
    setFieldMappings(newMappings);
  };

  const handleMappingIdChange = (csvField: string, mappedId: string) => {
    setFieldMappings(fieldMappings.map(m => 
      m.csvField === csvField ? { ...m, mappedId } : m
    ));
  };

  const validateRow = (row: CSVRow): { status: 'valid' | 'warning' | 'error'; messages: string[] } => {
    const messages: string[] = [];
    let status: 'valid' | 'warning' | 'error' = 'valid';

    // Check required fields
    const requiredMappings = ['orderNumber', 'customerName', 'customerPhone', 'total'];
    requiredMappings.forEach(field => {
      const mapping = fieldMappings.find(m => m.systemField === field);
      if (!mapping || !row[mapping.csvField]) {
        messages.push(`Campo obrigatório ausente: ${field}`);
        status = 'error';
      }
    });

    // Validate phone number
    const phoneMapping = fieldMappings.find(m => m.systemField === 'customerPhone');
    if (phoneMapping && row[phoneMapping.csvField]) {
      const phone = normalizePhone(row[phoneMapping.csvField]);
      if (phone.length < 10 || phone.length > 11) {
        messages.push(`Telefone inválido: ${row[phoneMapping.csvField]} (deve ter 10 ou 11 dígitos, sem código do país)`);
        status = 'warning';
      }
    }

    // Validate monetary values
    ['total', 'paymentReceivedAmount'].forEach(field => {
      const mapping = fieldMappings.find(m => m.systemField === field);
      if (mapping && row[mapping.csvField]) {
        const value = parseMonetaryValue(row[mapping.csvField]);
        if (isNaN(value) || value < 0) {
          messages.push(`Valor monetário inválido: ${field}`);
          status = 'warning';
        }
      }
    });

    // CPF is optional for import

    if (messages.length === 0) {
      messages.push('Dados válidos');
    }

    return { status, messages };
  };

  const parseMonetaryValue = (value: string): number => {
    if (!value) return 0;
    
    // Remove currency symbol and spaces
    let cleanValue = value.replace(/R\$\s?/g, '').trim();
    
    // Handle different number formats
    if (cleanValue.includes(',') && cleanValue.includes('.')) {
      // Format: 1.350,00 or 1,350.00
      if (cleanValue.lastIndexOf(',') > cleanValue.lastIndexOf('.')) {
        // Brazilian format: 1.350,00
        cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
      } else {
        // US format: 1,350.00
        cleanValue = cleanValue.replace(/,/g, '');
      }
    } else if (cleanValue.includes(',')) {
      // Could be 1350,00 or 1,350
      const parts = cleanValue.split(',');
      if (parts[1] && parts[1].length === 2) {
        // Likely decimal separator: 1350,00
        cleanValue = cleanValue.replace(',', '.');
      } else {
        // Likely thousands separator: 1,350
        cleanValue = cleanValue.replace(/,/g, '');
      }
    }
    
    const parsed = parseFloat(cleanValue);
    // Validate the parsed value is reasonable (not accidentally parsing tracking codes)
    if (isNaN(parsed) || parsed < 0 || parsed > 999999999) {
      return 0;
    }
    return parsed;
  };

  // Helper function to get proper case name - moved to component level
  const getProperCase = (name: string): string => {
    return name
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper function to normalize phone numbers
  const normalizePhone = (phone: string): string => {
    // Remove all non-digits
    let cleaned = phone.replace(/\D/g, '');
    
    // Remove country code if present (55 for Brazil)
    if (cleaned.startsWith('55') && cleaned.length > 11) {
      cleaned = cleaned.substring(2);
    }
    
    return cleaned;
  };

  const prepareImportPreview = () => {
    const preview: ImportPreview[] = csvData.slice(0, 10).map(row => {
      const validation = validateRow(row);
      return {
        row,
        status: validation.status,
        messages: validation.messages,
        mapped: true,
      };
    });
    setImportPreview(preview);
    setActiveStepWithLog(3);
  };

  const extractUniqueUsers = () => {
    // Extract unique seller and operator names from CSV data
    const sellersMap = new Map<string, string>(); // lowercase -> proper case
    const operatorsMap = new Map<string, string>(); // lowercase -> proper case
    
    // Find the mapped fields for sellers and operators
    const sellerMapping = fieldMappings.find(m => m.systemField === 'sellerName');
    const operatorMapping = fieldMappings.find(m => m.systemField === 'collectorName');
    
    csvData.forEach(row => {
      if (sellerMapping && row[sellerMapping.csvField]) {
        const sellerName = row[sellerMapping.csvField].trim();
        if (sellerName) {
          const lowerName = sellerName.toLowerCase();
          if (!sellersMap.has(lowerName)) {
            // First occurrence - use proper case
            sellersMap.set(lowerName, getProperCase(sellerName));
          }
        }
      }
      
      if (operatorMapping && row[operatorMapping.csvField]) {
        const operatorName = row[operatorMapping.csvField].trim();
        if (operatorName) {
          const lowerName = operatorName.toLowerCase();
          if (!operatorsMap.has(lowerName)) {
            // First occurrence - use proper case
            operatorsMap.set(lowerName, getProperCase(operatorName));
          }
        }
      }
    });
    
    // Get unique names in proper case
    setUniqueSellers(Array.from(sellersMap.values()).sort());
    setUniqueOperators(Array.from(operatorsMap.values()).sort());
    
    // Move to user mapping step
    setActiveStepWithLog(2);
  };

  const mapRowToOrder = (row: CSVRow): any => {
    const order: any = {};
    
    fieldMappings.forEach(mapping => {
      const value = row[mapping.csvField];
      
      if (value && value.trim() !== '') {
        // Handle different field types
        switch (mapping.systemField) {
          case 'total':
          case 'paymentReceivedAmount':
          case 'pagamentoParcial':
            const parsedValue = parseMonetaryValue(value);
            if (parsedValue > 0) {
              order[mapping.systemField] = parsedValue;
            }
            break;
          
          case 'parcial':
            order[mapping.systemField] = value.toLowerCase() === 'sim' || value === '1' || value === 'true';
            break;
          
          case 'oferta':
            // If a kit is mapped, use the kit ID, otherwise use the text value
            order.oferta = value;
            if (mapping.mappedId) {
              order.kitId = mapping.mappedId;
            }
            break;
          
          case 'sellerName':
            // Use user mapping if available - check for proper case version
            const properCaseSellerName = getProperCase(value);
            const mappedSellerId = userMappings.sellers[properCaseSellerName];
            if (mappedSellerId) {
              const seller = sellers.find(s => s.id === mappedSellerId);
              order.sellerName = seller?.name || value;
              order.sellerId = mappedSellerId;
            } else {
              order.sellerName = value;
            }
            break;
          
          case 'collectorName':
            // Use user mapping if available - check for proper case version
            const properCaseOperatorName = getProperCase(value);
            const mappedOperatorId = userMappings.operators[properCaseOperatorName];
            if (mappedOperatorId) {
              const operator = operators.find(o => o.id === mappedOperatorId);
              order.collectorName = operator?.name || value;
              order.collectorId = mappedOperatorId;
            } else {
              order.collectorName = value;
            }
            break;
          
          case 'customerPhone':
            // Clean and format phone number
            const cleanedPhone = normalizePhone(value);
            order.customerPhone = cleanedPhone;
            break;
          
          case 'status':
            // Status field - pass as is, backend will handle mapping
            console.log(`Mapping status field: "${value}"`);
            order[mapping.systemField] = value.trim();
            break;
            
          case 'statusCorreios':
            // Status Correios field - shipping status from postal service
            console.log(`Mapping statusCorreios field: "${value}" to ${mapping.systemField}`);
            order.statusCorreios = value.trim(); // Ensure it's always set on statusCorreios field
            console.log(`Order statusCorreios set to: "${order.statusCorreios}"`);
            break;
            
          case 'createdAt':
          case 'updatedAt':
          case 'paymentReceivedDate':
          case 'lastContactDate':
          case 'nextPaymentDate':
            // Parse date from DD/MM/YYYY or other formats
            const dateParts = value.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/);
            if (dateParts) {
              const [_, day, month, year] = dateParts;
              const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
              order[mapping.systemField] = date.toISOString();
            } else {
              // Try to parse as-is
              const date = new Date(value);
              if (!isNaN(date.getTime())) {
                order[mapping.systemField] = date.toISOString();
              } else {
                order[mapping.systemField] = value;
              }
            }
            break;
          
          default:
            // Handle nested fields like addressComponents.street
            if (mapping.systemField.includes('.')) {
              const [parent, child] = mapping.systemField.split('.');
              if (!order[parent]) {
                order[parent] = {};
              }
              order[parent][child] = value;
            } else {
              order[mapping.systemField] = value;
            }
        }
      }
    });
    
    return order;
  };

  const handleImport = async () => {
    setImporting(true);
    setImportProgress(0);
    const results = { success: 0, failed: 0, errors: [] as string[] };

    try {
      const totalRows = csvData.length;
      const batchSize = 5;
      const delayBetweenBatches = 1000;
      
      for (let i = 0; i < totalRows; i += batchSize) {
        const batch = csvData.slice(i, Math.min(i + batchSize, totalRows));
        
        for (let j = 0; j < batch.length; j++) {
          const row = batch[j];
          const rowIndex = i + j;
          
          try {
            const orderData = mapRowToOrder(row);
            
            // Debug log to check status fields
            console.log(`Row ${rowIndex + 2} - Order data:`, {
              orderNumber: orderData.orderNumber,
              status: orderData.status,
              statusCorreios: orderData.statusCorreios,
              customerName: orderData.customerName
            });
            
            if (skipDuplicates) {
              const exists = await OrderService.checkOrderExists(orderData.orderNumber);
              if (exists) {
                results.failed++;
                results.errors.push(`Linha ${rowIndex + 2}: Pedido ${orderData.orderNumber} já existe`);
                continue;
              }
            }
            
            await OrderService.importOrder(orderData);
            results.success++;
          } catch (error: any) {
            results.failed++;
            let errorMessage = error.response?.data?.message || error.message || 'Erro desconhecido';
            
            // Extract validation errors if present
            if (error.response?.data?.errors) {
              const validationErrors = error.response.data.errors;
              if (Array.isArray(validationErrors)) {
                errorMessage = validationErrors.join(', ');
              } else if (typeof validationErrors === 'object') {
                errorMessage = Object.entries(validationErrors)
                  .map(([field, messages]) => `${field}: ${messages}`)
                  .join(', ');
              }
            }
            
            console.error(`Import error for row ${rowIndex + 2}:`, {
              orderData,
              error: error.response?.data || error.message
            });
            
            results.errors.push(`Linha ${rowIndex + 2}: ${errorMessage}`);
            
            if (error.response?.status === 429) {
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
          }
          
          setImportProgress(((rowIndex + 1) / totalRows) * 100);
        }
        
        if (i + batchSize < totalRows) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
      }
      
      // Store results in state, ref, and localStorage
      setImportResults(results);
      importResultsRef.current = results;
      setImportCompleted(true);
      importCompletedRef.current = true;
      
      // Store in localStorage as failsafe
      localStorage.setItem('csvImportResults', JSON.stringify({
        results,
        timestamp: Date.now(),
        activeStep: 3
      }));
      
      // Keep the dialog on the same step to show results
      // Don't reset or change step - let the user see the results
      console.log('Import completed - BEFORE fetchOrders:', {
        activeStep,
        results,
        importing: false,
        importCompleted: true,
        dialogOpen: open
      });
      
      // Add a small delay before fetching to ensure state is updated
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (results.success > 0) {
        console.log('Calling fetchOrders...');
        await fetchOrders();
        console.log('fetchOrders completed');
      }
      
      console.log('Import completed - AFTER fetchOrders:', {
        activeStep: activeStep,
        importResults: importResultsRef.current,
        importCompleted: importCompletedRef.current,
        dialogOpen: open
      });
    } catch (error: any) {
      showNotification('Erro durante a importação', 'error');
      console.error('Import error:', error);
    } finally {
      setImporting(false);
      setImportProgress(100); // Ensure progress shows complete
    }
  };

  const handleClose = () => {
    // Don't allow closing while importing or if we have results to show
    if (importing) {
      console.log('Preventing close: import in progress');
      return;
    }
    
    const hasResults = importResults.success > 0 || importResults.failed > 0;
    if (hasResults && activeStep === 3) {
      console.log('Preventing auto-close: showing results');
      // Don't auto-close if we're showing results
      // User must explicitly click "Fechar e Ver Pedidos"
      return;
    }
    
    console.log('Closing dialog and resetting state');
    setActiveStepWithLog(0);
    setFile(null);
    setCsvData([]);
    setCsvHeaders([]);
    setFieldMappings([]);
    setImportPreview([]);
    setImportCompleted(false);
    importCompletedRef.current = false;
    setImportResults({ success: 0, failed: 0, errors: [] });
    importResultsRef.current = { success: 0, failed: 0, errors: [] };
    setUniqueSellers([]);
    setUniqueOperators([]);
    setUserMappings({ sellers: {}, operators: {} });
    localStorage.removeItem('csvImportResults'); // Clear stored results
    onClose();
  };

  const getFieldType = (systemField: string): string => {
    const field = SYSTEM_FIELDS.find(f => f.value === systemField);
    return field?.type || 'text';
  };

  return (
    <Dialog 
      open={open} 
      onClose={importing || (importResults.success > 0 || importResults.failed > 0) ? undefined : handleClose} 
      maxWidth="lg" 
      fullWidth
      disableEscapeKeyDown={importing || importCompleted}
      disableBackdropClick={true}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Importar Pedidos do CSV (Avançado)</Typography>
          <IconButton 
            onClick={handleClose} 
            size="small"
            disabled={importing}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step 1: File Upload */}
        {activeStep === 0 && (
          <Box textAlign="center" py={4}>
            <input
              accept=".csv"
              style={{ display: 'none' }}
              id="csv-file-input"
              type="file"
              onChange={handleFileSelect}
            />
            <label htmlFor="csv-file-input">
              <Button
                variant="contained"
                component="span"
                size="large"
                startIcon={<CloudUploadIcon />}
              >
                Selecionar Arquivo CSV
              </Button>
            </label>
            
            {file && (
              <Box mt={2}>
                <Chip
                  icon={<FileUploadIcon />}
                  label={file.name}
                  color="primary"
                  onDelete={() => setFile(null)}
                />
              </Box>
            )}
            
            <Alert severity="info" sx={{ mt: 3, textAlign: 'left' }}>
              <Typography variant="body2">
                O arquivo CSV deve conter as seguintes colunas:
                <ul>
                  <li>ID Venda (obrigatório)</li>
                  <li>Cliente (obrigatório)</li>
                  <li>Telefone (obrigatório)</li>
                  <li>Valor Venda (obrigatório)</li>
                  <li>Código de Rastreio</li>
                  <li>Vendedor (pode ser mapeado para vendedores existentes)</li>
                  <li>Operador (pode ser mapeado para operadores existentes)</li>
                  <li>Oferta (pode ser mapeado para kits existentes)</li>
                </ul>
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Step 2: Field Mapping */}
        {activeStep === 1 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              Mapeie os campos do CSV para os campos do sistema. Para Vendedor, Operador e Oferta, você pode mapear para registros existentes.
            </Alert>
            
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Campo do CSV</TableCell>
                    <TableCell>Exemplo de Dado</TableCell>
                    <TableCell>Campo do Sistema</TableCell>
                    <TableCell>Mapeamento Específico</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {csvHeaders.map((header) => {
                    const mapping = fieldMappings.find(m => m.csvField === header);
                    const fieldType = mapping ? getFieldType(mapping.systemField) : 'text';
                    
                    return (
                      <TableRow key={header}>
                        <TableCell>{header}</TableCell>
                        <TableCell>
                          <Typography variant="caption" color="textSecondary">
                            {csvData[0]?.[header] || '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <FormControl size="small" fullWidth>
                            <Select
                              value={mapping?.systemField || ''}
                              onChange={(e) => handleMappingChange(header, e.target.value)}
                            >
                              <MenuItem value="">
                                <em>Não mapear</em>
                              </MenuItem>
                              {SYSTEM_FIELDS.map((field) => (
                                <MenuItem key={field.value} value={field.value}>
                                  {field.label}
                                  {field.required && <Chip size="small" label="Obrigatório" sx={{ ml: 1 }} />}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          {mapping && fieldType === 'kit' && (
                            <Autocomplete
                              size="small"
                              options={kits}
                              getOptionLabel={(option) => option.name}
                              value={kits.find(k => k.id === mapping.mappedId) || null}
                              onChange={(_, newValue) => handleMappingIdChange(header, newValue?.id || '')}
                              renderInput={(params) => (
                                <TextField {...params} placeholder="Selecione um kit (opcional)" />
                              )}
                            />
                          )}
                          {mapping && fieldType === 'seller' && (
                            <Autocomplete
                              size="small"
                              options={sellers}
                              getOptionLabel={(option) => option.name}
                              value={sellers.find(s => s.id === mapping.mappedId) || null}
                              onChange={(_, newValue) => handleMappingIdChange(header, newValue?.id || '')}
                              renderInput={(params) => (
                                <TextField {...params} placeholder="Selecione um vendedor (opcional)" />
                              )}
                            />
                          )}
                          {mapping && fieldType === 'operator' && (
                            <Autocomplete
                              size="small"
                              options={operators}
                              getOptionLabel={(option) => option.name}
                              value={operators.find(o => o.id === mapping.mappedId) || null}
                              onChange={(_, newValue) => handleMappingIdChange(header, newValue?.id || '')}
                              renderInput={(params) => (
                                <TextField {...params} placeholder="Selecione um operador (opcional)" />
                              )}
                            />
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
            
            <Box mt={2} display="flex" justifyContent="space-between">
              <Button onClick={() => setActiveStepWithLog(0)}>Voltar</Button>
              <Button
                variant="contained"
                onClick={extractUniqueUsers}
                disabled={fieldMappings.length === 0}
              >
                Continuar
              </Button>
            </Box>
          </Box>
        )}

        {/* Step 3: User Mapping */}
        {activeStep === 2 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              Mapeie os nomes encontrados no CSV para os usuários do sistema. 
              Isso garante que os pedidos sejam atribuídos corretamente aos vendedores e cobradores.
            </Alert>
            
            {uniqueSellers.length > 0 && (
              <Box mb={3}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Vendedores encontrados no CSV
                </Typography>
                <Paper sx={{ p: 2 }}>
                  {uniqueSellers.map((csvName) => (
                    <Box key={csvName} sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                      <Typography sx={{ minWidth: 200 }}>{csvName}</Typography>
                      <Autocomplete
                        sx={{ flex: 1 }}
                        options={sellers}
                        getOptionLabel={(option) => option.name}
                        value={sellers.find(s => s.id === userMappings.sellers[csvName]) || null}
                        onChange={(_, newValue) => {
                          setUserMappings(prev => ({
                            ...prev,
                            sellers: {
                              ...prev.sellers,
                              [csvName]: newValue?.id || ''
                            }
                          }));
                        }}
                        renderInput={(params) => (
                          <TextField {...params} placeholder="Selecione um vendedor" size="small" />
                        )}
                      />
                    </Box>
                  ))}
                </Paper>
              </Box>
            )}
            
            {uniqueOperators.length > 0 && (
              <Box mb={3}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Cobradores encontrados no CSV
                </Typography>
                <Paper sx={{ p: 2 }}>
                  {uniqueOperators.map((csvName) => (
                    <Box key={csvName} sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                      <Typography sx={{ minWidth: 200 }}>{csvName}</Typography>
                      <Autocomplete
                        sx={{ flex: 1 }}
                        options={operators}
                        getOptionLabel={(option) => option.name}
                        value={operators.find(o => o.id === userMappings.operators[csvName]) || null}
                        onChange={(_, newValue) => {
                          setUserMappings(prev => ({
                            ...prev,
                            operators: {
                              ...prev.operators,
                              [csvName]: newValue?.id || ''
                            }
                          }));
                        }}
                        renderInput={(params) => (
                          <TextField {...params} placeholder="Selecione um cobrador" size="small" />
                        )}
                      />
                    </Box>
                  ))}
                </Paper>
              </Box>
            )}
            
            {uniqueSellers.length === 0 && uniqueOperators.length === 0 && (
              <Alert severity="warning">
                Nenhum vendedor ou cobrador foi encontrado no CSV. 
                Verifique se os campos foram mapeados corretamente na etapa anterior.
              </Alert>
            )}
            
            <Box mt={2} display="flex" justifyContent="space-between">
              <Button onClick={() => setActiveStepWithLog(1)}>Voltar</Button>
              <Button
                variant="contained"
                onClick={prepareImportPreview}
                color="primary"
              >
                Continuar
              </Button>
            </Box>
          </Box>
        )}

        {/* Step 4: Review and Import */}
        {console.log('About to render step content, activeStep:', activeStep)}
        {activeStep === 3 && (
          <Box>
            {/* Debug info */}
            {console.log('Step 3 render:', {
              activeStep,
              importing,
              importCompleted,
              importResults,
              importResultsRef: importResultsRef.current,
              showPreview: !importing && !importCompleted && importResults.success === 0,
              showResults: !importing && (importResults.success > 0 || importResults.failed > 0)
            })}
            {!importing && !importCompleted && importResults.success === 0 && importResults.failed === 0 && (
              <>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Revise os dados antes de importar. Mostrando as primeiras 10 linhas.
                </Alert>
                
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={skipDuplicates}
                      onChange={(e) => setSkipDuplicates(e.target.checked)}
                    />
                  }
                  label="Pular pedidos duplicados"
                  sx={{ mb: 2 }}
                />
                
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>ID Venda</TableCell>
                        <TableCell>Cliente</TableCell>
                        <TableCell>Telefone</TableCell>
                        <TableCell>Valor</TableCell>
                        <TableCell>Mensagens</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {importPreview.map((preview, index) => {
                        const mappedOrder = mapRowToOrder(preview.row);
                        
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              {preview.status === 'valid' && <CheckCircleIcon color="success" />}
                              {preview.status === 'warning' && <WarningIcon color="warning" />}
                              {preview.status === 'error' && <ErrorIcon color="error" />}
                            </TableCell>
                            <TableCell>{mappedOrder.orderNumber || '-'}</TableCell>
                            <TableCell>{mappedOrder.customerName || '-'}</TableCell>
                            <TableCell>{mappedOrder.customerPhone || '-'}</TableCell>
                            <TableCell>
                              {mappedOrder.total 
                                ? `R$ ${mappedOrder.total.toFixed(2).replace('.', ',')}`
                                : '-'
                              }
                            </TableCell>
                            <TableCell>
                              {preview.messages.map((msg, idx) => (
                                <Typography key={idx} variant="caption" display="block">
                                  {msg}
                                </Typography>
                              ))}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
                
                <Box mt={2} display="flex" justifyContent="space-between">
                  <Button onClick={() => setActiveStepWithLog(2)}>Voltar</Button>
                  <Button
                    variant="contained"
                    onClick={handleImport}
                    color="primary"
                  >
                    Importar {csvData.length} Pedidos
                  </Button>
                </Box>
              </>
            )}
            
            {importing && (
              <Box>
                <Typography variant="h6" gutterBottom>Importando...</Typography>
                <LinearProgress variant="determinate" value={importProgress} sx={{ mb: 2 }} />
                <Typography variant="body2" color="textSecondary">
                  {Math.round(importProgress)}% concluído
                </Typography>
              </Box>
            )}
            
            {console.log('Checking if should show results:', {
              importing,
              importResults,
              showResults: !importing && (importResults.success > 0 || importResults.failed > 0)
            })}
            {!importing && (importResults.success > 0 || importResults.failed > 0) && (
              <Box>
                <Paper elevation={3} sx={{ p: 3, mb: 2, bgcolor: 'background.paper' }}>
                  <Typography variant="h5" gutterBottom align="center" color="primary">
                    Resultado da Importação
                  </Typography>
                  
                  <Grid container spacing={2} sx={{ mt: 2 }}>
                    <Grid item xs={6}>
                      <Paper sx={{ p: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
                        <Typography variant="h3" align="center">
                          {importResults.success}
                        </Typography>
                        <Typography variant="subtitle1" align="center">
                          Importados com Sucesso
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={6}>
                      <Paper sx={{ p: 2, bgcolor: importResults.failed > 0 ? 'error.light' : 'grey.300', color: importResults.failed > 0 ? 'error.contrastText' : 'text.primary' }}>
                        <Typography variant="h3" align="center">
                          {importResults.failed}
                        </Typography>
                        <Typography variant="subtitle1" align="center">
                          Falharam
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  <Typography variant="body1" sx={{ mt: 3, textAlign: 'center' }}>
                    Total processado: {importResults.success + importResults.failed} de {csvData.length} pedidos
                  </Typography>
                </Paper>
                
                {importResults.failed > 0 && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    <AlertTitle>Erros encontrados durante a importação:</AlertTitle>
                    <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
                      {importResults.errors.map((error, index) => (
                        <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                          • {error}
                        </Typography>
                      ))}
                    </Box>
                  </Alert>
                )}
                
                {importResults.success > 0 && (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    Os pedidos importados com sucesso já estão disponíveis na lista de pedidos.
                  </Alert>
                )}
                
                <Box mt={3} display="flex" justifyContent="center">
                  <Button
                    variant="contained"
                    onClick={() => {
                      console.log('User clicked Fechar e Ver Pedidos');
                      // Force close by resetting everything
                      setActiveStepWithLog(0);
                      setFile(null);
                      setCsvData([]);
                      setCsvHeaders([]);
                      setFieldMappings([]);
                      setImportPreview([]);
                      setImportCompleted(false);
                      importCompletedRef.current = false;
                      setImportResults({ success: 0, failed: 0, errors: [] });
                      importResultsRef.current = { success: 0, failed: 0, errors: [] };
                      setUniqueSellers([]);
                      setUniqueOperators([]);
                      setUserMappings({ sellers: {}, operators: {} });
                      localStorage.removeItem('csvImportResults'); // Clear stored results
                      onClose();
                    }}
                    color="primary"
                    size="large"
                  >
                    Fechar e Ver Pedidos
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose}>
          {importResults.success > 0 ? 'Fechar' : 'Cancelar'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderImportDialogEnhanced;