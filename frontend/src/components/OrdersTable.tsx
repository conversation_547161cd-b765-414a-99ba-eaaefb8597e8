import React, { useState, useEffect } from 'react';
import {
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  Box,
  Chip,
  Typography,
  TableSortLabel,
  Tooltip,
  Drawer,
  IconButton,
  Grid,
  Divider,
  Button,
  Stack,
  TextField,
  List,
  ListItem,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { Order, COLUMN_LABELS } from '../types/Order';
import StatusChip from './StatusChip';
import TrackingDetails from './TrackingDetails';
import AdminEditForm from './AdminEditForm';
import UserEditForm from './UserEditForm';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import PendingOutlinedIcon from '@mui/icons-material/PendingOutlined';
import HandshakeOutlinedIcon from '@mui/icons-material/HandshakeOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';
import ReplayOutlinedIcon from '@mui/icons-material/ReplayOutlined';
import InventoryOutlinedIcon from '@mui/icons-material/InventoryOutlined';
import KeyboardReturnOutlinedIcon from '@mui/icons-material/KeyboardReturnOutlined';
import AccountBalanceOutlinedIcon from '@mui/icons-material/AccountBalanceOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import useUserPermissions from '../hooks/useUserPermissions';
import { useOrderData } from '../contexts/OrderDataContext';
import OrderService from '../services/OrderService';
import PaymentFinalizationDialog, { PaymentData } from './PaymentFinalizationDialog';
import { ShippingLabelDialog } from './ShippingLabelDialog';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import RefreshIcon from '@mui/icons-material/Refresh';
import { shippingService } from '../services/ShippingService';
import { useSnackbar } from 'notistack';

interface OrdersTableProps {
  orders: Order[];
  onOrderUpdate?: (updatedOrder: Order) => void;
  selectedOrderIds?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  showSelection?: boolean;
}

// Função auxiliar para converter data BR (DD/MM/YYYY HH:MM) para formato comparável
const parseDate = (dateStr: string): Date => {
  if (!dateStr) return new Date(0); // Data mínima para valores vazios

  // Handle format: "DD/MM/YYYY HH:MM"
  const dateTimeParts = dateStr.split(' ');
  if (dateTimeParts.length === 2) {
    const dateParts = dateTimeParts[0].split('/');
    const timeParts = dateTimeParts[1].split(':');
    
    if (dateParts.length === 3 && timeParts.length === 2) {
      const [day, month, year] = dateParts.map(Number);
      const [hours, minutes] = timeParts.map(Number);
      return new Date(year, month - 1, day, hours, minutes);
    }
  }

  // Try parsing just date if no time component
  const parts = dateStr.split('/');
  // Se não tiver formato DD/MM/YYYY, retornar como string
  if (parts.length !== 3) return new Date(dateStr);

  const [day, month, year] = parts.map(Number);
  return new Date(year, month - 1, day);
};

// Função para formatar tempo relativo
const formatRelativeTime = (dateStr: string): string => {
  if (!dateStr) return '-';

  try {
    const date = parseDate(dateStr);
    if (isNaN(date.getTime())) return dateStr;

    return `há ${formatDistanceToNow(date, { locale: ptBR })}`;
  } catch (error: any) {
    console.error('Erro ao formatar data relativa:', error);
    return dateStr;
  }
};

// Calculate days overdue for payment promises
const calculateDaysOverdue = (nextPaymentDate: string | undefined): number | null => {
  if (!nextPaymentDate) return null;
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const dueDate = new Date(nextPaymentDate);
  dueDate.setHours(0, 0, 0, 0);
  
  if (dueDate >= today) return null; // Not overdue
  
  const diffTime = today.getTime() - dueDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
};

// Verifica se o pedido está em uma situação crítica
const isCriticalStatus = (status: string): boolean => {
  if (!status) return false;

  const lowerStatus = status.toLowerCase();
  return (
    lowerStatus.includes('confirmar entrega') ||
    lowerStatus.includes('entrega falha') ||
    lowerStatus.includes('retirar nos correios')
  );
};

// Tipo para acompanhar o estado de ordenação
type Order_Direction = 'asc' | 'desc';
type OrderBy = 'orderNumber' | 'createdAt' | 'customerName' | 'status' | 'updatedAt' | 'lastContactDate' | 'paymentReceivedDate' | 'sellerName' | 'collectorName' | '';

interface CobrancaHistorico {
  data: string;
  observacao: string;
  situacao: string;
  usuario?: string; // Nome do usuário que fez a ação
}

const OrdersTable: React.FC<OrdersTableProps> = ({ 
  orders, 
  onOrderUpdate,
  selectedOrderIds = [],
  onSelectionChange,
  showSelection = false
}) => {
  // Use context for order updates
  const { updateOrderStatus, updateOrder, deleteOrder } = useOrderData();
  
  // Definir ordenação padrão por data de venda, do mais recente para o mais antigo
  const [orderBy, setOrderBy] = useState<OrderBy>('createdAt');
  const [order, setOrder] = useState<Order_Direction>('desc');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [newObservacao, setNewObservacao] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [permanentDeleteDialogOpen, setPermanentDeleteDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [promiseDateDialogOpen, setPromiseDateDialogOpen] = useState(false);
  const [promiseDate, setPromiseDate] = useState<string>('');
  const [pendingStatus, setPendingStatus] = useState<string>('');
  const [shippingLabelDialogOpen, setShippingLabelDialogOpen] = useState(false);

  // Estado para armazenar os valores editados
  const [editedValues, setEditedValues] = useState<Partial<Order>>({});

  // Utilizar o hook de permissões
  const {
    isAdmin,
    isSeller,
    isCollector: isOperator,
    canDeleteOrders,
    canViewDeletedOrders,
    filterOrdersByPermission
  } = useUserPermissions();

  // Snackbar hook
  const { enqueueSnackbar } = useSnackbar();

  // Histórico de cobrança do pedido
  const [historicoCobranca, setHistoricoCobranca] = useState<CobrancaHistorico[]>([]);

  // Helper function to sort histórico by date (newest first)
  const sortHistorico = (historico: CobrancaHistorico[]): CobrancaHistorico[] => {
    return [...historico].sort((a, b) => {
      const dateA = parseDate(a.data);
      const dateB = parseDate(b.data);
      return dateB.getTime() - dateA.getTime();
    });
  };

  // Load history when order is selected or observation changes
  useEffect(() => {
    if (selectedOrder) {
      // Load status history from the order
      const history: CobrancaHistorico[] = [];
      
      // Add observations as activity log entries
      if (selectedOrder.observation) {
        // Split observations by newline and add each as a separate entry
        const observations = selectedOrder.observation.split('\n').filter(obs => obs.trim());
        observations.forEach(obs => {
          // Extract date if present in the format "DD/MM/YYYY HH:MM - "
          const dateMatch = obs.match(/^(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}) - (.*)$/);
          if (dateMatch) {
            const [, date, content] = dateMatch;
            // Check if it's a status change
            const statusChangeMatch = content.match(/^(.+) → (.+) \(por (.+)\)$/);
            if (statusChangeMatch) {
              const [, fromStatus, toStatus, userName] = statusChangeMatch;
              history.push({
                data: date,
                observacao: `Status: ${fromStatus} → ${toStatus}`,
                situacao: toStatus,
                usuario: userName
              });
            } else {
              // Check if content has (por username) pattern
              const userMatch = content.match(/^(.*) \(por (.+)\)$/);
              if (userMatch) {
                const [, message, userName] = userMatch;
                history.push({
                  data: date,
                  observacao: message,
                  situacao: 'Log de Atividade',
                  usuario: userName
                });
              } else {
                history.push({
                  data: date,
                  observacao: content,
                  situacao: 'Log de Atividade'
                });
              }
            }
          }
        });
      }
      
      // Add status history if available
      if (selectedOrder.statusHistory && selectedOrder.statusHistory.length > 0) {
        selectedOrder.statusHistory.forEach(item => {
          history.push({
            data: formatDate(item.createdAt),
            observacao: `Status alterado de ${item.previousStatus} para ${item.newStatus}${item.changedByName ? ` por ${item.changedByName}` : ''}`,
            situacao: item.newStatus
          });
        });
      }
      
      // Add billing history if available
      if (selectedOrder.billingHistory && selectedOrder.billingHistory.length > 0) {
        selectedOrder.billingHistory.forEach(item => {
          history.push({
            data: item.date,
            observacao: item.notes || 'Registro de pagamento',
            situacao: 'Cobrança'
          });
        });
      }
      
      // Sort by date descending (most recent first)
      history.sort((a, b) => {
        const dateA = parseDate(a.data);
        const dateB = parseDate(b.data);
        return dateB.getTime() - dateA.getTime();
      });
      
      setHistoricoCobranca(sortHistorico(history));
    }
  }, [selectedOrder, selectedOrder?.observation]);

  // Helper function to format date with time
  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (error: any) {
      return dateStr;
    }
  };

  // Helper function to format date without time for table display
  const formatDateOnly = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    } catch (error: any) {
      return dateStr;
    }
  };

  // Helper function to get Zap status color
  const getZapStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      'Ativo': '#4caf50',
      'Aquecendo': '#ff9800',
      'Pronto': '#2196f3',
      'Bloqueado': '#f44336',
      'EmAnalise': '#9c27b0',
      'Recuperado': '#00bcd4',
      'StandBy': '#607d8b',
    };
    return colors[status] || '#757575';
  };

  // Função para lidar com solicitações de ordenação
  const handleRequestSort = (property: OrderBy) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Função para extrair a data da última cobrança do campo observation
  const extractLastBillingDate = (observation: string | undefined): string | null => {
    if (!observation) return null;
    
    const lines = observation.split('\n').filter(line => line.includes('Observação:'));
    if (lines.length > 0) {
      const lastLine = lines[lines.length - 1];
      const dateMatch = lastLine.match(/^(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2})/);
      if (dateMatch) {
        return dateMatch[1];
      }
    }
    return null;
  };

  // Função para criar um comparador de ordenação
  const getComparator = (
    order: Order_Direction,
    orderBy: OrderBy
  ): (a: Order, b: Order) => number => {
    return order === 'desc'
      ? (a, b) => -compareValues(a, b, orderBy) // Invertido para ordem decrescente (mais recente primeiro)
      : (a, b) => compareValues(a, b, orderBy);
  };

  // Função para comparar valores
  const compareValues = (a: Order, b: Order, orderBy: OrderBy): number => {
    if (orderBy === '') return 0;

    // Ordenação por campos de data
    if (orderBy === 'createdAt' || orderBy === 'updatedAt' || orderBy === 'paymentReceivedDate') {
      // Tratar valores vazios ou nulos
      const valueA = a[orderBy] as string || '';
      const valueB = b[orderBy] as string || '';

      // Se ambos os valores estão vazios, considerar iguais
      if (!valueA && !valueB) return 0;
      // Se apenas um está vazio, colocar o vazio por último
      if (!valueA) return 1;
      if (!valueB) return -1;

      const dateA = parseDate(valueA);
      const dateB = parseDate(valueB);

      // Comparar as datas (ordem normal - o sinal será invertido na função getComparator quando order='desc')
      return dateA.getTime() - dateB.getTime();
    }

    // Ordenação especial para dataNegociacao (última cobrança)
    if (orderBy === 'lastContactDate') {
      const dateStrA = extractLastBillingDate(a.observation);
      const dateStrB = extractLastBillingDate(b.observation);

      // Se ambos os valores estão vazios, considerar iguais
      if (!dateStrA && !dateStrB) return 0;
      // Se apenas um está vazio, colocar o vazio por último
      if (!dateStrA) return 1;
      if (!dateStrB) return -1;

      const dateA = parseDate(dateStrA);
      const dateB = parseDate(dateStrB);

      // Comparar as datas
      return dateA.getTime() - dateB.getTime();
    }

    // Ordenação por ID Venda (numérico ou alfanumérico)
    if (orderBy === 'orderNumber') {
      // Try to parse as number first
      const numA = parseFloat(a.orderNumber);
      const numB = parseFloat(b.orderNumber);
      
      // If both are valid numbers, compare numerically
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB;
      }
      
      // Otherwise, compare as strings (alphanumeric)
      const strA = a.orderNumber || '';
      const strB = b.orderNumber || '';
      return strA.localeCompare(strB, 'pt-BR', { numeric: true, sensitivity: 'base' });
    }

    // Ordenação por campos de texto
    if (orderBy === 'customerName' || orderBy === 'status' || orderBy === 'sellerName' || orderBy === 'collectorName') {
      const valueA = (a[orderBy] as string || '').toLowerCase();
      const valueB = (b[orderBy] as string || '').toLowerCase();
      
      if (valueA < valueB) return -1;
      if (valueA > valueB) return 1;
      return 0;
    }

    return 0;
  };

  // Garantir que todos os pedidos tenham valores numéricos válidos
  const validatedOrders = orders.map(order => ({
    ...order,
    total: order.total !== undefined && order.total !== null ? order.total : 0,
    paymentReceivedAmount: order.paymentReceivedAmount !== undefined && order.paymentReceivedAmount !== null ? order.paymentReceivedAmount : 0,
    pagamentoParcial: order.pagamentoParcial !== undefined && order.pagamentoParcial !== null ? order.pagamentoParcial : 0
  }));

  // Aplicar ordenação aos pedidos - sem limitação de quantidade
  const sortedOrders = orderBy
    ? [...validatedOrders].sort(getComparator(order, orderBy))
    : validatedOrders;


  const createSortHandler = (property: OrderBy) => () => {
    handleRequestSort(property);
  };

  // Função para abrir o painel com os detalhes do pedido
  const handleOpenDrawer = async (order: Order) => {
    try {
      // Always fetch the latest order data to ensure we have the most recent observation
      console.log('[handleOpenDrawer] Opening order:', order.id);
      console.log('[handleOpenDrawer] Order from table row observation:', order.observation);
      
      const freshOrder = await OrderService.getOrder(order.id);
      console.log('[handleOpenDrawer] Fresh order from backend:', freshOrder);
      console.log('[handleOpenDrawer] Fresh order observation:', freshOrder.observation);
      
      setSelectedOrder(freshOrder);
      setDrawerOpen(true);
      setEditMode(false);
      setNewObservacao('');
    } catch (error: any) {
      console.error('[handleOpenDrawer] Error fetching order:', error);
      // Fallback to using the order from the list
      const currentOrder = orders.find(o => o.id === order.id) || order;
      console.log('[handleOpenDrawer] Using fallback order:', currentOrder.id);
      console.log('[handleOpenDrawer] Fallback order observation:', currentOrder.observation);
      setSelectedOrder(currentOrder);
      setDrawerOpen(true);
      setEditMode(false);
      setNewObservacao('');
    }
  };

  // Função para fechar o painel
  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setEditMode(false);
    setSelectedOrder(null);
  };

  // Alternar modo de edição
  const handleEditClick = () => {
    if (!editMode) {
      // Inicializar os valores editados com os valores atuais do pedido
      setEditedValues(selectedOrder || {});
    } else {
      // Limpar os valores editados ao sair do modo de edição
      setEditedValues({});
    }
    setEditMode(!editMode);
  };

  // Função para salvar as alterações no pedido
  const handleSaveEdit = async () => {
    console.log('=== handleSaveEdit CALLED ===');
    console.log('selectedOrder exists?', !!selectedOrder);
    console.log('onOrderUpdate exists?', !!onOrderUpdate);
    console.log('selectedOrder:', selectedOrder);
    console.log('editedValues:', editedValues);
    console.log('editedValues keys:', Object.keys(editedValues));
    console.log('editedValues is empty?', Object.keys(editedValues).length === 0);
    
    if (!selectedOrder) {
      console.error('No selectedOrder!');
      alert('Erro: Nenhum pedido selecionado');
      return;
    }
    
    if (!onOrderUpdate) {
      console.error('No onOrderUpdate callback!');
      alert('Erro: Função de atualização não disponível');
      return;
    }
    
    try {
      // Criar o pedido atualizado com os valores editados
      const updatedOrder = {
        ...selectedOrder,
        ...editedValues,
        ultimaAtualizacao: new Date().toLocaleDateString('pt-BR')
      };

      // Se o status for "Completo" e o valor recebido estiver vazio, usar o valor da venda
      if (
        typeof updatedOrder.status === 'string' &&
        updatedOrder.status.toLowerCase() === 'completo' &&
        (!updatedOrder.paymentReceivedAmount || updatedOrder.paymentReceivedAmount === 0) &&
        updatedOrder.total
      ) {
        updatedOrder.paymentReceivedAmount = updatedOrder.total;
      }

      console.log('Saving edited values:', editedValues);
      console.log('Updated order:', updatedOrder);
      
      // Create activity log entry
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
      
      const userInfo = localStorage.getItem('unified_user_info');
      const userName = userInfo ? JSON.parse(userInfo).fullName : 'Sistema';
      
      // Build a description of what was changed
      const changedFields = Object.keys(editedValues).map(key => {
        const fieldNames: Record<string, string> = {
          customerName: 'Cliente',
          customerPhone: 'Telefone',
          total: 'Valor da Venda',
          paymentReceivedAmount: 'Valor Recebido',
          status: 'Status',
          fullAddress: 'Endereço',
          observacao: 'Observação'
        };
        return fieldNames[key] || key;
      }).join(', ');
      
      const logEntry = `${formattedDate} - Informações do pedido atualizadas: ${changedFields} (por ${userName})`;
      
      // Add to observation field
      const updatedObservation = selectedOrder.observation 
        ? `${selectedOrder.observation}\n${logEntry}`
        : logEntry;
      
      updatedOrder.observation = updatedObservation;
      
      console.log('Calling onOrderUpdate...');

      // Chamar o callback de atualização
      await onOrderUpdate(updatedOrder);
      
      console.log('onOrderUpdate completed successfully');

      // Atualizar o pedido selecionado
      setSelectedOrder(updatedOrder);

      // Adicionar registro ao histórico de cobrança
      const newHistoricoItem: CobrancaHistorico = {
        data: formattedDate,
        observacao: `Informações do pedido atualizadas: ${changedFields}`,
        situacao: updatedOrder.status,
        usuario: userName
      };

      setHistoricoCobranca(sortHistorico([newHistoricoItem, ...historicoCobranca]));

      // Limpar os valores editados e sair do modo de edição
      setEditedValues({});
      setEditMode(false);
      
      // Fechar o drawer após salvar com sucesso
      setDrawerOpen(false);

      // Mostrar alerta de sucesso
      alert('Alterações salvas com sucesso!');
    } catch (error: any) {
      console.error('Error in handleSaveEdit:', error);
      alert('Erro ao salvar alterações: ' + (error.message || 'Erro desconhecido'));
    }
  };

  // Função para obter transições válidas para um status
  const getValidTransitions = (currentStatus: string): string[] => {
    const normalized = currentStatus?.toLowerCase().replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/\s+/g, '');
    
    const transitions: Record<string, string[]> = {
      'analise': ['Separação', 'Cancelado'],
      'separacao': ['Trânsito', 'Cancelado'],
      'transito': ['Confirmar Entrega', 'Entrega Falha', 'Retirar Correios', 'Devolvido Correios'],
      'confirmarentrega': ['Completo', 'Pagamento Pendente', 'Negociação', 'Promessa', 'Parcial'],
      'pagamentopendente': ['Completo', 'Parcial', 'Negociação', 'Promessa', 'Frustrado'],
      'negociacao': ['Recuperação', 'Cancelado', 'Completo', 'Parcial', 'Frustrado', 'Promessa'],
      'promessa': ['Recuperação', 'Cancelado', 'Completo', 'Parcial', 'Frustrado', 'Negociação'],
      'parcial': ['Completo', 'Negociação', 'Promessa'],
      'recuperacao': ['Trânsito', 'Negociação', 'Cancelado', 'Completo', 'Parcial'],
      'entregafalha': ['Recuperação', 'Negociação', 'Frustrado'],
      'retirarcorreios': ['Frustrado', 'Cancelado'],
      'devolvidocorreios': ['Frustrado', 'Cancelado'],
      // Estados finais - não permitem transições
      'completo': [],
      'frustrado': [],
      'cancelado': []
    };
    
    return transitions[normalized] || [];
  };

  // Função para atualizar os valores editados
  const handleEditChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    console.log(`Editing field ${name} with value ${value}`);

    setEditedValues(prev => {
      const newValues = {
        ...prev,
        [name]: value
      };
      console.log('Updated editedValues:', newValues);
      return newValues;
    });
  };

  // Função para atualizar o status do pedido
  const handleUpdateStatus = async (newStatus: string) => {
    console.log('handleUpdateStatus called with:', newStatus);
    
    if (!selectedOrder) {
      console.error('No order selected');
      alert('Por favor, selecione um pedido primeiro');
      return;
    }
    
    // Check if status is Promessa and open date dialog
    if (newStatus === 'Promessa') {
      setPendingStatus(newStatus);
      setPromiseDateDialogOpen(true);
      return;
    }
    
    // Criar o pedido atualizado com o novo status
    let updatedOrderData: any = {
      status: newStatus
    };
    
    try {
        // Adicionar registro ao histórico de cobrança
        const currentDate = new Date();
        const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
        
        // Obter nome do usuário atual
        const userInfo = localStorage.getItem('unified_user_info');
        const userName = userInfo ? JSON.parse(userInfo).name : 'Sistema';
        const observacao = `Status atualizado para: ${newStatus} (por ${userName})`;

        // Se o status for "Completo" e o valor recebido estiver vazio, usar o valor da venda
        if (
          newStatus.toLowerCase() === 'completo' &&
          (!selectedOrder.paymentReceivedAmount || selectedOrder.paymentReceivedAmount === 0) &&
          selectedOrder.total
        ) {
          updatedOrderData.paymentReceivedAmount = selectedOrder.total;
        }
        
        // Add to observation field with simplified format for status changes
        const previousStatus = selectedOrder.status || 'Sem status';
        const statusChangeLog = `${previousStatus} → ${newStatus}`;
        const logEntry = `${formattedDate} - ${statusChangeLog} (por ${userName})`;
        const updatedObservation = selectedOrder.observation 
          ? `${selectedOrder.observation}\n${logEntry}`
          : logEntry;
        updatedOrderData.observation = updatedObservation;
        
        // Note: historico field no longer exists in unified schema
        // Observations are now stored in the observation field only

        // The previousStatus is already declared above, just create the log
        
        // Update everything in a single call to avoid race conditions
        const changes = {
          status: newStatus,
          paymentReceivedAmount: updatedOrderData.paymentReceivedAmount,
          observation: updatedObservation
        };
        
        await updateOrder(selectedOrder.id, changes);
        
        // Fechar o drawer após sucesso
        setDrawerOpen(false);
        setSelectedOrder(null);

        const newHistoricoItem: CobrancaHistorico = {
          data: formattedDate,
          observacao: observacao.replace(` (por ${userName})`, ''),
          situacao: newStatus,
          usuario: userName
        };

        setHistoricoCobranca(sortHistorico([newHistoricoItem, ...historicoCobranca]));
        
        // Show success message
        alert(`Status atualizado com sucesso para: ${newStatus}`);
      } catch (error: any) {
        console.error('Erro ao atualizar status:', error);
        console.error('Detalhes do erro:', {
          message: error?.message,
          response: error?.response,
          responseData: error?.response?.data,
          status: error?.response?.status,
          selectedOrderId: selectedOrder?.id,
          newStatus,
          updatedOrderData
        });
        
        // Mostrar mensagem de erro mais específica
        const errorMessage = error?.response?.data?.message || error?.message || 'Erro ao atualizar status';
        console.error('Erro completo:', error);
        console.error('Response data:', error?.response?.data);
        alert(`Erro: ${errorMessage}\n\nDetalhes: ${JSON.stringify(error?.response?.data || error, null, 2)}`);
      }
    }

  // Função para lidar com a confirmação do pagamento
  const handlePaymentConfirm = async (paymentData: PaymentData) => {
    console.log('handlePaymentConfirm called with:', paymentData);
    console.log('selectedOrder:', selectedOrder);
    console.log('selectedOrder.id:', selectedOrder?.id);
    console.log('selectedOrder.orderNumber:', selectedOrder?.orderNumber);
    
    if (selectedOrder && selectedOrder.id) {
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

      // Determinar o novo status
      const previousStatus = selectedOrder.status || 'Sem status';
      let newStatus = selectedOrder.status;
      let observacao = '';
      let statusChange = false;

      if (paymentData.paymentType === 'full') {
        newStatus = 'Completo';
        statusChange = true;
        observacao = `Pagamento total recebido: R$ ${paymentData.amountPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} via ${paymentData.paymentMethod}`;
      } else if (paymentData.paymentType === 'partial') {
        if (paymentData.isNegotiationComplete) {
          newStatus = 'Completo';
          statusChange = true;
          observacao = `Negociação finalizada - Pagamento acordado: R$ ${paymentData.amountPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} via ${paymentData.paymentMethod}`;
        } else {
          newStatus = 'Negociação';
          statusChange = previousStatus !== 'Negociação';
          observacao = `Pagamento parcial recebido: R$ ${paymentData.amountPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} via ${paymentData.paymentMethod}`;
        }
      } else {
        observacao = 'Tentativa de cobrança sem sucesso';
      }

      // Adicionar notas se houver
      if (paymentData.notes) {
        observacao += ` - ${paymentData.notes}`;
      }
      
      // Adicionar nome do usuário atual
      const userInfo = localStorage.getItem('unified_user_info');
      const userName = userInfo ? JSON.parse(userInfo).fullName : 'Sistema';
      observacao += ` (por ${userName})`;

      try {
        // Criar o pedido atualizado
        const currentPaymentReceivedAmount = selectedOrder.paymentReceivedAmount || 0;
        const newPaymentReceivedAmount = currentPaymentReceivedAmount + paymentData.amountPaid;
        const remainingBalance = selectedOrder.total - newPaymentReceivedAmount;
        
        console.log('Payment calculation:', {
          currentPaymentReceivedAmount,
          amountPaid: paymentData.amountPaid,
          newPaymentReceivedAmount,
          total: selectedOrder.total,
          remainingBalance,
          newStatus
        });
        
        const updatedOrder = {
          ...selectedOrder,
          status: newStatus,
          paymentReceivedAmount: newPaymentReceivedAmount,
          // Add to payment history
          paymentHistory: [
            ...(selectedOrder.paymentHistory || []),
            {
              id: Date.now().toString(),
              date: formattedDate,
              amount: paymentData.amountPaid,
              method: paymentData.paymentMethod,
              type: paymentData.paymentType as 'full' | 'partial',
              collector: userName,
              notes: paymentData.notes,
              remainingBalance: remainingBalance
            }
          ]
        };

        // Atualizar usando o contexto - updateOrder espera (orderId, changes)
        const changes: any = {
          status: newStatus,
          paymentReceivedAmount: newPaymentReceivedAmount,
          // Set payment confirmation status to pending for supervisor approval
          paymentConfirmationStatus: 'PENDING'
        };
        
        // If status is being set to Completo, update the payment received date
        if (newStatus === 'Completo') {
          changes.dataRecebimento = formattedDate;
          changes.paymentReceivedDate = new Date().toISOString();
        }
        
        // Add to observation field with proper format
        let logEntry: string;
        if (statusChange) {
          // Log status change
          logEntry = `${formattedDate} - ${previousStatus} → ${newStatus} (por ${userName})`;
          if (paymentData.amountPaid > 0) {
            // Add payment info on next line
            logEntry += `\n${formattedDate} - Pagamento: R$ ${paymentData.amountPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} via ${paymentData.paymentMethod} (por ${userName})`;
          }
        } else {
          // Just log the payment
          logEntry = `${formattedDate} - Pagamento: R$ ${paymentData.amountPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} via ${paymentData.paymentMethod} (por ${userName})`;
        }
        
        // Add observation from payment dialog if provided
        if (paymentData.notes && paymentData.notes.trim()) {
          logEntry += `\n${formattedDate} - Observação: ${paymentData.notes.trim()} (por ${userName})`;
        }
        
        const updatedObservation = selectedOrder.observation 
          ? `${selectedOrder.observation}\n${logEntry}`
          : logEntry;
        changes.observation = updatedObservation;
        
        // Update payment method
        changes.formaPagamento = paymentData.paymentMethod;
        
        // Add next payment date if provided
        if (paymentData.nextPaymentDate) {
          changes.nextPaymentDate = paymentData.nextPaymentDate;
        }
        
        console.log('Calling updateOrder with:', {
          orderId: selectedOrder.id,
          changes
        });
        
        // Skip payment recording API - payments are tracked in the order itself
        // The paymentReceivedAmount field and observation field will track payment history
        
        // Adicionar o registro de cobrança ao histórico (legacy)
        if (paymentData.amountPaid > 0) {
          const billingNotes = `${observacao} - ${formattedDate}`;
          console.log('Adding billing history:', {
            orderId: selectedOrder.id,
            amount: paymentData.amountPaid,
            notes: billingNotes
          });
          
          // Note: We need to add billing history through the service
          // await OrderService.addBillingHistory(selectedOrder.id, paymentData.amountPaid, billingNotes);
        }
        
        // Use the new payment update method that handles role permissions correctly
        const savedOrder = await OrderService.updateOrderWithPayment(selectedOrder.id, changes);
        
        console.log('Order updated successfully with payment');
        
        // Atualizar o pedido selecionado com os dados salvos do servidor
        setSelectedOrder(savedOrder);

        // Adicionar registro ao histórico de cobrança
        const newHistoricoItem: CobrancaHistorico = {
          data: formattedDate,
          observacao: observacao.replace(` (por ${userName})`, ''),
          situacao: newStatus,
          usuario: userName
        };

        setHistoricoCobranca(sortHistorico([newHistoricoItem, ...historicoCobranca]));
        
        // Mostrar mensagem de sucesso
        alert('Pagamento registrado com sucesso!');
      } catch (error: any) {
        console.error('Erro ao atualizar pedido:', error);
        const errorMessage = error?.message || 'Erro ao registrar pagamento';
        alert(`Erro: ${errorMessage}. Por favor, tente novamente.`);
      }
    } else {
      console.error('Missing selectedOrder or order ID');
      console.error('selectedOrder:', selectedOrder);
      if (selectedOrder && !selectedOrder.id) {
        alert('Erro: O pedido não possui um ID válido. Por favor, recarregue a página e tente novamente.');
      } else {
        alert('Erro: Pedido não selecionado');
      }
    }

    setPaymentDialogOpen(false);
  };

  // Função para confirmar a data de promessa
  const handlePromiseDateConfirm = async () => {
    if (!promiseDate) {
      alert('Por favor, selecione uma data para a promessa de pagamento');
      return;
    }
    
    if (!selectedOrder) {
      alert('Erro: Pedido não selecionado');
      return;
    }
    
    try {
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
      
      // Obter nome do usuário atual
      const userInfo = localStorage.getItem('unified_user_info');
      const userName = userInfo ? JSON.parse(userInfo).fullName : 'Sistema';
      const promiseDateFormatted = new Date(promiseDate).toLocaleDateString('pt-BR');
      
      // Check if we're updating an existing Negociação/Promessa or setting a new Promessa
      const isUpdatingDate = selectedOrder.status?.toLowerCase() === 'negociação' || 
                           selectedOrder.status?.toLowerCase() === 'promessa';
      
      let observacao: string;
      let newStatus: string;
      let logEntry: string;
      
      if (isUpdatingDate) {
        // Just updating the date, keep the current status
        newStatus = selectedOrder.status || 'Promessa';
        observacao = `Data de pagamento atualizada para ${promiseDateFormatted} (por ${userName})`;
        logEntry = `${formattedDate} - Data próximo pagamento: ${promiseDateFormatted} (por ${userName})`;
      } else {
        // Setting status to Promessa
        const previousStatus = selectedOrder.status || 'Sem status';
        newStatus = 'Promessa';
        observacao = `Status atualizado para: Promessa - Cliente prometeu pagar em ${promiseDateFormatted} (por ${userName})`;
        logEntry = `${formattedDate} - ${previousStatus} → ${newStatus} (por ${userName})`;
      }
      
      // Add to observation field
      const updatedObservation = selectedOrder.observation 
        ? `${selectedOrder.observation}\n${logEntry}`
        : logEntry;
      
      // Atualizar usando o contexto
      const changes = {
        status: newStatus,
        situacao: newStatus,
        nextPaymentDate: promiseDate,
        observation: updatedObservation
      };
      
      await updateOrder(selectedOrder.id, changes);
      
      // Update selected order with new data
      setSelectedOrder({
        ...selectedOrder,
        nextPaymentDate: promiseDate,
        status: newStatus,
        situacao: newStatus,
        observation: updatedObservation
      });
      
      // Fechar diálogos
      setPromiseDateDialogOpen(false);
      setPromiseDate('');
      setPendingStatus('');
      
      // Atualizar histórico
      const newHistoricoItem: CobrancaHistorico = {
        data: formattedDate,
        observacao: observacao,
        situacao: newStatus
      };
      
      setHistoricoCobranca(sortHistorico([newHistoricoItem, ...historicoCobranca]));
      
      alert(isUpdatingDate ? 'Data de pagamento atualizada com sucesso!' : 'Status atualizado para Promessa com sucesso!');
    } catch (error: any) {
      console.error('Erro ao atualizar:', error);
      const errorMessage = error?.response?.data?.message || error?.message || 'Erro ao atualizar';
      alert(`Erro: ${errorMessage}`);
    }
  };

  // Função para abrir o diálogo de confirmação de exclusão
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  // Função para cancelar a exclusão
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  // Função para confirmar a exclusão
  const handleDeleteConfirm = async () => {
    if (selectedOrder) {
      try {
        // Usar contexto para deletar
        await deleteOrder(selectedOrder.id, false);
        
        setDeleteDialogOpen(false);
        handleCloseDrawer(); // Fechar o drawer após a exclusão
        
        // Mostrar mensagem de sucesso
        alert('Pedido excluído com sucesso!');
      } catch (error: any) {
        console.error('Erro ao excluir pedido:', error);
        alert('Erro ao excluir pedido. Por favor, tente novamente.');
      }
    }
  };

  // Função para abrir o diálogo de exclusão permanente
  const handlePermanentDeleteClick = () => {
    setPermanentDeleteDialogOpen(true);
  };

  // Função para cancelar a exclusão permanente
  const handlePermanentDeleteCancel = () => {
    setPermanentDeleteDialogOpen(false);
  };

  // Função para confirmar a exclusão permanente
  const handlePermanentDeleteConfirm = async () => {
    if (selectedOrder) {
      try {
        console.log('Attempting to permanently delete order:', selectedOrder.id);
        
        // Usar contexto para deletar permanentemente
        await deleteOrder(selectedOrder.id, true);
        
        setPermanentDeleteDialogOpen(false);
        handleCloseDrawer(); // Fechar o drawer após a exclusão
        
        // Recarregar a página para atualizar a lista
        window.location.reload();
      } catch (error: any) {
        console.error('Erro ao excluir permanentemente o pedido:', {
          error,
          message: error?.message,
          response: error?.response,
          data: error?.response?.data
        });
        alert('Erro ao excluir permanentemente o pedido. Por favor, tente novamente.');
      }
    }
  };

  // Adicionar observação ao histórico de cobrança
  const handleAddCobranca = async () => {
    if (!newObservacao.trim() || !selectedOrder) return;

    try {
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

      // Get current user info
      const userInfo = localStorage.getItem('unified_user_info');
      const userName = userInfo ? JSON.parse(userInfo).fullName : 'Usuário';

      const newEntry = `${formattedDate} - Observação: ${newObservacao.trim()} (por ${userName})`;
      
      // Append to existing observation
      const updatedObservation = selectedOrder.observation 
        ? `${selectedOrder.observation}\n${newEntry}`
        : newEntry;
      
      console.log('[handleAddCobranca] Current observation:', selectedOrder.observation);
      console.log('[handleAddCobranca] New observation entry:', newEntry);
      console.log('[handleAddCobranca] Updated observation:', updatedObservation);
      
      // Save to backend
      const changes = {
        observation: updatedObservation
      };
      
      console.log('[handleAddCobranca] Order ID:', selectedOrder.id);
      console.log('[handleAddCobranca] Order ID type:', typeof selectedOrder.id);
      console.log('[handleAddCobranca] Saving changes:', changes);
      await updateOrder(selectedOrder.id, changes);
      
      // Fetch the updated order to get the latest data
      const savedOrder = await OrderService.getOrder(selectedOrder.id);
      console.log('[handleAddCobranca] Saved order:', savedOrder);
      
      // Update local state
      const newHistoricoItem: CobrancaHistorico = {
        data: formattedDate,
        observacao: newObservacao.trim(),
        situacao: selectedOrder?.status || 'Pendente',
        usuario: userName
      };

      setHistoricoCobranca(sortHistorico([newHistoricoItem, ...historicoCobranca]));
      setNewObservacao('');
      
      // Update selected order with the saved order from backend
      if (savedOrder) {
        console.log('[handleAddCobranca] Setting selected order with saved order:', savedOrder);
        console.log('[handleAddCobranca] Saved order observation:', savedOrder.observation);
        setSelectedOrder(savedOrder);
        
        // Also update the parent's order list if callback is provided
        if (onOrderUpdate) {
          onOrderUpdate(savedOrder);
        }
      } else {
        // If no saved order returned, manually update with changes
        const updatedOrder = { ...selectedOrder, ...changes };
        console.log('[handleAddCobranca] No saved order returned, using manual update:', updatedOrder);
        setSelectedOrder(updatedOrder);
        
        if (onOrderUpdate) {
          onOrderUpdate(updatedOrder);
        }
      }
      
    } catch (error: any) {
      console.error('Erro ao adicionar observação:', error);
      console.error('Detalhes do erro:', error.response?.data);
      
      // Better error message
      let errorMessage = 'Erro ao salvar observação.';
      if (error.response?.status === 404) {
        errorMessage = 'Pedido não encontrado. Por favor, recarregue a página.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      alert(`${errorMessage} Por favor, tente novamente.`);
    }
  };

  // Função para filtrar pedidos deletados e mostrar apenas para admin
  const filterOrders = (orders: Order[]) => {
    // Only filter deleted orders, don't apply role-based filtering
    // The backend already filters by role
    if (!isAdmin) {
      return orders.filter(order => {
        const isDeleted = order.status?.toLowerCase() === 'deletado';
        return !isDeleted;
      });
    }
    return orders;
  };

  // Aplicar o filtro de pedidos deletados
  const visibleOrders = filterOrders(sortedOrders);

  return (
    <>

      <TableContainer
        component={Paper}
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'auto',
          maxWidth: '100%',
        }}
      >
        <Table sx={{ minWidth: 1260, tableLayout: 'fixed' }}>
          <TableHead>
            <TableRow>
              {showSelection && (
                <TableCell padding="checkbox" sx={{ width: '50px' }}>
                  <Checkbox
                    color="primary"
                    indeterminate={selectedOrderIds.length > 0 && selectedOrderIds.length < visibleOrders.length}
                    checked={visibleOrders.length > 0 && selectedOrderIds.length === visibleOrders.length}
                    onChange={(event) => {
                      if (onSelectionChange) {
                        if (event.target.checked) {
                          onSelectionChange(visibleOrders.map(order => order.id));
                        } else {
                          onSelectionChange([]);
                        }
                      }
                    }}
                  />
                </TableCell>
              )}
              <TableCell sx={{ width: '90px' }}>
                <TableSortLabel
                  active={orderBy === 'orderNumber'}
                  direction={orderBy === 'orderNumber' ? order : 'asc'}
                  onClick={createSortHandler('orderNumber')}
                >
                  {COLUMN_LABELS.orderNumber}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '108px' }}>
                <TableSortLabel
                  active={orderBy === 'createdAt'}
                  direction={orderBy === 'createdAt' ? order : 'asc'}
                  onClick={createSortHandler('createdAt')}
                >
                  {COLUMN_LABELS.createdAt}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '180px' }}>
                <TableSortLabel
                  active={orderBy === 'customerName'}
                  direction={orderBy === 'customerName' ? order : 'asc'}
                  onClick={createSortHandler('customerName')}
                >
                  {COLUMN_LABELS.customerName}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '135px' }}>
                <TableSortLabel
                  active={orderBy === 'status'}
                  direction={orderBy === 'status' ? order : 'asc'}
                  onClick={createSortHandler('status')}
                >
                  {COLUMN_LABELS.status}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '162px' }}>{COLUMN_LABELS.statusCorreios}</TableCell>
              <TableCell sx={{ width: '140px' }}>
                <TableSortLabel
                  active={orderBy === 'updatedAt'}
                  direction={orderBy === 'updatedAt' ? order : 'asc'}
                  onClick={createSortHandler('updatedAt')}
                >
                  {COLUMN_LABELS.updatedAt}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '126px' }}>
                <TableSortLabel
                  active={orderBy === 'lastContactDate'}
                  direction={orderBy === 'lastContactDate' ? order : 'asc'}
                  onClick={createSortHandler('lastContactDate')}
                >
                  {COLUMN_LABELS.lastContactDate}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '108px' }}>
                <TableSortLabel
                  active={orderBy === 'paymentReceivedDate'}
                  direction={orderBy === 'paymentReceivedDate' ? order : 'asc'}
                  onClick={createSortHandler('paymentReceivedDate')}
                >
                  {COLUMN_LABELS.paymentReceivedDate}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '108px' }}>{COLUMN_LABELS.nextPaymentDate || 'Próx. Pagamento'}</TableCell>
              <TableCell sx={{ width: '108px' }}>
                <TableSortLabel
                  active={orderBy === 'sellerName'}
                  direction={orderBy === 'sellerName' ? order : 'asc'}
                  onClick={createSortHandler('sellerName')}
                >
                  {COLUMN_LABELS.sellerName}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              <TableCell sx={{ width: '108px' }}>
                <TableSortLabel
                  active={orderBy === 'collectorName'}
                  direction={orderBy === 'collectorName' ? order : 'asc'}
                  onClick={createSortHandler('collectorName')}
                >
                  {COLUMN_LABELS.collectorName}
                  <Box component="span" sx={{ border: 0, clip: 'rect(0 0 0 0)', height: 1, margin: -1, overflow: 'hidden', padding: 0, position: 'absolute', top: 20, width: 1 }}>
                    {order === 'desc' ? 'Ordenar decrescente' : 'Ordenar crescente'}
                  </Box>
                </TableSortLabel>
              </TableCell>
              {isAdmin && <TableCell sx={{ width: '80px' }}>Ações</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleOrders.map((order, index) => {
              const isCritical = isCriticalStatus(order.status);
              const isConfirmarEntrega = order.status?.toLowerCase().includes('confirmar entrega');
              const isRetirarCorreios = order.status?.toLowerCase().includes('retirar correios');
              const isEntregaFalha = order.status?.toLowerCase().includes('entrega falha');
              
              return (
                <TableRow
                  key={order.orderNumber || index}
                  sx={{
                    '&:hover': {
                      bgcolor: isConfirmarEntrega ? 'rgba(156, 39, 176, 0.15)' : 
                               isRetirarCorreios ? 'rgba(255, 112, 67, 0.15)' :
                               isEntregaFalha ? 'rgba(255, 72, 66, 0.18)' :
                               'primary.light',
                    },
                    ...(isConfirmarEntrega && {
                      bgcolor: 'rgba(156, 39, 176, 0.08)',
                      '&:hover': {
                        bgcolor: 'rgba(156, 39, 176, 0.15)',
                      },
                      borderLeft: '3px solid',
                      borderColor: '#9C27B0',
                    }),
                    ...(isRetirarCorreios && {
                      bgcolor: 'rgba(255, 112, 67, 0.08)',
                      '&:hover': {
                        bgcolor: 'rgba(255, 112, 67, 0.15)',
                      },
                      borderLeft: '3px solid',
                      borderColor: '#FF7043',
                    }),
                    ...(isEntregaFalha && {
                      bgcolor: 'rgba(255, 72, 66, 0.12)',
                      '&:hover': {
                        bgcolor: 'rgba(255, 72, 66, 0.18)',
                      },
                      borderLeft: '3px solid',
                      borderColor: '#FF5722',
                    }),
                    ...(isCritical && !isConfirmarEntrega && !isRetirarCorreios && !isEntregaFalha && {
                      bgcolor: 'rgba(255, 72, 66, 0.12)',
                      '&:hover': {
                        bgcolor: 'rgba(255, 72, 66, 0.18)',
                      },
                      borderLeft: '3px solid',
                      borderColor: 'error.main',
                    }),
                    cursor: 'pointer',
                  }}
                >
                  {showSelection && (
                    <TableCell padding="checkbox" onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        color="primary"
                        checked={selectedOrderIds.includes(order.id)}
                        onChange={(event) => {
                          if (onSelectionChange) {
                            if (event.target.checked) {
                              onSelectionChange([...selectedOrderIds, order.id]);
                            } else {
                              onSelectionChange(selectedOrderIds.filter(id => id !== order.id));
                            }
                          }
                        }}
                      />
                    </TableCell>
                  )}
                  <TableCell onClick={() => handleOpenDrawer(order)}>{order.orderNumber}</TableCell>
                  <TableCell onClick={() => handleOpenDrawer(order)}>{formatDateOnly(order.createdAt)}</TableCell>
                  <TableCell onClick={() => handleOpenDrawer(order)} sx={{ fontWeight: 500 }}>{order.customerName}</TableCell>
                  <TableCell onClick={() => handleOpenDrawer(order)}>
                    <StatusChip status={order.status} />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {order.statusCorreios || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                      {order.updatedAt ? formatRelativeTime(order.updatedAt) : '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Tooltip title={(() => {
                      // Extract the last observation date from the observation field
                      if (order.observation) {
                        const lines = order.observation.split('\n').filter(line => line.includes('Observação:'));
                        if (lines.length > 0) {
                          const lastLine = lines[lines.length - 1];
                          const dateMatch = lastLine.match(/^(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2})/);
                          if (dateMatch) {
                            return dateMatch[1];
                          }
                        }
                      }
                      return "Sem cobrança";
                    })()}>
                      <Typography variant="body2">
                        {(() => {
                          // Extract the last observation date from the observation field
                          if (order.observation) {
                            const lines = order.observation.split('\n').filter(line => line.includes('Observação:'));
                            if (lines.length > 0) {
                              const lastLine = lines[lines.length - 1];
                              const dateMatch = lastLine.match(/^(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2})/);
                              if (dateMatch) {
                                return formatRelativeTime(dateMatch[1]);
                              }
                            }
                          }
                          return "-";
                        })()}
                      </Typography>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{order.paymentReceivedDate ? formatDateOnly(order.paymentReceivedDate) : "-"}</TableCell>
                  <TableCell>
                    {/* Show nextPaymentDate for Negociação/Promessa, otherwise dataNegociacao */}
                    {(order.status?.toLowerCase() === 'negociação' || 
                      order.status?.toLowerCase() === 'promessa') && order.nextPaymentDate
                      ? formatDateOnly(order.nextPaymentDate)
                      : order.lastContactDate ? formatDateOnly(order.lastContactDate) : "-"}
                  </TableCell>
                  <TableCell>{order.sellerName || "-"}</TableCell>
                  <TableCell>{order.collectorName || "-"}</TableCell>
                  {isAdmin && (
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedOrder(order);
                          setPermanentDeleteDialogOpen(true);
                        }}
                        sx={{ color: '#d32f2f' }}
                        title="Excluir permanentemente"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  )}
                </TableRow>
              );
            })}
            {orders.length === 0 && (
              <TableRow>
                <TableCell colSpan={isAdmin ? 12 : 11} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    Nenhum pedido encontrado
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Painel lateral com detalhes do pedido */}
      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={handleCloseDrawer}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: '550px' },
            overflow: 'auto',
            p: 0,
            bgcolor: '#FAFAFA',
          },
        }}
      >
        {selectedOrder && (
          <>
            {/* Cabeçalho */}
            <Box
              sx={{
                p: 3,
                background: 'linear-gradient(to right, #f5f7ff, #eef1fd)',
                borderBottom: '1px solid rgba(0,0,0,0.08)',
                position: 'sticky',
                top: 0,
                zIndex: 10,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="h6" fontWeight={600} sx={{ color: '#2c3e50' }}>
                  #{selectedOrder.orderNumber}
                </Typography>
                <Box>
                  {/* Botão de edição - não disponível para vendedores */}
                  {!isSeller && (
                    <IconButton
                      aria-label="editar"
                      onClick={handleEditClick}
                      sx={{ color: '#2c3e50' }}
                    >
                      <EditIcon />
                    </IconButton>
                  )}

                  {/* Botão de excluir apenas para admins */}
                  {canDeleteOrders() && (
                    <>
                      <IconButton
                        aria-label="excluir"
                        onClick={handleDeleteClick}
                        sx={{ color: '#ff9800' }}
                        title="Marcar como deletado"
                      >
                        <DeleteIcon />
                      </IconButton>
                      <IconButton
                        aria-label="excluir permanentemente"
                        onClick={handlePermanentDeleteClick}
                        sx={{ color: '#d32f2f' }}
                        title="Excluir permanentemente"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </>
                  )}

                  {/* Botão de fechar */}
                  <IconButton
                    aria-label="fechar"
                    onClick={handleCloseDrawer}
                    sx={{ color: '#2c3e50' }}
                  >
                    <CloseIcon />
                  </IconButton>
                </Box>
              </Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {formatDate(selectedOrder.createdAt)} • {selectedOrder.customerName}
              </Typography>

              <StatusChip status={selectedOrder.status} />
            </Box>

            {/* ID Venda e Status Info */}
            <Box sx={{ px: 3, py: 2, borderBottom: '1px solid rgba(0, 0, 0, 0.08)' }}>
              <Grid container spacing={2}>
                <Grid item xs={selectedOrder.lastWebhookEvent ? 3 : 6}>
                  <Box>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                      {COLUMN_LABELS.orderNumber}
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600, color: '#2c3e50' }}>
                      {selectedOrder.orderNumber || selectedOrder.id}
                    </Typography>
                  </Box>
                </Grid>
                {selectedOrder.lastWebhookEvent && (
                  <Grid item xs={3}>
                    <Box>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                        Evento Correios
                      </Typography>
                      <Chip
                        label={selectedOrder.lastWebhookEvent}
                        size="small"
                        variant="outlined"
                        sx={{
                          borderColor: '#1976d2',
                          color: '#1976d2',
                          fontWeight: 500,
                          fontSize: '0.75rem'
                        }}
                      />
                    </Box>
                  </Grid>
                )}
                <Grid item xs={selectedOrder.lastWebhookEvent ? 6 : 6}>
                  <Box>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
                      {COLUMN_LABELS.statusCorreios}
                    </Typography>
                    {selectedOrder.statusCorreios ? (
                      <Chip 
                        label={selectedOrder.statusCorreios} 
                        size="small"
                        sx={{
                          backgroundColor: (() => {
                            const status = selectedOrder.statusCorreios?.toLowerCase() || '';
                            if (status.includes('entregue')) return '#388e3c';
                            if (status.includes('tentativa') || status.includes('ausente')) return '#f57c00';
                            if (status.includes('devol') || status.includes('extraviado')) return '#d32f2f';
                            if (status.includes('transito') || status.includes('postado')) return '#1976d2';
                            return '#616161';
                          })(),
                          color: 'white',
                          fontWeight: 500,
                          fontSize: '0.75rem'
                        }}
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">-</Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Conteúdo principal com scroll */}
            <Box sx={{ p: 3 }}>
              {/* Botões de ações rápidas */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'white'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 1.5, color: '#637381', fontWeight: 600 }}>
                  Ações Rápidas
                </Typography>
                
                <Grid container spacing={1}>
                  {/* Finalizar button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<CheckCircleOutlineIcon />}
                      onClick={() => setPaymentDialogOpen(true)}
                      color="success"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Finalizar
                    </Button>
                  </Grid>
                  
                  {/* Negociação button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<HandshakeOutlinedIcon />}
                      onClick={() => handleUpdateStatus('Negociação')}
                      color="warning"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Negociação
                    </Button>
                  </Grid>
                  
                  {/* Promessa button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<AccessTimeOutlinedIcon />}
                      onClick={() => handleUpdateStatus('Promessa')}
                      color="info"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Promessa
                    </Button>
                  </Grid>
                  
                  {/* Pagamento Pendente button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<PendingOutlinedIcon />}
                      onClick={() => handleUpdateStatus('Pagamento Pendente')}
                      color="warning"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Pag. Pendente
                    </Button>
                  </Grid>
                  
                  {/* Frustrado button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<ErrorOutlineOutlinedIcon />}
                      onClick={() => handleUpdateStatus('Frustrado')}
                      color="error"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Frustrado
                    </Button>
                  </Grid>
                  
                  {/* Obs. button */}
                  <Grid item xs={12} sm={6} md={4}>
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={() => {
                        const obsField = document.getElementById('observacao-field');
                        if (obsField) {
                          obsField.scrollIntoView({ behavior: 'smooth' });
                          obsField.focus();
                        }
                      }}
                      color="primary"
                      size="small"
                      fullWidth
                      sx={{
                        borderRadius: 1.5,
                        textTransform: 'none',
                        py: 0.7,
                      }}
                    >
                      Obs.
                    </Button>
                  </Grid>

                </Grid>

                {/* Admin actions */}
                {isAdmin && (
                  <Grid container spacing={1} sx={{ mt: 1 }}>
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={async () => {
                          if (!selectedOrder?.orderNumber) {
                            alert('Número do pedido não encontrado');
                            return;
                          }
                          
                          const orderNumber = selectedOrder.orderNumber;
                          
                          if (window.confirm(`Deseja reprocessar o último webhook do pedido ${orderNumber}?`)) {
                            try {
                              const result = await OrderService.rerunWebhook(orderNumber);
                              alert(`Webhook reprocessado com sucesso!\n\nMensagem: ${result.message}\nTempo de processamento: ${result.processingTimeMs}ms`);
                              
                              // Refresh the order data
                              if (result.orderId) {
                                const updatedOrder = await OrderService.getOrder(result.orderId);
                                setSelectedOrder(updatedOrder);
                                if (onOrderUpdate) {
                                  onOrderUpdate(updatedOrder);
                                }
                              }
                            } catch (error: any) {
                              alert(`Erro ao reprocessar webhook: ${error.message}`);
                            }
                          }
                        }}
                        color="secondary"
                        size="small"
                        fullWidth
                        sx={{
                          borderRadius: 1.5,
                          textTransform: 'none',
                          py: 0.7,
                        }}
                      >
                        Reprocessar Webhook
                      </Button>
                    </Grid>
                  </Grid>
                )}

                <Box sx={{ mt: 1.5, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant={editMode ? "contained" : "text"}
                    startIcon={<EditIcon />}
                    onClick={handleEditClick}
                    color="primary"
                    size="small"
                    sx={{
                      borderRadius: 1.5,
                      textTransform: 'none',
                    }}
                  >
                    {editMode ? 'Cancelar Edição' : 'Editar Informações'}
                  </Button>
                </Box>
              </Paper>

              {/* Informações principais - Compact layout */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'white'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#637381', fontWeight: 600 }}>
                  Informações do Cliente
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.customerName}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.customerName || '-'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.customerPhone}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.customerPhone || '-'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      CPF
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.customerCPF || '-'}
                    </Typography>
                  </Grid>

                  {selectedOrder.zapSource && (
                    <Grid item xs={12} sm={4}>
                      <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                        WhatsApp Source
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {selectedOrder.zapSource.name}
                        </Typography>
                        <Chip
                          label={selectedOrder.zapSource.status}
                          size="small"
                          sx={{
                            height: 20,
                            fontSize: '0.70rem',
                            bgcolor: getZapStatusColor(selectedOrder.zapSource.status),
                            color: '#fff',
                          }}
                        />
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Paper>

              {/* Informações do Pedido */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'white'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#637381', fontWeight: 600 }}>
                  Detalhes do Pedido
                </Typography>

                <Grid container spacing={2}>
                  {/* Product info */}
                  <Grid item xs={12}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      Produtos
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.items && selectedOrder.items.length > 0
                        ? selectedOrder.items.map(item => `${item.productName} (x${item.quantity})`).join(', ')
                        : 'Nenhum produto cadastrado'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.total}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600, color: '#4caf50', fontSize: '1rem' }}>
                      {selectedOrder.total !== undefined && selectedOrder.total !== null
                        ? `R$ ${selectedOrder.total.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                        : 'R$ 0,00'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.sellerName}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.sellerName || '-'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.collectorName}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.collectorName || '-'}
                    </Typography>
                  </Grid>

                  {/* Tracking info */}
                  {selectedOrder.trackingCode && (
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                      <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                        {COLUMN_LABELS.trackingCode}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {selectedOrder.trackingCode}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={async () => {
                            try {
                              const result = await shippingService.refreshTracking(selectedOrder.id);
                              if (result.tracking) {
                                enqueueSnackbar('Rastreamento atualizado', { variant: 'success' });
                              }
                            } catch (error: any) {
                              enqueueSnackbar('Erro ao atualizar rastreamento', { variant: 'error' });
                            }
                          }}
                          sx={{ p: 0.5 }}
                        >
                          <RefreshIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Paper>

              {/* Tracking details */}
              {selectedOrder.trackingCode && (
                <Paper
                  elevation={0}
                  sx={{
                    mb: 3,
                    borderRadius: 2,
                    border: '1px solid rgba(0,0,0,0.08)',
                    bgcolor: 'white',
                    overflow: 'hidden'
                  }}
                >
                  <TrackingDetails 
                    orderId={selectedOrder.id} 
                    trackingCode={selectedOrder.trackingCode}
                    statusCorreios={selectedOrder.statusCorreios}
                    ultimaAtualizacaoCorreios={selectedOrder.trackingLastUpdate}
                  />
                </Paper>
              )}


              {/* Endereço de Entrega */}
              {selectedOrder.addressComponents && (
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 3,
                    borderRadius: 2,
                    border: '1px solid rgba(0,0,0,0.08)',
                    bgcolor: 'white'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ mb: 2, color: '#637381', fontWeight: 600 }}>
                    Endereço de Entrega
                  </Typography>

                  <Grid container spacing={1}>
                    {/* Street and Number on same row */}
                    <Grid item xs={9}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Rua
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                        {selectedOrder.addressComponents.street}, {selectedOrder.addressComponents.streetNumber || 'S/N'}
                      </Typography>
                    </Grid>
                    <Grid item xs={3}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Complemento
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                        {selectedOrder.addressComponents.complement || '-'}
                      </Typography>
                    </Grid>
                    
                    {/* Neighborhood and City/State */}
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Bairro
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                        {selectedOrder.addressComponents.neighborhood}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        Cidade/UF
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                        {selectedOrder.addressComponents.city}/{selectedOrder.addressComponents.state}
                      </Typography>
                    </Grid>
                    
                    {/* CEP */}
                    <Grid item xs={12}>
                      <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                        CEP
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>
                        {selectedOrder.addressComponents.zipCode}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              )}

              {/* Informações de Pagamento */}
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  mb: 3,
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'white'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 2, color: '#637381', fontWeight: 600 }}>
                  Pagamento
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.paymentReceivedAmount}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500, color: '#4caf50' }}>
                      {(() => {
                        // For completed sales with empty valor recebido, use the valor venda
                        if (
                          typeof selectedOrder.status === 'string' &&
                          selectedOrder.status.toLowerCase() === 'completo' &&
                          (!selectedOrder.paymentReceivedAmount || selectedOrder.paymentReceivedAmount === 0)
                        ) {
                          return `R$ ${selectedOrder.total.toLocaleString('pt-BR', { minimumFractionDigits: 2 })} (valor da venda)`;
                        } else {
                          return selectedOrder.paymentReceivedAmount !== undefined && selectedOrder.paymentReceivedAmount !== null
                            ? `R$ ${selectedOrder.paymentReceivedAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
                            : 'R$ 0,00';
                        }
                      })()}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      Forma de Pagamento
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {'-'}
                    </Typography>
                  </Grid>

                  {(selectedOrder.paymentReceivedAmount > 0 && selectedOrder.paymentReceivedAmount < selectedOrder.total) && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                        Valor a Receber
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, color: '#ff9800' }}>
                        {(() => {
                          const valorRestante = selectedOrder.total - selectedOrder.paymentReceivedAmount;
                          return `R$ ${valorRestante.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
                        })()}
                      </Typography>
                    </Grid>
                  )}

                  <Grid item xs={12} sm={6}>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {COLUMN_LABELS.paymentReceivedDate}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {selectedOrder.paymentReceivedDate || '-'}
                    </Typography>
                  </Grid>

                  {selectedOrder.lastContactDate && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                        {COLUMN_LABELS.lastContactDate}
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {selectedOrder.lastContactDate}
                      </Typography>
                    </Grid>
                  )}
                  
                  {/* Next Payment Date for Negociação and Promessa */}
                  {(selectedOrder.status?.toLowerCase() === 'negociação' || 
                    selectedOrder.status?.toLowerCase() === 'promessa') && (
                    <>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                          {COLUMN_LABELS.nextPaymentDate}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {selectedOrder.nextPaymentDate 
                              ? (() => {
                                  const date = new Date(selectedOrder.nextPaymentDate + 'T00:00:00');
                                  return date.toLocaleDateString('pt-BR');
                                })()
                              : '-'}
                          </Typography>
                          {isOperator && (
                            <IconButton
                              size="small"
                              onClick={() => {
                                setPendingStatus(selectedOrder.status || '');
                                setPromiseDate(selectedOrder.nextPaymentDate || '');
                                setPromiseDateDialogOpen(true);
                              }}
                              sx={{ ml: 1 }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          )}
                        </Box>
                      </Grid>
                      
                      {/* Days Overdue */}
                      {(() => {
                        const daysOverdue = calculateDaysOverdue(selectedOrder.nextPaymentDate);
                        if (daysOverdue && daysOverdue > 0) {
                          return (
                            <Grid item xs={12} sm={6}>
                              <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                                Situação
                              </Typography>
                              <Chip
                                label={`${daysOverdue} ${daysOverdue === 1 ? 'dia' : 'dias'} vencido`}
                                color="error"
                                size="small"
                                sx={{ fontWeight: 600 }}
                              />
                            </Grid>
                          );
                        }
                        return null;
                      })()}
                    </>
                  )}
                </Grid>
              </Paper>

              {/* Histórico de Cobrança */}
              <Paper
                elevation={0}
                sx={{
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'white',
                  overflow: 'hidden',
                  mb: 3
                }}
              >
                <Box sx={{ p: 2, borderBottom: '1px solid rgba(0,0,0,0.08)' }}>
                  <Typography variant="subtitle2" sx={{ color: '#637381', fontWeight: 600 }}>
                    Histórico de Cobrança
                  </Typography>
                </Box>

                <Box sx={{ p: 2, bgcolor: 'rgba(0,0,0,0.02)' }}>
                  <TextField
                    id="observacao-field"
                    label="Nova observação"
                    multiline
                    rows={2}
                    value={newObservacao}
                    onChange={(e) => setNewObservacao(e.target.value)}
                    fullWidth
                    variant="outlined"
                    size="small"
                    sx={{
                      mb: 1.5,
                      '.MuiOutlinedInput-root': {
                        bgcolor: 'white',
                      }
                    }}
                  />

                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleAddCobranca}
                    disabled={!newObservacao.trim()}
                    size="small"
                    sx={{
                      borderRadius: 1.5,
                      textTransform: 'none',
                    }}
                  >
                    Adicionar Registro
                  </Button>
                </Box>

                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {(() => {
                    // Filter to show only manual observations
                    const manualObservations = historicoCobranca.filter(item => 
                      item.observacao.includes('Observação:')
                    );
                    
                    return manualObservations.map((item, index) => (
                      <React.Fragment key={index}>
                        <ListItem
                          alignItems="flex-start"
                          sx={{
                            py: 1.5,
                            px: 2,
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' },
                          }}
                        >
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {item.data} {item.usuario && `- ${item.usuario}`}
                                </Typography>
                              </Box>
                            }
                            secondary={
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  display: 'block',
                                  bgcolor: 'rgba(0,0,0,0.02)',
                                  p: 1,
                                  borderRadius: 1,
                                  mt: 0.5
                                }}
                              >
                                {item.observacao.replace('Observação: ', '')}
                              </Typography>
                            }
                          />
                        </ListItem>
                        {index < manualObservations.length - 1 && (
                          <Divider component="li" sx={{ borderStyle: 'dashed' }} />
                        )}
                      </React.Fragment>
                    ));
                  })()}
                  {historicoCobranca.filter(item => 
                    item.observacao.includes('Observação:')
                  ).length === 0 && (
                    <ListItem sx={{ py: 3 }}>
                      <ListItemText
                        primary={
                          <Typography
                            align="center"
                            color="text.secondary"
                            sx={{ fontStyle: 'italic' }}
                          >
                            Nenhuma observação registrada
                          </Typography>
                        }
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>

              {/* Log de Atividade do Sistema */}
              <Paper
                elevation={0}
                sx={{
                  borderRadius: 2,
                  border: '1px solid rgba(0,0,0,0.08)',
                  bgcolor: 'rgba(0, 0, 0, 0.06)',
                  overflow: 'hidden',
                  mb: 3
                }}
              >
                <Box sx={{ p: 2, borderBottom: '1px solid rgba(0,0,0,0.08)' }}>
                  <Typography variant="subtitle2" sx={{ color: '#637381', fontWeight: 600 }}>
                    Log de Atividade do Sistema
                  </Typography>
                </Box>
                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {(() => {
                    // Filter to show only system activity (status changes, payments, etc.)
                    const systemLogs = historicoCobranca.filter(item => 
                      !item.observacao.includes('Observação:') && (
                        item.observacao.includes('Status:') || 
                        item.observacao.includes('Status alterado') ||
                        item.observacao.includes('→') ||
                        item.observacao.includes('Pagamento:') ||
                        item.observacao.includes('Data próximo pagamento:') ||
                        item.observacao.includes('Informações do pedido atualizadas')
                      )
                    );
                    
                    if (systemLogs.length === 0) {
                      return (
                        <ListItem>
                          <ListItemText
                            primary={
                              <Typography variant="body2" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                                Nenhum log de atividade registrado
                              </Typography>
                            }
                          />
                        </ListItem>
                      );
                    }
                    
                    return systemLogs.map((item, index) => (
                      <React.Fragment key={index}>
                        <ListItem
                          alignItems="flex-start"
                          sx={{
                            py: 1.5,
                            px: 2,
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' },
                          }}
                        >
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {item.data} {item.usuario && `- ${item.usuario}`}
                                </Typography>
                                <StatusChip status={item.situacao} />
                              </Box>
                            }
                            secondary={
                              <Typography variant="body2" sx={{ color: 'text.secondary', whiteSpace: 'pre-wrap' }}>
                                {item.observacao}
                              </Typography>
                            }
                          />
                        </ListItem>
                        {index < systemLogs.length - 1 && <Divider variant="middle" />}
                      </React.Fragment>
                    ));
                  })()}
                </List>
              </Paper>


              {/* Formulário de Edição (visível apenas em modo de edição) */}
              {editMode && (
                <>
                  {isAdmin ? (
                    <AdminEditForm
                      selectedOrder={selectedOrder}
                      onSave={async (formData) => {
                        // Use updateOrder from context - pass the order id and changes
                        await updateOrder(selectedOrder.id, formData);
                        // Fetch the updated order to get the latest data
                        const updatedOrder = await OrderService.getOrder(selectedOrder.id);
                        // Update the selected order with the fresh data from server
                        setSelectedOrder(updatedOrder);
                      }}
                      onClose={() => {
                        setEditMode(false);
                        setEditedValues({});
                      }}
                    />
                  ) : (
                    <UserEditForm
                      selectedOrder={selectedOrder}
                      editedValues={editedValues}
                      handleEditChange={handleEditChange}
                      handleSaveEdit={handleSaveEdit}
                    />
                  )}
                </>
              )}

            </Box>
          </>
        )}
      </Drawer>

      {/* Diálogo de confirmação de exclusão */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Confirmar exclusão do pedido
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Tem certeza que deseja marcar este pedido como deletado? Esta ação só poderá ser visualizada por administradores.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancelar
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Deletar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo de confirmação de exclusão permanente */}
      <Dialog
        open={permanentDeleteDialogOpen}
        onClose={handlePermanentDeleteCancel}
        aria-labelledby="permanent-alert-dialog-title"
        aria-describedby="permanent-alert-dialog-description"
      >
        <DialogTitle id="permanent-alert-dialog-title" sx={{ color: '#d32f2f' }}>
          ⚠️ Confirmar exclusão PERMANENTE do pedido
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="permanent-alert-dialog-description">
            <strong>ATENÇÃO:</strong> Esta ação é IRREVERSÍVEL!
            <br /><br />
            Você está prestes a excluir PERMANENTEMENTE o pedido <strong>#{selectedOrder?.orderNumber}</strong>.
            <br /><br />
            Todos os dados relacionados a este pedido serão removidos definitivamente do banco de dados e não poderão ser recuperados.
            <br /><br />
            Tem certeza absoluta que deseja continuar?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handlePermanentDeleteCancel} color="primary" variant="contained">
            Cancelar
          </Button>
          <Button onClick={handlePermanentDeleteConfirm} color="error" autoFocus>
            Excluir Permanentemente
          </Button>
        </DialogActions>
      </Dialog>

      {/* Payment Finalization Dialog */}
      <PaymentFinalizationDialog
        open={paymentDialogOpen}
        onClose={() => setPaymentDialogOpen(false)}
        order={selectedOrder}
        onConfirm={handlePaymentConfirm}
      />

      {/* Promise Date Dialog */}
      <Dialog
        open={promiseDateDialogOpen}
        onClose={() => {
          setPromiseDateDialogOpen(false);
          setPromiseDate('');
          setPendingStatus('');
        }}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          {selectedOrder?.situacaoVenda?.toLowerCase() === 'negociação' || 
           selectedOrder?.situacaoVenda?.toLowerCase() === 'promessa' 
            ? 'Atualizar Data de Pagamento' 
            : 'Data da Promessa de Pagamento'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 3 }}>
            {selectedOrder?.situacaoVenda?.toLowerCase() === 'negociação' || 
             selectedOrder?.situacaoVenda?.toLowerCase() === 'promessa'
              ? 'Atualize a data em que o cliente prometeu realizar o pagamento.'
              : 'Selecione a data em que o cliente prometeu realizar o pagamento.'}
          </DialogContentText>
          <TextField
            fullWidth
            label="Data da Promessa"
            type="date"
            value={promiseDate}
            onChange={(e) => setPromiseDate(e.target.value)}
            InputLabelProps={{
              shrink: true,
            }}
            inputProps={{
              min: new Date().toISOString().split('T')[0],
            }}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              setPromiseDateDialogOpen(false);
              setPromiseDate('');
              setPendingStatus('');
            }} 
            color="inherit"
          >
            Cancelar
          </Button>
          <Button 
            onClick={handlePromiseDateConfirm} 
            variant="contained"
            disabled={!promiseDate}
          >
            Confirmar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Shipping Label Dialog */}
      {selectedOrder && (
        <ShippingLabelDialog
          open={shippingLabelDialogOpen}
          onClose={() => setShippingLabelDialogOpen(false)}
          order={selectedOrder}
          onSuccess={() => {
            // Refresh order data if needed
            handleCloseDrawer();
          }}
        />
      )}

    </>
  );
}

export default React.memo(OrdersTable);