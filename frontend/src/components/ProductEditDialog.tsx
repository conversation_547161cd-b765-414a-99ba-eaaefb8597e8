import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  InputAdornment,
  Chip,
  Tooltip,
  Avatar,
  Grid,
} from '@mui/material';
import {
  Close as CloseIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Image as ImageIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { Product, ProductVariation, UpdateProductDto, UpdateVariationDto } from '../types/Product';
import productService from '../services/ProductService';
import { resizeImage, formatFileSize, isImageFile } from '../utils/imageResizer';

interface ProductEditDialogProps {
  open: boolean;
  onClose: () => void;
  product: Product;
  onProductUpdated: () => void;
}

interface EditingVariation {
  id: string;
  price: number;
}

export const ProductEditDialog: React.FC<ProductEditDialogProps> = ({
  open,
  onClose,
  product,
  onProductUpdated,
}) => {
  const [name, setName] = useState(product.name);
  const [description, setDescription] = useState(product.description || '');
  const [imageUrl, setImageUrl] = useState(product.imageUrl || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingVariations, setEditingVariations] = useState<Record<string, EditingVariation>>({});
  const [variations, setVariations] = useState<ProductVariation[]>(product.variations || []);
  const [showNewVariation, setShowNewVariation] = useState(false);
  const [newVariationName, setNewVariationName] = useState('');
  const [newVariationPrice, setNewVariationPrice] = useState('');
  const [imageProcessing, setImageProcessing] = useState(false);
  const [imageInfo, setImageInfo] = useState<{
    originalSize: number;
    resizedSize: number;
    dimensions: { width: number; height: number };
  } | null>(null);

  useEffect(() => {
    // Always sync with the latest product data when dialog opens or product changes
    if (open) {
      setName(product.name);
      setDescription(product.description || '');
      setImageUrl(product.imageUrl || '');
      setVariations(product.variations || []);
      setEditingVariations({});
      setError(null);
      setShowNewVariation(false);
      setNewVariationName('');
      setNewVariationPrice('');
      setImageProcessing(false);
      setImageInfo(null);
    }
  }, [product, open]);
  
  // Also update variations when product.variations changes (after refresh)
  useEffect(() => {
    if (open && product.variations) {
      setVariations(product.variations);
    }
  }, [product.variations, open]);

  const handleEditVariation = (variation: ProductVariation) => {
    setEditingVariations({
      ...editingVariations,
      [variation.id]: {
        id: variation.id,
        price: variation.price,
      },
    });
  };

  const handleCancelEditVariation = (variationId: string) => {
    const newEditingVariations = { ...editingVariations };
    delete newEditingVariations[variationId];
    setEditingVariations(newEditingVariations);
  };

  const handleVariationPriceChange = (variationId: string, value: string) => {
    const price = parseFloat(value) || 0;
    setEditingVariations({
      ...editingVariations,
      [variationId]: {
        ...editingVariations[variationId],
        price,
      },
    });
  };

  const handleSaveVariation = async (variationId: string) => {
    const editingVariation = editingVariations[variationId];
    if (!editingVariation) return;

    try {
      setLoading(true);
      setError(null);

      const updateData: UpdateVariationDto = {
        price: editingVariation.price,
      };

      await productService.updateVariation(product.id, variationId, updateData);
      
      // Atualizar a lista local de variações
      setVariations(variations.map(v => 
        v.id === variationId 
          ? { ...v, price: editingVariation.price }
          : v
      ));

      handleCancelEditVariation(variationId);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao atualizar variação');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVariation = async () => {
    if (!newVariationName.trim() || !newVariationPrice) return;

    try {
      setLoading(true);
      setError(null);

      // Always generate a unique SKU
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
      const sku = `${product.name.substring(0, 3).toUpperCase()}-${newVariationName.substring(0, 3).toUpperCase()}-${timestamp}-${randomSuffix}`;

      const variationData = {
        type: 'CUSTOM',
        customName: newVariationName.trim(),
        price: parseFloat(newVariationPrice) || 0,
        sku: sku,
      };

      const response = await productService.createVariation(product.id, variationData);
      
      // Format the response to ensure price is a number
      const newVariation = {
        ...response,
        price: Number(response.price) || 0,
        variation: response.variation || newVariationName.trim(),
      };
      
      // Adicionar a nova variação à lista local
      setVariations([...variations, newVariation]);
      
      // Resetar o formulário
      setShowNewVariation(false);
      setNewVariationName('');
      setNewVariationPrice('');
      
      // Call onProductUpdated to refresh the product data
      // This ensures the parent component has the latest data
      await onProductUpdated();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao criar variação');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Atualizar produto com todos os campos
      const updateData: UpdateProductDto = {
        name: name.trim(),
      };
      
      // Only add description if it's different from current
      if (description.trim() !== (product.description || '')) {
        updateData.description = description.trim();
      }
      
      // Only add imageUrl if it's different from current
      if (imageUrl !== (product.imageUrl || '')) {
        updateData.imageUrl = imageUrl;
      }

      await productService.updateProduct(product.id, updateData);
      
      // Chamar callback para atualizar a página
      onProductUpdated();
      onClose();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao atualizar produto');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleImageUpload called', event.target.files);
    const file = event.target.files?.[0];
    if (!file) {
      console.log('No file selected');
      return;
    }

    console.log('File selected:', file.name, file.type, file.size);

    // Validate file type
    if (!isImageFile(file)) {
      setError('Por favor, selecione um arquivo de imagem válido (JPEG, PNG, WebP, GIF)');
      return;
    }

    setImageProcessing(true);
    setError(null);

    try {
      const originalSize = file.size;
      
      // Resize the image
      const { blob, dataUrl } = await resizeImage(file, {
        maxWidth: 1200,
        maxHeight: 1200,
        quality: 0.8,
        outputFormat: 'image/jpeg' // Convert all images to JPEG for consistency
      });

      const resizedSize = blob.size;
      
      // Get dimensions for display
      const img = new Image();
      await new Promise((resolve) => {
        img.onload = resolve;
        img.src = dataUrl;
      });

      // Update state with resized image
      setImageUrl(dataUrl);
      setImageInfo({
        originalSize,
        resizedSize,
        dimensions: { width: img.width, height: img.height }
      });

      // Log size reduction
      const reduction = ((1 - resizedSize / originalSize) * 100).toFixed(1);
      console.log(`Image resized: ${formatFileSize(originalSize)} → ${formatFileSize(resizedSize)} (${reduction}% reduction)`);
      
    } catch (error: any) {
      console.error('Error processing image:', error);
      setError('Erro ao processar imagem. Por favor, tente novamente.');
    } finally {
      setImageProcessing(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Editar Produto</Typography>
          <IconButton edge="end" color="inherit" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="Nome do Produto"
            value={name}
            onChange={(e) => setName(e.target.value)}
            disabled={loading}
            margin="normal"
          />
          
          <TextField
            fullWidth
            label="Descrição"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            disabled={loading}
            margin="normal"
            multiline
            rows={2}
          />
          
          <Box sx={{ mt: 3, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Imagem do Produto
            </Typography>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={imageUrl}
                sx={{ width: 80, height: 80 }}
              >
                {imageProcessing ? <CircularProgress size={30} /> : <ImageIcon />}
              </Avatar>
              <Box>
                <input
                  type="file"
                  id="product-image-upload"
                  accept="image/*"
                  onChange={handleImageUpload}
                  disabled={imageProcessing || loading}
                  style={{ display: 'none' }}
                />
                <label htmlFor="product-image-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    size="small"
                    startIcon={imageProcessing ? <CircularProgress size={20} /> : <ImageIcon />}
                    disabled={imageProcessing || loading}
                  >
                    {imageProcessing ? 'Processando...' : 'Alterar Imagem'}
                  </Button>
                </label>
              </Box>
            </Box>
            
            {/* Image info display */}
            {imageInfo && (
              <Box mt={1}>
                <Typography variant="caption" color="text.secondary" display="block">
                  <InfoIcon sx={{ fontSize: 14, verticalAlign: 'middle', mr: 0.5 }} />
                  {imageInfo.dimensions.width} x {imageInfo.dimensions.height}px
                </Typography>
                <Typography variant="caption" color="success.main" display="block">
                  {formatFileSize(imageInfo.originalSize)} → {formatFileSize(imageInfo.resizedSize)}
                  {' '}({((1 - imageInfo.resizedSize / imageInfo.originalSize) * 100).toFixed(0)}% reduzido)
                </Typography>
              </Box>
            )}
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Variações e Preços de Custo
            </Typography>
            <Button
              size="small"
              startIcon={<AddIcon />}
              onClick={() => setShowNewVariation(true)}
              disabled={loading || showNewVariation}
            >
              Nova Variação
            </Button>
          </Box>

          {variations.length === 0 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Este produto não possui variações. Clique em "Nova Variação" para adicionar.
            </Alert>
          )}
          
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Variação</TableCell>
                  <TableCell>SKU</TableCell>
                  <TableCell align="right">Preço de Custo</TableCell>
                  <TableCell align="center">Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {showNewVariation && (
                  <TableRow>
                    <TableCell>
                      <TextField
                        size="small"
                        placeholder="Nome da variação"
                        value={newVariationName}
                        onChange={(e) => setNewVariationName(e.target.value)}
                        disabled={loading}
                        fullWidth
                        autoFocus
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption" color="text.secondary">
                        Auto-gerado
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <TextField
                        size="small"
                        type="number"
                        placeholder="0,00"
                        value={newVariationPrice}
                        onChange={(e) => setNewVariationPrice(e.target.value)}
                        InputProps={{
                          startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                        }}
                        inputProps={{
                          step: 0.01,
                          min: 0,
                        }}
                        disabled={loading}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={handleCreateVariation}
                          disabled={loading || !newVariationName.trim() || !newVariationPrice}
                        >
                          <SaveIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="default"
                          onClick={() => {
                            setShowNewVariation(false);
                            setNewVariationName('');
                            setNewVariationPrice('');
                          }}
                          disabled={loading}
                        >
                          <CancelIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
                {variations.map((variation) => {
                  const isEditing = !!editingVariations[variation.id];
                  const editingData = editingVariations[variation.id];

                  return (
                    <TableRow key={variation.id}>
                      <TableCell>{variation.variation}</TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {variation.sku || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {isEditing ? (
                          <TextField
                            size="small"
                            type="number"
                            value={editingData.price}
                            onChange={(e) => handleVariationPriceChange(variation.id, e.target.value)}
                            InputProps={{
                              startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                            }}
                            inputProps={{
                              step: 0.01,
                              min: 0,
                            }}
                            disabled={loading}
                          />
                        ) : (
                          formatPrice(variation.price)
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {isEditing ? (
                          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleSaveVariation(variation.id)}
                              disabled={loading}
                            >
                              <SaveIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="default"
                              onClick={() => handleCancelEditVariation(variation.id)}
                              disabled={loading}
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        ) : (
                          <IconButton
                            size="small"
                            onClick={() => handleEditVariation(variation)}
                            disabled={loading}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          
          <Alert severity="info" sx={{ mt: 2 }} icon={false}>
            <Typography variant="caption">
              <strong>Nota:</strong> Variações existentes não podem ser excluídas se possuem vendas associadas. 
              Você pode adicionar novas variações ou editar os preços das existentes.
            </Typography>
          </Alert>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || !name.trim()}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          Salvar Alterações
        </Button>
      </DialogActions>
    </Dialog>
  );
};