import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Box, useTheme, useMediaQuery, Typography } from '@mui/material';
import Navbar from './Navbar';
import ModernSidebar, { drawerWidth, drawerWidthCollapsed } from './ModernSidebar';
import { StatusFilter } from './Sidebar';
import { useOrderData } from '../contexts/OrderDataContext';
import { Order } from '../types/Order';

interface LayoutProps {
  orders?: Order[];
  onStatusSelect?: (filter: StatusFilter | null) => void;
  selectedStatus?: StatusFilter | null;
  children?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ 
  orders: propOrders, 
  onStatusSelect: propOnStatusSelect, 
  selectedStatus: propSelectedStatus, 
  children 
}) => {
  const { orders: contextOrders } = useOrderData();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  // Use props if provided, otherwise use context
  const orders = propOrders || contextOrders;
  const [selectedStatus, setSelectedStatus] = useState<StatusFilter | null>(propSelectedStatus || null);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

  // Update selectedStatus when prop changes
  useEffect(() => {
    if (propSelectedStatus !== undefined) {
      setSelectedStatus(propSelectedStatus);
    }
  }, [propSelectedStatus]);

  // Update sidebar state when screen size changes
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Handle status selection
  const handleStatusSelect = (filter: StatusFilter | null) => {
    setSelectedStatus(filter);
    if (propOnStatusSelect) {
      propOnStatusSelect(filter);
    }
  };

  const handleSidebarToggle = (open: boolean) => {
    setSidebarOpen(open);
  };

  return (
    <Box sx={{ display: 'flex', width: '100%', overflow: 'hidden' }}>
      <Navbar />
      <ModernSidebar 
        orders={orders}
        onStatusSelect={handleStatusSelect}
        selectedStatus={selectedStatus}
        open={sidebarOpen}
        onToggle={handleSidebarToggle}
      />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: theme => theme.palette.mode === 'dark' ? '#0A0E27' : '#f5f5f5',
          minHeight: '100vh',
          mt: '64px',
          // Remove margin-left since sidebar with position:relative already creates the space
          ml: 0,
          width: '100%',
          transition: theme => theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflow: 'auto',
          p: { xs: 2, sm: 3 },
        }}
      >
        <Box sx={{ 
          maxWidth: '100%', 
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {children || <Outlet context={{ 
            orders, 
            setOrders: () => {}, // Add a no-op setOrders for now
            selectedStatus, 
            setSelectedStatus: handleStatusSelect 
          }} />}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout; 