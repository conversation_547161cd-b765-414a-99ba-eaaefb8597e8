import React from 'react';
import { Outlet } from 'react-router-dom';
import Box from '@mui/material/Box';
import Navbar from './Navbar';
import Sidebar, { StatusFilter } from './Sidebar';
import { Order } from '../types/Order';

interface AppLayoutProps {
  orders: Order[];
  selectedStatus: StatusFilter | null;
  onStatusSelect: (filter: StatusFilter | null) => void;
}

const drawerWidth = 260;

const AppLayout: React.FC<AppLayoutProps> = ({ orders, selectedStatus, onStatusSelect }) => {
  return (
    <Box sx={{ display: 'flex' }}>
      <Navbar />
      <Sidebar
        orders={orders}
        onStatusSelect={onStatusSelect}
        selectedStatus={selectedStatus}
      />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: '#f5f5f5',
          minHeight: '100vh',
          mt: '64px',
          // Remove ml since the Sidebar with position:relative already takes up space
          width: '100%' // Use full width since sidebar already pushes content
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default AppLayout;