import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Grid,
  Paper,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert
} from '@mui/material';
import { Order, COLUMN_LABELS } from '../types/Order';
import productService from '../services/ProductService';
import { useAuth } from '../contexts/AuthContext';
import { useUsuarios } from '../contexts/UsuariosContext';

interface AdminEditFormProps {
  selectedOrder: Order;
  onSave: (formData: Partial<Order>) => Promise<void>;
  onClose: () => void;
}

// Define operator-only statuses
const OPERATOR_STATUSES = [
  'Completo',
  'Negociação',
  'Promessa',
  'Pagamento Pendente',
  'Frustrado'
];

// Define all possible statuses (operator statuses first)
const ALL_STATUSES = [
  // Operator statuses (always at top)
  'Completo',
  'Negocia<PERSON>',
  '<PERSON>mes<PERSON>',
  'Pagamento Pendente',
  '<PERSON>ust<PERSON>',
  // Other statuses
  'Pendente',
  'Cancelado',
  'Separação',
  'Recuperação',
  'Devolvido Correios',
  'Liberação',
  'Possíveis Duplicados',
  'Em Trânsito',
  'Entregue',
  'Parcial',
  'Retirar Correios',
  'Entrega Falha',
  'Confirmar Entrega',
  'Análise',
  'Trânsito',
  'Deletado'
];

const AdminEditForm: React.FC<AdminEditFormProps> = ({
  selectedOrder,
  onSave,
  onClose
}) => {
  // Initialize form data with selected order
  const [formData, setFormData] = useState<Partial<Order>>({ ...selectedOrder });
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  
  // Update form data when selected order changes
  useEffect(() => {
    setFormData({ ...selectedOrder });
  }, [selectedOrder]);
  
  // Get auth context
  const { userInfo } = useAuth();
  
  // Get usuarios from context with loading and error states
  const { usuarios, loading: loadingUsers, error: usersError, fetchUsuarios } = useUsuarios();
  
  // State for dropdown options
  const [kits, setKits] = useState<Array<{value: string, label: string}>>([]);
  const [situacoesVenda, setSituacoesVenda] = useState<string[]>([]);

  // Filter usuarios by role - include admin and supervisor in both dropdowns
  const vendedores = usuarios.filter(u =>
    u.role === 'vendedor' || u.role === 'admin' || u.role === 'supervisor'
  );
  const operadores = usuarios.filter(u =>
    u.role === 'operador' || u.role === 'collector' || u.role === 'admin' || u.role === 'supervisor'
  );
  

  // Sync sellerId and collectorId when users are loaded
  useEffect(() => {
    if (usuarios.length > 0 && selectedOrder) {
      setFormData(prev => {
        const updates: Partial<Order> = { ...prev };
        
        // If we have vendedor name but no ID, find and set the ID
        if (selectedOrder.sellerName && !prev.sellerId) {
          const vendedor = vendedores.find(v => v.name === selectedOrder.sellerName);
          if (vendedor) {
            updates.sellerId = vendedor.id;
          }
        }
        
        // If we have operador name but no ID, find and set the ID
        if (selectedOrder.collectorName && !prev.collectorId) {
          const operador = operadores.find(o => o.name === selectedOrder.collectorName);
          if (operador) {
            updates.collectorId = operador.id;
          }
        }
        
        return updates;
      });
    }
  }, [usuarios, selectedOrder, vendedores, operadores]);

  // Fetch users on mount
  useEffect(() => {
    fetchUsuarios();
  }, [fetchUsuarios]);

  // Load available options on component mount
  useEffect(() => {
    // Load kits
    const loadKits = async () => {
      try {
        const activeKits = await productService.getActiveKits();
        const kitOptions = activeKits.map(kit => ({
          value: kit.displayName,
          label: kit.displayName
        }));
        setKits(kitOptions);
      } catch (error: any) {
        console.error('Error loading kits:', error);
        setKits([]);
      }
    };

    // Load situações de venda (sale statuses) with role-based filtering
    const loadSituacoesVenda = () => {
      // Determine available statuses based on user role
      const role = userInfo?.role;
      const availableStatuses = role === 'admin' ? ALL_STATUSES : OPERATOR_STATUSES;
      
      // Sort the statuses alphabetically
      setSituacoesVenda(availableStatuses.sort());
    };

    // Load all options
    loadKits();
    loadSituacoesVenda();
  }, [userInfo?.role]);

  // Handle text field changes
  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Generic handler for select changes
  const handleChange = (field: string) => (e: SelectChangeEvent<string>) => {
    setFormData(fd => ({ ...fd, [field]: e.target.value }));
  };

  // Handle select changes (for existing selects that use name attribute)
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle save
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setSaveError(null);
      
      
      await onSave(formData);
      onClose();
    } catch (err: any) {
      console.error('Save failed:', err);
      const errorMessage = err.response?.data?.message || err.message || 'Erro desconhecido ao salvar';
      setSaveError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };
  // Show loading state while fetching usuarios
  if (loadingUsers) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state if usuarios failed to load
  if (usersError) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Erro ao carregar usuários: {usersError}
        </Alert>
        <Button variant="contained" onClick={fetchUsuarios}>
          Tentar Novamente
        </Button>
      </Box>
    );
  }

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        mb: 3,
        borderRadius: 2,
        border: '1px solid rgba(0,0,0,0.08)',
        bgcolor: 'white',
        borderLeft: '4px solid #3f51b5'
      }}
    >
      <Typography variant="subtitle2" sx={{ mb: 2, color: '#637381', fontWeight: 600 }}>
        Editar Informações (Modo Administrador)
      </Typography>

      {saveError && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setSaveError(null)}>
          {saveError}
        </Alert>
      )}

      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle2" sx={{ mt: 1, mb: 1, color: '#637381', fontWeight: 600 }}>
            Informações de Pagamento
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.paymentReceivedAmount}
            name="paymentReceivedAmount"
            value={formData.paymentReceivedAmount || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
            type="number"
            InputProps={{
              startAdornment: <Box component="span" sx={{ color: 'text.secondary', mr: 0.5 }}>R$</Box>,
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.lastContactDate}
            name="lastContactDate"
            value={formData.lastContactDate || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
            placeholder="DD/MM/AAAA"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Forma de Pagamento"
            name="formaPagamento"
            value={formData.formaPagamento || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.paymentReceivedDate}
            name="paymentReceivedDate"
            value={formData.paymentReceivedDate || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
            placeholder="DD/MM/AAAA"
          />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1, color: '#637381', fontWeight: 600 }}>
            Informações do Cliente
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.customerName}
            name="customerName"
            value={formData.customerName || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.customerPhone}
            name="customerPhone"
            value={formData.customerPhone || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="CPF"
            name="customerCPF"
            value={formData.customerCPF || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="kit-label">Kit</InputLabel>
            <Select
              labelId="kit-label"
              name="oferta"
              value={''}
              onChange={handleSelectChange}
              label="Kit"
            >
              {kits.map((kit) => (
                <MenuItem key={kit.value} value={kit.value}>
                  {kit.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.total}
            name="total"
            value={formData.total || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
            type="number"
            InputProps={{
              startAdornment: <Box component="span" sx={{ color: 'text.secondary', mr: 0.5 }}>R$</Box>,
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label={COLUMN_LABELS.trackingCode}
            name="trackingCode"
            value={formData.trackingCode || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="vendedor-label">Vendedor</InputLabel>
            <Select
              labelId="vendedor-label"
              value={formData.sellerId || ''}
              onChange={handleChange('sellerId')}
              label={COLUMN_LABELS.sellerName}
              displayEmpty
            >
              <MenuItem value="">
                <em>Selecione um vendedor</em>
              </MenuItem>
              {vendedores.map((vendedor) => (
                <MenuItem key={vendedor.id} value={vendedor.id}>
                  {vendedor.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="operador-label">Operador</InputLabel>
            <Select
              labelId="operador-label"
              value={formData.collectorId || ''}
              onChange={handleChange('collectorId')}
              label={COLUMN_LABELS.collectorName}
              displayEmpty
            >
              <MenuItem value="">
                <em>Selecione um operador</em>
              </MenuItem>
              {operadores.map((operador) => (
                <MenuItem key={operador.id} value={operador.id}>
                  {operador.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="status-label">{COLUMN_LABELS.status}</InputLabel>
            <Select
              labelId="status-label"
              name="status"
              value={formData.status || ''}
              onChange={handleSelectChange}
              label={COLUMN_LABELS.status}
            >
              {situacoesVenda.map((situacao) => (
                <MenuItem key={situacao} value={situacao}>
                  {situacao}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="subtitle2" sx={{ mt: 2, mb: 1, color: '#637381', fontWeight: 600 }}>
            Endereço de Entrega
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Rua"
            name="street"
            value={formData.addressComponents?.street || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Número"
            name="streetNumber"
            value={formData.addressComponents?.streetNumber || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Complemento"
            name="complement"
            value={formData.addressComponents?.complement || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Bairro"
            name="neighborhood"
            value={formData.addressComponents?.neighborhood || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="CEP"
            name="zipCode"
            value={formData.addressComponents?.zipCode || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Cidade"
            name="city"
            value={formData.addressComponents?.city || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Estado"
            name="state"
            value={formData.addressComponents?.state || ''}
            onChange={handleTextChange}
            fullWidth
            size="small"
          />
        </Grid>

        <Grid item xs={12}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={isSaving}
            sx={{
              mt: 1,
              borderRadius: 1.5,
              textTransform: 'none',
            }}
          >
            {isSaving ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AdminEditForm;
