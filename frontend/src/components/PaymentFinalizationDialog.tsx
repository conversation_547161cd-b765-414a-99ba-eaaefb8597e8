import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  InputAdornment,
  Alert,
  Divider,
  Chip,
  LinearProgress,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import {
  AttachMoney as AttachMoneyIcon,
  Receipt as ReceiptIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { Order } from '../types/Order';
import api from '../services/api';

interface PaymentFinalizationDialogProps {
  open: boolean;
  onClose: () => void;
  order: Order | null;
  onConfirm: (paymentData: PaymentData) => void;
}

export interface PaymentData {
  paymentType: 'full' | 'partial' | 'none';
  amountPaid: number;
  paymentMethod: string;
  paymentMethodId: string;
  notes: string;
  isNegotiationComplete?: boolean;
  nextPaymentDate?: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  isActive: boolean;
}

const PaymentFinalizationDialog: React.FC<PaymentFinalizationDialogProps> = ({
  open,
  onClose,
  order,
  onConfirm,
}) => {
  const [paymentType, setPaymentType] = useState<'full' | 'partial' | 'none'>('full');
  const [amountPaid, setAmountPaid] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<string>('');
  const [paymentMethodId, setPaymentMethodId] = useState<string>('');
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loadingMethods, setLoadingMethods] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isNegotiationComplete, setIsNegotiationComplete] = useState<boolean>(false);
  const [nextPaymentDate, setNextPaymentDate] = useState<string>('');

  // Calculate values
  const totalAmount = order?.total || 0;
  const alreadyPaid = order?.paymentReceivedAmount || 0;
  const remainingAmount = totalAmount - alreadyPaid;
  const paymentProgress = totalAmount > 0 ? (alreadyPaid / totalAmount) * 100 : 0;
  
  // Fetch payment methods when dialog opens
  useEffect(() => {
    if (open) {
      fetchPaymentMethods();
    }
  }, [open]);

  useEffect(() => {
    if (open && order) {
      // Set default amount based on payment type
      if (paymentType === 'full') {
        setAmountPaid(remainingAmount.toFixed(2));
      } else {
        setAmountPaid('');
      }
      setError('');
    }
  }, [open, order, paymentType, remainingAmount]);

  const fetchPaymentMethods = async () => {
    try {
      setLoadingMethods(true);
      const response = await api.get('/payment-methods', {
        params: { isActive: true }
      });
      setPaymentMethods(response.data);
      
      // Set default payment method if available
      if (response.data.length > 0 && !paymentMethodId) {
        const defaultMethod = response.data.find((m: PaymentMethod) => m.name.toLowerCase() === 'pix') || response.data[0];
        setPaymentMethod(defaultMethod.name);
        setPaymentMethodId(defaultMethod.id);
      }
    } catch (err) {
      console.error('Error fetching payment methods:', err);
      setError('Erro ao carregar métodos de pagamento');
    } finally {
      setLoadingMethods(false);
    }
  };

  const handlePaymentTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newType = event.target.value as 'full' | 'partial' | 'none';
    setPaymentType(newType);
    
    if (newType === 'full') {
      setAmountPaid(remainingAmount.toFixed(2));
    } else if (newType === 'none') {
      setAmountPaid('0');
    } else {
      setAmountPaid('');
    }
  };

  const handleAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    // Allow only numbers and decimal point
    if (/^\d*\.?\d*$/.test(value) || value === '') {
      setAmountPaid(value);
      setError('');
    }
  };

  const handleConfirm = () => {
    console.log('PaymentFinalizationDialog handleConfirm called');
    console.log('Payment type:', paymentType);
    console.log('Amount paid:', amountPaid);
    console.log('Remaining amount:', remainingAmount);
    
    const amount = parseFloat(amountPaid) || 0;

    // Validation
    if (paymentType === 'partial' && amount <= 0) {
      setError('Por favor, insira um valor válido');
      return;
    }

    if (paymentType === 'partial' && amount > remainingAmount) {
      setError(`O valor não pode ser maior que o saldo devedor (R$ ${remainingAmount.toFixed(2)})`);
      return;
    }

    // Validate payment method
    if (paymentType !== 'none' && !paymentMethodId) {
      setError('Por favor, selecione um método de pagamento');
      return;
    }

    // Validate next payment date for partial payments
    if (paymentType === 'partial' && !isNegotiationComplete && !nextPaymentDate) {
      setError('Por favor, selecione a data do próximo pagamento');
      return;
    }

    const paymentData: PaymentData = {
      paymentType,
      amountPaid: paymentType === 'full' ? remainingAmount : amount,
      paymentMethod: paymentType === 'none' ? '' : paymentMethod,
      paymentMethodId: paymentType === 'none' ? '' : paymentMethodId,
      notes,
      isNegotiationComplete: paymentType === 'partial' ? isNegotiationComplete : false,
      nextPaymentDate: paymentType === 'partial' && !isNegotiationComplete ? nextPaymentDate : undefined,
    };

    console.log('Calling onConfirm with:', paymentData);
    onConfirm(paymentData);
    handleClose();
  };

  const handleClose = () => {
    setPaymentType('full');
    setAmountPaid('');
    setPaymentMethod('');
    setPaymentMethodId('');
    setNotes('');
    setError('');
    setIsNegotiationComplete(false);
    setNextPaymentDate('');
    onClose();
  };

  if (!order) return null;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ReceiptIcon sx={{ color: 'primary.main' }} />
          <Typography variant="h6">Finalizar Cobrança</Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {/* Order Summary */}
        <Box sx={{ 
          bgcolor: 'grey.50', 
          p: 2, 
          borderRadius: 2, 
          mb: 3,
          border: '1px solid',
          borderColor: 'grey.200'
        }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Resumo do Pedido #{order.id}
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Valor Total:</Typography>
            <Typography variant="body2" fontWeight={600}>
              R$ {totalAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Já Recebido:</Typography>
            <Typography variant="body2" fontWeight={600} color="success.main">
              R$ {alreadyPaid.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </Typography>
          </Box>
          
          <Divider sx={{ my: 1 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body1" fontWeight={600}>Saldo Devedor:</Typography>
            <Typography variant="body1" fontWeight={600} color="primary.main">
              R$ {remainingAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </Typography>
          </Box>

          {alreadyPaid > 0 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="caption" color="text.secondary">
                  Progresso do Pagamento
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {paymentProgress.toFixed(0)}%
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={paymentProgress} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    bgcolor: paymentProgress === 100 ? 'success.main' : 'primary.main'
                  }
                }}
              />
            </Box>
          )}
        </Box>

        {/* Payment Type Selection */}
        <FormControl component="fieldset" sx={{ width: '100%', mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
            Tipo de Pagamento
          </Typography>
          <RadioGroup value={paymentType} onChange={handlePaymentTypeChange}>
            <FormControlLabel
              value="full"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20 }} />
                  <Typography>
                    Pagamento Total (R$ {remainingAmount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})
                  </Typography>
                </Box>
              }
            />
            <FormControlLabel
              value="partial"
              control={<Radio />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WarningIcon sx={{ color: 'warning.main', fontSize: 20 }} />
                  <Typography>Pagamento Parcial</Typography>
                </Box>
              }
            />
            <FormControlLabel
              value="none"
              control={<Radio />}
              label="Não Recebido"
            />
          </RadioGroup>
        </FormControl>

        {/* Payment Details */}
        {paymentType !== 'none' && (
          <>
            {paymentType === 'partial' && (
              <TextField
                fullWidth
                label="Valor Recebido"
                value={amountPaid}
                onChange={handleAmountChange}
                error={!!error}
                helperText={error}
                InputProps={{
                  startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                }}
                sx={{ mb: 3 }}
                autoFocus
              />
            )}

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Método de Pagamento</InputLabel>
              <Select
                value={paymentMethodId}
                onChange={(e) => {
                  const selectedMethod = paymentMethods.find(m => m.id === e.target.value);
                  if (selectedMethod) {
                    setPaymentMethodId(selectedMethod.id);
                    setPaymentMethod(selectedMethod.name);
                  }
                }}
                label="Método de Pagamento"
                disabled={loadingMethods}
              >
                {loadingMethods ? (
                  <MenuItem disabled>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CircularProgress size={16} />
                      <span>Carregando...</span>
                    </Box>
                  </MenuItem>
                ) : paymentMethods.length === 0 ? (
                  <MenuItem disabled>Nenhum método disponível</MenuItem>
                ) : (
                  paymentMethods.map((method) => (
                    <MenuItem key={method.id} value={method.id}>
                      {method.name}
                    </MenuItem>
                  ))
                )}
              </Select>
            </FormControl>

            {/* Negotiation Complete Checkbox - Only for partial payments */}
            {paymentType === 'partial' && (
              <Box sx={{ 
                mb: 3, 
                p: 2, 
                bgcolor: 'info.lighter',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'info.light'
              }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isNegotiationComplete}
                      onChange={(e) => setIsNegotiationComplete(e.target.checked)}
                      color="primary"
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        Negociação completa?
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'text.secondary', mt: 0.5 }}>
                        Marque se este valor parcial é o pagamento final acordado com o cliente.
                        O pedido será marcado como "Completo" mesmo sendo um valor menor que o original.
                      </Typography>
                    </Box>
                  }
                />
              </Box>
            )}
            
            {/* Next Payment Date - Only for partial payments without negotiation complete */}
            {paymentType === 'partial' && !isNegotiationComplete && (
              <TextField
                fullWidth
                label="Data do Próximo Pagamento"
                type="date"
                value={nextPaymentDate}
                onChange={(e) => setNextPaymentDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  min: new Date().toISOString().split('T')[0], // Minimum date is today
                }}
                sx={{ mb: 3 }}
                error={!!error && error.includes('data')}
                helperText="Quando o cliente prometeu fazer o próximo pagamento"
                required
              />
            )}
          </>
        )}

        {/* Notes */}
        <TextField
          fullWidth
          label="Observações"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          multiline
          rows={3}
          placeholder="Adicione observações sobre este pagamento..."
        />

        {/* Status Preview */}
        <Alert 
          severity={
            paymentType === 'full' || (paymentType === 'partial' && isNegotiationComplete) 
              ? 'success' 
              : paymentType === 'partial' 
                ? 'warning' 
                : 'error'
          }
          sx={{ mt: 3 }}
          icon={paymentType === 'full' || (paymentType === 'partial' && isNegotiationComplete) ? <CheckCircleIcon /> : <WarningIcon />}
        >
          {paymentType === 'full' && 'O pedido será marcado como "Completo"'}
          {paymentType === 'partial' && isNegotiationComplete && 'O pedido será marcado como "Completo" (negociação finalizada)'}
          {paymentType === 'partial' && !isNegotiationComplete && 'O pedido será marcado como "Negociação"'}
          {paymentType === 'none' && 'O pedido permanecerá como "Pendente"'}
        </Alert>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={handleClose} color="inherit">
          Cancelar
        </Button>
        <Button 
          onClick={handleConfirm} 
          variant="contained"
          startIcon={<AttachMoneyIcon />}
        >
          Confirmar Pagamento
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentFinalizationDialog;