import React, { useState } from 'react';
import { Button, Alert, CircularProgress, Box } from '@mui/material';
import { setupTestData } from '../utils/setupTestData';

const SetupTestData: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSetup = async () => {
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      await setupTestData();
      setSuccess(true);
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err: any) {
      setError(err.message || 'Failed to setup test data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ position: 'fixed', bottom: 20, right: 20, zIndex: 9999 }}>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 2 }}>Test data created successfully! Reloading...</Alert>}
      <Button
        variant="contained"
        color="secondary"
        onClick={handleSetup}
        disabled={loading}
        startIcon={loading ? <CircularProgress size={20} /> : null}
      >
        {loading ? 'Setting up...' : 'Setup Test Data'}
      </Button>
    </Box>
  );
};

export default SetupTestData;