import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Typography,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
  useTheme,
  alpha,
  Chip,
  ListItem,
} from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import { styled } from '@mui/material/styles';

// Icons
import DashboardRoundedIcon from '@mui/icons-material/DashboardRounded';
import ShoppingBagRoundedIcon from '@mui/icons-material/ShoppingBagRounded';
import InventoryRoundedIcon from '@mui/icons-material/InventoryRounded';
import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import AttachMoneyRoundedIcon from '@mui/icons-material/AttachMoneyRounded';
import SecurityRoundedIcon from '@mui/icons-material/SecurityRounded';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import AssessmentRoundedIcon from '@mui/icons-material/AssessmentRounded';
// Status icons
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import PaidOutlinedIcon from '@mui/icons-material/PaidOutlined';
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import HourglassEmptyOutlinedIcon from '@mui/icons-material/HourglassEmptyOutlined';
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';
import ReplayOutlinedIcon from '@mui/icons-material/ReplayOutlined';
import HandshakeOutlinedIcon from '@mui/icons-material/HandshakeOutlined';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import ThumbUpAltOutlinedIcon from '@mui/icons-material/ThumbUpAltOutlined';
import KeyboardReturnOutlinedIcon from '@mui/icons-material/KeyboardReturnOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import ContentCopyOutlinedIcon from '@mui/icons-material/ContentCopyOutlined';
import DeleteIcon from '@mui/icons-material/Delete';

import UnifiedAuthService from '../services/UnifiedAuthService';
import { Order } from '../types/Order';
import { StatusFilter } from './Sidebar';
import { useOrderData } from '../contexts/OrderDataContext';

interface ModernSidebarProps {
  orders?: Order[];
  onStatusSelect?: (filter: StatusFilter | null) => void;
  selectedStatus?: StatusFilter | null;
  open?: boolean;
  onToggle?: (open: boolean) => void;
}

const drawerWidth = 260;
const drawerWidthCollapsed = 72;

const StyledDrawer = styled(Drawer, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
  width: open ? drawerWidth : drawerWidthCollapsed,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  boxSizing: 'border-box',
  [theme.breakpoints.down('sm')]: {
    width: 0,
  },
  '& .MuiDrawer-paper': {
    width: open ? drawerWidth : drawerWidthCollapsed,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: 'hidden',
    overflowY: 'auto',
    backgroundColor: theme.palette.mode === 'dark' ? '#0F1420' : '#ffffff',
    color: theme.palette.mode === 'dark' ? '#E4E4E7' : '#000000',
    borderRight: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.08)',
    boxShadow: theme.palette.mode === 'dark' ? '4px 0 24px rgba(0, 0, 0, 0.24)' : '2px 0 8px rgba(0, 0, 0, 0.08)',
    marginTop: '64px',
    height: 'calc(100vh - 64px)',
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      position: 'fixed',
      width: open ? '80vw' : 0,
      maxWidth: drawerWidth,
      zIndex: theme.zIndex.drawer,
    },
  },
}));

const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
  margin: '4px 8px',
  borderRadius: '8px',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.08) : 'rgba(0, 0, 0, 0.04)',
  },
  '&.Mui-selected': {
    backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.16) : 'rgba(33, 150, 243, 0.08)',
    color: '#2196F3',
    '&:hover': {
      backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.24) : 'rgba(33, 150, 243, 0.12)',
    },
    '& .MuiListItemIcon-root': {
      color: '#2196F3',
    },
    '& .MuiListItemText-primary': {
      color: '#2196F3',
      fontWeight: 600,
    },
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
  minWidth: 45,
  color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'inherit',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  padding: '20px 24px 8px',
  fontSize: '0.75rem',
  fontWeight: 600,
  letterSpacing: '0.1em',
  textTransform: 'uppercase',
  color: theme.palette.mode === 'dark' ? '#71717A' : '#64748B',
}));

// Helper function to normalize strings for comparison
const normalizeString = (str: string) => {
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '').toLowerCase().replace(/\s+/g, '');
};

const ModernSidebar: React.FC<ModernSidebarProps> = ({ 
  orders: propOrders,
  onStatusSelect, 
  selectedStatus, 
  open: propOpen, 
  onToggle 
}) => {
  const theme = useTheme();
  const location = useLocation();
  const [localOpen, setLocalOpen] = useState(true);
  const open = propOpen !== undefined ? propOpen : localOpen;
  const [userInfo, setUserInfo] = useState<any>(null);
  const [currentUserEmail, setCurrentUserEmail] = useState('');
  const [currentUserName, setCurrentUserName] = useState('');
  
  // Use orders from context or props
  const { orders: contextOrders, getStatusCounts } = useOrderData();
  const orders = propOrders || contextOrders;

  const isSeller = userInfo?.role === 'seller';
  const isAdmin = userInfo?.role === 'admin';
  const isSupervisor = userInfo?.role === 'supervisor';
  const isOperator = userInfo?.role === 'collector';

  useEffect(() => {
    const info = UnifiedAuthService.getUserInfo();
    setUserInfo(info);
    if (info) {
      setCurrentUserEmail(info.email);
      setCurrentUserName(info.fullName);
    }
  }, []);

  const handleDrawerToggle = () => {
    const newState = !open;
    if (propOpen === undefined) {
      setLocalOpen(newState);
    }
    if (onToggle) {
      onToggle(newState);
    }
  };

  // Get memoized status counts from context
  const statusCounts = useMemo(() => getStatusCounts(), [getStatusCounts, orders]);
  
  // Helper to get count for a status
  const getStatusCount = (status: string): number => {
    const normalizedStatus = normalizeString(status);
    return statusCounts[normalizedStatus] || 0;
  };

  const statusItems = useMemo(() => [
    { text: 'Todos os Pedidos', icon: <ArticleOutlinedIcon />, count: statusCounts['todos'] || 0, filter: null },
    { text: 'Completo', icon: <CheckCircleOutlinedIcon sx={{ color: '#81C784' }} />, count: getStatusCount('completo'), filter: { field: 'status', value: 'Completo' } },
    { text: 'Receber Hoje', icon: <CalendarTodayOutlinedIcon sx={{ color: '#2196F3' }} />, count: statusCounts['receberhoje'] || 0, filter: { field: 'special', value: 'paymentReceivedDate' } },
    { text: 'Confirmar Entrega', icon: <ThumbUpAltOutlinedIcon sx={{ color: '#9C27B0' }} />, count: getStatusCount('confirmar entrega'), filter: { field: 'status', value: 'Confirmar Entrega' } },
    { text: 'Retirar Correios', icon: <LocalShippingOutlinedIcon sx={{ color: '#FF7043' }} />, count: getStatusCount('retirar correios'), filter: { field: 'status', value: 'Retirar Correios' } },
    { text: 'Entrega Falha', icon: <WarningAmberOutlinedIcon sx={{ color: '#F44336' }} />, count: getStatusCount('entrega falha'), filter: { field: 'status', value: 'Entrega Falha' } },
    { text: 'Devolvido Correios', icon: <KeyboardReturnOutlinedIcon sx={{ color: '#FF5722' }} />, count: getStatusCount('devolvido correios'), filter: { field: 'status', value: 'Devolvido Correios' } },
    { text: 'Promessa', icon: <AccessTimeOutlinedIcon sx={{ color: '#CE93D8' }} />, count: getStatusCount('promessa'), filter: { field: 'status', value: 'Promessa' } },
    { text: 'Negociação', icon: <HandshakeOutlinedIcon sx={{ color: '#3F51B5' }} />, count: getStatusCount('negociacao'), filter: { field: 'status', value: 'Negociação' } },
    { text: 'Pagamento Pendente', icon: <PaidOutlinedIcon sx={{ color: '#FFE082' }} />, count: getStatusCount('pagamento pendente'), filter: { field: 'status', value: 'Pagamento Pendente' } },
    { text: 'Recuperação', icon: <ReplayOutlinedIcon sx={{ color: '#673AB7' }} />, count: getStatusCount('recuperacao'), filter: { field: 'status', value: 'Recuperação' } },
    { text: 'Análise', icon: <HourglassEmptyOutlinedIcon sx={{ color: '#FFD54F' }} />, count: getStatusCount('analise'), filter: { field: 'status', value: 'Análise' } },
    { text: 'Separação', icon: <HourglassEmptyOutlinedIcon sx={{ color: '#2196F3' }} />, count: getStatusCount('separacao'), filter: { field: 'status', value: 'Separação' } },
    { text: 'Trânsito', icon: <LocalShippingOutlinedIcon sx={{ color: '#90CAF9' }} />, count: getStatusCount('transito'), filter: { field: 'status', value: 'Trânsito' } },
    { text: 'Frustrado', icon: <ErrorOutlineOutlinedIcon sx={{ color: '#F44336' }} />, count: getStatusCount('frustrado'), filter: { field: 'status', value: 'Frustrado' } },
    { text: 'Cancelado', icon: <CancelOutlinedIcon sx={{ color: '#E57373' }} />, count: getStatusCount('cancelado'), filter: { field: 'status', value: 'Cancelado' } },
  ], [statusCounts, getStatusCount]);

  const adminStatusItems = useMemo(() => [
    { text: 'Deletado', icon: <DeleteIcon sx={{ color: '#F44336' }} />, count: getStatusCount('deletado'), filter: { field: 'status', value: 'Deletado' } },
  ], [getStatusCount]);

  const isFilterEqual = (a: StatusFilter | null, b: StatusFilter | null) => {
    if (a === null && b === null) return true;
    if (a === null || b === null) return false;
    return a.field === b.field && a.value === b.value;
  };

  const handleStatusClick = (filter: StatusFilter | null) => {
    console.log('[ModernSidebar] Status clicked:', filter);
    console.log('[ModernSidebar] Current user role:', userInfo?.role);
    console.log('[ModernSidebar] Status counts:', statusCounts);
    
    if (onStatusSelect) {
      onStatusSelect(filter);
    }
  };

  const isActive = (path: string) => location.pathname === path;

  const handleLogout = () => {
    UnifiedAuthService.logout();
  };

  const listItemStyle = {
    borderRadius: '8px',
    mb: 0.5,
    py: 1,
    px: 1.5,
    '&.Mui-selected': {
      bgcolor: 'rgba(33, 150, 243, 0.08)',
      color: '#2196F3',
      '& .MuiListItemIcon-root': {
        color: '#2196F3',
      },
      '&:hover': {
        bgcolor: 'rgba(33, 150, 243, 0.12)',
      }
    },
    '&:hover': {
      bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)'
    }
  };

  // Debug log to see what's happening
  useEffect(() => {
    console.log('ModernSidebar Debug:', {
      ordersLength: orders.length,
      statusCounts,
      currentUserName,
      currentUserEmail,
      userRole: userInfo?.role
    });
  }, [orders.length, statusCounts, currentUserName, currentUserEmail, userInfo]);

  return (
    <StyledDrawer variant="permanent" open={open}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Toggle Button */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
          <IconButton
            onClick={handleDrawerToggle}
            sx={{
              color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'text.secondary',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.08),
              },
            }}
          >
            {open ? <ChevronLeftRoundedIcon /> : <ChevronRightRoundedIcon />}
          </IconButton>
        </Box>

        {/* Main Navigation */}
        <List sx={{ pt: 2, pl: 2, pr: 0.5 }}>
          <ListItem
            button
            component={Link}
            to="/dashboard"
            selected={location.pathname === '/dashboard'}
            sx={listItemStyle}
          >
            <ListItemIcon>
              <DashboardRoundedIcon sx={{ color: location.pathname === '/dashboard' ? '#2196F3' : undefined }} />
            </ListItemIcon>
            {open && <ListItemText primary="Dashboard" />}
          </ListItem>

          <ListItem
            button
            component={Link}
            to="/dashboard/pedidos"
            selected={location.pathname === '/dashboard/pedidos'}
            sx={listItemStyle}
          >
            <ListItemIcon>
              <ShoppingBagRoundedIcon sx={{ color: location.pathname === '/dashboard/pedidos' ? '#2196F3' : '#FF9800' }} />
            </ListItemIcon>
            {open && <ListItemText primary="Pedidos" />}
          </ListItem>

          {(isAdmin || isSupervisor) && (
            <ListItem
              button
              component={Link}
              to="/dashboard/produtos"
              selected={location.pathname === '/dashboard/produtos'}
              sx={listItemStyle}
            >
              <ListItemIcon>
                <InventoryRoundedIcon sx={{ color: location.pathname === '/dashboard/produtos' ? '#2196F3' : '#9C27B0' }} />
              </ListItemIcon>
              {open && <ListItemText primary="Produtos" />}
            </ListItem>
          )}

          {(isAdmin || isSupervisor) && (
            <>
              <ListItem
                button
                component={Link}
                to="/dashboard/antifraud"
                selected={location.pathname === '/dashboard/antifraud'}
                sx={listItemStyle}
              >
                <ListItemIcon>
                  <SecurityRoundedIcon sx={{ color: location.pathname === '/dashboard/antifraud' ? '#2196F3' : '#F44336' }} />
                </ListItemIcon>
                {open && <ListItemText primary="Anti-Fraude" />}
              </ListItem>

              <ListItem
                button
                component={Link}
                to="/dashboard/payment-confirmations"
                selected={location.pathname === '/dashboard/payment-confirmations'}
                sx={listItemStyle}
              >
                <ListItemIcon>
                  <AttachMoneyRoundedIcon sx={{ color: location.pathname === '/dashboard/payment-confirmations' ? '#2196F3' : '#4CAF50' }} />
                </ListItemIcon>
                {open && <ListItemText primary="Confirmar Pagamentos" />}
              </ListItem>

              <ListItem
                button
                component={Link}
                to="/dashboard/zaps"
                selected={location.pathname === '/dashboard/zaps'}
                sx={listItemStyle}
              >
                <ListItemIcon>
                  <WhatsAppIcon sx={{ color: location.pathname === '/dashboard/zaps' ? '#2196F3' : '#25D366' }} />
                </ListItemIcon>
                {open && <ListItemText primary="WhatsApp Sources" />}
              </ListItem>

              {isAdmin && (
                <ListItem
                  button
                  component={Link}
                  to="/dashboard/zap-reports"
                  selected={location.pathname.includes('/dashboard/zap-reports')}
                  sx={listItemStyle}
                >
                  <ListItemIcon>
                    <AssessmentRoundedIcon sx={{ color: location.pathname.includes('/dashboard/zap-reports') ? '#2196F3' : '#25D366' }} />
                  </ListItemIcon>
                  {open && <ListItemText primary="Relatórios WhatsApp" />}
                </ListItem>
              )}
            </>
          )}
        </List>

        {/* Status Filters for Pedidos page */}
        {location.pathname === '/dashboard/pedidos' && open && (
          <>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ pl: 1.5, pr: 1, pb: 2 }}>
              <SectionTitle>Filtros de Status</SectionTitle>
              <List sx={{ p: 0 }}>
                {statusItems.map((item, index) => (
                  <ListItem
                    button
                    key={index}
                    selected={!!(selectedStatus && isFilterEqual(selectedStatus, item.filter))}
                    onClick={() => handleStatusClick(item.filter)}
                    sx={{
                      py: 1,
                      borderRadius: '8px',
                      mb: 0.5,
                      '&.Mui-selected': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(33, 150, 243, 0.12)'
                        }
                      },
                      '&:hover': {
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                      }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 45 }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{ fontSize: '0.875rem', fontWeight: 500 }}
                    />
                    <Chip
                      label={item.count}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.70rem',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                        color: theme.palette.mode === 'dark' ? '#A1A1AA' : '#64748B',
                        fontWeight: 600,
                      }}
                    />
                  </ListItem>
                ))}

                {/* Admin-only deleted status */}
                {isAdmin && adminStatusItems.map((item, index) => (
                  <ListItem
                    button
                    key={`admin-${index}`}
                    selected={!!(selectedStatus && isFilterEqual(selectedStatus, item.filter))}
                    onClick={() => handleStatusClick(item.filter)}
                    sx={{
                      py: 1,
                      mt: 1,
                      borderTop: '1px dashed',
                      borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                      borderTopLeftRadius: 0,
                      borderTopRightRadius: 0
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 45 }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: '#D32F2F'
                      }}
                    />
                    <Chip
                      label={item.count}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.70rem',
                        bgcolor: 'rgba(211, 47, 47, 0.1)',
                        color: '#D32F2F',
                        fontWeight: 600,
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </>
        )}

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* User Profile (at bottom) */}
        {open && userInfo && (
          <>
            <Divider sx={{ borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.06)', mx: 2 }} />
            <Box sx={{ px: 3, py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: theme.palette.primary.main,
                    fontSize: '1rem',
                  }}
                >
                  {userInfo.fullName?.charAt(0).toUpperCase()}
                </Avatar>
                <Box sx={{ overflow: 'hidden' }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 600,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {userInfo.fullName}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'text.secondary',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {userInfo.role === 'admin' ? 'Administrador' :
                     userInfo.role === 'supervisor' ? 'Supervisor' :
                     userInfo.role === 'collector' ? 'Cobrador' :
                     userInfo.role === 'seller' ? 'Vendedor' : userInfo.role}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </>
        )}

        {/* Logout Button */}
        <Box sx={{ p: 2 }}>
          <StyledListItemButton
            onClick={handleLogout}
            sx={{
              color: '#EF4444',
              '&:hover': {
                backgroundColor: alpha('#EF4444', 0.08),
              },
            }}
          >
            <Tooltip title={!open ? 'Sair' : ''} placement="right">
              <StyledListItemIcon sx={{ color: '#EF4444' }}>
                <LogoutRoundedIcon />
              </StyledListItemIcon>
            </Tooltip>
            {open && (
              <ListItemText
                primary="Sair"
                primaryTypographyProps={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              />
            )}
          </StyledListItemButton>
        </Box>
      </Box>
    </StyledDrawer>
  );
};

export default React.memo(ModernSidebar);
export { drawerWidth, drawerWidthCollapsed };