import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  Grid,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Autocomplete,
  InputAdornment,
  Divider,
  Chip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
  ShoppingCart as ShoppingCartIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Note as NoteIcon,
} from '@mui/icons-material';
import axios from 'axios';
import productService from '../services/ProductService';
import { kitService } from '../services/KitService';
import { VariationType } from '../types/Product';
import { useAuth } from '../contexts/AuthContext';
import { useUsuarios } from '../contexts/UsuariosContext';

interface DialogOrderItem {
  kitId: string;
  kitName: string;
  productName: string;
  quantity: number;
  unitPrice: number;
}

interface OrderCreationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (order: {
    customerName: string;
    customerPhone: string;
    customerCPF: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      unitPrice: number;
    }>;
    address?: {
      cep: string;
      street: string;
      number: string;
      complement?: string;
      neighborhood: string;
      city: string;
      state: string;
    };
    observation?: string;
    customerId?: string;
    collectorId?: string;
    zapId?: string;
  }) => Promise<void>;
}

const OrderCreationDialog: React.FC<OrderCreationDialogProps> = ({
  open,
  onClose,
  onSave,
}) => {
  // Get auth context for current user
  const { userInfo } = useAuth();
  
  // Get usuarios context for operator assignment
  const { usuarios, lastOperatorIndex, setLastOperatorIndex, fetchUsuarios } = useUsuarios();
  
  const [loading, setLoading] = useState(false);
  const [retryMessage, setRetryMessage] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [customerCPF, setCustomerCPF] = useState('');
  const [items, setItems] = useState<DialogOrderItem[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Address fields
  const [cep, setCep] = useState('');
  const [street, setStreet] = useState('');
  const [number, setNumber] = useState('');
  const [complement, setComplement] = useState('');
  const [neighborhood, setNeighborhood] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [loadingCep, setLoadingCep] = useState(false);
  
  // Observation
  const [observation, setObservation] = useState('');
  
  // Product and kit data
  const [products, setProducts] = useState<any[]>([]);
  const [kits, setKits] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  
  // Fetch usuarios on mount if needed
  useEffect(() => {
    if (usuarios.length === 0) {
      fetchUsuarios();
    }
  }, [usuarios.length, fetchUsuarios]);
  const [selectedKit, setSelectedKit] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [loadingKits, setLoadingKits] = useState(false);

  // Load products when dialog opens
  useEffect(() => {
    if (open) {
      loadProducts();
      // Reset form
      setCustomerName('');
      setCustomerPhone('');
      setItems([]);
      setSelectedProduct(null);
      setSelectedKit(null);
      setKits([]);
      setQuantity(1);
      setErrors({});
      // Reset address
      setCep('');
      setStreet('');
      setNumber('');
      setComplement('');
      setNeighborhood('');
      setCity('');
      setState('');
      setObservation('');
    }
  }, [open]);

  // Load kits when product is selected
  useEffect(() => {
    if (selectedProduct) {
      loadKitsForProduct(selectedProduct.id);
    } else {
      setKits([]);
      setSelectedKit(null);
    }
  }, [selectedProduct]);

  const loadProducts = async () => {
    try {
      console.log('OrderCreationDialog - Loading products...');
      const productsData = await productService.getActiveProducts();
      console.log('OrderCreationDialog - Products loaded:', productsData);
      setProducts(productsData);
    } catch (error: any) {
      console.error('Error loading products:', error);
    }
  };

  const loadKitsForProduct = async (productId: string) => {
    try {
      setLoadingKits(true);
      console.log('OrderCreationDialog - Loading kits for product:', productId);
      const productKits = await kitService.getKitsByProductId(productId);
      console.log('OrderCreationDialog - Kits loaded:', productKits);
      // Filter only active kits
      const activeKits = productKits.filter(kit => kit.active);
      console.log('OrderCreationDialog - Active kits:', activeKits);
      setKits(activeKits);
    } catch (error: any) {
      console.error('Error loading kits:', error);
    } finally {
      setLoadingKits(false);
    }
  };

  const formatPhone = (value: string) => {
    // Remove non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Format as Brazilian phone number
    if (numbers.length <= 11) {
      if (numbers.length <= 2) return numbers;
      if (numbers.length <= 6) return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      if (numbers.length <= 10) return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    }
    return value;
  };

  const formatCpf = (value: string) => {
    // Remove non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Format as CPF (000.000.000-00)
    if (numbers.length <= 11) {
      if (numbers.length <= 3) return numbers;
      if (numbers.length <= 6) return `${numbers.slice(0, 3)}.${numbers.slice(3)}`;
      if (numbers.length <= 9) return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6)}`;
      return `${numbers.slice(0, 3)}.${numbers.slice(3, 6)}.${numbers.slice(6, 9)}-${numbers.slice(9, 11)}`;
    }
    return value;
  };

  const handleCpfChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedCpf = formatCpf(e.target.value);
    setCustomerCPF(formattedCpf);
  };

  const formatCep = (value: string) => {
    // Remove non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Format as CEP (00000-000)
    if (numbers.length <= 8) {
      if (numbers.length <= 5) return numbers;
      return `${numbers.slice(0, 5)}-${numbers.slice(5, 8)}`;
    }
    return value;
  };

  const handleCepChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedCep = formatCep(e.target.value);
    setCep(formattedCep);
    
    // Check if CEP is complete (8 digits)
    const cleanCep = formattedCep.replace(/\D/g, '');
    if (cleanCep.length === 8) {
      setLoadingCep(true);
      setErrors({});
      
      try {
        // Try ViaCEP API
        const response = await axios.get(`https://viacep.com.br/ws/${cleanCep}/json/`);
        
        if (response.data && !response.data.erro) {
          setStreet(response.data.logradouro || '');
          setNeighborhood(response.data.bairro || '');
          setCity(response.data.localidade || '');
          setState(response.data.uf || '');
          
          // Clear address error if it exists
          const newErrors = { ...errors };
          delete newErrors.address;
          setErrors(newErrors);
        } else {
          // CEP not found - mark fields for manual entry
          setErrors({ ...errors, address: 'CEP não encontrado. Por favor, preencha o endereço manualmente.' });
          setStreet('');
          setNeighborhood('');
          setCity('');
          setState('');
        }
      } catch (error: any) {
        console.error('Error fetching CEP:', error);
        setErrors({ ...errors, address: 'Erro ao buscar CEP. Por favor, preencha o endereço manualmente.' });
      } finally {
        setLoadingCep(false);
      }
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setCustomerPhone(formatted);
  };

  const addItem = () => {
    if (!selectedProduct || !selectedKit) return;

    console.log('Adding item - selectedKit:', selectedKit);
    console.log('Kit ID:', selectedKit.id);

    const newItem: DialogOrderItem = {
      kitId: selectedKit.id,
      kitName: selectedKit.name,
      productName: selectedProduct.name,
      quantity,
      unitPrice: Number(selectedKit.price) || 0,
    };

    setItems([...items, newItem]);
    setSelectedProduct(null);
    setSelectedKit(null);
    setQuantity(1);
  };

  const getKitComposition = (kit: any) => {
    if (!kit.items) return '';
    return kit.items.map((item: any) => {
      const variation = item.productVariation;
      if (!variation) return '';
      return `${item.quantity} ${variation.variation || ''}`;
    }).join(', ');
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    const newItems = [...items];
    newItems[index].quantity = newQuantity;
    setItems(newItems);
  };

  const getTotalAmount = () => {
    return items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!customerName.trim()) {
      newErrors.name = 'Nome do cliente é obrigatório';
    }

    if (!customerPhone.trim()) {
      newErrors.phone = 'Telefone é obrigatório';
    } else if (customerPhone.replace(/\D/g, '').length < 10) {
      newErrors.phone = 'Telefone inválido';
    }

    if (!customerCPF.trim()) {
      newErrors.cpf = 'CPF é obrigatório';
    } else if (customerCPF.replace(/\D/g, '').length !== 11) {
      newErrors.cpf = 'CPF inválido - deve ter 11 dígitos';
    }

    if (items.length === 0) {
      newErrors.items = 'Adicione pelo menos um item ao pedido';
    }

    // Validate address
    if (!cep.trim()) {
      newErrors.cep = 'CEP é obrigatório';
    }
    if (!street.trim()) {
      newErrors.street = 'Rua é obrigatória';
    }
    if (!number.trim()) {
      newErrors.number = 'Número é obrigatório';
    }
    if (!neighborhood.trim()) {
      newErrors.neighborhood = 'Bairro é obrigatório';
    }
    if (!city.trim()) {
      newErrors.city = 'Cidade é obrigatória';
    }
    if (!state.trim()) {
      newErrors.state = 'Estado é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Track last submission time to prevent rapid clicks
  const lastSubmissionRef = React.useRef<number>(0);
  
  const resetForm = () => {
    setCustomerName('');
    setCustomerPhone('');
    setCustomerCPF('');
    setItems([]);
    setCep('');
    setStreet('');
    setNumber('');
    setComplement('');
    setNeighborhood('');
    setCity('');
    setState('');
    setObservation('');
    setErrors({});
    setSelectedProduct(null);
    setSelectedKit(null);
    setQuantity(1);
    setRetryMessage('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    // Prevent rapid clicks (minimum 2 seconds between submissions)
    const now = Date.now();
    if (now - lastSubmissionRef.current < 2000) {
      console.log('Preventing rapid submission');
      return;
    }
    lastSubmissionRef.current = now;

    setLoading(true);
    setRetryMessage('');
    
    try {
      // Get operadores for round-robin assignment - include admin and supervisor
      const operadores = usuarios.filter(u =>
        u.role === 'operador' || u.role === 'collector' || u.role === 'admin' || u.role === 'supervisor'
      );
      
      let assignedOperatorId: string | undefined;
      if (operadores.length > 0) {
        // Round-robin assignment
        const nextIndex = (lastOperatorIndex + 1) % operadores.length;
        const assignedOperator = operadores[nextIndex];
        assignedOperatorId = assignedOperator.id;
        setLastOperatorIndex(nextIndex);
        console.log(`Assigned operator: ${assignedOperator.name} (index: ${nextIndex})`);
      }
      
      // Log items to debug
      console.log('Items being sent:', items);
      console.log('Mapped items:', items.map(item => ({
        productId: item.kitId,
        productName: `${item.productName} - ${item.kitName}`,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
      })));
      
      await onSave({
        customerName,
        customerPhone: customerPhone.replace(/\D/g, ''), // Send only numbers
        customerCPF: customerCPF.replace(/\D/g, ''), // Send only numbers
        items: items.map(item => ({
          productId: item.kitId,
          productName: `${item.productName} - ${item.kitName}`,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
        address: {
          cep: cep.replace(/\D/g, ''),
          street,
          number,
          complement,
          neighborhood,
          city,
          state,
        },
        observation: observation || undefined,
        collectorId: assignedOperatorId,
      });
      resetForm();
      onClose();
    } catch (error: any) {
      console.error('Error creating order:', error);
      
      // Check if it's a retry message
      if (error.message && error.message.includes('tentativas')) {
        setRetryMessage('O sistema está tentando novamente... Por favor, aguarde.');
      }
      
      // Don't close the dialog on error
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Criar Novo Pedido</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Customer Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Informações do Cliente
            </Typography>
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Nome do Cliente"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon />
                  </InputAdornment>
                ),
              }}
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Telefone"
              value={customerPhone}
              onChange={handlePhoneChange}
              error={!!errors.phone}
              helperText={errors.phone}
              placeholder="(11) 99999-9999"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="CPF"
              value={customerCPF}
              onChange={handleCpfChange}
              error={!!errors.cpf}
              helperText={errors.cpf}
              placeholder="000.000.000-00"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon />
                  </InputAdornment>
                ),
              }}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
          </Grid>

          {/* Address Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Endereço de Entrega
            </Typography>
            {errors.address && (
              <Alert severity="warning" sx={{ mb: 2 }}>{errors.address}</Alert>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="CEP"
              value={cep}
              onChange={handleCepChange}
              error={!!errors.cep}
              helperText={errors.cep || (loadingCep ? 'Buscando endereço...' : '')}
              placeholder="00000-000"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationIcon />
                  </InputAdornment>
                ),
              }}
              required
              disabled={loadingCep}
            />
          </Grid>

          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Rua"
              value={street}
              onChange={(e) => setStreet(e.target.value)}
              error={!!errors.street || !!errors.address}
              helperText={errors.street}
              required
              InputProps={{
                sx: errors.address ? { '& .MuiOutlinedInput-notchedOutline': { borderColor: 'error.main' } } : {}
              }}
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Número"
              value={number}
              onChange={(e) => setNumber(e.target.value)}
              error={!!errors.number}
              helperText={errors.number}
              required
            />
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Complemento"
              value={complement}
              onChange={(e) => setComplement(e.target.value)}
              placeholder="Apto, Bloco, etc."
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Bairro"
              value={neighborhood}
              onChange={(e) => setNeighborhood(e.target.value)}
              error={!!errors.neighborhood || !!errors.address}
              helperText={errors.neighborhood}
              required
              InputProps={{
                sx: errors.address ? { '& .MuiOutlinedInput-notchedOutline': { borderColor: 'error.main' } } : {}
              }}
            />
          </Grid>

          <Grid item xs={12} md={5}>
            <TextField
              fullWidth
              label="Cidade"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              error={!!errors.city || !!errors.address}
              helperText={errors.city}
              required
              InputProps={{
                sx: errors.address ? { '& .MuiOutlinedInput-notchedOutline': { borderColor: 'error.main' } } : {}
              }}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="Estado"
              value={state}
              onChange={(e) => setState(e.target.value)}
              error={!!errors.state || !!errors.address}
              helperText={errors.state}
              required
              inputProps={{ maxLength: 2 }}
              InputProps={{
                sx: errors.address ? { '& .MuiOutlinedInput-notchedOutline': { borderColor: 'error.main' } } : {}
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
          </Grid>

          {/* Product Selection */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Adicionar Produtos
            </Typography>
            {errors.items && (
              <Alert severity="error" sx={{ mb: 2 }}>{errors.items}</Alert>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Autocomplete
              value={selectedProduct}
              onChange={(_, newValue) => {
                setSelectedProduct(newValue);
                setSelectedKit(null);
              }}
              options={products}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField {...params} label="Produto" size="small" />
              )}
            />
          </Grid>

          {selectedProduct && (
            <Grid item xs={12} md={4}>
              <Autocomplete
                value={selectedKit}
                onChange={(_, newValue) => setSelectedKit(newValue)}
                options={kits}
                loading={loadingKits}
                getOptionLabel={(option) => option.name}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Box>
                      <Typography variant="body2">{option.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getKitComposition(option)} - R$ {(Number(option.price) || 0).toFixed(2)}
                      </Typography>
                    </Box>
                  </Box>
                )}
                renderInput={(params) => (
                  <TextField 
                    {...params} 
                    label="Kit" 
                    size="small" 
                    required
                    helperText={loadingKits ? "Carregando kits..." : kits.length === 0 ? "Nenhum kit ativo para este produto" : ""}
                  />
                )}
                disabled={!selectedProduct || loadingKits}
              />
            </Grid>
          )}

          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="Quantidade"
              type="number"
              value={quantity}
              onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
              size="small"
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addItem}
              disabled={!selectedProduct || !selectedKit}
            >
              Adicionar
            </Button>
          </Grid>

          {/* Items List */}
          {items.length > 0 && (
            <Grid item xs={12}>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell align="right">Preço</TableCell>
                      <TableCell align="center">Quantidade</TableCell>
                      <TableCell align="right">Total</TableCell>
                      <TableCell align="center">Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">{item.productName}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {item.kitName}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          R$ {item.unitPrice.toFixed(2)}
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                            <IconButton
                              size="small"
                              onClick={() => updateItemQuantity(index, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                            >
                              <RemoveIcon fontSize="small" />
                            </IconButton>
                            <Typography>{item.quantity}</Typography>
                            <IconButton
                              size="small"
                              onClick={() => updateItemQuantity(index, item.quantity + 1)}
                            >
                              <AddIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          R$ {(item.unitPrice * item.quantity).toFixed(2)}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => removeItem(index)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Typography variant="h6">
                  Total: R$ {getTotalAmount().toFixed(2)}
                </Typography>
              </Box>
            </Grid>
          )}

          {/* Observation */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Observações
            </Typography>
            <TextField
              fullWidth
              label="Observações (opcional)"
              value={observation}
              onChange={(e) => setObservation(e.target.value)}
              multiline
              rows={3}
              placeholder="Digite aqui observações sobre o pedido..."
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <NoteIcon />
                  </InputAdornment>
                ),
              }}
              helperText="Sua assinatura será adicionada automaticamente"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        {retryMessage && (
          <Alert severity="info" sx={{ flex: 1, mr: 2 }}>
            {retryMessage}
          </Alert>
        )}
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || items.length === 0}
          startIcon={<ShoppingCartIcon />}
        >
          {loading ? 'Criando...' : 'Criar Pedido'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderCreationDialog;