import React, { useState } from 'react';
import { Button, Box, Alert } from '@mui/material';
import { createProduct } from '../services/ProductService';

const TestProductCreation: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const testCreateProduct = async () => {
    setError(null);
    setSuccess(false);
    
    const testPayload = {
      name: 'Test Product ' + Date.now(),
      description: 'Test description',
      active: true,
      variations: [
        {
          variation: 'Cápsulas',
          sku: 'TEST-CAP-001',
          price: 10.00
        }
      ]
    };

    console.log('Sending test payload:', testPayload);

    try {
      const result = await createProduct(testPayload);
      console.log('Success:', result);
      setSuccess(true);
    } catch (err: any) {
      console.error('Error details:', err.response?.data);
      const errorMessage = err.response?.data?.message || err.message;
      setError(Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage);
    }
  };

  // This is a development-only component
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Box>
      <Button 
        variant="outlined" 
        color="secondary" 
        onClick={testCreateProduct}
        size="large"
      >
        Test Create Product (Dev)
      </Button>
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Product created successfully!
        </Alert>
      )}
    </Box>
  );
};

export default TestProductCreation;