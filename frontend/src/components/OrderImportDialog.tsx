import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  <PERSON>,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  LinearProgress,
  Checkbox,
  FormControlLabel,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Close as CloseIcon,
  FileUpload as FileUploadIcon,
} from '@mui/icons-material';
import Papa from 'papaparse';
import { Order } from '../types/Order';
import OrderService from '../services/OrderService';
import { useOrderData } from '../contexts/OrderDataContext';
import { useNotificationContext } from '../contexts/NotificationContext';

interface OrderImportDialogProps {
  open: boolean;
  onClose: () => void;
}

interface CSVRow {
  [key: string]: string;
}

interface FieldMapping {
  csvField: string;
  systemField: string;
  required: boolean;
}

interface ImportPreview {
  row: CSVRow;
  status: 'valid' | 'warning' | 'error';
  messages: string[];
  mapped: boolean;
}

const SYSTEM_FIELDS = [
  { value: 'dataVenda', label: 'Data da Venda', required: true },
  { value: 'idVenda', label: 'ID da Venda', required: true },
  { value: 'cliente', label: 'Nome do Cliente', required: true },
  { value: 'telefone', label: 'Telefone', required: true },
  { value: 'oferta', label: 'Produto/Oferta', required: false },
  { value: 'valorVenda', label: 'Valor da Venda', required: true },
  { value: 'status', label: 'Status', required: false },
  { value: 'situacaoVenda', label: 'Situação da Venda', required: false },
  { value: 'valorRecebido', label: 'Valor Recebido', required: false },
  { value: 'historico', label: 'Histórico', required: false },
  { value: 'ultimaAtualizacao', label: 'Última Atualização', required: false },
  { value: 'codigoRastreio', label: 'Código de Rastreio', required: false },
  { value: 'statusCorreios', label: 'Status Correios', required: false },
  { value: 'vendedor', label: 'Vendedor', required: false },
  { value: 'operador', label: 'Operador', required: false },
  { value: 'zap', label: 'WhatsApp', required: false },
  { value: 'estadoDestinatario', label: 'Estado', required: false },
  { value: 'cidadeDestinatario', label: 'Cidade', required: false },
  { value: 'ruaDestinatario', label: 'Rua', required: false },
  { value: 'cepDestinatario', label: 'CEP', required: false },
  { value: 'complementoDestinatario', label: 'Complemento', required: false },
  { value: 'bairroDestinatario', label: 'Bairro', required: false },
  { value: 'numeroEnderecoDestinatario', label: 'Número', required: false },
  { value: 'dataEstimadaChegada', label: 'Data Estimada Chegada', required: false },
  { value: 'codigoAfiliado', label: 'Código Afiliado', required: false },
  { value: 'nomeAfiliado', label: 'Nome Afiliado', required: false },
  { value: 'emailAfiliado', label: 'Email Afiliado', required: false },
  { value: 'documentoAfiliado', label: 'Documento Afiliado', required: false },
  { value: 'dataRecebimento', label: 'Data Recebimento', required: false },
  { value: 'dataNegociacao', label: 'Data Negociação', required: false },
  { value: 'formaPagamento', label: 'Forma de Pagamento', required: false },
  { value: 'documentoCliente', label: 'CPF Cliente', required: false },
  { value: 'parcial', label: 'Pagamento Parcial', required: false },
  { value: 'pagamentoParcial', label: 'Valor Parcial', required: false },
  { value: 'formaPagamentoParcial', label: 'Forma Pagamento Parcial', required: false },
  { value: 'dataPagamentoParcial', label: 'Data Pagamento Parcial', required: false },
];

const DEFAULT_MAPPINGS: { [key: string]: string } = {
  'Data Venda': 'dataVenda',
  'ID Venda': 'idVenda',
  'Cliente': 'cliente',
  'Telefone': 'telefone',
  'Oferta': 'oferta',
  'Valor Venda': 'valorVenda',
  'Status': 'status',
  'Situação Venda': 'situacaoVenda',
  'Valor Recebido': 'valorRecebido',
  'Historico': 'historico',
  'Ultima Atualização': 'ultimaAtualizacao',
  'Código de Rastreio': 'codigoRastreio',
  'Status Correios': 'statusCorreios',
  'Vendedor': 'vendedor',
  'Operador': 'operador',
  'Zap': 'zap',
  'ESTADO DO DESTINATÁRIO': 'estadoDestinatario',
  'CIDADE DO DESTINATÁRIO': 'cidadeDestinatario',
  'RUA DO DESTINATÁRIO': 'ruaDestinatario',
  'CEP DO DESTINATÁRIO': 'cepDestinatario',
  'COMPLEMENTO DO DESTINATÁRIO': 'complementoDestinatario',
  'BAIRRO DO DESTINATÁRIO': 'bairroDestinatario',
  'NÚMERO DO ENDEREÇO DO DESTINATÁRIO': 'numeroEnderecoDestinatario',
  'DATA ESTIMADA DE CHEGADA': 'dataEstimadaChegada',
  'CÓDIGO DO AFILIADO': 'codigoAfiliado',
  'NOME DO AFILIADO': 'nomeAfiliado',
  'E-MAIL DO AFILIADO': 'emailAfiliado',
  'DOCUMENTO DO AFILIADO': 'documentoAfiliado',
  'DATA DE RECEBIMENTO': 'dataRecebimento',
  'Data_Negociacao': 'dataNegociacao',
  'FormaPagamento': 'formaPagamento',
  'DOCUMENTO CLIENTE': 'documentoCliente',
  'Parcial': 'parcial',
  'Pagamento_Parcial': 'pagamentoParcial',
  'FormaPagamentoParcial': 'formaPagamentoParcial',
  'DataPagamentoParcial': 'dataPagamentoParcial',
};

const OrderImportDialog: React.FC<OrderImportDialogProps> = ({ open, onClose }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVRow[]>([]);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [fieldMappings, setFieldMappings] = useState<{ [key: string]: string }>({});
  const [importPreview, setImportPreview] = useState<ImportPreview[]>([]);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<{
    success: number;
    failed: number;
    errors: string[];
  }>({ success: 0, failed: 0, errors: [] });
  const [skipDuplicates, setSkipDuplicates] = useState(true);
  
  const { fetchOrders } = useOrderData();
  const { showNotification } = useNotificationContext();

  const steps = ['Upload do Arquivo', 'Mapear Campos', 'Revisar e Importar'];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      parseCSV(selectedFile);
    }
  };

  const parseCSV = (file: File) => {
    Papa.parse(file, {
      complete: (result) => {
        if (result.data && result.data.length > 0) {
          const headers = Object.keys(result.data[0] as any);
          setCsvHeaders(headers);
          setCsvData(result.data as CSVRow[]);
          
          // Auto-map fields based on default mappings
          const autoMappings: { [key: string]: string } = {};
          headers.forEach(header => {
            if (DEFAULT_MAPPINGS[header]) {
              autoMappings[header] = DEFAULT_MAPPINGS[header];
            }
          });
          setFieldMappings(autoMappings);
          
          setActiveStep(1);
        }
      },
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8',
    });
  };

  const handleMappingChange = (csvField: string, systemField: string) => {
    setFieldMappings(prev => ({
      ...prev,
      [csvField]: systemField,
    }));
  };

  const validateRow = (row: CSVRow): { status: 'valid' | 'warning' | 'error'; messages: string[] } => {
    const messages: string[] = [];
    let status: 'valid' | 'warning' | 'error' = 'valid';

    // Check required fields
    const requiredFields = ['idVenda', 'cliente', 'telefone', 'valorVenda'];
    for (const field of requiredFields) {
      const csvField = Object.keys(fieldMappings).find(key => fieldMappings[key] === field);
      if (!csvField || !row[csvField] || row[csvField].trim() === '') {
        messages.push(`Campo obrigatório ausente: ${field}`);
        status = 'error';
      }
    }

    // Validate phone number
    const phoneField = Object.keys(fieldMappings).find(key => fieldMappings[key] === 'telefone');
    if (phoneField && row[phoneField]) {
      const phone = row[phoneField].replace(/\D/g, '');
      if (phone.length < 10 || phone.length > 11) {
        messages.push('Telefone inválido');
        status = status === 'error' ? 'error' : 'warning';
      }
    }

    // Validate monetary values
    const moneyFields = ['valorVenda', 'valorRecebido', 'pagamentoParcial'];
    moneyFields.forEach(field => {
      const csvField = Object.keys(fieldMappings).find(key => fieldMappings[key] === field);
      if (csvField && row[csvField]) {
        const value = row[csvField].replace('R$', '').replace(/\./g, '').replace(',', '.');
        if (isNaN(parseFloat(value))) {
          messages.push(`Valor monetário inválido: ${field}`);
          status = status === 'error' ? 'error' : 'warning';
        }
      }
    });

    if (messages.length === 0) {
      messages.push('Dados válidos');
    }

    return { status, messages };
  };

  const prepareImportPreview = () => {
    const preview: ImportPreview[] = csvData.slice(0, 10).map(row => {
      const validation = validateRow(row);
      return {
        row,
        status: validation.status,
        messages: validation.messages,
        mapped: true,
      };
    });
    setImportPreview(preview);
    setActiveStep(2);
  };

  const handleImport = async () => {
    setImporting(true);
    setImportProgress(0);
    const results = { success: 0, failed: 0, errors: [] as string[] };

    try {
      const totalRows = csvData.length;
      const batchSize = 5; // Reduced batch size to avoid rate limiting
      const delayBetweenBatches = 1000; // 1 second delay between batches
      
      for (let i = 0; i < totalRows; i += batchSize) {
        const batch = csvData.slice(i, Math.min(i + batchSize, totalRows));
        
        // Process batch sequentially instead of in parallel to avoid rate limiting
        for (let j = 0; j < batch.length; j++) {
          const row = batch[j];
          const rowIndex = i + j;
          
          try {
            // Map CSV row to order object
            const orderData = mapRowToOrder(row);
            
            if (skipDuplicates) {
              // Check if order already exists
              const exists = await OrderService.checkOrderExists(orderData.idVenda);
              if (exists) {
                results.failed++;
                results.errors.push(`Linha ${rowIndex + 2}: Pedido ${orderData.idVenda} já existe`);
                continue;
              }
            }
            
            // Create the order
            await OrderService.importOrder(orderData);
            results.success++;
          } catch (error: any) {
            results.failed++;
            const errorMessage = error.response?.data?.message || error.message || 'Erro desconhecido';
            results.errors.push(`Linha ${rowIndex + 2}: ${errorMessage}`);
            console.error(`Error importing row ${rowIndex + 2}:`, error);
            
            // If we get a rate limit error, add extra delay
            if (error.response?.status === 429) {
              console.log('Rate limit hit, waiting 2 seconds...');
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
          }
          
          // Update progress after each row
          setImportProgress(((rowIndex + 1) / totalRows) * 100);
        }
        
        // Delay between batches to avoid rate limiting
        if (i + batchSize < totalRows) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
      }
      
      setImportResults(results);
      
      if (results.success > 0) {
        await fetchOrders(); // Refresh the orders list
        showNotification(`${results.success} pedidos importados com sucesso!`, 'success');
      }
      
      if (results.failed > 0) {
        showNotification(`${results.failed} pedidos falharam na importação`, 'error');
      }
    } catch (error: any) {
      showNotification('Erro durante a importação', 'error');
      console.error('Import error:', error);
    } finally {
      setImporting(false);
    }
  };

  const mapRowToOrder = (row: CSVRow): any => {
    const order: any = {};
    
    console.log('Mapping row:', row);
    console.log('Field mappings:', fieldMappings);
    
    Object.keys(fieldMappings).forEach(csvField => {
      const systemField = fieldMappings[csvField];
      const value = row[csvField];
      
      console.log(`Mapping ${csvField} (${value}) -> ${systemField}`);
      
      if (value && value.trim() !== '') {
        // Handle monetary values
        if (['valorVenda', 'valorRecebido', 'pagamentoParcial'].includes(systemField)) {
          order[systemField] = parseFloat(value.replace('R$', '').replace(/\./g, '').replace(',', '.'));
        }
        // Handle boolean values
        else if (systemField === 'parcial') {
          order[systemField] = value.toLowerCase() === 'sim' || value === '1' || value === 'true';
        }
        // Handle dates
        else if (systemField.includes('data') || systemField.includes('Data')) {
          order[systemField] = value;
        }
        // Default to string
        else {
          order[systemField] = value;
        }
      }
    });
    
    console.log('Final mapped order:', order);
    return order;
  };

  const handleClose = () => {
    setActiveStep(0);
    setFile(null);
    setCsvData([]);
    setCsvHeaders([]);
    setFieldMappings({});
    setImportPreview([]);
    setImportResults({ success: 0, failed: 0, errors: [] });
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Importar Pedidos do CSV</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step 1: File Upload */}
        {activeStep === 0 && (
          <Box textAlign="center" py={4}>
            <input
              accept=".csv"
              style={{ display: 'none' }}
              id="csv-file-input"
              type="file"
              onChange={handleFileSelect}
            />
            <label htmlFor="csv-file-input">
              <Button
                variant="contained"
                component="span"
                size="large"
                startIcon={<CloudUploadIcon />}
              >
                Selecionar Arquivo CSV
              </Button>
            </label>
            
            {file && (
              <Box mt={2}>
                <Chip
                  icon={<FileUploadIcon />}
                  label={file.name}
                  color="primary"
                  onDelete={() => setFile(null)}
                />
              </Box>
            )}
            
            <Alert severity="info" sx={{ mt: 3, textAlign: 'left' }}>
              <Typography variant="body2">
                O arquivo CSV deve conter as seguintes colunas:
              </Typography>
              <Typography variant="caption" component="div" sx={{ mt: 1 }}>
                Data Venda, ID Venda, Cliente, Telefone, Valor Venda, etc.
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Step 2: Field Mapping */}
        {activeStep === 1 && (
          <Box>
            <Alert severity="info" sx={{ mb: 2 }}>
              Mapeie os campos do CSV para os campos do sistema
            </Alert>
            
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Campo do CSV</TableCell>
                    <TableCell>Exemplo de Dado</TableCell>
                    <TableCell>Campo do Sistema</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {csvHeaders.map((header) => (
                    <TableRow key={header}>
                      <TableCell>{header}</TableCell>
                      <TableCell>
                        <Typography variant="caption" color="textSecondary">
                          {csvData[0]?.[header] || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <FormControl size="small" fullWidth>
                          <Select
                            value={fieldMappings[header] || ''}
                            onChange={(e) => handleMappingChange(header, e.target.value)}
                          >
                            <MenuItem value="">
                              <em>Não mapear</em>
                            </MenuItem>
                            {SYSTEM_FIELDS.map((field) => (
                              <MenuItem key={field.value} value={field.value}>
                                {field.label}
                                {field.required && <Chip size="small" label="Obrigatório" sx={{ ml: 1 }} />}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            <Box mt={2} display="flex" justifyContent="space-between">
              <Button onClick={() => setActiveStep(0)}>Voltar</Button>
              <Button
                variant="contained"
                onClick={prepareImportPreview}
                disabled={Object.keys(fieldMappings).length === 0}
              >
                Continuar
              </Button>
            </Box>
          </Box>
        )}

        {/* Step 3: Review and Import */}
        {activeStep === 2 && (
          <Box>
            {!importing && importResults.success === 0 && (
              <>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Revise os dados antes de importar. Mostrando as primeiras 10 linhas.
                </Alert>
                
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={skipDuplicates}
                      onChange={(e) => setSkipDuplicates(e.target.checked)}
                    />
                  }
                  label="Pular pedidos duplicados"
                  sx={{ mb: 2 }}
                />
                
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>ID Venda</TableCell>
                        <TableCell>Cliente</TableCell>
                        <TableCell>Telefone</TableCell>
                        <TableCell>Valor</TableCell>
                        <TableCell>Mensagens</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {importPreview.map((preview, index) => {
                        const idField = Object.keys(fieldMappings).find(k => fieldMappings[k] === 'idVenda');
                        const clienteField = Object.keys(fieldMappings).find(k => fieldMappings[k] === 'cliente');
                        const telefoneField = Object.keys(fieldMappings).find(k => fieldMappings[k] === 'telefone');
                        const valorField = Object.keys(fieldMappings).find(k => fieldMappings[k] === 'valorVenda');
                        
                        return (
                          <TableRow key={index}>
                            <TableCell>
                              {preview.status === 'valid' && <CheckCircleIcon color="success" />}
                              {preview.status === 'warning' && <WarningIcon color="warning" />}
                              {preview.status === 'error' && <ErrorIcon color="error" />}
                            </TableCell>
                            <TableCell>{idField ? preview.row[idField] : '-'}</TableCell>
                            <TableCell>{clienteField ? preview.row[clienteField] : '-'}</TableCell>
                            <TableCell>{telefoneField ? preview.row[telefoneField] : '-'}</TableCell>
                            <TableCell>{valorField ? preview.row[valorField] : '-'}</TableCell>
                            <TableCell>
                              {preview.messages.map((msg, i) => (
                                <Typography key={i} variant="caption" display="block">
                                  {msg}
                                </Typography>
                              ))}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
                
                <Box mt={2}>
                  <Alert severity="warning">
                    Total de {csvData.length} pedidos serão importados.
                  </Alert>
                </Box>
              </>
            )}
            
            {importing && (
              <Box py={4}>
                <Typography variant="h6" gutterBottom>Importando pedidos...</Typography>
                <LinearProgress variant="determinate" value={importProgress} sx={{ mb: 2 }} />
                <Typography variant="body2" color="textSecondary">
                  {Math.round(importProgress)}% completo
                </Typography>
              </Box>
            )}
            
            {!importing && importResults.success + importResults.failed > 0 && (
              <Box>
                <Alert severity={importResults.failed === 0 ? 'success' : 'warning'} sx={{ mb: 2 }}>
                  <Typography variant="body1">
                    Importação concluída!
                  </Typography>
                  <Typography variant="body2">
                    ✓ {importResults.success} pedidos importados com sucesso
                  </Typography>
                  {importResults.failed > 0 && (
                    <Typography variant="body2">
                      ✗ {importResults.failed} pedidos falharam
                    </Typography>
                  )}
                </Alert>
                
                {importResults.errors.length > 0 && (
                  <Box sx={{ maxHeight: 200, overflow: 'auto', bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom>Erros:</Typography>
                    {importResults.errors.map((error, index) => (
                      <Typography key={index} variant="caption" display="block" color="error">
                        {error}
                      </Typography>
                    ))}
                  </Box>
                )}
              </Box>
            )}
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        {activeStep === 2 && !importing && importResults.success + importResults.failed === 0 && (
          <>
            <Button onClick={() => setActiveStep(1)}>Voltar</Button>
            <Button
              variant="contained"
              onClick={handleImport}
              color="primary"
              disabled={importPreview.some(p => p.status === 'error')}
            >
              Importar {csvData.length} Pedidos
            </Button>
          </>
        )}
        
        {(importing || importResults.success + importResults.failed > 0) && (
          <Button onClick={handleClose}>Fechar</Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default OrderImportDialog;