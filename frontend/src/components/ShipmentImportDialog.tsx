import React, { useState, useRef } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Paper,
  IconButton,
} from '@mui/material';
import {
  CloudUpload,
  Download,
  CheckCircle,
  Error as ErrorIcon,
  Close,
  FileUpload,
} from '@mui/icons-material';
import { shippingService, ImportResult } from '../services/ShippingService';
import { useSnackbar } from 'notistack';

interface ShipmentImportDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const ShipmentImportDialog: React.FC<ShipmentImportDialogProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      if (!['xlsx', 'xls', 'csv'].includes(fileExtension || '')) {
        enqueueSnackbar('Por favor, selecione um arquivo Excel (.xlsx, .xls) ou CSV', { variant: 'error' });
        return;
      }
      setFile(selectedFile);
      setResult(null);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      enqueueSnackbar('Por favor, selecione um arquivo', { variant: 'error' });
      return;
    }

    try {
      setLoading(true);
      const importResult = await shippingService.importShipments(file);
      setResult(importResult);
      
      if (importResult.successful > 0) {
        enqueueSnackbar(`${importResult.successful} etiquetas importadas com sucesso!`, { 
          variant: 'success' 
        });
        onSuccess?.();
      }
      
      if (importResult.failed > 0) {
        enqueueSnackbar(`${importResult.failed} etiquetas falharam na importação`, { 
          variant: 'warning' 
        });
      }
    } catch (error: any) {
      enqueueSnackbar(error.message || 'Erro ao importar arquivo', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setResult(null);
    onClose();
  };

  const downloadTemplate = () => {
    // Create a CSV template
    const template = `Pedido,Cliente,CPF,Telefone,Email,CEP,Rua,Número,Complemento,Bairro,Cidade,Estado,Serviço,Peso,Altura,Largura,Comprimento,Valor Declarado
12345,João Silva,12345678900,11999999999,<EMAIL>,01310100,Av Paulista,1000,Apto 101,Bela Vista,São Paulo,SP,04014,0.5,10,15,20,100.00
12346,Maria Santos,98765432100,11888888888,<EMAIL>,20040020,Av Atlântica,500,,Copacabana,Rio de Janeiro,RJ,04510,1.0,15,20,25,200.00`;
    
    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template_importacao_etiquetas.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            <FileUpload />
            Importar Etiquetas de Envio
          </Box>
          <IconButton onClick={handleClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          {/* Instructions */}
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" gutterBottom>
              <strong>Instruções:</strong>
            </Typography>
            <Typography variant="body2" component="div">
              1. Baixe o modelo de arquivo clicando no botão abaixo<br />
              2. Preencha os dados dos pedidos no arquivo<br />
              3. Salve como Excel (.xlsx) ou CSV<br />
              4. Faça o upload do arquivo preenchido
            </Typography>
          </Alert>

          {/* Download Template Button */}
          <Box sx={{ mb: 3, textAlign: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={downloadTemplate}
            >
              Baixar Modelo CSV
            </Button>
          </Box>

          {/* File Upload Area */}
          <Paper
            sx={{
              p: 3,
              border: '2px dashed',
              borderColor: 'divider',
              backgroundColor: 'background.default',
              textAlign: 'center',
              cursor: 'pointer',
              '&:hover': {
                borderColor: 'primary.main',
                backgroundColor: 'action.hover',
              },
            }}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
            
            <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            
            {file ? (
              <Box>
                <Typography variant="body1" gutterBottom>
                  Arquivo selecionado:
                </Typography>
                <Chip
                  label={file.name}
                  onDelete={() => {
                    setFile(null);
                    setResult(null);
                  }}
                  color="primary"
                />
              </Box>
            ) : (
              <Typography variant="body1" color="text.secondary">
                Clique para selecionar ou arraste um arquivo Excel/CSV
              </Typography>
            )}
          </Paper>

          {/* Progress */}
          {loading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
              <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                Processando arquivo...
              </Typography>
            </Box>
          )}

          {/* Results */}
          {result && !loading && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Resultado da Importação
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Chip
                  icon={<CheckCircle />}
                  label={`${result.successful} Sucesso`}
                  color="success"
                  variant="outlined"
                />
                <Chip
                  icon={<ErrorIcon />}
                  label={`${result.failed} Falhas`}
                  color="error"
                  variant="outlined"
                />
              </Box>

              {result.errors.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Erros encontrados:
                  </Typography>
                  <Paper sx={{ maxHeight: 200, overflow: 'auto' }}>
                    <List dense>
                      {result.errors.map((error, index) => (
                        <ListItem key={index}>
                          <ListItemText
                            primary={`Pedido ${error.orderNumber}`}
                            secondary={error.error}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          Fechar
        </Button>
        <Button
          onClick={handleUpload}
          variant="contained"
          disabled={!file || loading}
          startIcon={<CloudUpload />}
        >
          Importar
        </Button>
      </DialogActions>
    </Dialog>
  );
};