{"name": "zencash", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.15.10", "@mui/x-data-grid": "^8.0.0", "@mui/x-date-pickers": "^6.19.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^27.5.2", "@types/node": "^16.18.80", "@types/papaparse": "^5.3.15", "@types/react": "^18.2.55", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.19", "antd": "^5.24.7", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "express": "^5.1.0", "papaparse": "^5.5.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-confetti": "^6.4.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "react-use": "^17.6.0", "react-window": "^1.8.11", "recharts": "^2.15.2", "serve": "^14.2.4", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^5.0.6"}, "scripts": {"start": "node server.js", "start:serve": "serve -s build -p ${PORT:-3000}", "dev": "react-scripts start", "start:prod": "node server.js", "start-port": "PORT=3001 react-scripts start", "build": "NODE_OPTIONS='--max-old-space-size=4096' CI=false TSC_COMPILE_ON_ERROR=true DISABLE_ESLINT_PLUGIN=true react-scripts build", "build:railway": "NODE_OPTIONS='--max-old-space-size=30720' CI=false TSC_COMPILE_ON_ERROR=true DISABLE_ESLINT_PLUGIN=true react-scripts build", "test": "react-scripts test", "test:e2e": "cypress open", "test:e2e:headless": "cypress run", "eject": "react-scripts eject", "heroku-postbuild": "npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-window": "^1.8.8", "cypress": "^14.5.1"}}