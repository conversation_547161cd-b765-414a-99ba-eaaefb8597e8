const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();

const PORT = process.env.PORT || 3000;
const buildPath = path.join(__dirname, 'build');
const indexPath = path.join(buildPath, 'index.html');

// Check if build directory exists
if (!fs.existsSync(buildPath)) {
  console.error('Build directory not found! Make sure to run "npm run build" first.');
  process.exit(1);
}

// Check if index.html exists
if (!fs.existsSync(indexPath)) {
  console.error('index.html not found in build directory!');
  process.exit(1);
}

console.log(`Build directory: ${buildPath}`);
console.log(`Index file: ${indexPath}`);

// Serve static files from the build directory
app.use(express.static(buildPath, {
  maxAge: '1d', // Cache static assets for 1 day
  etag: true
}));

// Health check endpoint for Railway
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Handle React Router - send all requests to index.html
app.get('*', (req, res) => {
  console.log(`Serving ${req.path} -> index.html`);
  res.sendFile(indexPath);
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server is running on port ${PORT}`);
  console.log(`📁 Serving static files from: ${buildPath}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});
