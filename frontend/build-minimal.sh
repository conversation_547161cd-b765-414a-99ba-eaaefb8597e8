#!/bin/bash

# Minimal build - skip all checks
export SKIP_PREFLIGHT_CHECK=true
export TSC_COMPILE_ON_ERROR=true
export ESLINT_NO_DEV_ERRORS=true
export DISABLE_ESLINT_PLUGIN=true
export GENERATE_SOURCEMAP=false
export NODE_OPTIONS="--max-old-space-size=8192"
export CI=false

# Clean
rm -rf build

echo "Building with all checks disabled..."
npm run build || true

# Check if build exists
if [ -d "build" ] && [ -f "build/index.html" ]; then
    echo "✅ Build completed (with warnings/errors ignored)"
    echo "📁 Deploying to Vercel..."
    vercel --prod --prebuilt
else
    echo "❌ Build failed - no index.html found"
fi