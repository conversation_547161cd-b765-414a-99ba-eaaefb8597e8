# Test Plan for Order Status Transitions

## Overview
This test plan ensures that orders don't disappear from the sidebar when changing status.

## Implementation Summary
1. **OrderService**: Added localStorage caching with 5-minute expiry
2. **PedidosPage**: Implemented optimistic updates with visual feedback
3. **OrderDataContext**: Enhanced retry logic (3 attempts) with optimistic update merging
4. **ModernSidebar**: Added status count caching to prevent 0 counts during refresh
5. **OrdersTable**: Dispatches detailed events with updated order data

## Test Cases

### 1. Basic Status Transitions
- [ ] Análise → Separação
- [ ] Separação → Trânsito  
- [ ] Trânsito → Confirmar Entrega
- [ ] Confirmar Entrega → Completo
- [ ] Confirmar En<PERSON>ga → Pagamento Pendente
- [ ] Pagamento Pendente → Completo
- [ ] Pagamento Pendente → Negociação
- [ ] Negociação → Recuperação
- [ ] Any Status → Cancelado

### 2. Error Handling
- [ ] Invalid transition (should show error but keep orders visible)
- [ ] Network error (should use cached data)
- [ ] Empty response from server (should keep current orders)

### 3. UI Feedback
- [ ] Success snackbar appears after status change
- [ ] Orders remain visible in sidebar during refresh
- [ ] Status counts don't drop to 0 during refresh
- [ ] Updated order shows new status immediately

### 4. Data Persistence
- [ ] Orders are cached in localStorage
- [ ] Cache is used when API fails
- [ ] Optimistic updates are merged correctly
- [ ] Multiple rapid status changes are handled

## How to Test

1. Open the browser console to monitor logs
2. Navigate to the Pedidos page
3. Click on an order to open details
4. Change the status using the buttons
5. Watch for:
   - Success message
   - Sidebar counts updating
   - Orders remaining visible
   - Console logs showing retry attempts

## Expected Behavior
- Orders should NEVER disappear from the sidebar
- Status changes should be reflected immediately
- If the API is slow, optimistic updates should show
- On errors, cached data should be used
- Status counts should update smoothly without dropping to 0