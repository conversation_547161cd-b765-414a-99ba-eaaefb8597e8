#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting Railway build process...');

// Set environment variables
process.env.NODE_ENV = 'production';
process.env.CI = 'false';
process.env.TSC_COMPILE_ON_ERROR = 'true';
process.env.DISABLE_ESLINT_PLUGIN = 'true';
process.env.GENERATE_SOURCEMAP = 'false';
process.env.IMAGE_INLINE_SIZE_LIMIT = '0';
process.env.SKIP_PREFLIGHT_CHECK = 'true';
process.env.NODE_OPTIONS = '--max-old-space-size=8192';

// Clean build directory
const buildDir = path.join(__dirname, '..', 'build');
if (fs.existsSync(buildDir)) {
  console.log('Cleaning existing build directory...');
  fs.rmSync(buildDir, { recursive: true, force: true });
}

try {
  // Run the build with craco
  console.log('Running build with optimized configuration...');
  execSync('npx craco build', {
    stdio: 'inherit',
    env: process.env
  });

  // Verify build output
  const indexPath = path.join(buildDir, 'index.html');
  if (fs.existsSync(indexPath)) {
    console.log('✅ Build completed successfully!');
    console.log('Build directory contents:');
    const files = fs.readdirSync(buildDir);
    files.forEach(file => {
      console.log(`  - ${file}`);
    });
  } else {
    console.error('❌ Build failed: index.html not found');
    process.exit(1);
  }
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}