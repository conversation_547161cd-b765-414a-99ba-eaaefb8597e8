#!/usr/bin/env node

// This script builds the app without TypeScript type checking to avoid memory issues
process.env.SKIP_PREFLIGHT_CHECK = 'true';
process.env.DISABLE_ESLINT_PLUGIN = 'true';
process.env.TSC_COMPILE_ON_ERROR = 'true';
process.env.GENERATE_SOURCEMAP = 'false';
process.env.CI = 'false';

// Remove fork-ts-checker from the environment
process.env.REACT_APP_DISABLE_FORK_TS_CHECKER = 'true';

// Import and run the build script
require('react-scripts/scripts/build');