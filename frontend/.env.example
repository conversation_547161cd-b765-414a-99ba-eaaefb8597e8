# ========================================
# VERCEL ENVIRONMENT VARIABLES
# ========================================

# API Configuration
# IMPORTANT: No trailing slash on the API URL
# For Vercel deployment, use your Railway backend URL
REACT_APP_API_URL=https://your-backend.railway.app/api/v1

# Tenant Configuration
# Default tenant ID for the application
REACT_APP_TENANT_ID=28a833c0-c2a1-4498-85ca-b028f982ffb2

# Environment
REACT_APP_ENV=production

# Features (optional)
REACT_APP_ENABLE_DEBUG=false

# ========================================
# PHASE 0.2 - CORREIOS INTEGRATION
# ========================================
# If frontend needs to directly call Correios API (not recommended - use backend instead)
REACT_APP_CORREIOS_API_URL=https://api.correios.com.br/v1
REACT_APP_CORREIOS_API_KEY=your-correios-api-key-here

# Application Configuration
REACT_APP_APP_NAME=ZenCash
REACT_APP_APP_TITLE=Sistema de Cobrança Inteligente

# Analytics (optional)
REACT_APP_GA_TRACKING_ID=UA-XXXXXXXXX-X
REACT_APP_GTM_ID=GTM-XXXXXXX

# Sentry Configuration (optional - for error tracking)
REACT_APP_SENTRY_DSN=https://<EMAIL>/project-id

# Feature Flags (optional)
REACT_APP_ENABLE_DARK_MODE=true
REACT_APP_ENABLE_MULTI_LANGUAGE=false
REACT_APP_ENABLE_ADVANCED_REPORTS=true

# Authentication
REACT_APP_AUTH_TOKEN_KEY=zencash_auth_token
REACT_APP_AUTH_USER_KEY=zencash_user

# API Timeouts (in milliseconds)
REACT_APP_API_TIMEOUT=30000

# File Upload Limits
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=.jpg,.jpeg,.png,.pdf,.csv,.xlsx

# Date/Time Configuration
REACT_APP_TIMEZONE=America/Sao_Paulo
REACT_APP_DATE_FORMAT=DD/MM/YYYY
REACT_APP_TIME_FORMAT=HH:mm:ss

# Currency Configuration
REACT_APP_CURRENCY=BRL
REACT_APP_CURRENCY_SYMBOL=R$

# Support/Contact
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_SUPPORT_PHONE=+55 11 99999-9999

# Social Media (optional)
REACT_APP_FACEBOOK_URL=https://facebook.com/zencash
REACT_APP_INSTAGRAM_URL=https://instagram.com/zencash
REACT_APP_LINKEDIN_URL=https://linkedin.com/company/zencash

# WhatsApp Integration (optional)
REACT_APP_WHATSAPP_NUMBER=5511999999999
REACT_APP_WHATSAPP_DEFAULT_MESSAGE=Olá, preciso de ajuda com o ZenCash
