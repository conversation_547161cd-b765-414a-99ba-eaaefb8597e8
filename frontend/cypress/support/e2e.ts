// ***********************************************************
// This file is processed and loaded automatically before test files.
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add custom matchers from Testing Library
import '@testing-library/cypress/add-commands';

// Global before each to set up common headers
beforeEach(() => {
  // Set tenant ID header for all requests
  cy.intercept('*', (req) => {
    req.headers['x-tenant-id'] = Cypress.env('tenantId');
  });
});