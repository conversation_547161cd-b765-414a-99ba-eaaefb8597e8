/// <reference types="cypress" />
import '@testing-library/cypress/add-commands';

// Custom commands for authentication
Cypress.Commands.add('login', (role: 'admin' | 'supervisor' | 'operator' = 'admin') => {
  const users = {
    admin: {
      email: '<EMAIL>',
      password: 'test123',
      token: 'test-admin-token',
      role: 'ADMIN',
    },
    supervisor: {
      email: '<EMAIL>',
      password: 'test123',
      token: 'test-supervisor-token',
      role: 'SUPERVISOR',
    },
    operator: {
      email: '<EMAIL>',
      password: 'test123',
      token: 'test-operator-token',
      role: 'OPERATOR',
    },
  };

  const user = users[role];

  // Mock the login API
  cy.intercept('POST', '**/auth/login', {
    statusCode: 200,
    body: {
      access_token: user.token,
      user: {
        id: `${role}-user-id`,
        email: user.email,
        role: user.role,
        tenantId: Cypress.env('tenantId'),
      },
    },
  }).as('login');

  // Set auth token in localStorage
  window.localStorage.setItem('authToken', user.token);
  window.localStorage.setItem(
    'user',
    JSON.stringify({
      id: `${role}-user-id`,
      email: user.email,
      role: user.role,
      tenantId: Cypress.env('tenantId'),
    })
  );
});

// Command to set up antifraud API mocks
Cypress.Commands.add('mockAntifraudAPIs', () => {
  // Mock review queue
  cy.intercept('GET', '**/antifraud/duplicates/review-queue*', {
    statusCode: 200,
    body: {
      items: [
        {
          id: 'order-1',
          orderNumber: 'ORD-001',
          customerName: 'João Silva',
          customerCPF: '123.456.789-00',
          customerEmail: '<EMAIL>',
          fullAddress: 'Rua das Flores, 123, São Paulo - SP',
          totalAmount: 150.00,
          paymentMethod: 'COD',
          status: 'PENDING',
          duplicateMatchScore: 85,
          matchedComponents: ['street', 'number', 'neighborhood'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'order-2',
          orderNumber: 'ORD-002',
          customerName: 'João da Silva',
          customerCPF: '123.456.789-00',
          customerEmail: '<EMAIL>',
          fullAddress: 'R. das Flores, 123, São Paulo - SP',
          totalAmount: 180.00,
          paymentMethod: 'COD',
          status: 'PENDING',
          duplicateMatchScore: 85,
          matchedComponents: ['street', 'number', 'neighborhood'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      total: 2,
      page: 1,
      totalPages: 1,
    },
  }).as('getReviewQueue');

  // Mock order details
  cy.intercept('GET', '**/antifraud/orders/*', (req) => {
    const orderId = req.url.split('/').pop();
    req.reply({
      statusCode: 200,
      body: {
        id: orderId,
        orderNumber: orderId === 'order-2' ? 'ORD-002' : 'ORD-001',
        customerName: orderId === 'order-2' ? 'João da Silva' : 'João Silva',
        customerCPF: '123.456.789-00',
        customerEmail: orderId === 'order-2' ? '<EMAIL>' : '<EMAIL>',
        fullAddress: orderId === 'order-2' ? 'R. das Flores, 123, São Paulo - SP' : 'Rua das Flores, 123, São Paulo - SP',
        totalAmount: orderId === 'order-2' ? 180.00 : 150.00,
        paymentMethod: 'COD',
        status: 'PENDING',
        duplicateMatchScore: 85,
        matchedComponents: ['street', 'number', 'neighborhood'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });
  }).as('getOrderDetails');

  // Mock audit trail
  cy.intercept('GET', '**/antifraud/orders/*/audit-trail', {
    statusCode: 200,
    body: [
      {
        id: 'audit-1',
        action: 'ORDER_CREATED',
        performedBy: 'System',
        performedByRole: 'SYSTEM',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        details: { source: 'API' },
      },
      {
        id: 'audit-2',
        action: 'DUPLICATE_DETECTED',
        performedBy: 'Anti-fraud System',
        performedByRole: 'SYSTEM',
        timestamp: new Date(Date.now() - 3500000).toISOString(),
        details: { matchScore: 85, matchedWith: 'ORD-001' },
      },
    ],
  }).as('getAuditTrail');

  // Mock review submission
  cy.intercept('POST', '**/antifraud/duplicates/*/review', {
    statusCode: 200,
    body: { success: true },
  }).as('submitReview');

  // Mock bulk review
  cy.intercept('POST', '**/antifraud/duplicates/bulk-review', {
    statusCode: 200,
    body: { 
      success: true,
      processed: 2,
      successful: 2,
      failed: 0,
    },
  }).as('bulkReview');
});

// Command to wait for antifraud to load
Cypress.Commands.add('waitForAntifraudLoad', () => {
  cy.wait('@getReviewQueue');
  cy.findByTestId('review-queue-loaded', { timeout: 10000 }).should('exist');
});

// Declare custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      login(role?: 'admin' | 'supervisor' | 'operator'): Chainable<void>;
      mockAntifraudAPIs(): Chainable<void>;
      waitForAntifraudLoad(): Chainable<void>;
    }
  }
}

export {};