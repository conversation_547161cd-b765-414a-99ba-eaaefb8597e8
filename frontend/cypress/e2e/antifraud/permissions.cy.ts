/// <reference types="cypress" />

describe('Anti-fraud Permissions', () => {
  beforeEach(() => {
    cy.mockAntifraudAPIs();
  });

  it('should allow admin users full access', () => {
    cy.login('admin');
    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Should see the dashboard
    cy.findByText('Painel Anti-Fraude Avançado').should('be.visible');
    
    // Should see review queue
    cy.findByText('Fila de Revisão').should('be.visible');
    
    // Should have all action buttons
    cy.findAllByLabelText(/Aprovar/i).should('have.length.at.least', 1);
    cy.findAllByLabelText(/Negar/i).should('have.length.at.least', 1);
    cy.findByLabelText(/Ações em massa/i).should('be.visible');
  });

  it('should allow supervisor users full access', () => {
    cy.login('supervisor');
    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Should see the dashboard
    cy.findByText('Painel Anti-Fraude Avançado').should('be.visible');
    
    // Should have review capabilities
    cy.findAllByLabelText(/Aprovar/i).first().should('be.enabled');
    cy.findAllByLabelText(/Negar/i).first().should('be.enabled');
  });

  it('should deny access to operator users', () => {
    cy.login('operator');
    cy.visit('/dashboard/antifraud');

    // Should see access denied message
    cy.findByText('Acesso Negado').should('be.visible');
    cy.findByText(/Você não tem permissão para acessar o painel anti-fraude/i).should('be.visible');
    
    // Should not see any review queue
    cy.findByText('Fila de Revisão').should('not.exist');
  });

  it('should handle unauthenticated access', () => {
    // Clear any stored auth
    cy.window().then((win) => {
      win.localStorage.clear();
    });

    cy.visit('/dashboard/antifraud');

    // Should redirect to login or show unauthorized
    cy.url().should('include', '/login');
    // OR
    // cy.findByText(/Não autorizado/i).should('be.visible');
  });

  it('should respect feature flags', () => {
    // Mock feature flag as disabled
    cy.intercept('GET', '**/config/feature-flags', {
      statusCode: 200,
      body: {
        antifraud: {
          enabled: false,
        },
      },
    }).as('getFeatureFlags');

    cy.login('admin');
    cy.visit('/dashboard/antifraud');
    cy.wait('@getFeatureFlags');

    // Should show feature disabled message
    cy.findByText(/Funcionalidade desabilitada/i).should('be.visible');
    cy.findByText(/O módulo anti-fraude está temporariamente desabilitado/i).should('be.visible');
  });

  it('should audit all review actions with user info', () => {
    cy.login('supervisor');
    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Intercept review request to check headers
    cy.intercept('POST', '**/antifraud/duplicates/*/review', (req) => {
      // Verify user context is sent
      expect(req.headers).to.have.property('authorization');
      expect(req.headers['x-tenant-id']).to.equal('test-tenant-e2e');
      
      req.reply({
        statusCode: 200,
        body: { success: true },
      });
    }).as('reviewWithUserContext');

    // Approve an order
    cy.findAllByLabelText(/Aprovar/i).first().click();
    cy.findByRole('button', { name: /Confirmar/i }).click();

    // Wait and verify the request
    cy.wait('@reviewWithUserContext');
  });

  it('should enforce tenant isolation', () => {
    // Login as admin of tenant A
    cy.login('admin');
    
    // Mock API to return orders from tenant A only
    cy.intercept('GET', '**/antifraud/duplicates/review-queue*', (req) => {
      expect(req.headers['x-tenant-id']).to.equal('test-tenant-e2e');
      
      req.reply({
        statusCode: 200,
        body: {
          items: [
            {
              id: 'tenant-a-order',
              orderNumber: 'TENANT-A-001',
              customerName: 'Tenant A Customer',
              tenantId: 'test-tenant-e2e',
              // ... other fields
            },
          ],
          total: 1,
          page: 1,
          totalPages: 1,
        },
      });
    }).as('getTenantSpecificQueue');

    cy.visit('/dashboard/antifraud');
    cy.wait('@getTenantSpecificQueue');

    // Should only see orders from their tenant
    cy.findByText('TENANT-A-001').should('be.visible');
    
    // Should not see orders from other tenants
    cy.findByText('TENANT-B-001').should('not.exist');
  });

  it('should show role-appropriate statistics', () => {
    // Mock statistics API
    cy.intercept('GET', '**/antifraud/statistics', {
      statusCode: 200,
      body: {
        pendingReviews: 15,
        approvedToday: 23,
        deniedToday: 7,
        duplicateRate: 12.5,
      },
    }).as('getStatistics');

    cy.login('supervisor');
    cy.visit('/dashboard/antifraud');
    cy.wait('@getStatistics');

    // Check statistics cards
    cy.findByText('Pendentes de Revisão').parent().within(() => {
      cy.findByText('15').should('be.visible');
    });

    cy.findByText('Aprovados Hoje').parent().within(() => {
      cy.findByText('23').should('be.visible');
    });

    cy.findByText('Negados Hoje').parent().within(() => {
      cy.findByText('7').should('be.visible');
    });

    cy.findByText('Taxa de Duplicação').parent().within(() => {
      cy.findByText('12.5%').should('be.visible');
    });
  });

  it('should track user actions in audit log', () => {
    cy.login('admin');
    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Open comparison view
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Switch to History tab
    cy.findByRole('tab', { name: /Histórico/i }).click();
    
    // Approve order
    cy.findByRole('button', { name: /Aprovar ORD-001/i }).click();
    cy.findByRole('button', { name: /Confirmar/i }).click();
    cy.wait('@submitReview');

    // Mock updated audit trail with new entry
    cy.intercept('GET', '**/antifraud/orders/*/audit-trail', {
      statusCode: 200,
      body: [
        {
          id: 'audit-1',
          action: 'ORDER_CREATED',
          performedBy: 'System',
          performedByRole: 'SYSTEM',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
        },
        {
          id: 'audit-2',
          action: 'DUPLICATE_DETECTED',
          performedBy: 'Anti-fraud System',
          performedByRole: 'SYSTEM',
          timestamp: new Date(Date.now() - 3500000).toISOString(),
        },
        {
          id: 'audit-3',
          action: 'DUPLICATE_APPROVED',
          performedBy: '<EMAIL>',
          performedByRole: 'ADMIN',
          timestamp: new Date().toISOString(),
          details: { reason: 'Approved via comparison view' },
        },
      ],
    }).as('getUpdatedAuditTrail');

    // Refresh to see new audit entry
    cy.findByLabelText(/Atualizar histórico/i).click();
    cy.wait('@getUpdatedAuditTrail');

    // New audit entry should be visible
    cy.findByText('DUPLICATE_APPROVED').should('be.visible');
    cy.findByText('<EMAIL>').should('be.visible');
    cy.findByText('ADMIN').should('be.visible');
  });
});