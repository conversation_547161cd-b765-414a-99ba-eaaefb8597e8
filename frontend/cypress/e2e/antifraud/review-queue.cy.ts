/// <reference types="cypress" />

describe('Anti-fraud Review Queue', () => {
  beforeEach(() => {
    cy.mockAntifraudAPIs();
    cy.login('supervisor');
    cy.visit('/dashboard/antifraud');
  });

  it('should display the review queue with pending orders', () => {
    cy.waitForAntifraudLoad();

    // Check page title and stats
    cy.findByText('Painel Anti-Fraude Avançado').should('be.visible');
    cy.findByText('Pendentes de Revisão').should('be.visible');
    
    // Check that orders are displayed
    cy.findByText('ORD-001').should('be.visible');
    cy.findByText('ORD-002').should('be.visible');
    
    // Check customer information
    cy.findByText('João <PERSON>').should('be.visible');
    cy.findByText('<PERSON>').should('be.visible');
    
    // Check CPF (masked)
    cy.findAllByText('123.456.789-00').should('have.length.at.least', 2);
    
    // Check match scores
    cy.findAllByText('85%').should('have.length.at.least', 2);
  });

  it('should filter orders by search term', () => {
    cy.waitForAntifraudLoad();

    // Search for specific customer
    cy.findByPlaceholderText(/Buscar por nome, CPF ou pedido/i).type('João Silva');
    
    // Wait for filter to be applied
    cy.wait(500);
    
    // Should show only matching order
    cy.findByText('ORD-001').should('be.visible');
    cy.findByText('ORD-002').should('not.exist');
    
    // Clear search
    cy.findByPlaceholderText(/Buscar por nome, CPF ou pedido/i).clear();
    
    // Both orders should be visible again
    cy.findByText('ORD-001').should('be.visible');
    cy.findByText('ORD-002').should('be.visible');
  });

  it('should filter orders by score range', () => {
    cy.waitForAntifraudLoad();

    // Change minimum score
    cy.findByLabelText(/Score mínimo/i).clear().type('90');
    
    // Wait for filter to be applied
    cy.wait(500);
    
    // Should show no orders (both have score 85)
    cy.findByText('Nenhum pedido duplicado pendente de revisão').should('be.visible');
    
    // Reset score filter
    cy.findByLabelText(/Score mínimo/i).clear().type('70');
    
    // Orders should be visible again
    cy.findByText('ORD-001').should('be.visible');
    cy.findByText('ORD-002').should('be.visible');
  });

  it('should sort orders by score', () => {
    cy.waitForAntifraudLoad();

    // Click on score column header
    cy.findByText(/Score/i).closest('div').click();
    
    // Verify sort direction indicator changes
    cy.findByTestId('sort-indicator-score').should('have.attr', 'data-direction', 'asc');
    
    // Click again to reverse
    cy.findByText(/Score/i).closest('div').click();
    cy.findByTestId('sort-indicator-score').should('have.attr', 'data-direction', 'desc');
  });

  it('should select individual orders', () => {
    cy.waitForAntifraudLoad();

    // Select first order
    cy.findAllByRole('checkbox').eq(1).click();
    
    // Check that bulk actions are available
    cy.findByLabelText(/Ações em massa/i).should('be.enabled');
    
    // Select all orders
    cy.findAllByRole('checkbox').eq(0).click(); // Select all checkbox
    
    // All checkboxes should be checked
    cy.findAllByRole('checkbox').should('be.checked');
  });

  it('should approve an order', () => {
    cy.waitForAntifraudLoad();

    // Click approve button for first order
    cy.findAllByLabelText(/Aprovar/i).first().click();
    
    // Confirmation dialog should appear
    cy.findByText(/Confirmar Aprovação/i).should('be.visible');
    cy.findByText(/Tem certeza que deseja aprovar este pedido/i).should('be.visible');
    
    // Add reason (optional)
    cy.findByLabelText(/Motivo/i).type('Cliente confiável, pedidos em datas diferentes');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API call
    cy.wait('@submitReview');
    
    // Success message should appear
    cy.findByText(/Pedido aprovado com sucesso/i).should('be.visible');
  });

  it('should deny an order with reason', () => {
    cy.waitForAntifraudLoad();

    // Click deny button for first order
    cy.findAllByLabelText(/Negar/i).first().click();
    
    // Confirmation dialog should appear
    cy.findByText(/Confirmar Negação/i).should('be.visible');
    
    // Reason is required for denial
    cy.findByLabelText(/Motivo/i).type('Tentativa confirmada de fraude');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API call
    cy.wait('@submitReview');
    
    // Success message should appear
    cy.findByText(/Pedido negado com sucesso/i).should('be.visible');
  });

  it('should perform bulk approval', () => {
    cy.waitForAntifraudLoad();

    // Select all orders
    cy.findAllByRole('checkbox').eq(0).click();
    
    // Open bulk actions menu
    cy.findByLabelText(/Ações em massa/i).click();
    
    // Select bulk approve
    cy.findByText(/Aprovar selecionados/i).click();
    
    // Confirmation dialog
    cy.findByText(/Aprovar 2 pedidos/i).should('be.visible');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API call
    cy.wait('@bulkReview');
    
    // Success message
    cy.findByText(/2 pedidos processados com sucesso/i).should('be.visible');
  });

  it('should export data to CSV', () => {
    cy.waitForAntifraudLoad();

    // Click export button
    cy.findByLabelText(/Exportar para CSV/i).click();
    
    // Verify download started (checking for blob URL creation)
    cy.window().then((win) => {
      cy.stub(win.URL, 'createObjectURL').returns('blob:mock-url');
    });
  });

  it('should refresh queue data', () => {
    cy.waitForAntifraudLoad();

    // Click refresh button
    cy.findByLabelText(/Atualizar/i).click();
    
    // Wait for new API call
    cy.wait('@getReviewQueue');
    
    // Queue should reload
    cy.findByText('ORD-001').should('be.visible');
  });

  it('should handle keyboard shortcuts', () => {
    cy.waitForAntifraudLoad();

    // Test Ctrl+F for search focus
    cy.get('body').type('{ctrl}f');
    cy.findByPlaceholderText(/Buscar por nome, CPF ou pedido/i).should('have.focus');
    
    // Test Ctrl+A for select all
    cy.get('body').type('{ctrl}a');
    cy.findAllByRole('checkbox').should('be.checked');
    
    // Test Ctrl+R for refresh
    cy.get('body').type('{ctrl}r');
    cy.wait('@getReviewQueue');
  });

  it('should show empty state when no orders', () => {
    // Override mock to return empty queue
    cy.intercept('GET', '**/antifraud/duplicates/review-queue*', {
      statusCode: 200,
      body: {
        items: [],
        total: 0,
        page: 1,
        totalPages: 0,
      },
    }).as('getEmptyQueue');

    cy.visit('/dashboard/antifraud');
    cy.wait('@getEmptyQueue');

    // Empty state message
    cy.findByText(/Nenhum pedido duplicado pendente de revisão/i).should('be.visible');
    cy.findByText(/Todos os pedidos foram revisados/i).should('be.visible');
  });

  it('should handle API errors gracefully', () => {
    // Override mock to return error
    cy.intercept('GET', '**/antifraud/duplicates/review-queue*', {
      statusCode: 500,
      body: { message: 'Internal server error' },
    }).as('getQueueError');

    cy.visit('/dashboard/antifraud');
    cy.wait('@getQueueError');

    // Error message should be displayed
    cy.findByText(/Erro ao carregar fila de revisão/i).should('be.visible');
    
    // Retry button should be available
    cy.findByRole('button', { name: /Tentar novamente/i }).should('be.visible');
  });
});