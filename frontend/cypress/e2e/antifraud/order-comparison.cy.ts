/// <reference types="cypress" />

describe('Order Comparison View', () => {
  beforeEach(() => {
    cy.mockAntifraudAPIs();
    cy.login('supervisor');
    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();
  });

  it('should open comparison view when clicking compare button', () => {
    // Click compare button on first order
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Comparison view should open
    cy.findByText(/Comparação de Pedidos/i).should('be.visible');
    
    // Both orders should be displayed
    cy.findByTestId('left-order').within(() => {
      cy.findByText('ORD-001').should('be.visible');
      cy.findByText('<PERSON>').should('be.visible');
    });
    
    cy.findByTestId('right-order').within(() => {
      cy.findByText('ORD-002').should('be.visible');
      cy.findByText('<PERSON>').should('be.visible');
    });
  });

  it('should highlight differences between orders', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Check that differences are highlighted
    cy.findAllByTestId('diff-highlight').should('have.length.at.least', 1);
    
    // Customer names should be highlighted as different
    cy.findByTestId('left-order').within(() => {
      cy.findByText('João Silva').parent().should('have.attr', 'data-testid', 'diff-highlight');
    });
    
    cy.findByTestId('right-order').within(() => {
      cy.findByText('João da Silva').parent().should('have.attr', 'data-testid', 'diff-highlight');
    });
    
    // CPF should not be highlighted (same value)
    cy.findAllByText('123.456.789-00').each(($el) => {
      cy.wrap($el).parent().should('not.have.attr', 'data-testid', 'diff-highlight');
    });
  });

  it('should switch between tabs in comparison view', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Initially on General tab
    cy.findByRole('tabpanel', { name: /Geral/i }).should('be.visible');
    
    // Switch to Addresses tab
    cy.findByRole('tab', { name: /Endereços/i }).click();
    cy.findByRole('tabpanel', { name: /Endereços/i }).should('be.visible');
    cy.findByText(/Componentes do Endereço/i).should('be.visible');
    
    // Switch to History tab
    cy.findByRole('tab', { name: /Histórico/i }).click();
    cy.findByRole('tabpanel', { name: /Histórico/i }).should('be.visible');
    cy.findByText(/Auditoria do Pedido/i).should('be.visible');
    
    // Switch to Map tab
    cy.findByRole('tab', { name: /Mapa/i }).click();
    cy.findByRole('tabpanel', { name: /Mapa/i }).should('be.visible');
  });

  it('should display match score breakdown', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Match score should be displayed
    cy.findByText('85%').should('be.visible');
    cy.findByText(/Alta Similaridade/i).should('be.visible');
    
    // Matched components should be listed
    cy.findByText(/Componentes correspondentes:/i).should('be.visible');
    cy.findByText(/street, number, neighborhood/i).should('be.visible');
  });

  it('should swap order positions', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Initial positions
    cy.findByTestId('left-order').within(() => {
      cy.findByText('ORD-001').should('be.visible');
    });
    
    // Click swap button
    cy.findByLabelText(/Trocar pedidos/i).click();
    
    // Positions should be swapped
    cy.findByTestId('left-order').within(() => {
      cy.findByText('ORD-002').should('be.visible');
    });
    
    cy.findByTestId('right-order').within(() => {
      cy.findByText('ORD-001').should('be.visible');
    });
  });

  it('should approve both orders from comparison view', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Click approve both button
    cy.findByRole('button', { name: /Aprovar Ambos/i }).click();
    
    // Confirmation dialog
    cy.findByText(/Aprovar ambos os pedidos/i).should('be.visible');
    
    // Add reason
    cy.findByLabelText(/Motivo/i).type('Pedidos válidos, entregas diferentes');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API calls
    cy.wait('@submitReview');
    cy.wait('@submitReview');
    
    // Success message
    cy.findByText(/Ambos os pedidos foram aprovados/i).should('be.visible');
  });

  it('should deny both orders from comparison view', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Click deny both button
    cy.findByRole('button', { name: /Negar Ambos/i }).click();
    
    // Confirmation dialog
    cy.findByText(/Negar ambos os pedidos/i).should('be.visible');
    
    // Reason is required
    cy.findByLabelText(/Motivo/i).type('Fraude confirmada');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API calls
    cy.wait('@submitReview');
    cy.wait('@submitReview');
    
    // Success message
    cy.findByText(/Ambos os pedidos foram negados/i).should('be.visible');
  });

  it('should approve one and deny other', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Click approve first, deny second
    cy.findByRole('button', { name: /Aprovar ORD-001/i }).click();
    
    // Confirmation dialog
    cy.findByText(/Aprovar ORD-001 e negar ORD-002/i).should('be.visible');
    
    // Add reason
    cy.findByLabelText(/Motivo para negar/i).type('Apenas o segundo é fraudulento');
    
    // Confirm
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for API calls
    cy.wait('@submitReview');
    cy.wait('@submitReview');
    
    // Success message
    cy.findByText(/Pedidos processados com decisões diferentes/i).should('be.visible');
  });

  it('should display address components comparison', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Switch to Addresses tab
    cy.findByRole('tab', { name: /Endereços/i }).click();
    
    // Check address components are displayed
    cy.findByTestId('left-order').within(() => {
      cy.findByText('Rua das Flores').should('be.visible');
      cy.findByText('123').should('be.visible');
      cy.findByText('São Paulo').should('be.visible');
      cy.findByText('SP').should('be.visible');
    });
    
    cy.findByTestId('right-order').within(() => {
      cy.findByText('R. das Flores').should('be.visible'); // Abbreviated
      cy.findByText('123').should('be.visible');
      cy.findByText('São Paulo').should('be.visible');
      cy.findByText('SP').should('be.visible');
    });
    
    // Street abbreviation should be highlighted as different
    cy.findByText('Rua das Flores').parent().should('have.attr', 'data-testid', 'diff-highlight');
    cy.findByText('R. das Flores').parent().should('have.attr', 'data-testid', 'diff-highlight');
  });

  it('should display audit trail for both orders', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Switch to History tab
    cy.findByRole('tab', { name: /Histórico/i }).click();
    
    // Wait for audit trail to load
    cy.wait('@getAuditTrail');
    
    // Check audit entries are displayed
    cy.findByText('ORDER_CREATED').should('be.visible');
    cy.findByText('DUPLICATE_DETECTED').should('be.visible');
    cy.findByText('Anti-fraud System').should('be.visible');
  });

  it('should close comparison view', () => {
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Comparison view should be open
    cy.findByText(/Comparação de Pedidos/i).should('be.visible');
    
    // Click close button
    cy.findByLabelText(/Fechar comparação/i).click();
    
    // Comparison view should close
    cy.findByText(/Comparação de Pedidos/i).should('not.exist');
    
    // Review queue should still be visible
    cy.findByText('Fila de Revisão').should('be.visible');
  });

  it('should handle loading states in comparison view', () => {
    // Delay the order details response
    cy.intercept('GET', '**/antifraud/orders/*', (req) => {
      req.reply((res) => {
        res.delay(1000); // 1 second delay
        res.send({
          statusCode: 200,
          body: {
            id: 'order-2',
            orderNumber: 'ORD-002',
            customerName: 'João da Silva',
            // ... rest of order data
          },
        });
      });
    }).as('getDelayedOrder');
    
    cy.findAllByLabelText(/Comparar pedidos/i).first().click();
    
    // Loading state should be shown
    cy.findByRole('progressbar').should('be.visible');
    
    // Wait for order to load
    cy.wait('@getDelayedOrder');
    
    // Content should appear
    cy.findByText('ORD-002').should('be.visible');
  });
});