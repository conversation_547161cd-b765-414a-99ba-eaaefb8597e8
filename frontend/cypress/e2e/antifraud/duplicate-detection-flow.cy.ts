/// <reference types="cypress" />

describe('Duplicate Detection End-to-End Flow', () => {
  beforeEach(() => {
    cy.login('admin');
  });

  it('should detect duplicate order on creation and allow review', () => {
    // Mock order creation API
    cy.intercept('POST', '**/orders', (req) => {
      const { customerCPF } = req.body;
      
      // Simulate duplicate detection for specific CPF
      if (customerCPF === '123.456.789-00') {
        req.reply({
          statusCode: 201,
          body: {
            id: 'new-order-1',
            orderNumber: 'ORD-NEW-001',
            ...req.body,
            isDuplicate: true,
            duplicateStatus: 'PENDING_REVIEW',
            duplicateMatchScore: 88,
            duplicateMatchedWith: 'ORD-EXISTING-001',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        });
      } else {
        req.reply({
          statusCode: 201,
          body: {
            id: 'new-order-2',
            orderNumber: 'ORD-NEW-002',
            ...req.body,
            isDuplicate: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        });
      }
    }).as('createOrder');

    // Step 1: Create a potentially duplicate order
    cy.visit('/dashboard/orders/new');
    
    // Fill order form
    cy.findByLabelText(/Nome do Cliente/i).type('João Silva');
    cy.findByLabelText(/CPF/i).type('123.456.789-00');
    cy.findByLabelText(/Email/i).type('<EMAIL>');
    cy.findByLabelText(/Telefone/i).type('(11) 98765-4321');
    cy.findByLabelText(/Endereço Completo/i).type('Rua das Flores, 123, Apt 101, São Paulo - SP');
    
    // Add product
    cy.findByLabelText(/Produto/i).select('Product 1');
    cy.findByLabelText(/Quantidade/i).type('2');
    cy.findByLabelText(/Preço/i).should('have.value', '50.00');
    
    // Select payment method
    cy.findByLabelText(/Forma de Pagamento/i).select('COD');
    
    // Submit order
    cy.findByRole('button', { name: /Criar Pedido/i }).click();
    
    // Wait for order creation
    cy.wait('@createOrder');
    
    // Step 2: System should show duplicate warning
    cy.findByText(/Possível Pedido Duplicado Detectado/i).should('be.visible');
    cy.findByText(/Score de Similaridade: 88%/i).should('be.visible');
    cy.findByText(/Pedido similar: ORD-EXISTING-001/i).should('be.visible');
    
    // Options should be presented
    cy.findByRole('button', { name: /Revisar Agora/i }).should('be.visible');
    cy.findByRole('button', { name: /Continuar Mesmo Assim/i }).should('be.visible');
    
    // Step 3: Navigate to anti-fraud dashboard
    cy.findByRole('button', { name: /Revisar Agora/i }).click();
    
    // Should redirect to anti-fraud dashboard with the order highlighted
    cy.url().should('include', '/dashboard/antifraud');
    
    // Mock the review queue with the new order
    cy.mockAntifraudAPIs();
    cy.intercept('GET', '**/antifraud/duplicates/review-queue*', {
      statusCode: 200,
      body: {
        items: [
          {
            id: 'new-order-1',
            orderNumber: 'ORD-NEW-001',
            customerName: 'João Silva',
            customerCPF: '123.456.789-00',
            customerEmail: '<EMAIL>',
            fullAddress: 'Rua das Flores, 123, Apt 101, São Paulo - SP',
            totalAmount: 100.00,
            paymentMethod: 'COD',
            status: 'PENDING',
            duplicateMatchScore: 88,
            matchedComponents: ['cpf', 'street', 'number'],
            duplicateMatchedWith: 'ORD-EXISTING-001',
            createdAt: new Date().toISOString(),
            isNew: true, // Flag to highlight new order
          },
        ],
        total: 1,
        page: 1,
        totalPages: 1,
      },
    }).as('getQueueWithNewOrder');
    
    cy.wait('@getQueueWithNewOrder');
    
    // New order should be highlighted
    cy.findByText('ORD-NEW-001').parent().parent().should('have.class', 'highlighted-new');
    cy.findByText(/Novo/i).should('be.visible'); // New badge
    
    // Step 4: Compare with existing order
    cy.findByLabelText(/Comparar pedidos/i).click();
    
    // Comparison view should load both orders
    cy.findByTestId('left-order').within(() => {
      cy.findByText('ORD-NEW-001').should('be.visible');
      cy.findByText('<EMAIL>').should('be.visible');
    });
    
    cy.findByTestId('right-order').within(() => {
      cy.findByText('ORD-EXISTING-001').should('be.visible');
    });
    
    // Step 5: Make decision
    cy.findByRole('button', { name: /Aprovar Ambos/i }).click();
    cy.findByLabelText(/Motivo/i).type('Cliente confirmado, pedidos em endereços diferentes');
    cy.findByRole('button', { name: /Confirmar/i }).click();
    
    // Wait for review submission
    cy.wait('@submitReview');
    
    // Success notification
    cy.findByText(/Pedidos aprovados com sucesso/i).should('be.visible');
    
    // Order should be removed from queue
    cy.findByText('ORD-NEW-001').should('not.exist');
  });

  it('should handle batch order import with duplicate detection', () => {
    // Mock batch import API
    cy.intercept('POST', '**/orders/batch-import', {
      statusCode: 200,
      body: {
        imported: 10,
        duplicates: 3,
        errors: 0,
        duplicateOrders: [
          { orderNumber: 'BATCH-001', duplicateMatchScore: 92 },
          { orderNumber: 'BATCH-005', duplicateMatchScore: 85 },
          { orderNumber: 'BATCH-008', duplicateMatchScore: 78 },
        ],
      },
    }).as('batchImport');

    cy.visit('/dashboard/orders/import');
    
    // Upload CSV file
    cy.findByLabelText(/Selecionar arquivo CSV/i).selectFile({
      contents: Cypress.Buffer.from(`customerName,customerCPF,customerEmail,fullAddress,totalAmount,paymentMethod
João Silva,123.456.789-00,<EMAIL>,"Rua A, 100",150.00,COD
Maria Santos,987.654.321-00,<EMAIL>,"Rua B, 200",200.00,CREDIT_CARD
João Silva,123.456.789-00,<EMAIL>,"R. A, 100",180.00,COD`),
      fileName: 'orders.csv',
      mimeType: 'text/csv',
    });
    
    // Start import
    cy.findByRole('button', { name: /Importar Pedidos/i }).click();
    
    // Wait for import
    cy.wait('@batchImport');
    
    // Show import results
    cy.findByText(/Importação Concluída/i).should('be.visible');
    cy.findByText(/10 pedidos importados/i).should('be.visible');
    cy.findByText(/3 possíveis duplicatas detectadas/i).should('be.visible');
    
    // Option to review duplicates
    cy.findByRole('button', { name: /Revisar Duplicatas/i }).click();
    
    // Should navigate to anti-fraud dashboard with filter
    cy.url().should('include', '/dashboard/antifraud');
    cy.url().should('include', 'filter=batch-import');
    
    // Duplicates should be shown
    cy.findByText('BATCH-001').should('be.visible');
    cy.findByText('BATCH-005').should('be.visible');
    cy.findByText('BATCH-008').should('be.visible');
  });

  it('should update duplicate detection when order is modified', () => {
    // Mock order update API
    cy.intercept('PUT', '**/orders/*', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          ...req.body,
          id: req.url.split('/').pop(),
          isDuplicate: true,
          duplicateStatus: 'PENDING_REVIEW',
          duplicateMatchScore: 75,
          message: 'Order updated. Duplicate status recalculated.',
        },
      });
    }).as('updateOrder');

    // Navigate to order edit page
    cy.visit('/dashboard/orders/edit/existing-order-1');
    
    // Change address (which might affect duplicate detection)
    cy.findByLabelText(/Endereço Completo/i).clear().type('Av. Paulista, 1000, São Paulo - SP');
    
    // Save changes
    cy.findByRole('button', { name: /Salvar Alterações/i }).click();
    
    // Wait for update
    cy.wait('@updateOrder');
    
    // Notification about duplicate recalculation
    cy.findByText(/Status de duplicata recalculado/i).should('be.visible');
    cy.findByText(/Novo score: 75%/i).should('be.visible');
    
    // Option to review if still duplicate
    cy.findByRole('button', { name: /Revisar Status/i }).should('be.visible');
  });

  it('should handle real-time duplicate notifications', () => {
    // Mock WebSocket connection for real-time updates
    cy.window().then((win) => {
      const mockWs = {
        send: cy.stub(),
        close: cy.stub(),
        addEventListener: cy.stub(),
      };
      
      cy.stub(win, 'WebSocket').returns(mockWs);
      
      // Simulate receiving duplicate notification
      setTimeout(() => {
        const messageHandler = mockWs.addEventListener.args.find(
          args => args[0] === 'message'
        )?.[1];
        
        if (messageHandler) {
          messageHandler({
            data: JSON.stringify({
              type: 'DUPLICATE_DETECTED',
              order: {
                id: 'realtime-order-1',
                orderNumber: 'ORD-RT-001',
                customerName: 'Pedro Santos',
                duplicateMatchScore: 91,
              },
            }),
          });
        }
      }, 2000);
    });

    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Wait for real-time notification
    cy.findByText(/Nova duplicata detectada/i, { timeout: 5000 }).should('be.visible');
    cy.findByText(/ORD-RT-001/i).should('be.visible');
    cy.findByText(/Score: 91%/i).should('be.visible');
    
    // Notification should have action buttons
    cy.findByRole('button', { name: /Ver Agora/i }).should('be.visible');
    cy.findByRole('button', { name: /Dispensar/i }).should('be.visible');
    
    // Click to view
    cy.findByRole('button', { name: /Ver Agora/i }).click();
    
    // Should scroll to or highlight the new order
    cy.findByText('ORD-RT-001').should('be.visible').parent().should('have.class', 'highlighted');
  });

  it('should export anti-fraud report', () => {
    cy.mockAntifraudAPIs();
    
    // Mock export API
    cy.intercept('GET', '**/antifraud/reports/export*', {
      statusCode: 200,
      headers: {
        'content-type': 'application/pdf',
        'content-disposition': 'attachment; filename="antifraud-report.pdf"',
      },
      body: Cypress.Buffer.from('PDF content'),
    }).as('exportReport');

    cy.visit('/dashboard/antifraud');
    cy.waitForAntifraudLoad();

    // Open export dialog
    cy.findByRole('button', { name: /Exportar Relatório/i }).click();
    
    // Select date range
    cy.findByLabelText(/Data Inicial/i).type('2024-01-01');
    cy.findByLabelText(/Data Final/i).type('2024-01-31');
    
    // Select format
    cy.findByLabelText(/Formato/i).select('PDF');
    
    // Include options
    cy.findByLabelText(/Incluir estatísticas/i).check();
    cy.findByLabelText(/Incluir gráficos/i).check();
    cy.findByLabelText(/Incluir detalhes dos pedidos/i).check();
    
    // Export
    cy.findByRole('button', { name: /Gerar Relatório/i }).click();
    
    // Wait for export
    cy.wait('@exportReport');
    
    // Verify download
    cy.readFile('cypress/downloads/antifraud-report.pdf').should('exist');
  });
});