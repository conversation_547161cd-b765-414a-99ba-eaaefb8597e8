# Railway Environment Variables Checklist

## Required Environment Variables in Railway Dashboard:

### 1. API Configuration
- [ ] `REACT_APP_API_URL` = `https://zencash-production.up.railway.app/api/v1`
  - ⚠️ Must start with `REACT_APP_` for Create React App
  - ⚠️ Must be the full URL including `/api/v1`

### 2. Tenant Configuration  
- [ ] `REACT_APP_TENANT_ID` = `28a833c0-c2a1-4498-85ca-b028f982ffb2`
  - Your specific tenant UUID

### 3. Build Configuration
- [ ] `NODE_ENV` = `production`
- [ ] `GENERATE_SOURCEMAP` = `false` (optional, reduces build size)
- [ ] `CI` = `false` (prevents build failures on warnings)

### 4. Optional Performance Settings
- [ ] `NODE_OPTIONS` = `--max-old-space-size=4096`
- [ ] `DISABLE_ESLINT_PLUGIN` = `true`

## How to Set in Railway:

1. Go to your Railway project
2. Select your frontend service
3. Click on "Variables" tab
4. Add each variable with the correct value
5. Railway will automatically rebuild when you save

## Verify Configuration:

After deployment, check:
1. Browser DevTools > Network tab > Check API calls are going to correct URL
2. Console should not show any CORS errors
3. Login page should load without redirects