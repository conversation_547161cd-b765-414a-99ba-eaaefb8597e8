<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Sistema de Faturamento para Suplementos"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />
    <title>Sistema de Faturamento</title>

    <!-- Add global utility functions -->
    <script>
      // Make CPF validation function globally available
      window.validateCPF = function(cpf) {
        // Remove any non-digit characters
        cpf = cpf.toString().replace(/[^\d]/g, '');
        
        // Check if it has 11 digits
        if (cpf.length !== 11) {
          return false;
        }
        
        // Check if all digits are the same (invalid case)
        if (/^(\d)\1+$/.test(cpf)) {
          return false;
        }
        
        // Validate first check digit
        let sum = 0;
        for (let i = 0; i < 9; i++) {
          sum += parseInt(cpf.charAt(i)) * (10 - i);
        }
        let mod = sum % 11;
        const firstCheckDigit = mod < 2 ? 0 : 11 - mod;
        if (parseInt(cpf.charAt(9)) !== firstCheckDigit) {
          return false;
        }
        
        // Validate second check digit
        sum = 0;
        for (let i = 0; i < 10; i++) {
          sum += parseInt(cpf.charAt(i)) * (11 - i);
        }
        mod = sum % 11;
        const secondCheckDigit = mod < 2 ? 0 : 11 - mod;
        if (parseInt(cpf.charAt(10)) !== secondCheckDigit) {
          return false;
        }
        
        return true;
      };
    </script>
    
    <!-- Debug Script for Order Creation (TEMPORARY - REMOVE IN PRODUCTION) -->
    <script>
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const script = document.createElement('script');
        script.src = '/debug-order-creation.js';
        script.async = true;
        document.head.appendChild(script);
      }
    </script>
  </head>
  <body>
    <noscript>Você precisa habilitar o JavaScript para executar este aplicativo.</noscript>
    <div id="root"></div>

    <!-- Production build - no debug scripts -->
  </body>
</html>