<!DOCTYPE html>
<html>
<head>
    <title>API Connection Test</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f0f0f0; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>ZenCash API Connection Test</h1>
    <button onclick="testConnection()">Test API Connection</button>
    <div id="results"></div>

    <script>
        async function testConnection() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Testing...</h2>';
            
            const tests = [
                {
                    name: 'Backend Health Check',
                    url: 'https://zencash-production.up.railway.app/api/v1/health',
                    method: 'GET'
                },
                {
                    name: 'Login Endpoint',
                    url: 'https://zencash-production.up.railway.app/api/v1/auth/login',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                }
            ];
            
            let html = '<h2>Test Results:</h2>';
            
            for (const test of tests) {
                html += `<h3>${test.name}</h3>`;
                try {
                    const options = {
                        method: test.method,
                        headers: test.headers || {},
                        body: test.body
                    };
                    
                    const response = await fetch(test.url, options);
                    const text = await response.text();
                    
                    if (response.ok) {
                        html += `<p class="success">✓ Success (${response.status})</p>`;
                        try {
                            const json = JSON.parse(text);
                            html += `<pre>${JSON.stringify(json, null, 2)}</pre>`;
                        } catch {
                            html += `<pre>${text}</pre>`;
                        }
                    } else {
                        html += `<p class="error">✗ Error (${response.status})</p>`;
                        html += `<pre>${text}</pre>`;
                    }
                } catch (error) {
                    html += `<p class="error">✗ Network Error</p>`;
                    html += `<pre>${error.message}</pre>`;
                    if (error.message.includes('CORS')) {
                        html += '<p>This is a CORS error - the backend needs to allow this origin.</p>';
                    }
                }
            }
            
            results.innerHTML = html;
        }
        
        // Auto-run on load
        window.onload = testConnection;
    </script>
</body>
</html>