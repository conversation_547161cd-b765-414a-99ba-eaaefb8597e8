// Script to check the current tenant ID
(function() {
  const userInfo = localStorage.getItem('unified_user_info');
  if (userInfo) {
    try {
      const parsed = JSON.parse(userInfo);
      console.log('Current user info:', parsed);
      console.log('Tenant ID:', parsed.tenantId);
      
      // Check if it's a valid UUID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (parsed.tenantId && !uuidRegex.test(parsed.tenantId)) {
        console.warn('WARNING: Tenant ID is not a valid UUID format!');
        console.log('Expected format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx');
      }
    } catch (e) {
      console.error('Error parsing user info:', e);
    }
  } else {
    console.log('No user info found in localStorage');
  }
})();