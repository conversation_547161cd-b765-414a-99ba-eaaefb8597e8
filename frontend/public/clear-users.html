<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Usuários - Man<PERSON> Apenas Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #2c3e50;
        }
        button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #c0392b;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #eee;
        }
        .result {
            background-color: #f5f7ff;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3f51b5;
            margin: 20px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left-color: #4caf50;
        }
        .warning {
            background-color: #fff8e1;
            border-left-color: #ffc107;
        }
        .danger {
            background-color: #ffebee;
            border-left-color: #f44336;
        }
        .log-container {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #1976d2;
        }
        .log-entry.success {
            color: #4caf50;
        }
        .log-entry.error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <h1>Limpar Usuários - Manter Apenas Admin</h1>

    <div class="card danger">
        <h2>⚠️ Atenção</h2>
        <p>Esta ferramenta irá remover <strong>TODOS</strong> os usuários do sistema, exceto o usuário Admin.</p>
        <p>Esta ação é <strong>irreversível</strong>.</p>

        <button id="btn-clear-users">Limpar Todos os Usuários (Manter Admin)</button>
        <button id="btn-show-users">Mostrar Usuários Atuais</button>
    </div>

    <div id="log-container" class="log-container">
        <div class="log-entry info">Aguardando ação...</div>
    </div>

    <div id="result" class="result" style="display: none;"></div>

    <script>
        // Elementos da UI
        const btnClearUsers = document.getElementById('btn-clear-users');
        const btnShowUsers = document.getElementById('btn-show-users');
        const logContainer = document.getElementById('log-container');
        const resultElement = document.getElementById('result');

        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Função para limpar todos os usuários exceto admin
        btnClearUsers.addEventListener('click', function() {
            // Confirmar antes de limpar
            if (!confirm('ATENÇÃO: Esta ação irá remover TODOS os usuários do sistema, exceto o usuário Admin. Esta ação NÃO pode ser desfeita. Deseja continuar?')) {
                addLog('Operação cancelada pelo usuário.', 'info');
                return;
            }

            // Pedir confirmação adicional
            if (!confirm('Confirme novamente: Todos os usuários serão REMOVIDOS. Apenas o Admin permanecerá. Tem certeza absoluta?')) {
                addLog('Operação cancelada na confirmação secundária.', 'info');
                return;
            }

            try {
                addLog('Iniciando limpeza de usuários...', 'info');

                // 1. Obter usuários atuais para contagem
                const usersStr = localStorage.getItem('users');
                const users = usersStr ? JSON.parse(usersStr) : [];
                const userCount = users.length;

                // 2. Criar e carregar o UserDataManager
                addLog('Carregando UserDataManager...', 'info');
                const script = document.createElement('script');
                script.textContent = `
                    // Definir a classe UserDataManager
                    class UserDataManager {
                        // Chaves de armazenamento no localStorage
                        static USERS_KEY = 'users';
                        static DEFAULT_USERS_KEY = 'default_users';
                        static MOCK_USERS_KEY = 'mockUsers';
                        static USER_PASSWORDS_KEY = 'user_passwords';
                        static USER_INFO_KEY = 'userInfo';

                        // Limpa todos os usuários e mantém apenas o admin
                        static clearAllUsersExceptAdmin() {
                            try {
                                // Criar usuário admin
                                const adminUser = {
                                    id: 1,
                                    nome: "Admin",
                                    email: "<EMAIL>",
                                    papeis: ["admin"],
                                    permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                                    ativo: true
                                };

                                // Definir apenas o admin como usuário
                                localStorage.setItem(this.USERS_KEY, JSON.stringify([adminUser]));

                                // Sincronizar com default_users
                                const defaultUsers = {
                                    '<EMAIL>': {
                                        id: 1,
                                        email: '<EMAIL>',
                                        fullName: 'Admin',
                                        role: 'admin',
                                        password: 'admin123'
                                    }
                                };
                                localStorage.setItem(this.DEFAULT_USERS_KEY, JSON.stringify(defaultUsers));

                                // Sincronizar com mockUsers
                                localStorage.setItem(this.MOCK_USERS_KEY, JSON.stringify([{
                                    id: 1,
                                    nome: "Admin",
                                    email: "<EMAIL>",
                                    perfil: "Administrador",
                                    ativo: true
                                }]));

                                // Sincronizar senhas
                                const adminPasswords = {
                                    '<EMAIL>': 'admin123'
                                };
                                localStorage.setItem(this.USER_PASSWORDS_KEY, JSON.stringify(adminPasswords));

                                // Atualizar userInfo se for o admin
                                const userInfoStr = localStorage.getItem(this.USER_INFO_KEY);
                                if (userInfoStr) {
                                    const userInfo = JSON.parse(userInfoStr);
                                    if (userInfo.email === "<EMAIL>") {
                                        const adminUserInfo = {
                                            id: 1,
                                            email: "<EMAIL>",
                                            fullName: "Admin",
                                            role: "admin"
                                        };
                                        localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(adminUserInfo));
                                    }
                                }

                                console.log('UserDataManager: Todos os usuários foram removidos, exceto o admin');
                                return true;
                            } catch (error) {
                                console.error('UserDataManager: Erro ao limpar usuários:', error);
                                return false;
                            }
                        }
                    }

                    // Executar a limpeza
                    const result = UserDataManager.clearAllUsersExceptAdmin();

                    // Disparar evento para notificar a página
                    window.dispatchEvent(new CustomEvent('users-cleared', { detail: { success: result } }));

                    // Forçar atualização da página após 2 segundos
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                `;
                document.body.appendChild(script);

                // Ouvir o evento de conclusão
                window.addEventListener('users-cleared', function(e) {
                    const customEvent = e as CustomEvent;
                    if (customEvent.detail && customEvent.detail.success) {
                        addLog('Limpeza concluída com sucesso!', 'success');
                    } else {
                        addLog('Erro durante a limpeza via UserDataManager', 'error');
                    }

                    // Remover o script
                    document.body.removeChild(script);
                });

                // Mostrar resultado
                addLog(`Limpeza iniciada! ${userCount - 1} usuários serão removidos.`, 'success');

                resultElement.innerHTML = `
                    <h3>Limpeza em Andamento</h3>
                    <p>Todos os usuários estão sendo removidos, exceto o Admin.</p>
                    <ul>
                        <li>Usuários antes: ${userCount}</li>
                        <li>Usuários a remover: ${userCount - 1}</li>
                        <li>Usuários restantes: 1 (Admin)</li>
                    </ul>
                    <p><strong>Importante:</strong> A página será recarregada automaticamente em alguns segundos para aplicar todas as alterações.</p>
                `;
                resultElement.className = 'result success';
                resultElement.style.display = 'block';

            } catch (error) {
                addLog(`Erro durante a limpeza: ${error.message}`, 'error');

                resultElement.innerHTML = `
                    <h3>Erro</h3>
                    <p>Ocorreu um erro durante a limpeza de usuários:</p>
                    <p>${error.message}</p>
                `;
                resultElement.className = 'result danger';
                resultElement.style.display = 'block';
            }
        });

        // Função para mostrar usuários atuais
        btnShowUsers.addEventListener('click', function() {
            try {
                addLog('Coletando informações de usuários...', 'info');

                // 1. Obter usuários do contexto principal
                const usersStr = localStorage.getItem('users');
                const users = usersStr ? JSON.parse(usersStr) : [];

                // 2. Obter usuários do sistema de emergência
                const defaultUsersStr = localStorage.getItem('default_users');
                const defaultUsers = defaultUsersStr ? JSON.parse(defaultUsersStr) : {};

                // 3. Obter mock users
                const mockUsersStr = localStorage.getItem('mockUsers');
                const mockUsers = mockUsersStr ? JSON.parse(mockUsersStr) : [];

                // 4. Obter senhas
                const passwordsStr = localStorage.getItem('user_passwords');
                const passwords = passwordsStr ? JSON.parse(passwordsStr) : {};

                // 5. Criar HTML para exibir os resultados
                let html = `
                    <h3>Usuários Atuais</h3>

                    <h4>Usuários do Contexto Principal (${users.length})</h4>
                    <pre>${JSON.stringify(users, null, 2)}</pre>

                    <h4>Usuários do Sistema de Emergência (${Object.keys(defaultUsers).length})</h4>
                    <pre>${JSON.stringify(defaultUsers, null, 2)}</pre>

                    <h4>Mock Users (${mockUsers.length})</h4>
                    <pre>${JSON.stringify(mockUsers, null, 2)}</pre>

                    <h4>Senhas (${Object.keys(passwords).length})</h4>
                    <pre>${JSON.stringify(passwords, null, 2)}</pre>
                `;

                resultElement.innerHTML = html;
                resultElement.className = 'result';
                resultElement.style.display = 'block';

                addLog('Informações de usuários coletadas com sucesso.', 'success');

            } catch (error) {
                addLog(`Erro ao coletar informações: ${error.message}`, 'error');

                resultElement.innerHTML = `
                    <h3>Erro</h3>
                    <p>Ocorreu um erro ao coletar informações de usuários:</p>
                    <p>${error.message}</p>
                `;
                resultElement.className = 'result danger';
                resultElement.style.display = 'block';
            }
        });
    </script>
</body>
</html>
