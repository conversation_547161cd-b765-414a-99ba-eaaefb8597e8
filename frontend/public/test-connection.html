<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Backend Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Backend Connection Test</h1>
    
    <div id="config" class="test-result info"></div>
    
    <button onclick="testSimpleFetch()">Test Simple Fetch</button>
    <button onclick="testWithCredentials()">Test With Credentials</button>
    <button onclick="testProducts()">Test Products Endpoint</button>
    <button onclick="testCreateProduct()">Test Create Product</button>
    
    <div id="results"></div>

    <script>
        const API_URL = 'https://zencash-production.up.railway.app/api/v1';
        const TENANT_ID = '28a833c0-c2a1-4498-85ca-b028f982ffb2';
        
        // Show configuration
        document.getElementById('config').innerHTML = `
            <strong>Configuration:</strong><br>
            API URL: ${API_URL}<br>
            Tenant ID: ${TENANT_ID}
        `;
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function testSimpleFetch() {
            addResult('<strong>Testing simple fetch to /test-cors...</strong>');
            try {
                const response = await fetch(`${API_URL}/test-cors`);
                const data = await response.json();
                addResult(`Success! Response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
            } catch (error) {
                addResult(`Error: ${error.message}`, 'error');
                if (error.message === 'Failed to fetch') {
                    addResult('This usually indicates a CORS issue or the backend is unreachable', 'error');
                }
            }
        }
        
        async function testWithCredentials() {
            addResult('<strong>Testing fetch with credentials to /test-cors...</strong>');
            try {
                const response = await fetch(`${API_URL}/test-cors`, {
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                const data = await response.json();
                addResult(`Success! Response: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
            } catch (error) {
                addResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function testProducts() {
            addResult('<strong>Testing products endpoint...</strong>');
            try {
                const response = await fetch(`${API_URL}/products`, {
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-tenant-id': TENANT_ID,
                    },
                });
                
                if (!response.ok) {
                    const text = await response.text();
                    addResult(`Failed with status ${response.status}: ${text}`, 'error');
                } else {
                    const data = await response.json();
                    addResult(`Success! Found ${data.length} products`, 'success');
                }
            } catch (error) {
                addResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function testCreateProduct() {
            addResult('<strong>Testing product creation...</strong>');
            
            const productData = {
                name: 'Test Product',
                description: 'Test Description',
                category: 'supplements',
                active: true,
                variations: [{
                    sku: 'TEST-001',
                    type: 'CAPSULAS',
                    customName: null,
                    price: 100,
                    active: true,
                    inventory: {
                        quantity: 100,
                        minAlert: 10
                    }
                }]
            };
            
            addResult(`Sending: <pre>${JSON.stringify(productData, null, 2)}</pre>`, 'info');
            
            try {
                const response = await fetch(`${API_URL}/products`, {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-tenant-id': TENANT_ID,
                    },
                    body: JSON.stringify(productData)
                });
                
                if (!response.ok) {
                    const text = await response.text();
                    addResult(`Failed with status ${response.status}: ${text}`, 'error');
                } else {
                    const data = await response.json();
                    addResult(`Success! Created product: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                }
            } catch (error) {
                addResult(`Error: ${error.message}`, 'error');
                addResult(`Full error: <pre>${JSON.stringify(error, null, 2)}</pre>`, 'error');
            }
        }
    </script>
</body>
</html>