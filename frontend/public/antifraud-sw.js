// Anti-fraud Service Worker
const CACHE_NAME = 'antifraud-v1';
const API_CACHE_NAME = 'antifraud-api-v1';

// URLs to cache
const urlsToCache = [
  '/dashboard/antifraud',
  '/static/js/antifraud.bundle.js',
  '/static/css/antifraud.css',
];

// API endpoints to cache
const apiEndpoints = [
  '/antifraud/duplicates/review-queue',
  '/antifraud/statistics',
];

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll(urlsToCache);
    })
  );
});

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (request.method === 'GET' && apiEndpoints.some(endpoint => url.pathname.includes(endpoint))) {
    event.respondWith(
      caches.open(API_CACHE_NAME).then((cache) => {
        return fetch(request).then((response) => {
          // Cache successful responses
          if (response.status === 200) {
            cache.put(request, response.clone());
          }
          return response;
        }).catch(() => {
          // Return cached response if offline
          return cache.match(request);
        });
      })
    );
    return;
  }

  // Handle static assets
  if (request.method === 'GET') {
    event.respondWith(
      caches.match(request).then((response) => {
        return response || fetch(request).then((response) => {
          // Don't cache non-successful responses
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // Clone the response
          const responseToCache = response.clone();

          caches.open(CACHE_NAME).then((cache) => {
            cache.put(request, responseToCache);
          });

          return response;
        });
      })
    );
  }
});

// Background sync for review submissions
self.addEventListener('sync', (event) => {
  if (event.tag === 'review-sync') {
    event.waitUntil(syncReviews());
  }
});

async function syncReviews() {
  const db = await openDB();
  const tx = db.transaction('pending-reviews', 'readonly');
  const reviews = await tx.objectStore('pending-reviews').getAll();

  for (const review of reviews) {
    try {
      const response = await fetch(`/api/antifraud/duplicates/${review.orderId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${review.token}`,
          'x-tenant-id': review.tenantId,
        },
        body: JSON.stringify(review.data),
      });

      if (response.ok) {
        // Remove from pending
        const deleteTx = db.transaction('pending-reviews', 'readwrite');
        await deleteTx.objectStore('pending-reviews').delete(review.id);
      }
    } catch (error) {
      console.error('Sync failed for review:', review.id);
    }
  }
}

// Simple IndexedDB wrapper
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('antifraud-db', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('pending-reviews')) {
        db.createObjectStore('pending-reviews', { keyPath: 'id', autoIncrement: true });
      }
    };
  });
}

// Listen for messages from the main thread
self.addEventListener('message', (event) => {
  if (event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});