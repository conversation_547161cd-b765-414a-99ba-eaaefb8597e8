<!DOCTYPE html>
<html lang="pt-BR"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Cobrança - Gestão Completa de Pedidos e Logística</title>
    <meta name="description" content="Sistema completo de gestão de pedidos e cobrança. Rastreamento automático, distribuição inteligente e análise de desempenho. Otimize suas operações!">
    <link href="./landing-assets/all.min.css" rel="stylesheet">
    <link href="./landing-assets/css2" rel="stylesheet">
    <link rel="stylesheet" href="./landing-assets/styles.css">
    <link rel="stylesheet" href="./landing-assets/animations.css">
    <link rel="stylesheet" href="./landing-assets/marketing-theme.css">
    <link rel="stylesheet" href="./landing-assets/logistics-animations.css">
    <style>
        /* Critical overflow fixes */
        .hero-section {
            overflow: hidden !important;
        }
        
        .hero-image {
            position: relative;
            overflow: hidden !important;
            padding: 20px;
        }
        
        /* Fix logo animation background */
        .logo::before {
            display: none !important;
        }
        
        /* Fix truck icon special effect */
        .logo .fa-truck::after {
            display: none !important;
        }
        
        /* Ensure logo displays properly */
        .logo {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            z-index: 10;
        }
        
        .logo i {
            color: #10b981;
            font-size: 2rem;
        }
        
        .logo span {
            font-weight: 900;
            font-size: 1.5rem;
            color: #10b981;
        }
        
        /* Hide floating elements */
        .floating-elements {
            display: none !important;
        }
        
        /* Fix hero section rotating background */
        .hero-section::before {
            display: none !important;
        }
        
        /* Prevent any truck icon animations */
        .fa-truck::before,
        .fa-truck::after {
            display: none !important;
        }
        
        /* Fix any stat card pseudo elements */
        .stat-card::before,
        .stat-card::after {
            display: none !important;
        }
        
        /* Fix navbar overflow */
        .navbar {
            overflow: hidden !important;
        }
        
        /* Ensure no elements extend beyond viewport */
        body {
            overflow-x: hidden !important;
            position: relative;
        }
        
        /* Fix modal z-index if it appears */
        .modal {
            z-index: 9999 !important;
        }
        
        /* Fix chart container */
        .preview-chart {
            position: relative;
            height: 150px !important;
            max-height: 150px !important;
            overflow: hidden !important;
            margin-top: 0.5rem;
        }
        
        #previewChart {
            position: absolute !important;
            top: 0;
            left: 0;
            width: 100% !important;
            height: 100% !important;
            max-height: 150px !important;
        }
        
        /* Ensure dashboard preview doesn't overflow */
        .dashboard-preview {
            position: relative;
            z-index: 10;
            max-width: 500px;
            margin: 2rem auto 0;
            background: white;
        }
        
        /* Align hero image better */
        .hero-image {
            display: flex;
            align-items: center;
            padding-top: 0;
            position: relative;
        }
        
        
        /* Contain the hero section properly */
        .hero-content {
            position: relative;
            z-index: 1;
            align-items: center !important;
            padding-top: 4rem !important;
        }
        
        /* Reduce preview content padding */
        .preview-content {
            padding: 1rem !important;
        }
        
        /* Make preview header smaller */
        .preview-header {
            padding: 0.75rem !important;
        }
        
        /* Make stat cards more compact */
        .stat-card {
            padding: 0.75rem 1rem !important;
        }
        
        .stat-cards {
            margin-bottom: 0.75rem !important;
        }
        
        .stat-value {
            font-size: 1.25rem !important;
        }
        
        .stat-icon {
            width: 40px !important;
            height: 40px !important;
            margin-bottom: 0.5rem !important;
        }
        
        .stat-icon i {
            font-size: 1.25rem !important;
        }
        
        /* Reduce animation amplitude for safety */
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }
        
        @keyframes floatReverse {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(5px);
            }
        }
        
        /* Disable problematic animations */
        .animate-float,
        .animate-float-slow,
        .animate-float-reverse {
            animation: none !important;
        }
        
        /* Keep only the dashboard float animation */
        .dashboard-preview {
            animation: float 6s ease-in-out infinite !important;
        }
        
        /* Fix wave divider positioning */
        .wave-divider {
            position: relative !important;
            z-index: 0 !important;
            margin-top: -1px;
        }
        
        /* Ensure features section stays below hero */
        .features-section {
            position: relative;
            z-index: 2;
            background: white;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>SisCobranca</span>
                </div>
                <div class="nav-links">
                    <a href="#features">Recursos</a>
                    <a href="#benefits">Benefícios</a>
                    <a href="#pricing">Preços</a>
                    <a href="#contact">Contato</a>
                    <button class="btn-login" onclick="window.location.href='/login'">Login</button>
                    <button class="btn-demo" onclick="showDemo()">Demo Grátis</button>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </nav>

        <div class="hero-content">
            <div class="hero-text">
                <div class="badge animate-fade-in">
                    <i class="fas fa-award"></i>
                    #1 Sistema de Gestão de Cobranças do Brasil
                </div>
                <h1 class="hero-title animate-slide-up">
                    Transforme sua gestão de <span class="gradient-text">Pedidos e Cobranças</span> em resultados
                </h1>
                <p class="hero-subtitle animate-fade-in-delay">
                    Sistema completo para gestão de pedidos com rastreamento automático, 
                    distribuição inteligente e análise de desempenho em tempo real.
                </p>
                <div class="hero-buttons animate-fade-in-delay-2">
                    <button class="btn-primary" onclick="showDemo()">
                        <i class="fas fa-play"></i>
                        Começar Agora
                    </button>
                    <button class="btn-secondary" onclick="watchVideo()">
                        <i class="fas fa-video"></i>
                        Ver Demo
                    </button>
                </div>
                <div class="trust-badges animate-fade-in-delay-3">
                    <div class="trust-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>100% Seguro</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-headset"></i>
                        <span>Suporte 24/7</span>
                    </div>
                    <div class="trust-item">
                        <i class="fas fa-chart-line"></i>
                        <span>+300% ROI</span>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <div class="dashboard-preview animate-float">
                    <div class="preview-header">
                        <div class="preview-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                    <div class="preview-content">
                        <div class="stat-cards">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
                                <div class="stat-value">1,234</div>
                                <div class="stat-label">Pedidos Hoje</div>
                                <div class="stat-change positive">+23%</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                                <div class="stat-value">R$ 45.6K</div>
                                <div class="stat-label">Faturamento</div>
                                <div class="stat-change positive">+18%</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-truck"></i></div>
                                <div class="stat-value">89%</div>
                                <div class="stat-label">Taxa Entrega</div>
                                <div class="stat-change positive">+5%</div>
                            </div>
                        </div>
                        <div class="preview-chart">
                            <canvas id="previewChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="floating-elements">
                    <div class="float-1 animate-float-slow"><i class="fas fa-shipping-fast"></i></div>
                    <div class="float-2 animate-float-reverse"><i class="fas fa-truck-loading"></i></div>
                    <div class="float-3 animate-float-slow"><i class="fas fa-route"></i></div>
                    <div class="float-4 animate-float"><i class="fas fa-box-open"></i></div>
                </div>
            </div>
        </div>

        <div class="wave-divider">
            <svg viewBox="0 0 1440 120" preserveAspectRatio="none">
                <path d="M0,32L48,37.3C96,43,192,53,288,58.7C384,64,480,64,576,58.7C672,53,768,43,864,48C960,53,1056,75,1152,80C1248,85,1344,75,1392,69.3L1440,64L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">RECURSOS</span>
                <h2 class="section-title">Tudo que você precisa em um só lugar</h2>
                <p class="section-subtitle">
                    Sistema completo desenvolvido por especialistas em gestão financeira e cobrança
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <h3>Distribuição Inteligente</h3>
                    <p>Alocação automática de cobranças baseada em performance e especialização da equipe</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> IA para melhor alocação</li>
                        <li><i class="fas fa-check"></i> Métricas de sucesso por operador</li>
                        <li><i class="fas fa-check"></i> Fila prioritária inteligente</li>
                    </ul>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3>Rastreamento Inteligente</h3>
                    <p>Acompanhamento completo de entregas e status de pagamento em tempo real</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Integração Correios automática</li>
                        <li><i class="fas fa-check"></i> Alertas de atraso</li>
                        <li><i class="fas fa-check"></i> Histórico completo</li>
                    </ul>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Prevenção de Fraudes</h3>
                    <p>Identificação automática de pedidos suspeitos e clientes problemáticos</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Validação CPF/CNPJ</li>
                        <li><i class="fas fa-check"></i> Score de risco</li>
                        <li><i class="fas fa-check"></i> Blacklist automática</li>
                    </ul>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Analytics Avançado</h3>
                    <p>Visão completa da saúde financeira com previsões e insights acionáveis</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Previsão de recebimentos</li>
                        <li><i class="fas fa-check"></i> Análise de inadimplência</li>
                        <li><i class="fas fa-check"></i> KPIs em tempo real</li>
                    </ul>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Gestão de Equipes</h3>
                    <p>Controle total sobre permissões e acompanhamento de performance individual</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Hierarquia flexível</li>
                        <li><i class="fas fa-check"></i> Metas e comissões</li>
                        <li><i class="fas fa-check"></i> Ranking de desempenho</li>
                    </ul>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Integrações Poderosas</h3>
                    <p>Conecte com suas ferramentas favoritas e automatize todo o fluxo</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> WhatsApp Business API</li>
                        <li><i class="fas fa-check"></i> Webhook personalizado</li>
                        <li><i class="fas fa-check"></i> API REST completa</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="benefits-section">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">BENEFÍCIOS</span>
                <h2 class="section-title">Por que escolher nosso Sistema?</h2>
                <p class="section-subtitle">Resultados comprovados por mais de 500 empresas que transformaram sua gestão</p>
            </div>

            <div class="benefits-grid">
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">01</div>
                    <h3>Aumente sua taxa de recuperação em 85%</h3>
                    <p>Sistema inteligente que identifica o melhor momento e canal para cobrança, aumentando drasticamente suas taxas de sucesso e redução de inadimplência</p>
                </div>
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">02</div>
                    <h3>Economize 6 horas diárias da equipe</h3>
                    <p>Automatize tarefas repetitivas, distribuição de pedidos e acompanhamento, liberando sua equipe para focar em atividades estratégicas</p>
                </div>
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">03</div>
                    <h3>Visão 360° do seu negócio em tempo real</h3>
                    <p>Dashboards interativos com métricas essenciais, previsões de fluxo de caixa e insights acionáveis para tomada de decisão rápida</p>
                </div>
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">04</div>
                    <h3>Integração total com Correios e WhatsApp</h3>
                    <p>Rastreamento automático de encomendas e comunicação integrada via WhatsApp, mantendo clientes informados e reduzindo consultas manuais em 90%</p>
                </div>
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">05</div>
                    <h3>Segurança e conformidade garantidas</h3>
                    <p>Sistema auditado com criptografia de ponta, backups automáticos e total conformidade com LGPD, protegendo dados sensíveis do seu negócio</p>
                </div>
                <div class="benefit-item animate-on-scroll">
                    <div class="benefit-number">06</div>
                    <h3>ROI comprovado em 45 dias</h3>
                    <p>Nossos clientes recuperam o investimento em média em 45 dias através do aumento de eficiência, redução de perdas e melhoria nas taxas de conversão</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Integration Section -->
    <section class="integration-section" style="background: white; padding: 80px 0; position: relative;">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">INTEGRAÇÕES</span>
                <h2 class="section-title">Conecte com suas ferramentas favoritas</h2>
                <p class="section-subtitle">Integração perfeita com as principais plataformas do mercado</p>
            </div>
            <div class="integrations-grid" style="display: flex; flex-wrap: wrap; justify-content: center; gap: 40px; margin-top: 50px;">
                <div class="integration-item animate-on-scroll" style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                    <div style="width: 80px; height: 80px; background: #25D366; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px; box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);">
                        <i class="fab fa-whatsapp" style="font-size: 40px; color: white;"></i>
                    </div>
                    <h4 style="margin: 0; font-size: 1.1rem;">WhatsApp</h4>
                    <p style="margin: 5px 0 0; color: #64748b; font-size: 0.9rem;">Mensagens automáticas</p>
                </div>
                <div class="integration-item animate-on-scroll" style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                    <div style="width: 80px; height: 80px; background: #FFD700; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px; box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);">
                        <i class="fas fa-truck" style="font-size: 40px; color: #333;"></i>
                    </div>
                    <h4 style="margin: 0; font-size: 1.1rem;">Correios</h4>
                    <p style="margin: 5px 0 0; color: #64748b; font-size: 0.9rem;">Rastreamento em tempo real</p>
                </div>
                <div class="integration-item animate-on-scroll" style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                    <div style="width: 80px; height: 80px; background: #1877F2; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px; box-shadow: 0 4px 20px rgba(24, 119, 242, 0.3);">
                        <i class="fab fa-facebook" style="font-size: 40px; color: white;"></i>
                    </div>
                    <h4 style="margin: 0; font-size: 1.1rem;">Facebook</h4>
                    <p style="margin: 5px 0 0; color: #64748b; font-size: 0.9rem;">Importação de leads</p>
                </div>
                <div class="integration-item animate-on-scroll" style="display: flex; flex-direction: column; align-items: center; text-align: center;">
                    <div style="width: 80px; height: 80px; background: #6366F1; border-radius: 16px; display: flex; align-items: center; justify-content: center; margin-bottom: 15px; box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);">
                        <i class="fas fa-webhook" style="font-size: 40px; color: white;"></i>
                    </div>
                    <h4 style="margin: 0; font-size: 1.1rem;">Webhook</h4>
                    <p style="margin: 5px 0 0; color: #64748b; font-size: 0.9rem;">API personalizada</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section" style="background: #f8f9fa; padding: 80px 0;">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">DEPOIMENTOS</span>
                <h2 class="section-title">O que nossos clientes dizem</h2>
                <p class="section-subtitle">Histórias reais de sucesso com nosso sistema</p>
            </div>
            <div class="testimonials-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; margin-top: 50px;">
                <div class="testimonial-card animate-on-scroll" style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stars" style="color: #fbbf24; margin-bottom: 20px;">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p style="font-size: 1.1rem; line-height: 1.8; color: #334155; margin-bottom: 20px;">"Reduzimos nossa inadimplência em 65% nos primeiros 3 meses. O sistema de distribuição inteligente mudou completamente nossa operação."</p>
                    <div class="testimonial-author" style="display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 50px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user" style="color: #9ca3af;"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; color: #1e293b;">João Silva</div>
                            <div style="font-size: 0.9rem; color: #64748b;">CEO - TechStore</div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card animate-on-scroll" style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stars" style="color: #fbbf24; margin-bottom: 20px;">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p style="font-size: 1.1rem; line-height: 1.8; color: #334155; margin-bottom: 20px;">"A integração com WhatsApp aumentou nossa taxa de resposta em 300%. Clientes adoram receber atualizações automáticas."</p>
                    <div class="testimonial-author" style="display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 50px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user" style="color: #9ca3af;"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; color: #1e293b;">Maria Santos</div>
                            <div style="font-size: 0.9rem; color: #64748b;">Diretora Financeira - ModaBrasil</div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card animate-on-scroll" style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stars" style="color: #fbbf24; margin-bottom: 20px;">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <p style="font-size: 1.1rem; line-height: 1.8; color: #334155; margin-bottom: 20px;">"ROI em 30 dias! Economizamos 8 horas diárias de trabalho manual e aumentamos recuperação em R$ 150 mil/mês."</p>
                    <div class="testimonial-author" style="display: flex; align-items: center; gap: 15px;">
                        <div style="width: 50px; height: 50px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user" style="color: #9ca3af;"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; color: #1e293b;">Pedro Oliveira</div>
                            <div style="font-size: 0.9rem; color: #64748b;">Gerente de Cobrança - FastCommerce</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section" style="background: #f8f9fa; padding: 80px 0;">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">RESULTADOS</span>
                <h2 class="section-title">Números que falam por si</h2>
                <p class="section-subtitle">Dados reais de clientes que utilizam nosso sistema</p>
            </div>
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 50px;">
                <div class="stat-box animate-on-scroll" style="text-align: center; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stat-number" style="font-size: 3rem; font-weight: 800; color: #10b981; margin-bottom: 10px;">R$ 2.5M+</div>
                    <div class="stat-label" style="font-size: 1.1rem; color: #64748b;">Recuperados mensalmente</div>
                </div>
                <div class="stat-box animate-on-scroll" style="text-align: center; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stat-number" style="font-size: 3rem; font-weight: 800; color: #6366f1; margin-bottom: 10px;">85%</div>
                    <div class="stat-label" style="font-size: 1.1rem; color: #64748b;">Taxa de recuperação</div>
                </div>
                <div class="stat-box animate-on-scroll" style="text-align: center; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stat-number" style="font-size: 3rem; font-weight: 800; color: #f59e0b; margin-bottom: 10px;">500+</div>
                    <div class="stat-label" style="font-size: 1.1rem; color: #64748b;">Empresas ativas</div>
                </div>
                <div class="stat-box animate-on-scroll" style="text-align: center; padding: 30px; background: white; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <div class="stat-number" style="font-size: 3rem; font-weight: 800; color: #ef4444; margin-bottom: 10px;">24/7</div>
                    <div class="stat-label" style="font-size: 1.1rem; color: #64748b;">Suporte dedicado</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section" style="background: white; padding: 80px 0;">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">PREÇOS</span>
                <h2 class="section-title">Planos que crescem com seu negócio</h2>
                <p class="section-subtitle">Escolha o plano ideal para sua empresa. Sem surpresas, sem taxas ocultas.</p>
            </div>
            <div class="pricing-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 30px; margin-top: 50px; max-width: 1000px; margin-left: auto; margin-right: auto;">
                <div class="pricing-card animate-on-scroll" style="background: white; border: 2px solid #e5e7eb; border-radius: 20px; padding: 40px 30px; text-align: center; position: relative; transition: all 0.3s;">
                    <h3 style="font-size: 1.5rem; margin-bottom: 10px; color: #1e293b;">Starter</h3>
                    <p style="color: #64748b; margin-bottom: 30px;">Ideal para pequenos negócios</p>
                    <div class="price" style="margin-bottom: 30px;">
                        <span style="font-size: 3rem; font-weight: 800; color: #1e293b;">R$ 197</span>
                        <span style="color: #64748b;">/mês</span>
                    </div>
                    <ul style="list-style: none; padding: 0; margin-bottom: 30px; text-align: left;">
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Até 500 pedidos/mês</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> 3 usuários inclusos</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> WhatsApp automático</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Relatórios básicos</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Suporte por email</li>
                    </ul>
                    <button class="btn-secondary" style="width: 100%; padding: 15px; border: 2px solid #e5e7eb; background: white; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s;" onclick="showDemo()">Começar teste grátis</button>
                </div>
                <div class="pricing-card animate-on-scroll featured" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); border: none; border-radius: 20px; padding: 50px 30px; text-align: center; position: relative; color: white; transform: scale(1.05); box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); background: #10b981; color: white; padding: 5px 20px; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">MAIS POPULAR</div>
                    <h3 style="font-size: 1.5rem; margin-bottom: 10px; color: white;">Professional</h3>
                    <p style="color: rgba(255,255,255,0.9); margin-bottom: 30px;">Para empresas em crescimento</p>
                    <div class="price" style="margin-bottom: 30px;">
                        <span style="font-size: 3rem; font-weight: 800; color: white;">R$ 497</span>
                        <span style="color: rgba(255,255,255,0.9);">/mês</span>
                    </div>
                    <ul style="list-style: none; padding: 0; margin-bottom: 30px; text-align: left;">
                        <li style="padding: 10px 0; color: white;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Até 2.000 pedidos/mês</li>
                        <li style="padding: 10px 0; color: white;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> 10 usuários inclusos</li>
                        <li style="padding: 10px 0; color: white;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Todas integrações</li>
                        <li style="padding: 10px 0; color: white;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Analytics avançado</li>
                        <li style="padding: 10px 0; color: white;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Suporte prioritário</li>
                    </ul>
                    <button class="btn-primary" style="width: 100%; padding: 15px; border: none; background: white; color: #6366f1; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s;" onclick="showDemo()">Começar teste grátis</button>
                </div>
                <div class="pricing-card animate-on-scroll" style="background: white; border: 2px solid #e5e7eb; border-radius: 20px; padding: 40px 30px; text-align: center; position: relative; transition: all 0.3s;">
                    <h3 style="font-size: 1.5rem; margin-bottom: 10px; color: #1e293b;">Enterprise</h3>
                    <p style="color: #64748b; margin-bottom: 30px;">Soluções personalizadas</p>
                    <div class="price" style="margin-bottom: 30px;">
                        <span style="font-size: 2rem; font-weight: 800; color: #1e293b;">Sob consulta</span>
                    </div>
                    <ul style="list-style: none; padding: 0; margin-bottom: 30px; text-align: left;">
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Pedidos ilimitados</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Usuários ilimitados</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> API personalizada</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Servidor dedicado</li>
                        <li style="padding: 10px 0; color: #475569;"><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Gerente de sucesso</li>
                    </ul>
                    <button class="btn-secondary" style="width: 100%; padding: 15px; border: 2px solid #e5e7eb; background: white; border-radius: 10px; font-weight: 600; cursor: pointer; transition: all 0.3s;" onclick="showDemo()">Falar com vendas</button>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Pronto para transformar sua gestão de cobrança?</h2>
                <p>Comece agora mesmo e veja resultados em menos de 7 dias</p>
                <div class="cta-buttons">
                    <button class="btn-primary large" onclick="showDemo()">
                        Começar Teste Grátis
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <div class="cta-info">
                        <i class="fas fa-check-circle"></i>
                        <span>Sem cartão de crédito • Cancele quando quiser</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq-section" style="background: #f8f9fa; padding: 80px 0;">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">FAQ</span>
                <h2 class="section-title">Perguntas Frequentes</h2>
                <p class="section-subtitle">Tire suas dúvidas sobre nosso sistema</p>
            </div>
            <div class="faq-grid" style="max-width: 800px; margin: 50px auto 0;">
                <div class="faq-item animate-on-scroll" style="background: white; border-radius: 12px; padding: 25px 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <h4 style="margin: 0 0 15px; color: #1e293b; font-size: 1.2rem;">Como funciona o período de teste?</h4>
                    <p style="margin: 0; color: #64748b; line-height: 1.6;">Oferecemos 14 dias de teste gratuito com acesso completo a todas as funcionalidades. Não é necessário cartão de crédito e você pode cancelar a qualquer momento.</p>
                </div>
                <div class="faq-item animate-on-scroll" style="background: white; border-radius: 12px; padding: 25px 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <h4 style="margin: 0 0 15px; color: #1e293b; font-size: 1.2rem;">Posso migrar meus dados do sistema atual?</h4>
                    <p style="margin: 0; color: #64748b; line-height: 1.6;">Sim! Nossa equipe oferece suporte completo na migração. Importamos seus pedidos, clientes e histórico de forma segura e sem perda de dados.</p>
                </div>
                <div class="faq-item animate-on-scroll" style="background: white; border-radius: 12px; padding: 25px 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <h4 style="margin: 0 0 15px; color: #1e293b; font-size: 1.2rem;">O sistema funciona offline?</h4>
                    <p style="margin: 0; color: #64748b; line-height: 1.6;">O sistema principal requer conexão com internet, mas dados críticos são salvos localmente. Assim que a conexão retorna, tudo é sincronizado automaticamente.</p>
                </div>
                <div class="faq-item animate-on-scroll" style="background: white; border-radius: 12px; padding: 25px 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <h4 style="margin: 0 0 15px; color: #1e293b; font-size: 1.2rem;">Como é feita a integração com WhatsApp?</h4>
                    <p style="margin: 0; color: #64748b; line-height: 1.6;">Utilizamos a API oficial do WhatsApp Business. Configuramos tudo para você e em minutos suas mensagens automáticas estarão funcionando.</p>
                </div>
                <div class="faq-item animate-on-scroll" style="background: white; border-radius: 12px; padding: 25px 30px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <h4 style="margin: 0 0 15px; color: #1e293b; font-size: 1.2rem;">Qual o tempo de implementação?</h4>
                    <p style="margin: 0; color: #64748b; line-height: 1.6;">A implementação básica leva de 1 a 3 dias. Com migração de dados e integrações personalizadas, o prazo médio é de 7 dias úteis.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: #1e293b; color: white; padding: 50px 0 30px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
                <div>
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 20px;">
                        <i class="fas fa-chart-line" style="font-size: 32px; color: #10b981;"></i>
                        <span style="font-size: 24px; font-weight: 700;">SisCobranca</span>
                    </div>
                    <p style="color: #94a3b8; line-height: 1.6;">Sistema completo de gestão de pedidos e cobrança. Transforme sua operação com tecnologia de ponta.</p>
                </div>
                <div>
                    <h4 style="margin-bottom: 20px; font-size: 1.1rem;">Links Úteis</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 12px;"><a href="#features" style="color: #94a3b8; text-decoration: none; transition: color 0.3s;">Recursos</a></li>
                        <li style="margin-bottom: 12px;"><a href="#benefits" style="color: #94a3b8; text-decoration: none; transition: color 0.3s;">Benefícios</a></li>
                        <li style="margin-bottom: 12px;"><a href="#pricing" style="color: #94a3b8; text-decoration: none; transition: color 0.3s;">Preços</a></li>
                        <li style="margin-bottom: 12px;"><a href="#faq" style="color: #94a3b8; text-decoration: none; transition: color 0.3s;">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 20px; font-size: 1.1rem;">Contato</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 12px; color: #94a3b8;"><i class="fas fa-envelope" style="margin-right: 10px;"></i> <EMAIL></li>
                        <li style="margin-bottom: 12px; color: #94a3b8;"><i class="fas fa-phone" style="margin-right: 10px;"></i> (11) 9999-9999</li>
                        <li style="margin-bottom: 12px; color: #94a3b8;"><i class="fab fa-whatsapp" style="margin-right: 10px;"></i> (11) 99999-9999</li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 20px; font-size: 1.1rem;">Newsletter</h4>
                    <p style="color: #94a3b8; margin-bottom: 20px;">Receba dicas e novidades sobre gestão de cobrança</p>
                    <form style="display: flex; gap: 10px;">
                        <input type="email" placeholder="Seu email" style="flex: 1; padding: 12px; border: none; border-radius: 8px; background: #334155;">
                        <button type="submit" style="padding: 12px 20px; background: #10b981; border: none; border-radius: 8px; color: white; font-weight: 600; cursor: pointer;">Inscrever</button>
                    </form>
                </div>
            </div>
            <div style="border-top: 1px solid #334155; padding-top: 30px; text-align: center; color: #94a3b8;">
                <p style="margin: 0;">&copy; 2024 SisCobranca. Todos os direitos reservados. | <a href="#" style="color: #94a3b8;">Termos de Uso</a> | <a href="#" style="color: #94a3b8;">Política de Privacidade</a></p>
            </div>
        </div>
    </footer>

    <script src="./landing-assets/chart.js"></script>
    <script src="./landing-assets/config.js"></script>
    <script src="./landing-assets/api-v2.js"></script>
    <script>
        // Override chart initialization to prevent overflow
        window.addEventListener('load', function() {
            const originalInitChart = window.initializeChart;
            window.initializeChart = function() {
                const canvas = document.getElementById('previewChart');
                if (!canvas) return;
                
                // Set fixed dimensions
                const container = canvas.parentElement;
                container.style.height = '150px';
                container.style.position = 'relative';
                container.style.overflow = 'hidden';
                
                // Create chart with constrained options
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                        datasets: [{
                            label: 'Vendas',
                            data: [30, 45, 60, 80, 95, 120],
                            borderColor: '#6366f1',
                            backgroundColor: 'rgba(99, 102, 241, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        onResize: function(chart, size) {
                            // Prevent infinite resize
                            if (size.height > 150) {
                                chart.canvas.style.height = '150px';
                                return false;
                            }
                        }
                    }
                });
            };
        });
    </script>
    <script src="./landing-assets/app.js"></script>
    <script src="./landing-assets/fix-login.js"></script>

</body></html>