// Debug helper for product creation
// Run this in the browser console to test product creation

window.debugProductCreation = async function() {
  console.log('🔍 Starting product creation debug...');
  
  // Test payload that should work
  const testPayload = {
    name: 'Debug Test Product',
    description: 'Testing product creation',
    variations: [
      {
        variation: 'Cápsulas',
        sku: 'TEST-CAP-' + Date.now(),
        price: 99.99
      }
    ]
  };
  
  console.log('📦 Test payload:', testPayload);
  
  try {
    // Get the API URL from environment
    const apiUrl = process.env.REACT_APP_API_URL || 'https://zencash-production.up.railway.app/api/v1';
    console.log('🌐 API URL:', apiUrl);
    
    // Get auth token
    const authTokens = localStorage.getItem('unified_auth_tokens');
    const token = authTokens ? JSON.parse(authTokens).access_token : null;
    console.log('🔑 Auth token:', token ? 'Found' : 'Not found');
    
    // Get tenant ID
    const userInfo = localStorage.getItem('unified_user_info');
    const tenantId = userInfo ? JSON.parse(userInfo).tenantId : null;
    console.log('🏢 Tenant ID:', tenantId || 'Not found');
    
    // Make the request
    const response = await fetch(`${apiUrl}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        'x-tenant-id': tenantId || ''
      },
      body: JSON.stringify(testPayload)
    });
    
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch (e) {
      responseData = responseText;
    }
    
    if (response.ok) {
      console.log('✅ Success! Product created:', responseData);
    } else {
      console.error('❌ Error! Status:', response.status);
      console.error('Response:', responseData);
      
      if (responseData.message) {
        console.error('Validation errors:');
        if (Array.isArray(responseData.message)) {
          responseData.message.forEach((msg, i) => {
            console.error(`  ${i + 1}. ${msg}`);
          });
        } else {
          console.error(`  ${responseData.message}`);
        }
      }
    }
    
    return responseData;
  } catch (error) {
    console.error('❌ Network error:', error);
    return error;
  }
};

console.log('🔧 Debug function loaded! Run debugProductCreation() to test');