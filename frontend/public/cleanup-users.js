// Script to clean up user data and keep only admin user
// Run this in the browser console

(function() {
  console.log('Starting user cleanup...');
  
  // Keys to remove that might contain mock user data
  const keysToRemove = ['default_users', 'users', 'user_passwords'];
  
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key)) {
      console.log(`Removing key: ${key}`);
      localStorage.removeItem(key);
    }
  });
  
  // Keep only the current authenticated user info
  const authInfo = localStorage.getItem('unified_user_info');
  if (authInfo) {
    try {
      const user = JSON.parse(authInfo);
      console.log(`Keeping authenticated user: ${user.email}`);
    } catch (e) {
      console.error('Error parsing auth info:', e);
    }
  }
  
  console.log('User cleanup complete!');
  console.log('Please refresh the page to see changes.');
})();