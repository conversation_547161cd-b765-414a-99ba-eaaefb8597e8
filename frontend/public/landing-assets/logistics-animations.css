/* ===== LOGISTICS ANIMATIONS ===== */

/* Truck Icon Special Effects */
.fa-truck {
    position: relative;
    display: inline-block;
}

/* Animated delivery route */
.logo .fa-truck::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: repeating-linear-gradient(
        90deg,
        var(--zen-green),
        var(--zen-green) 5px,
        transparent 5px,
        transparent 10px
    );
    animation: routeMove 2s linear infinite;
}

@keyframes routeMove {
    0% { background-position: 0 0; }
    100% { background-position: 10px 0; }
}

/* Package bouncing */
.logo .fa-truck::after {
    content: '📦';
    position: absolute;
    top: -8px;
    right: -15px;
    font-size: 0.7rem;
    animation: packageBounce 2s ease-in-out infinite;
}

@keyframes packageBounce {
    0%, 100% { transform: translateY(0) rotate(-5deg); }
    25% { transform: translateY(-3px) rotate(5deg); }
    50% { transform: translateY(-5px) rotate(-5deg); }
    75% { transform: translateY(-3px) rotate(5deg); }
}

/* Dashboard truck animation */
.nav-item .fa-truck {
    transition: transform 0.3s ease;
}

.nav-item:hover .fa-truck {
    animation: drive 0.5s ease;
}

@keyframes drive {
    0% { transform: translateX(0); }
    50% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

/* Delivery status indicators */
.delivery-animation {
    position: relative;
    display: inline-block;
}

.delivery-animation::before {
    content: '🚚';
    position: absolute;
    font-size: 1.5rem;
    animation: deliveryRoute 4s linear infinite;
}

@keyframes deliveryRoute {
    0% { left: 0; transform: scaleX(1); }
    49% { transform: scaleX(1); }
    50% { left: 100%; transform: scaleX(-1); }
    99% { transform: scaleX(-1); }
    100% { left: 0; transform: scaleX(1); }
}

/* Loading truck */
.truck-loader {
    display: inline-block;
    width: 50px;
    height: 30px;
    position: relative;
}

.truck-loader::before {
    content: '🚛';
    position: absolute;
    font-size: 2rem;
    animation: loadingTruck 1.5s ease-in-out infinite;
}

@keyframes loadingTruck {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(-2deg); }
    75% { transform: translateY(-3px) rotate(2deg); }
}

/* Tracking dots animation */
.tracking-dots {
    display: inline-flex;
    gap: 10px;
    align-items: center;
}

.tracking-dots::before,
.tracking-dots::after,
.tracking-dots span::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--zen-green);
    animation: trackingPulse 1.5s ease-in-out infinite;
}

.tracking-dots::after {
    animation-delay: 0.3s;
}

.tracking-dots span::before {
    animation-delay: 0.6s;
}

@keyframes trackingPulse {
    0%, 100% { 
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% { 
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Express delivery badge */
.express-badge {
    position: relative;
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--gradient-fire);
    color: white;
    border-radius: 50px;
    font-weight: bold;
    overflow: hidden;
}

.express-badge::before {
    content: '⚡';
    position: absolute;
    left: -20px;
    animation: lightning 2s linear infinite;
}

@keyframes lightning {
    0% { left: -20px; }
    100% { left: calc(100% + 20px); }
}

/* Rotating globe for worldwide delivery */
.globe-delivery {
    display: inline-block;
    font-size: 2rem;
    animation: globeRotate 10s linear infinite;
}

@keyframes globeRotate {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

/* Success delivery checkmark */
.delivery-success {
    position: relative;
    display: inline-block;
}

.delivery-success::after {
    content: '✓';
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: var(--zen-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    animation: checkPop 0.5s ease;
}

@keyframes checkPop {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}