// ===== CONSTANTS =====
const API_URL = '/api';

// ===== PAGE INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeAnimations();
    initializeChart();
    observeScrollAnimations();
    setupEventListeners();
    
    // Check if user is logged in
    if (api && api.isAuthenticated()) {
        // If on landing page and logged in, show dashboard link
        updateNavForLoggedInUser();
    }
});

function updateNavForLoggedInUser() {
    const loginBtn = document.querySelector('.btn-login');
    if (loginBtn) {
        loginBtn.textContent = 'Dashboard';
        loginBtn.onclick = () => window.location.href = 'dashboard.html';
    }
}

// ===== ANIMATIONS =====
function initializeAnimations() {
    // Add stagger effect to feature cards
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Parallax effect on scroll
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-image');
        if (parallax) {
            parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
}

// ===== CHART INITIALIZATION =====
function initializeChart() {
    const canvas = document.getElementById('previewChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
            datasets: [{
                label: 'Vendas',
                data: [30, 45, 60, 80, 95, 120],
                borderColor: '#6366f1',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// ===== SCROLL ANIMATIONS =====
function observeScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });
    
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// ===== EVENT LISTENERS =====
function setupEventListeners() {
    // Mobile menu
    const mobileMenu = document.querySelector('.mobile-menu');
    if (mobileMenu) {
        mobileMenu.addEventListener('click', toggleMobileMenu);
    }
    
    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// ===== REDIRECT FUNCTIONS =====
function showLogin() {
    window.location.href = '/login';
}

function showDemo() {
    window.location.href = '/login';
}

function watchVideo() {
    // Placeholder for video demo
    alert('Video demo em breve!');
}

// ===== DASHBOARD REDIRECT =====
function redirectToDashboard() {
    window.location.href = 'dashboard.html';
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // Add styles dynamically
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            z-index: 9999;
            animation: slideLeft 0.3s ease-out;
        }
        
        .notification.success { border-left: 4px solid #10b981; }
        .notification.error { border-left: 4px solid #ef4444; }
        .notification.info { border-left: 4px solid #3b82f6; }
        
        .notification i {
            font-size: 1.25rem;
        }
        
        .notification.success i { color: #10b981; }
        .notification.error i { color: #ef4444; }
        .notification.info i { color: #3b82f6; }
    `;
    document.head.appendChild(style);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideRight 0.3s ease-out';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function toggleMobileMenu() {
    // Implementation for mobile menu toggle
    const navLinks = document.querySelector('.nav-links');
    if (navLinks) {
        navLinks.classList.toggle('mobile-active');
    }
}

function watchVideo() {
    // Open video modal or redirect to video
    showNotification('Vídeo demo em breve!', 'info');
}

// ===== API FUNCTIONS =====
async function apiCall(endpoint, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        }
    };
    
    const response = await fetch(`${API_URL}${endpoint}`, {
        ...defaultOptions,
        ...options
    });
    
    if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
}

// ===== EASTER EGG =====
let konamiCode = [];
const konamiPattern = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'b', 'a'];

document.addEventListener('keydown', (e) => {
    konamiCode.push(e.key);
    konamiCode = konamiCode.slice(-10);
    
    if (konamiCode.join(',') === konamiPattern.join(',')) {
        document.body.classList.add('gradient-animated');
        showNotification('🎉 Modo especial ativado!', 'success');
    }
});

// ===== WINDOW CLICK HANDLER =====
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}async function showDemo() {
    // Redirect to login page with demo parameter
    showNotification('Redirecting to registration...', 'info');
    
    // Show registration modal instead of auto-login
    document.getElementById('loginModal').style.display = 'none';
    document.getElementById('registerModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // Pre-fill demo email
    const emailField = document.getElementById('registerEmail');
    if (emailField) {
        emailField.value = '<EMAIL>';
    }
}
