/* ===== VARIABLES & RESET ===== */
:root {
    --primary: #10b981;
    --primary-dark: #059669;
    --primary-light: #34d399;
    --secondary: #6366f1;
    --danger: #ef4444;
    --warning: #f59e0b;
    --dark: #1e293b;
    --gray: #64748b;
    --light: #f8fafc;
    --white: #ffffff;
    
    --gradient-1: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-2: linear-gradient(135deg, #10b981 0%, #065f46 100%);
    --gradient-3: linear-gradient(135deg, #34d399 0%, #10b981 100%);
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    --radius: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--dark);
    line-height: 1.6;
    overflow-x: hidden;
    background: var(--light);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--dark);
}

.gradient-text {
    background: var(--gradient-2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== NAVBAR ===== */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary);
}

.logo i {
    font-size: 2rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--gray);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary);
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary, .btn-login, .btn-demo {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-2);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: white;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-secondary:hover {
    background: var(--primary);
    color: white;
}

.btn-login {
    background: transparent;
    color: var(--dark);
    border: 2px solid var(--gray);
}

.btn-login:hover {
    border-color: var(--primary);
    color: var(--primary);
}

.btn-demo {
    background: var(--gradient-2);
    color: white;
    box-shadow: var(--shadow);
}

.btn-demo:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary.large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.full-width {
    width: 100%;
    justify-content: center;
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 4rem 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    min-height: calc(100vh - 80px);
}

.hero-text {
    z-index: 2;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.trust-badges {
    display: flex;
    gap: 2rem;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray);
    font-size: 0.875rem;
}

.trust-item i {
    color: var(--primary);
}

/* ===== DASHBOARD PREVIEW ===== */
.hero-image {
    position: relative;
    z-index: 1;
}

.dashboard-preview {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-10deg);
}

.preview-header {
    background: var(--dark);
    padding: 1rem;
    display: flex;
    align-items: center;
}

.preview-dots {
    display: flex;
    gap: 0.5rem;
}

.preview-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ef4444;
}

.preview-dots span:nth-child(2) {
    background: #f59e0b;
}

.preview-dots span:nth-child(3) {
    background: #10b981;
}

.preview-content {
    padding: 2rem;
}

.stat-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--light);
    padding: 1.5rem;
    border-radius: var(--radius);
    text-align: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-2);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.stat-icon i {
    color: white;
    font-size: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: var(--dark);
}

.stat-label {
    color: var(--gray);
    font-size: 0.875rem;
}

.stat-change {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.stat-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--secondary);
}

.preview-chart {
    height: 200px;
    position: relative;
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.float-1, .float-2, .float-3 {
    position: absolute;
    width: 60px;
    height: 60px;
    background: white;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xl);
}

.float-1 {
    top: -20px;
    right: -20px;
    background: var(--gradient-2);
}

.float-2 {
    bottom: 50px;
    left: -30px;
    background: var(--gradient-3);
}

.float-3 {
    top: 50%;
    right: -40px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.float-1 i, .float-2 i, .float-3 i {
    color: white;
    font-size: 1.5rem;
}

/* ===== WAVE DIVIDER ===== */
.wave-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.wave-divider svg {
    position: relative;
    display: block;
    width: calc(100% + 1.3px);
    height: 120px;
}

.wave-divider path {
    fill: var(--white);
}

/* ===== FEATURES SECTION ===== */
.features-section {
    padding: 5rem 0;
    background: white;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-block;
    background: var(--gradient-2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--light);
    padding: 2rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: var(--gradient-2);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--gray);
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray);
    margin-bottom: 0.5rem;
}

.feature-list i {
    color: var(--secondary);
}

/* ===== BENEFITS SECTION ===== */
.benefits-section {
    padding: 5rem 0;
    background: var(--light);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.benefit-item {
    text-align: center;
}

.benefit-number {
    display: inline-block;
    font-size: 4rem;
    font-weight: 900;
    background: var(--gradient-2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.benefit-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.benefit-item p {
    color: var(--gray);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: 5rem 0;
    background: var(--gradient-2);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 3rem;
    color: white;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.cta-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.8;
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    width: 90%;
    max-width: 500px;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    position: relative;
}

.close {
    position: absolute;
    right: 2rem;
    top: 2rem;
    font-size: 2rem;
    font-weight: bold;
    color: var(--gray);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--dark);
}

.login-container {
    padding: 3rem;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header i {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.login-header h2 {
    margin-bottom: 0.5rem;
}

.login-header p {
    color: var(--gray);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: var(--radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary);
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    color: var(--gray);
}

.login-footer a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
}

/* ===== MOBILE MENU ===== */
.mobile-menu {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .mobile-menu {
        display: block;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-image {
        display: none;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
    
    .trust-badges {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
}