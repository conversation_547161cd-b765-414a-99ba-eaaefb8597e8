// API Configuration
const API_CONFIG = {
  // Switch between 'demo' and 'real' modes
  mode: 'real', // Change to 'demo' to use demo server
  
  // Demo server configuration
  demo: {
    baseURL: 'http://localhost:3001/api',
    timeout: 5000
  },
  
  // Real backend configuration
  real: {
    baseURL: '/api/api',
    timeout: 10000
  }
};

// Export the active configuration
const getApiConfig = () => {
  const config = API_CONFIG[API_CONFIG.mode];
  if (!config) {
    console.error(`Invalid API mode: ${API_CONFIG.mode}`);
    return API_CONFIG.demo; // Fallback to demo
  }
  return config;
};

// Make it available globally
window.API_CONFIG = getApiConfig();