/* ===== KEYFRAMES ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeft {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideRight {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes floatReverse {
    0%, 100% {
        transform: translateY(-20px);
    }
    50% {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.5);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes shimmer {
    from {
        background-position: -200% 0;
    }
    to {
        background-position: 200% 0;
    }
}

/* ===== ANIMATION CLASSES ===== */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-delay {
    opacity: 0;
    animation: fadeIn 0.6s ease-out 0.3s forwards;
}

.animate-fade-in-delay-2 {
    opacity: 0;
    animation: fadeIn 0.6s ease-out 0.6s forwards;
}

.animate-fade-in-delay-3 {
    opacity: 0;
    animation: fadeIn 0.6s ease-out 0.9s forwards;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.6s ease-out;
}

.animate-slide-left {
    animation: slideLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideRight 0.6s ease-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-float-slow {
    animation: float 5s ease-in-out infinite;
}

.animate-float-reverse {
    animation: floatReverse 4s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
    animation: bounce 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 10s linear infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* ===== SCROLL ANIMATIONS ===== */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* ===== HOVER ANIMATIONS ===== */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* ===== LOADING ANIMATIONS ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

.skeleton-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* ===== GRADIENT ANIMATIONS ===== */
.gradient-animated {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 10s ease infinite;
}

/* ===== INTERACTION FEEDBACK ===== */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* ===== PARALLAX EFFECTS ===== */
.parallax-container {
    position: relative;
    overflow: hidden;
}

.parallax-element {
    will-change: transform;
    transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* ===== MORPH ANIMATIONS ===== */
.morph {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
    0% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    50% {
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    100% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
}

/* ===== SPECIAL EFFECTS ===== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-glow {
    text-shadow: 
        0 0 10px rgba(99, 102, 241, 0.8),
        0 0 20px rgba(99, 102, 241, 0.6),
        0 0 30px rgba(99, 102, 241, 0.4);
}

.holographic {
    background: linear-gradient(45deg, #ff0080, #ff8c00, #40e0d0, #ff0080);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}