// API Client v2 - Fixed version with proper config loading
// This version properly waits for config to be loaded

// Function to get the current configuration
function getApiConfig() {
  // If window.API_CONFIG exists, use it
  if (window.API_CONFIG && window.API_CONFIG.baseURL) {
    return {
      baseURL: window.API_CONFIG.baseURL,
      timeout: window.API_CONFIG.timeout || 10000
    };
  }
  
  // Otherwise, use default configuration
  return {
    baseURL: '/api/api',
    timeout: 10000
  };
}

// Token and user management
let authToken = localStorage.getItem('authToken');
let currentUser = localStorage.getItem('currentUser') ? JSON.parse(localStorage.getItem('currentUser')) : null;
let currentTenant = localStorage.getItem('currentTenant') ? JSON.parse(localStorage.getItem('currentTenant')) : null;

// API Response adapter to handle differences between demo and real backend
class ApiResponseAdapter {
  static adaptLoginResponse(data, email) {
    const config = getApiConfig();
    const isDemoMode = config.baseURL.includes('3000');
    
    if (isDemoMode || data.access_token) {
      return {
        token: data.access_token,
        user: data.user,
        tenant: null
      };
    }
    
    // Real backend response adaptation
    return {
      token: data.accessToken || data.access_token,
      user: {
        id: data.user?.id || data.userId,
        email: data.user?.email || email,
        firstName: data.user?.firstName || data.firstName || email.split('@')[0],
        lastName: data.user?.lastName || data.lastName || '',
        name: data.user?.name || `${data.firstName || ''} ${data.lastName || ''}`.trim(),
        role: data.user?.role || data.role || 'operator',
        tenantId: data.user?.tenantId || data.tenantId
      },
      tenant: data.tenant || null
    };
  }
  
  static adaptOrderResponse(order) {
    const config = getApiConfig();
    const isDemoMode = config.baseURL.includes('3001');
    
    if (isDemoMode) {
      return order;
    }
    
    // Real backend may have different field names
    return {
      ...order,
      orderNumber: order.orderNumber || order.number,
      totalValue: order.totalValue || (order.productValue + order.shippingCost),
      customer: order.customer || order.customerId,
      createdAt: order.createdAt || order.created_at,
      updatedAt: order.updatedAt || order.updated_at
    };
  }
  
  static adaptOrdersListResponse(data) {
    const config = getApiConfig();
    const isDemoMode = config.baseURL.includes('3001');
    
    if (isDemoMode) {
      return Array.isArray(data) ? data : data.orders || [];
    }
    
    // Real backend with pagination
    if (data.data) {
      return data.data.map(order => ApiResponseAdapter.adaptOrderResponse(order));
    }
    
    return Array.isArray(data) ? data.map(order => ApiResponseAdapter.adaptOrderResponse(order)) : [];
  }
}

// Enhanced API client
const api = {
  // Set headers with auth token and tenant
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };
    
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    // Add tenant header for real backend
    const config = getApiConfig();
    const isDemoMode = config.baseURL.includes('3001');
    if (!isDemoMode && currentTenant) {
      headers['X-Tenant-Id'] = currentTenant.id;
    }
    
    return headers;
  },
  
  // Enhanced fetch with timeout and error handling
  async fetchWithTimeout(url, options = {}) {
    const config = getApiConfig();
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), config.timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeout);
      return response;
    } catch (error) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  },

  // Auth methods
  async login(email, password) {
    try {
      const config = getApiConfig();
      console.log('Login attempt with config:', config);
      
      const response = await this.fetchWithTimeout(`${config.baseURL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || 'Login failed');
      }
      
      const data = await response.json();
      const adapted = ApiResponseAdapter.adaptLoginResponse(data, email);
      
      // Store auth data
      authToken = adapted.token;
      currentUser = adapted.user;
      if (adapted.tenant) {
        currentTenant = adapted.tenant;
        localStorage.setItem('currentTenant', JSON.stringify(currentTenant));
      }
      
      localStorage.setItem('authToken', authToken);
      localStorage.setItem('currentUser', JSON.stringify(currentUser));
      
      return {
        access_token: authToken,
        user: currentUser,
        tenant: currentTenant
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  async register(userData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Registration failed');
    }
    
    const data = await response.json();
    const adapted = ApiResponseAdapter.adaptLoginResponse(data, userData.email);
    
    authToken = adapted.token;
    currentUser = adapted.user;
    if (adapted.tenant) {
      currentTenant = adapted.tenant;
      localStorage.setItem('currentTenant', JSON.stringify(currentTenant));
    }
    
    localStorage.setItem('authToken', authToken);
    localStorage.setItem('currentUser', JSON.stringify(currentUser));
    
    return data;
  },

  logout() {
    authToken = null;
    currentUser = null;
    currentTenant = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('currentTenant');
    window.location.href = '/';
  },

  // Check if user is authenticated
  isAuthenticated() {
    return !!authToken;
  },

  // Get current user
  getCurrentUser() {
    return currentUser;
  },

  // Orders methods
  async getOrders(params = {}) {
    const config = getApiConfig();
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.status) queryParams.append('status', params.status);
    if (params.search) queryParams.append('search', params.search);
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    
    // Role-based parameters
    if (params.createdById) queryParams.append('createdById', params.createdById);
    if (params.myOrdersOnly) queryParams.append('myOrdersOnly', 'true');
    if (params.assignedToId) queryParams.append('assignedToId', params.assignedToId);
    
    const url = `${config.baseURL}/orders${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    const response = await this.fetchWithTimeout(url, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch orders');
    }
    
    const data = await response.json();
    return ApiResponseAdapter.adaptOrdersListResponse(data);
  },

  async getOrderById(orderId) {
    const config = getApiConfig();
    
    const response = await this.fetchWithTimeout(`${config.baseURL}/orders/${orderId}`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error('Failed to fetch order');
    }
    
    const data = await response.json();
    return ApiResponseAdapter.adaptOrderResponse(data);
  },

  async createOrder(orderData) {
    const config = getApiConfig();
    const isDemoMode = config.baseURL.includes('3001');
    
    // Adapt order data for real backend
    const payload = isDemoMode ? orderData : {
      customerId: orderData.customerId,
      productValue: Number(orderData.productValue),
      shippingCost: Number(orderData.shippingCost),
      shippingAddress: typeof orderData.shippingAddress === 'string' 
        ? {
            street: orderData.shippingAddress.split(',')[0] || '',
            number: 'S/N',
            neighborhood: 'Centro',
            city: 'São Paulo',
            state: 'SP',
            zipCode: '00000-000'
          }
        : orderData.shippingAddress,
      notes: orderData.notes || '',
      metadata: {
        saleCode: orderData.saleCode,
        externalId: orderData.externalId,
        createdById: orderData.createdById,
        assignedToId: orderData.assignedToId
      }
    };
    
    const response = await this.fetchWithTimeout(`${config.baseURL}/orders`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(payload),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to create order');
    }
    
    const data = await response.json();
    return ApiResponseAdapter.adaptOrderResponse(data);
  },

  async getOrder(id) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/orders/${id}`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch order');
    }
    
    const data = await response.json();
    return ApiResponseAdapter.adaptOrderResponse(data);
  },

  async updateOrder(id, updates) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/orders/${id}`, {
      method: 'PATCH',
      headers: this.getHeaders(),
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to update order');
    }
    
    const data = await response.json();
    return ApiResponseAdapter.adaptOrderResponse(data);
  },

  // Product Management
  async getProducts() {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch products');
    }
    
    return await response.json();
  },

  async getProduct(id) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${id}`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch product');
    }
    
    return await response.json();
  },

  async createProduct(productData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(productData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to create product');
    }
    
    return await response.json();
  },

  async updateProduct(id, productData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${id}`, {
      method: 'PATCH',
      headers: this.getHeaders(),
      body: JSON.stringify(productData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to update product');
    }
    
    return await response.json();
  },

  async deleteProduct(id) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to delete product');
    }
  },

  // Kit Management
  async createKit(productId, kitData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${productId}/kits`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(kitData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to create kit');
    }
    
    return await response.json();
  },

  async updateKit(productId, kitId, kitData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${productId}/kits/${kitId}`, {
      method: 'PATCH',
      headers: this.getHeaders(),
      body: JSON.stringify(kitData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to update kit');
    }
    
    return await response.json();
  },

  async deleteKit(productId, kitId) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/products/${productId}/kits/${kitId}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to delete kit');
    }
  },

  // User Management
  async getUsers() {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch users');
    }
    
    return await response.json();
  },

  async createUser(userData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(userData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to create user');
    }
    
    return await response.json();
  },

  async updateUser(id, userData) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users/${id}`, {
      method: 'PATCH',
      headers: this.getHeaders(),
      body: JSON.stringify(userData),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to update user');
    }
    
    return await response.json();
  },

  async deleteUser(id) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users/${id}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to delete user');
    }
  },

  async resetUserPassword(userId) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users/${userId}/reset-password`, {
      method: 'POST',
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || 'Failed to reset password');
    }
    
    return await response.json();
  },

  // Performance methods
  async getSellerPerformance(userId) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users/${userId}/performance/seller`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch seller performance');
    }
    
    return await response.json();
  },

  async getOperatorPerformance(userId) {
    const config = getApiConfig();
    const response = await this.fetchWithTimeout(`${config.baseURL}/users/${userId}/performance/operator`, {
      headers: this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch operator performance');
    }
    
    return await response.json();
  },

};

// Export for use in other scripts
window.api = api;

// Log for debugging
console.log('api-v2.js loaded successfully');
console.log('API methods available:', Object.keys(api).join(', '));