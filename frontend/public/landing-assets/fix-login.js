// Fix login and demo button behavior for landing page
document.addEventListener('DOMContentLoaded', function() {
    // Fix login button
    const loginButton = document.querySelector('.btn-login');
    if (loginButton) {
        loginButton.onclick = function() {
            window.location.href = '/login';
        };
    }
    
    // Fix all demo buttons
    const demoButtons = document.querySelectorAll('[onclick*="showDemo"]');
    demoButtons.forEach(button => {
        button.onclick = function() {
            window.location.href = '/login';
        };
    });
    
    // Fix watch video buttons
    const videoButtons = document.querySelectorAll('[onclick*="watchVideo"]');
    videoButtons.forEach(button => {
        button.onclick = function() {
            alert('Vídeo demo em breve!');
        };
    });
});