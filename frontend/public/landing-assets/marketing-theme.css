/* ===== ZENCASH MARKETING THEME ===== */

:root {
    /* Brand Colors - Verde para marca */
    --zen-green: #10b981;
    --zen-green-dark: #059669;
    --zen-green-light: #34d399;
    
    /* Marketing Colors - Vibrantes */
    --marketing-purple: #8b5cf6;
    --marketing-pink: #ec4899;
    --marketing-blue: #3b82f6;
    --marketing-orange: #f97316;
    --marketing-yellow: #eab308;
    --marketing-cyan: #06b6d4;
    
    /* Gradientes Marketing Impactantes */
    --gradient-fire: linear-gradient(135deg, #f97316 0%, #ec4899 100%);
    --gradient-ocean: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
    --gradient-sunset: linear-gradient(135deg, #f59e0b 0%, #dc2626 100%);
    --gradient-aurora: linear-gradient(135deg, #8b5cf6 0%, #ec4899 50%, #3b82f6 100%);
    --gradient-neon: linear-gradient(135deg, #10b981 0%, #06b6d4 50%, #8b5cf6 100%);
    --gradient-rainbow: linear-gradient(135deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6);
}

/* ===== LOGO STYLING ===== */
.logo {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    color: var(--zen-green);
    font-size: 2rem;
    position: relative;
    z-index: 1;
}

.logo::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 45px;
    height: 45px;
    background: var(--gradient-neon);
    border-radius: 12px;
    opacity: 0.2;
    animation: pulse 2s ease-in-out infinite;
}

.logo span {
    font-weight: 900;
    background: linear-gradient(135deg, var(--zen-green) 0%, var(--zen-green-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== HERO SECTION MARKETING ===== */
.hero-section {
    background: linear-gradient(135deg, #f0fdf4 0%, #e0f2fe 50%, #ede9fe 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
    animation: rotate 30s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== DASHBOARD COLORS ===== */
.stat-card.gradient-1 { 
    background: var(--gradient-fire);
    box-shadow: 0 10px 30px rgba(249, 115, 22, 0.3);
}

.stat-card.gradient-2 { 
    background: var(--gradient-ocean);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.stat-card.gradient-3 { 
    background: var(--gradient-sunset);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
}

.stat-card.gradient-4 { 
    background: var(--gradient-aurora);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

/* ===== ANIMATED STATS ===== */
.stat-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
}

.stat-card:hover::before {
    animation: shine 0.5s ease;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
}

/* ===== FEATURE CARDS COLORFUL ===== */
.feature-card:nth-child(1) .feature-icon { background: var(--gradient-fire); }
.feature-card:nth-child(2) .feature-icon { background: var(--gradient-ocean); }
.feature-card:nth-child(3) .feature-icon { background: var(--gradient-sunset); }
.feature-card:nth-child(4) .feature-icon { background: var(--gradient-aurora); }
.feature-card:nth-child(5) .feature-icon { background: var(--gradient-neon); }
.feature-card:nth-child(6) .feature-icon { background: var(--gradient-rainbow); }

.feature-card:hover {
    border-color: transparent;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* ===== BUTTONS MARKETING STYLE ===== */
.btn-primary {
    background: var(--gradient-neon);
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
}

.btn-demo {
    background: var(--gradient-aurora);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
    animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
    0%, 100% { box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4); }
    50% { box-shadow: 0 8px 30px rgba(139, 92, 246, 0.6); }
}

/* ===== DASHBOARD PREVIEW ===== */
.dashboard-preview {
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(-5deg) scale(1.02);
}

/* ===== FLOATING ELEMENTS COLORFUL ===== */
.float-1 {
    background: var(--gradient-fire);
    animation: float 3s ease-in-out infinite, colorShift 5s ease-in-out infinite;
}

.float-2 {
    background: var(--gradient-ocean);
    animation: floatReverse 4s ease-in-out infinite, colorShift 5s ease-in-out infinite 1s;
}

.float-3 {
    background: var(--gradient-aurora);
    animation: float 5s ease-in-out infinite, colorShift 5s ease-in-out infinite 2s;
}

.float-4 {
    background: var(--gradient-neon);
    animation: floatReverse 3.5s ease-in-out infinite, colorShift 5s ease-in-out infinite 3s;
    bottom: 20%;
    left: 10%;
}

@keyframes colorShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(180deg); }
}

/* ===== CHARTS COLORFUL ===== */
canvas {
    filter: saturate(1.5) contrast(1.1);
}

/* ===== TRUST BADGES ===== */
.trust-badges {
    position: relative;
}

.trust-item {
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.trust-item i {
    color: var(--zen-green);
}

/* ===== CTA SECTION MARKETING ===== */
.cta-section {
    background: var(--gradient-aurora);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,144C960,149,1056,139,1152,122.7C1248,107,1344,85,1392,74.7L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
    background-size: cover;
}

/* ===== BENEFITS COLORFUL ===== */
.benefit-number {
    background: var(--gradient-rainbow);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 5rem;
    animation: pulse 2s ease-in-out infinite;
}

/* ===== NOTIFICATION COLORS ===== */
.notification.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
}

.notification.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
}

.notification.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
}

/* ===== SIDEBAR COLORFUL ===== */
.nav-item:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1));
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
}

.nav-item.active::before {
    background: var(--gradient-neon);
}

/* ===== MODAL MARKETING ===== */
.modal-content {
    border-top: 4px solid transparent;
    border-image: var(--gradient-aurora) 1;
}

.login-header i {
    color: var(--zen-green);
    font-size: 4rem;
    position: relative;
}

.login-header i::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-neon);
    border-radius: 2px;
}

/* ===== SPECIAL EFFECTS ===== */
.gradient-text {
    background: var(--gradient-aurora);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(30deg); }
}

/* ===== TRUCK ICON SPECIAL ===== */
.fa-truck {
    position: relative;
}

.logo .fa-truck::after {
    content: '📦';
    position: absolute;
    top: -5px;
    right: -10px;
    font-size: 0.8rem;
    animation: bounce 2s ease-in-out infinite;
}