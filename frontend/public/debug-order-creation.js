// Debug Script for Order Creation
// Copy and paste this into the browser console

(function() {
  console.clear();
  console.log('%c🔍 Order Creation Debug Script Started', 'color: #4CAF50; font-size: 16px; font-weight: bold');
  console.log('=====================================');

  // Store original fetch to intercept requests
  const originalFetch = window.fetch;
  let lastOrderRequest = null;
  let lastOrderError = null;

  // Override fetch to capture order creation requests
  window.fetch = function(...args) {
    const [url, options] = args;
    
    // Check if this is an order creation request
    if (url.includes('/orders') && options?.method === 'POST') {
      console.log('%c📤 Order Creation Request Detected', 'color: #2196F3; font-weight: bold');
      console.log('URL:', url);
      console.log('Headers:', options.headers);
      
      try {
        const payload = JSON.parse(options.body);
        lastOrderRequest = payload;
        
        console.log('%c📦 Payload Analysis:', 'color: #FF9800; font-weight: bold');
        console.log('Raw Payload:', payload);
        
        // Validate required fields
        const requiredFields = ['customerName', 'customerPhone', 'customerCPF', 'items'];
        const missingFields = requiredFields.filter(field => !payload[field]);
        
        if (missingFields.length > 0) {
          console.error('❌ Missing required fields:', missingFields);
        } else {
          console.log('✅ All required fields present');
        }
        
        // Validate items
        if (payload.items && payload.items.length > 0) {
          console.log('\n%c🛒 Items Analysis:', 'color: #9C27B0; font-weight: bold');
          payload.items.forEach((item, index) => {
            console.log(`Item ${index + 1}:`, item);
            
            const itemRequiredFields = ['productId', 'productName', 'quantity', 'unitPrice'];
            const itemMissingFields = itemRequiredFields.filter(field => !item[field]);
            
            if (itemMissingFields.length > 0) {
              console.error(`  ❌ Item ${index + 1} missing fields:`, itemMissingFields);
            } else {
              console.log(`  ✅ Item ${index + 1} valid`);
            }
            
            // Check field types
            if (item.productId && typeof item.productId !== 'string') {
              console.error(`  ❌ Item ${index + 1} productId must be a string, got:`, typeof item.productId);
            }
            if (item.productName && typeof item.productName !== 'string') {
              console.error(`  ❌ Item ${index + 1} productName must be a string, got:`, typeof item.productName);
            }
            if (item.quantity && typeof item.quantity !== 'number') {
              console.error(`  ❌ Item ${index + 1} quantity must be a number, got:`, typeof item.quantity);
            }
            if (item.unitPrice && typeof item.unitPrice !== 'number') {
              console.error(`  ❌ Item ${index + 1} unitPrice must be a number, got:`, typeof item.unitPrice);
            }
          });
        } else {
          console.error('❌ No items in payload');
        }
        
        // Check for retry mechanism fields
        if (payload.clientReference) {
          console.log('📌 Client Reference:', payload.clientReference);
        }
        if (payload.suggestedOrderNumber) {
          console.log('🔢 Suggested Order Number:', payload.suggestedOrderNumber);
        }
        
        // Check for unexpected fields
        const knownFields = ['customerName', 'customerPhone', 'customerCPF', 'items', 'collectorId', 'observation', 'address', 'customerId', 'zapId', 'clientReference', 'suggestedOrderNumber'];
        const unexpectedFields = Object.keys(payload).filter(field => !knownFields.includes(field));
        
        if (unexpectedFields.length > 0) {
          console.warn('⚠️  Unexpected fields in payload:', unexpectedFields);
        }
        
      } catch (e) {
        console.error('Failed to parse request body:', e);
      }
    }
    
    // Call original fetch and intercept response
    return originalFetch.apply(this, args).then(async response => {
      if (url.includes('/orders') && options?.method === 'POST') {
        const responseClone = response.clone();
        
        try {
          const responseData = await responseClone.json();
          
          if (!response.ok) {
            lastOrderError = responseData;
            console.error('%c❌ Order Creation Failed', 'color: #F44336; font-weight: bold');
            console.error('Status:', response.status);
            console.error('Response:', responseData);
            
            // Check for different error message locations
            const errorMessage = responseData.message || responseData.error || responseData.detail || 'Unknown error';
            console.error('Error message:', errorMessage);
            
            // Check for database constraint errors
            if (errorMessage.includes('Unique constraint failed')) {
              console.error('%c⚠️  Database Constraint Error', 'color: #FF9800; font-weight: bold');
              console.error('This usually means the order number already exists in the database.');
              console.error('The backend needs to fix the order number generation logic.');
            }
            
            // Parse validation errors if present
            if (responseData.message && responseData.message.includes('property')) {
              console.log('\n%c📋 Validation Errors:', 'color: #F44336; font-weight: bold');
              const errors = responseData.message.split(',');
              errors.forEach(error => {
                console.error('  •', error.trim());
              });
            }
          } else {
            console.log('%c✅ Order Created Successfully', 'color: #4CAF50; font-weight: bold');
            console.log('Response:', responseData);
          }
        } catch (e) {
          console.error('Failed to parse response:', e);
        }
      }
      
      return response;
    }).catch(error => {
      if (url.includes('/orders') && options?.method === 'POST') {
        lastOrderError = error;
        console.error('%c❌ Network Error', 'color: #F44336; font-weight: bold');
        console.error('Error:', error);
      }
      throw error;
    });
  };

  // Debug functions
  window.debugOrder = {
    // Get last request payload
    getLastRequest: () => {
      if (lastOrderRequest) {
        console.log('Last Order Request:', lastOrderRequest);
        return lastOrderRequest;
      } else {
        console.log('No order request captured yet. Try creating an order.');
        return null;
      }
    },
    
    // Get last error
    getLastError: () => {
      if (lastOrderError) {
        console.log('Last Order Error:', lastOrderError);
        return lastOrderError;
      } else {
        console.log('No order error captured yet.');
        return null;
      }
    },
    
    // Test with minimal payload
    testMinimalOrder: async () => {
      console.log('\n%c🧪 Testing Minimal Order Creation', 'color: #00BCD4; font-weight: bold');
      
      const testPayload = {
        customerName: "Test Customer",
        customerPhone: "11999999999",
        customerCPF: "12345678901",
        items: [{
          productId: "test-product-id",
          productName: "Test Product",
          quantity: 1,
          unitPrice: 100
        }]
      };
      
      console.log('Test Payload:', testPayload);
      
      try {
        const response = await fetch('/api/v1/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${JSON.parse(localStorage.getItem('unified_auth_tokens')).access_token}`,
            'x-tenant-id': JSON.parse(localStorage.getItem('unified_user_info')).tenantId
          },
          body: JSON.stringify(testPayload)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
          console.error('Test failed:', result);
        } else {
          console.log('Test successful:', result);
        }
        
        return result;
      } catch (error) {
        console.error('Test error:', error);
        return error;
      }
    },
    
    // Test with product from database
    testWithRealProduct: async () => {
      console.log('\n%c🧪 Testing with Real Product', 'color: #00BCD4; font-weight: bold');
      
      try {
        // Get products first
        const productsResponse = await fetch('/api/v1/products', {
          headers: {
            'Authorization': `Bearer ${JSON.parse(localStorage.getItem('unified_auth_tokens')).access_token}`,
            'x-tenant-id': JSON.parse(localStorage.getItem('unified_user_info')).tenantId
          }
        });
        
        const products = await productsResponse.json();
        console.log('Available products:', products);
        
        if (products.length === 0) {
          console.error('No products available');
          return;
        }
        
        // Get kits for first product
        const kitsResponse = await fetch(`/api/v1/products/${products[0].id}/kits`, {
          headers: {
            'Authorization': `Bearer ${JSON.parse(localStorage.getItem('unified_auth_tokens')).access_token}`,
            'x-tenant-id': JSON.parse(localStorage.getItem('unified_user_info')).tenantId
          }
        });
        
        const kits = await kitsResponse.json();
        console.log('Available kits:', kits);
        
        if (kits.length === 0) {
          console.error('No kits available');
          return;
        }
        
        const testPayload = {
          customerName: "Test Customer",
          customerPhone: "11999999999",
          customerCPF: "12345678901",
          items: [{
            productId: kits[0].id,
            productName: `${products[0].name} - ${kits[0].name}`,
            quantity: 1,
            unitPrice: kits[0].price
          }]
        };
        
        console.log('Test Payload with Real Product:', testPayload);
        
        const response = await fetch('/api/v1/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${JSON.parse(localStorage.getItem('unified_auth_tokens')).access_token}`,
            'x-tenant-id': JSON.parse(localStorage.getItem('unified_user_info')).tenantId
          },
          body: JSON.stringify(testPayload)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
          console.error('Test failed:', result);
        } else {
          console.log('Test successful:', result);
        }
        
        return result;
      } catch (error) {
        console.error('Test error:', error);
        return error;
      }
    },
    
    // Show all debug info
    showAll: () => {
      console.log('\n%c📊 Complete Debug Information', 'color: #9E9E9E; font-weight: bold');
      console.log('=====================================');
      console.log('Last Request:', lastOrderRequest);
      console.log('Last Error:', lastOrderError);
      console.log('Current User:', JSON.parse(localStorage.getItem('unified_user_info')));
      const authTokens = localStorage.getItem('unified_auth_tokens');
      let token = null;
      if (authTokens) {
        try {
          const tokens = JSON.parse(authTokens);
          token = tokens.access_token;
        } catch (e) {
          console.error('Failed to parse auth tokens');
        }
      }
      console.log('Auth Token:', token ? 'Present' : 'Missing');
    },
    
    // Reset debug data
    reset: () => {
      lastOrderRequest = null;
      lastOrderError = null;
      console.log('Debug data reset');
    }
  };

  console.log('\n%c📚 Available Debug Commands:', 'color: #673AB7; font-weight: bold');
  console.log('debugOrder.getLastRequest()    - Show last order request payload');
  console.log('debugOrder.getLastError()      - Show last order error');
  console.log('debugOrder.testMinimalOrder()  - Test with minimal payload');
  console.log('debugOrder.testWithRealProduct() - Test with real product from DB');
  console.log('debugOrder.showAll()           - Show all debug information');
  console.log('debugOrder.reset()             - Reset debug data');
  console.log('\n%c💡 Now try creating an order and watch the console for detailed analysis', 'color: #FF5722; font-weight: bold');
})();