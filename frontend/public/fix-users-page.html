<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .danger {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .info {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button.danger {
            background-color: #dc3545;
            border-left: none;
            padding: 10px 15px;
        }
        button.success {
            background-color: #28a745;
            border-left: none;
            padding: 10px 15px;
        }
        button:hover {
            opacity: 0.9;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #17a2b8;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Corrigir Página de Usuários</h1>
        
        <div class="warning">
            <h2>⚠️ ATENÇÃO</h2>
            <p>Esta ferramenta irá corrigir a página de usuários (/users) para garantir que ela esteja usando a fonte de dados correta.</p>
            <p>Use esta ferramenta se a página de usuários não estiver mostrando os dados corretos após limpar os usuários.</p>
        </div>
        
        <div class="info">
            <h3>O que esta ferramenta faz:</h3>
            <ul>
                <li>Força a página de usuários a usar a fonte de dados correta</li>
                <li>Limpa o cache da página de usuários</li>
                <li>Sincroniza todos os dados de usuários</li>
            </ul>
        </div>
        
        <div class="actions">
            <button id="btn-fix" class="success">Corrigir Página de Usuários</button>
            <button id="btn-go-back" onclick="window.history.back()">Voltar</button>
            <button id="btn-go-home" onclick="window.location.href='/'">Ir para Home</button>
        </div>
    </div>
    
    <div id="log-container" class="log-container">
        <div class="log-entry info">Aguardando ação...</div>
    </div>
    
    <script>
        // Elementos da UI
        const btnFix = document.getElementById('btn-fix');
        const btnGoBack = document.getElementById('btn-go-back');
        const btnGoHome = document.getElementById('btn-go-home');
        const logContainer = document.getElementById('log-container');
        
        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Função para corrigir a página de usuários
        function fixUsersPage() {
            try {
                addLog('Iniciando correção da página de usuários...', 'info');
                
                // 1. Verificar se há usuários no localStorage
                const usersStr = localStorage.getItem('users');
                const users = usersStr ? JSON.parse(usersStr) : [];
                
                addLog(`Encontrados ${users.length} usuários no localStorage.`, 'info');
                
                // 2. Verificar se há o admin
                const adminUser = users.find(user => user.email === '<EMAIL>');
                
                if (!adminUser) {
                    addLog('Usuário admin não encontrado. Criando...', 'warning');
                    
                    // Criar usuário admin
                    const newAdminUser = {
                        id: 1,
                        nome: "Admin",
                        email: "<EMAIL>",
                        papeis: ["admin"],
                        permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                        ativo: true
                    };
                    
                    // Adicionar admin aos usuários
                    users.push(newAdminUser);
                    
                    // Salvar usuários
                    localStorage.setItem('users', JSON.stringify(users));
                    
                    addLog('Usuário admin criado com sucesso!', 'success');
                }
                
                // 3. Sincronizar usuários com todas as fontes
                addLog('Sincronizando usuários com todas as fontes...', 'info');
                
                // 3.1. Sincronizar com default_users
                const defaultUsers = {};
                users.forEach(user => {
                    // Mapear papel para role
                    let role = 'user';
                    if (user.papeis.includes('admin')) role = 'admin';
                    else if (user.papeis.includes('supervisor')) role = 'supervisor';
                    else if (user.papeis.includes('collector') || user.papeis.includes('operador')) role = 'collector';
                    else if (user.papeis.includes('seller') || user.papeis.includes('vendedor')) role = 'seller';
                    
                    // Obter senha existente ou usar padrão
                    const passwordsStr = localStorage.getItem('user_passwords');
                    const passwords = passwordsStr ? JSON.parse(passwordsStr) : {};
                    const password = passwords[user.email] || 'senha123';
                    
                    defaultUsers[user.email] = {
                        id: user.id,
                        email: user.email,
                        fullName: user.nome,
                        role: role,
                        password: password
                    };
                });
                
                localStorage.setItem('default_users', JSON.stringify(defaultUsers));
                
                // 3.2. Sincronizar com mockUsers
                const mockUsers = users.map(user => {
                    // Determinar perfil
                    let perfil = 'Usuário';
                    if (user.papeis.includes('admin')) perfil = 'Administrador';
                    else if (user.papeis.includes('supervisor')) perfil = 'Supervisor';
                    else if (user.papeis.includes('collector') || user.papeis.includes('operador')) perfil = 'Operador';
                    else if (user.papeis.includes('seller') || user.papeis.includes('vendedor')) perfil = 'Vendedor';
                    
                    return {
                        id: user.id,
                        nome: user.nome,
                        email: user.email,
                        perfil: perfil,
                        permissoes: user.permissoes,
                        ativo: user.ativo
                    };
                });
                
                localStorage.setItem('mockUsers', JSON.stringify(mockUsers));
                
                // 3.3. Sincronizar senhas
                const passwordsStr = localStorage.getItem('user_passwords');
                const passwords = passwordsStr ? JSON.parse(passwordsStr) : {};
                
                // Verificar senhas nos default_users e adicioná-las se não existirem
                users.forEach(user => {
                    // Se não existe senha para este usuário, mas existe no default_users
                    if (!passwords[user.email] && defaultUsers[user.email]?.password) {
                        passwords[user.email] = defaultUsers[user.email].password;
                    }
                });
                
                localStorage.setItem('user_passwords', JSON.stringify(passwords));
                
                addLog('Sincronização concluída!', 'success');
                
                // 4. Disparar eventos para notificar a aplicação
                addLog('Disparando eventos para notificar a aplicação...', 'info');
                
                window.dispatchEvent(new CustomEvent('user-data-reset', { detail: { users: users } }));
                window.dispatchEvent(new CustomEvent('user-list-reset'));
                
                addLog('Eventos disparados com sucesso!', 'success');
                
                // 5. Mostrar resultado
                addLog('Correção concluída! A página de usuários agora deve mostrar os dados corretos.', 'success');
                addLog('Recarregue a página de usuários para ver as alterações.', 'info');
                
                // 6. Forçar recarga da página após 3 segundos
                addLog('Esta página será recarregada em 3 segundos...', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
                
            } catch (error) {
                addLog(`Erro durante a correção: ${error.message}`, 'error');
                console.error('Erro ao corrigir página de usuários:', error);
            }
        }
        
        // Adicionar event listeners
        btnFix.addEventListener('click', fixUsersPage);
        
        // Mostrar mensagem inicial
        addLog('Ferramenta de correção da página de usuários carregada.', 'info');
        addLog('Esta ferramenta permite corrigir a página de usuários para garantir que ela esteja usando a fonte de dados correta.', 'info');
        addLog('Use o botão "Corrigir Página de Usuários" para iniciar o processo.', 'info');
    </script>
</body>
</html>
