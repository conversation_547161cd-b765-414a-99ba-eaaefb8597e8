<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redefinição Direta de Usuários</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .danger {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .info {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button.danger {
            background-color: #dc3545;
            border-left: none;
            padding: 10px 15px;
        }
        button.success {
            background-color: #28a745;
            border-left: none;
            padding: 10px 15px;
        }
        button:hover {
            opacity: 0.9;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #17a2b8;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .user-list {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .user-list table {
            width: 100%;
            border-collapse: collapse;
        }
        .user-list th, .user-list td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .user-list th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .user-list tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .user-list tr:hover {
            background-color: #f5f5f5;
        }
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Redefinição Direta de Usuários</h1>
        
        <div class="danger">
            <h2>⚠️ ATENÇÃO: OPERAÇÃO CRÍTICA</h2>
            <p>Esta ferramenta irá <strong>REMOVER TODOS</strong> os usuários do sistema, exceto o usuário Admin.</p>
            <p>Esta ação é <strong>IRREVERSÍVEL</strong> e afetará todas as áreas do sistema.</p>
        </div>
        
        <div class="info">
            <h3>O que esta ferramenta faz:</h3>
            <ul>
                <li>Remove <strong>DIRETAMENTE</strong> todos os usuários de <strong>TODAS</strong> as áreas de armazenamento</li>
                <li>Mantém apenas o usuário Admin com todas as permissões</li>
                <li>Limpa completamente todas as áreas de armazenamento de usuários</li>
                <li>Força a atualização de todas as páginas que exibem usuários</li>
            </ul>
        </div>
        
        <div class="actions">
            <button id="btn-reset-users" class="danger">Redefinir Todos os Usuários (Manter Admin)</button>
            <button id="btn-show-users" class="success">Mostrar Usuários Atuais</button>
            <button id="btn-go-back" onclick="window.history.back()">Voltar</button>
            <button id="btn-go-home" onclick="window.location.href='/'">Ir para Home</button>
        </div>
    </div>
    
    <div id="log-container" class="log-container">
        <div class="log-entry info">Aguardando ação...</div>
    </div>
    
    <div id="user-container" class="container hidden">
        <h2>Usuários no Sistema</h2>
        <div id="user-list" class="user-list">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>Email</th>
                        <th>Papéis</th>
                        <th>Status</th>
                        <th>Armazenamento</th>
                    </tr>
                </thead>
                <tbody id="user-table-body">
                    <!-- Usuários serão inseridos aqui -->
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // Elementos da UI
        const btnResetUsers = document.getElementById('btn-reset-users');
        const btnShowUsers = document.getElementById('btn-show-users');
        const btnGoBack = document.getElementById('btn-go-back');
        const btnGoHome = document.getElementById('btn-go-home');
        const logContainer = document.getElementById('log-container');
        const userContainer = document.getElementById('user-container');
        const userTableBody = document.getElementById('user-table-body');
        
        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Função para mostrar usuários
        function showUsers() {
            try {
                addLog('Coletando informações de usuários de todas as fontes...', 'info');
                userContainer.classList.remove('hidden');
                
                // Limpar tabela
                userTableBody.innerHTML = '';
                
                // Obter usuários de todas as fontes
                const sources = {
                    users: localStorage.getItem('users') ? JSON.parse(localStorage.getItem('users')) : [],
                    defaultUsers: localStorage.getItem('default_users') ? JSON.parse(localStorage.getItem('default_users')) : {},
                    mockUsers: localStorage.getItem('mockUsers') ? JSON.parse(localStorage.getItem('mockUsers')) : []
                };
                
                // Mapa para rastrear todos os emails únicos
                const emailMap = new Map();
                
                // Adicionar usuários do contexto principal
                sources.users.forEach(user => {
                    if (user.email) {
                        const lowerEmail = user.email.toLowerCase();
                        emailMap.set(lowerEmail, {
                            ...user,
                            source: 'users'
                        });
                    }
                });
                
                // Adicionar usuários do sistema de emergência
                Object.entries(sources.defaultUsers).forEach(([email, userData]) => {
                    const lowerEmail = email.toLowerCase();
                    if (!emailMap.has(lowerEmail)) {
                        // Converter papel para papéis
                        const papeis = [];
                        if (userData.role === 'admin') papeis.push('admin');
                        else if (userData.role === 'supervisor') papeis.push('supervisor');
                        else if (userData.role === 'collector') papeis.push('collector');
                        else if (userData.role === 'seller') papeis.push('seller');
                        
                        emailMap.set(lowerEmail, {
                            id: userData.id || Date.now(),
                            nome: userData.fullName || email.split('@')[0],
                            email: email,
                            papeis: papeis,
                            permissoes: [],
                            ativo: true,
                            source: 'default_users'
                        });
                    }
                });
                
                // Adicionar mock users
                sources.mockUsers.forEach(user => {
                    if (user.email) {
                        const lowerEmail = user.email.toLowerCase();
                        if (!emailMap.has(lowerEmail)) {
                            // Converter perfil para papéis
                            const papeis = [];
                            if (user.perfil === 'Administrador') papeis.push('admin');
                            else if (user.perfil === 'Supervisor') papeis.push('supervisor');
                            else if (user.perfil === 'Operador') papeis.push('collector');
                            else if (user.perfil === 'Vendedor') papeis.push('seller');
                            
                            emailMap.set(lowerEmail, {
                                id: user.id || Date.now(),
                                nome: user.nome || user.email.split('@')[0],
                                email: user.email,
                                papeis: papeis,
                                permissoes: [],
                                ativo: user.ativo !== undefined ? user.ativo : true,
                                source: 'mockUsers'
                            });
                        }
                    }
                });
                
                // Converter para array
                const allUsers = Array.from(emailMap.values());
                
                if (allUsers.length === 0) {
                    addLog('Nenhum usuário encontrado!', 'warning');
                    userTableBody.innerHTML = '<tr><td colspan="6" style="text-align: center;">Nenhum usuário encontrado</td></tr>';
                    return;
                }
                
                // Adicionar usuários à tabela
                allUsers.forEach(user => {
                    const row = document.createElement('tr');
                    
                    // ID
                    const idCell = document.createElement('td');
                    idCell.textContent = user.id;
                    row.appendChild(idCell);
                    
                    // Nome
                    const nameCell = document.createElement('td');
                    nameCell.textContent = user.nome;
                    row.appendChild(nameCell);
                    
                    // Email
                    const emailCell = document.createElement('td');
                    emailCell.textContent = user.email;
                    row.appendChild(emailCell);
                    
                    // Papéis
                    const rolesCell = document.createElement('td');
                    rolesCell.textContent = user.papeis ? user.papeis.join(', ') : '';
                    row.appendChild(rolesCell);
                    
                    // Status
                    const statusCell = document.createElement('td');
                    statusCell.textContent = user.ativo ? 'Ativo' : 'Inativo';
                    statusCell.style.color = user.ativo ? '#28a745' : '#dc3545';
                    row.appendChild(statusCell);
                    
                    // Armazenamento
                    const sourceCell = document.createElement('td');
                    sourceCell.textContent = user.source || 'Desconhecido';
                    row.appendChild(sourceCell);
                    
                    userTableBody.appendChild(row);
                });
                
                addLog(`${allUsers.length} usuários encontrados.`, 'success');
                
            } catch (error) {
                addLog(`Erro ao mostrar usuários: ${error.message}`, 'error');
                console.error('Erro ao mostrar usuários:', error);
            }
        }
        
        // Função para redefinir todos os usuários
        function resetAllUsers() {
            try {
                // Confirmar antes de redefinir
                if (!confirm('ATENÇÃO: Esta ação irá REMOVER TODOS os usuários do sistema, exceto o usuário Admin. Esta ação NÃO pode ser desfeita. Deseja continuar?')) {
                    addLog('Operação cancelada pelo usuário.', 'info');
                    return;
                }
                
                // Pedir confirmação adicional
                if (!confirm('Confirme novamente: Todos os usuários serão REMOVIDOS. Apenas o Admin permanecerá. Tem certeza absoluta?')) {
                    addLog('Operação cancelada na confirmação secundária.', 'info');
                    return;
                }
                
                addLog('Iniciando redefinição direta de usuários...', 'info');
                
                // 1. Criar usuário admin
                const adminUser = {
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    papeis: ["admin"],
                    permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                    ativo: true
                };
                
                // 2. Limpar e definir usuários no contexto principal
                addLog('Limpando usuários do contexto principal...', 'info');
                localStorage.setItem('users', JSON.stringify([adminUser]));
                
                // 3. Limpar e definir usuários no sistema de emergência
                addLog('Limpando usuários do sistema de emergência...', 'info');
                const defaultUsers = {
                    '<EMAIL>': {
                        id: 1,
                        email: '<EMAIL>',
                        fullName: 'Admin',
                        role: 'admin',
                        password: 'admin123'
                    }
                };
                localStorage.setItem('default_users', JSON.stringify(defaultUsers));
                
                // 4. Limpar e definir mock users
                addLog('Limpando mock users...', 'info');
                localStorage.setItem('mockUsers', JSON.stringify([{
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    perfil: "Administrador",
                    ativo: true
                }]));
                
                // 5. Limpar e definir senhas
                addLog('Limpando senhas de usuários...', 'info');
                const adminPasswords = {
                    '<EMAIL>': 'admin123'
                };
                localStorage.setItem('user_passwords', JSON.stringify(adminPasswords));
                
                // 6. Verificar e limpar todos os armazenamentos de usuários
                addLog('Verificando todos os armazenamentos de usuários...', 'info');
                
                // Lista de chaves específicas de usuários que verificamos explicitamente
                const checkedUserKeys = ['users', 'default_users', 'user_passwords', 'mockUsers', 'userInfo'];
                
                // Remover quaisquer outros armazenamentos de usuários
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('user') ||
                        key.includes('User') ||
                        key.includes('usuario') ||
                        key.includes('Usuario') ||
                        key.includes('perfil') ||
                        key.includes('Perfil') ||
                        key.includes('role') ||
                        key.includes('Role') ||
                        key.includes('admin') ||
                        key.includes('Admin')
                    )) {
                        // Verificar se não é uma das chaves que já tratamos explicitamente
                        if (!checkedUserKeys.includes(key)) {
                            // Limpar ou definir para um array vazio
                            localStorage.setItem(key, JSON.stringify([]));
                            addLog(`Limpando armazenamento adicional de usuários: ${key}`, 'info');
                        }
                    }
                }
                
                // 7. Disparar eventos para notificar a aplicação
                addLog('Disparando eventos para notificar a aplicação...', 'info');
                
                // Criar e disparar eventos
                window.dispatchEvent(new CustomEvent('user-data-reset', { detail: { users: [adminUser] } }));
                window.dispatchEvent(new CustomEvent('user-list-reset'));
                
                // 8. Mostrar resultado
                addLog('Redefinição direta concluída! Todos os usuários foram removidos, exceto o Admin.', 'success');
                
                // 9. Mostrar usuários atualizados
                showUsers();
                
                // 10. Forçar recarga da página após 3 segundos
                addLog('A página será recarregada em 3 segundos para aplicar todas as alterações...', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
                
            } catch (error) {
                addLog(`Erro durante a redefinição: ${error.message}`, 'error');
                console.error('Erro ao redefinir usuários:', error);
            }
        }
        
        // Adicionar event listeners
        btnResetUsers.addEventListener('click', resetAllUsers);
        btnShowUsers.addEventListener('click', showUsers);
        
        // Mostrar mensagem inicial
        addLog('Ferramenta de redefinição direta de usuários carregada.', 'info');
        addLog('Esta ferramenta permite redefinir todos os usuários do sistema, mantendo apenas o Admin.', 'info');
        addLog('Use o botão "Mostrar Usuários Atuais" para ver os usuários existentes.', 'info');
        addLog('Use o botão "Redefinir Todos os Usuários" para remover todos os usuários exceto o Admin.', 'warning');
    </script>
</body>
</html>
