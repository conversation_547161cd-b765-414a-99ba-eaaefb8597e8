<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Users - Sistema de Cobrança</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1976d2;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 10px;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        button {
            background-color: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #d32f2f;
        }
        button.success {
            background-color: #4caf50;
            display: none;
        }
        button.success:hover {
            background-color: #388e3c;
        }
        .log-container {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #1976d2;
        }
        .log-entry.success {
            color: #4caf50;
            display: block;
        }
        .log-entry.error {
            color: #f44336;
            display: block;
        }
    </style>
</head>
<body>
    <h1>Reset Users - Sistema de Cobrança</h1>

    <div class="card">
        <h2>Remover Todos os Usuários (Exceto Admin)</h2>
        <div class="warning">
            <strong>Atenção!</strong> Esta ferramenta irá remover TODOS os usuários do sistema, exceto o usuário administrador.
            Esta ação não pode ser desfeita.
        </div>

        <div id="success-message" class="success">
            <strong>Sucesso!</strong> Todos os usuários foram removidos. Apenas o administrador permanece no sistema.
        </div>

        <div id="error-message" class="error">
            <strong>Erro!</strong> <span id="error-details"></span>
        </div>

        <div style="margin-bottom: 15px;">
            <button id="reset-button" onclick="resetUsers()">Remover Todos os Usuários (Exceto Admin)</button>
            <button class="success" onclick="goToUsers()">Verificar Usuários</button>
        </div>

        <div class="log-container" id="log-container">
            <div class="log-entry info">Aguardando ação...</div>
        </div>
    </div>

    <script>
        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Função para resetar usuários
        function resetUsers() {
            // Confirmar duas vezes devido à gravidade da operação
            if (!confirm('ATENÇÃO: Todos os usuários serão removidos, exceto o administrador. Esta ação não pode ser desfeita. Deseja continuar?')) {
                addLog('Operação cancelada pelo usuário.', 'info');
                return;
            }

            if (!confirm('Confirme novamente: TODOS OS USUÁRIOS SERÃO REMOVIDOS. Apenas o administrador permanecerá. Tem certeza absoluta?')) {
                addLog('Operação cancelada na confirmação secundária.', 'info');
                return;
            }

            addLog('Iniciando remoção de usuários...', 'info');

            try {
                // 1. Definir o usuário administrador
                const adminUser = {
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    papeis: ["admin"],
                    permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                    ativo: true
                };

                // 2. Atualizar todos os armazenamentos de usuários para conter apenas o admin
                addLog('Definindo apenas o administrador em todos os armazenamentos...', 'info');

                // Para o contexto principal - APENAS ADMIN
                localStorage.setItem('users', JSON.stringify([adminUser]));

                // Para o sistema de emergência - APENAS ADMIN
                const defaultUsers = {
                    '<EMAIL>': {
                        id: 1,
                        email: '<EMAIL>',
                        fullName: 'Admin',
                        role: 'admin',
                        password: 'admin123'
                    }
                };
                localStorage.setItem('default_users', JSON.stringify(defaultUsers));

                // Para senhas de usuários - APENAS ADMIN
                const adminPasswords = {
                    '<EMAIL>': 'admin123'
                };
                localStorage.setItem('user_passwords', JSON.stringify(adminPasswords));

                // Para mock users - APENAS ADMIN
                localStorage.setItem('mockUsers', JSON.stringify([{
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    perfil: "Administrador",
                    ativo: true
                }]));

                // 3. Disparar eventos para notificar a aplicação React
                addLog('Notificando a aplicação sobre as alterações...', 'info');
                window.dispatchEvent(new CustomEvent('user-list-reset'));
                window.dispatchEvent(new Event('local-storage')); // Para hooks useLocalStorage
                window.dispatchEvent(new Event('storage')); // Para sincronização entre abas

                // 4. Executar script de forçar recarga
                const syncScript = document.createElement('script');
                syncScript.textContent = `
                    // Forçar sincronização correta de usuários
                    try {
                        // Garantir que apenas o admin exista em todos os contextos
                        const adminUser = {
                            id: 1,
                            nome: "Admin",
                            email: "<EMAIL>",
                            papeis: ["admin"],
                            permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                            ativo: true
                        };
                        
                        localStorage.setItem('users', JSON.stringify([adminUser]));
                        
                        console.log('Usuários redefinidos para apenas admin');
                        setTimeout(() => {
                            // Atualizar a página principal em 2 segundos
                            if (window.opener) {
                                window.opener.location.reload();
                            }
                        }, 2000);
                    } catch (err) {
                        console.error('Erro ao forçar sincronização de usuários:', err);
                    }
                `;
                document.body.appendChild(syncScript);
                setTimeout(() => {
                    document.body.removeChild(syncScript);
                }, 1000);

                // 5. Mostrar mensagem de sucesso
                document.getElementById('success-message').style.display = 'block';
                document.getElementById('error-message').style.display = 'none';
                addLog('Todos os usuários foram removidos com sucesso! Apenas o administrador permanece no sistema.', 'success');

                // Habilitar botão de verificação
                document.querySelector('button.success').style.display = 'inline-block';

            } catch (error) {
                document.getElementById('success-message').style.display = 'none';
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-details').textContent = error.message;
                addLog(`Erro inesperado: ${error.message}`, 'error');
                console.error('Erro ao resetar usuários:', error);
            }
        }

        // Função para ir para a página de usuários
        function goToUsers() {
            window.location.href = '/users';
        }
    </script>
</body>
</html> 