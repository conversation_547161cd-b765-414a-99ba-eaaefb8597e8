const fs = require('fs');
const content = fs.readFileSync('src/components/OrdersTable.tsx', 'utf-8');
const lines = content.split('\n');

// Start from component and track context
let braceDepth = 0;
let parenDepth = 0;
const componentStart = 106; // line 107 in 1-based

console.log('Checking for brace/paren mismatches...');

for (let i = componentStart; i < 690; i++) {
  const line = lines[i];
  let inString = false;
  let stringChar = null;
  
  // Track all brackets
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    const next = j < line.length - 1 ? line[j + 1] : '';
    const prev = j > 0 ? line[j - 1] : '';
    
    // Handle strings
    if (!inString && (char === '"' || char === "'" || char === '`')) {
      inString = true;
      stringChar = char;
      continue;
    }
    if (inString && char === stringChar && prev !== '\\') {
      inString = false;
      stringChar = null;
      continue;
    }
    
    if (inString) continue;
    
    // Skip comments
    if (char === '/' && next === '/') break; // rest of line is comment
    
    // Count brackets
    if (char === '(') parenDepth++;
    if (char === ')') parenDepth--;
    if (char === '{') braceDepth++;
    if (char === '}') braceDepth--;
    
    // Check for issues
    if (parenDepth < 0) {
      console.log(`ERROR: Extra closing paren at line ${i + 1}, position ${j}`);
      console.log(`Line: ${line}`);
      process.exit(1);
    }
    if (braceDepth < 0) {
      console.log(`ERROR: Extra closing brace at line ${i + 1}, position ${j}`);
      console.log(`Line: ${line}`);
      console.log(`Context: lines ${i-2} to ${i+2}`);
      for (let k = Math.max(0, i-2); k <= Math.min(i+2, lines.length-1); k++) {
        console.log(`  ${k+1}: ${lines[k]}`);
      }
      process.exit(1);
    }
  }
  
  if (i === 450) {
    console.log(`Line 451: braceDepth=${braceDepth}, parenDepth=${parenDepth}`);
  }
}

console.log(`At line 690: braceDepth=${braceDepth}, parenDepth=${parenDepth}`);