// Simple Express server for SPA with proper routing
const express = require('express');
const path = require('path');
const app = express();

const PORT = process.env.PORT || 3000;
const BUILD_DIR = path.join(__dirname, 'build');

// Serve static files
app.use(express.static(BUILD_DIR, {
  maxAge: '1y',
  etag: false,
  lastModified: false,
  setHeaders: (res, filepath) => {
    if (filepath.endsWith('index.html')) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    }
  }
}));

// SPA fallback - Always return index.html for any route
app.get('*', (req, res) => {
  res.sendFile(path.join(BUILD_DIR, 'index.html'));
});

app.listen(PORT, () => {
  console.log(`SPA server running on port ${PORT}`);
});