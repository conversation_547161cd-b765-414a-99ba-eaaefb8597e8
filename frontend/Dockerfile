# Stage 1: Build the React app
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the app
RUN npm run build

# Stage 2: Serve the static files
FROM node:20-alpine

WORKDIR /app

# Copy the build folder and server.js from the builder stage
COPY --from=builder /app/build ./build
COPY --from=builder /app/server.js ./server.js

# The PORT will be provided by Railway
EXPOSE 3000

# Start with Express server for reliable SPA routing
CMD ["node", "server.js"]