#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting optimized build process..."

# Set environment variables
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP=false
export SKIP_PREFLIGHT_CHECK=true
export TSC_COMPILE_ON_ERROR=true
export ESLINT_NO_DEV_ERRORS=true

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf build

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm ci
fi

# Build with increased memory
echo "🔨 Building with 8GB memory allocation..."
npm run build

echo "✅ Build completed successfully!"
echo "📁 Build output is in the 'build' directory"