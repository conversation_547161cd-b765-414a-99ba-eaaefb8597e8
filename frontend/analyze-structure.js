const fs = require('fs');
const content = fs.readFileSync('src/components/OrdersTable.tsx', 'utf-8');
const lines = content.split('\n');

let braceStack = [];
let componentLevel = -1;
let inString = false;
let stringChar = null;

console.log('=== Analyzing OrdersTable.tsx structure ===\n');

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  const lineNum = i + 1;
  
  // Track component start
  if (line.includes('const OrdersTable: React.FC')) {
    componentLevel = braceStack.length;
    console.log(`Line ${lineNum}: Component OrdersTable starts (level ${componentLevel})`);
  }
  
  // Simple string tracking (not perfect but good enough)
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    const prevChar = j > 0 ? line[j-1] : null;
    
    if (!inString && (char === '"' || char === "'" || char === '`')) {
      inString = true;
      stringChar = char;
    } else if (inString && char === stringChar && prevChar !== '\\') {
      inString = false;
      stringChar = null;
    }
    
    if (!inString) {
      if (char === '{') {
        braceStack.push({ line: lineNum, pos: j, context: line.trim().substring(0, 50) });
      } else if (char === '}') {
        const opening = braceStack.pop();
        
        // Check if this closes the component
        if (componentLevel !== -1 && braceStack.length === componentLevel) {
          console.log(`Line ${lineNum}: Component OrdersTable closes`);
          console.log(`  Opening was at line ${opening?.line}`);
          console.log(`  Current line: ${line.trim()}`);
          componentLevel = -1;
          
          // Check what comes after
          if (i < lines.length - 1) {
            console.log(`\nLines after component close:`);
            for (let k = i + 1; k < Math.min(i + 10, lines.length); k++) {
              if (lines[k].trim()) {
                console.log(`  Line ${k + 1}: ${lines[k].trim().substring(0, 60)}...`);
              }
            }
          }
        }
      }
    }
  }
  
  // Report issues at specific lines
  if (lineNum === 451) {
    console.log(`\nLine 451 analysis:`);
    console.log(`  Content: ${line.trim()}`);
    console.log(`  Brace stack depth: ${braceStack.length}`);
    console.log(`  Component level: ${componentLevel}`);
  }
  
  if (lineNum === 683) {
    console.log(`\nLine 683 analysis:`);
    console.log(`  Content: ${line.trim()}`);
    console.log(`  Brace stack depth: ${braceStack.length}`);
    console.log(`  Inside component: ${componentLevel !== -1}`);
    break;
  }
}

if (componentLevel !== -1) {
  console.log('\nWARNING: Component never closed properly!');
}