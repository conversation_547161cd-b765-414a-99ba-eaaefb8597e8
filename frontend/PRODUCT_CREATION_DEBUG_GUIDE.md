# Product Creation Debug Guide

## Summary of the 400 Error Issue

The 400 error occurs when the frontend sends extra fields that are not part of the backend's `CreateProductDto`. The backend strictly validates the incoming payload and rejects any unexpected fields.

### Expected Backend Structure

```typescript
CreateProductDto {
  name: string (required)
  description?: string (optional)
  price?: number (optional)
  imageUrl?: string (optional)
  active?: boolean (optional, defaults to true)
  variations: [
    {
      variation: string (required)
      price: number (required)
      sku: string (required)
    }
  ] (at least one required)
}
```

### Common Invalid Fields Being Sent

The following fields should NOT be sent in the product creation payload:
- `tenantId` - This is sent via the `x-tenant-id` header, not in the body
- `kits` - Kits are created separately after the product exists
- `type` - Not a product field
- `costPrice` - Not part of the product model
- `createdAt`/`updatedAt` - Managed by the backend
- `id` - Generated by the backend
- `_count` - Computed field

For variations, these fields should NOT be included:
- `type` - Frontend only field
- `customName` - Frontend only, should be converted to `variation`
- `costPrice` - Not in the backend DTO
- `active` - Not in the creation DTO
- `productId` - Set by backend
- `id` - Generated by backend
- `createdAt`/`updatedAt` - Managed by backend
- `inventory` - Created separately

## Debug Tools Available

### 1. Browser Console Logging

The `ProductService` has extensive logging that shows:
- The payload being sent to the backend
- The response or error received
- Validation error details

To see these logs:
1. Open Chrome DevTools (F12)
2. Go to the Console tab
3. Try to create a product
4. Look for logs with these prefixes:
   - `🔄` - Transformation logs
   - `📤` - Request being sent
   - `❌` - Error details
   - `🔍` - Validation errors

### 2. Debug Functions in Console

After loading the products page, you can run these functions in the browser console:

```javascript
// Test product creation with a minimal valid payload
debugProductCreation()

// Test the transform function with various inputs
testTransform()
```

### 3. Debug Page (Development Only)

Navigate to `/produtos/debug` or click the "Debug Mode" button on the products page to access a comprehensive debug interface that:
- Tests various payload scenarios
- Shows exactly what's being sent vs. what's expected
- Displays validation errors clearly
- Provides copy-to-clipboard functionality for payloads

### 4. Manual API Test

To test the API directly, you can use this curl command:

```bash
curl -X POST http://localhost:3000/api/v1/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "x-tenant-id: YOUR_TENANT_ID" \
  -d '{
    "name": "Test Product",
    "description": "Test description",
    "variations": [
      {
        "variation": "Cápsulas",
        "sku": "TEST-001",
        "price": 50
      }
    ]
  }'
```

## How to Fix the Issue

1. **Check the transformer functions** in `/frontend/src/utils/productTransformers.ts`:
   - `transformProductForBackend` - Should strip all extra fields
   - `transformVariationForBackend` - Should only return `variation`, `sku`, and `price`

2. **Verify the service is using the transformer**:
   - Check that `ProductService.createProduct` calls `transformProductForBackend`

3. **Ensure the dialog is not adding extra fields**:
   - `ProductCreationV2Dialog` should only pass the required fields to `onSave`

4. **Check for any middleware or interceptors** that might be adding fields

## Next Steps

1. Open the browser console and try creating a product
2. Look for the `📤 Request Body being sent to backend:` log
3. Check if it contains any of the invalid fields listed above
4. If yes, the transformer needs to be fixed
5. If no, check the backend logs for more details about the validation error

The debug page at `/produtos/debug` will run automated tests to help identify exactly which fields are causing the issue.