const fs = require('fs');
const content = fs.readFileSync('src/components/OrdersTable.tsx', 'utf-8');
const lines = content.split('\n');

console.log('=== Detailed Structure Analysis ===\n');

// Track all function definitions
const functions = [];
let currentFunction = null;
let braceCount = 0;

for (let i = 0; i < Math.min(lines.length, 700); i++) {
  const line = lines[i];
  const lineNum = i + 1;
  
  // Track function definitions
  const funcMatch = line.match(/const\s+(\w+)\s*=\s*(async\s*)?\(/);
  if (funcMatch) {
    currentFunction = {
      name: funcMatch[1],
      startLine: lineNum,
      braceCountAtStart: braceCount
    };
  }
  
  // Count braces
  for (const char of line) {
    if (char === '{') braceCount++;
    if (char === '}') {
      braceCount--;
      
      // Check if function ended
      if (currentFunction && braceCount === currentFunction.braceCountAtStart) {
        currentFunction.endLine = lineNum;
        functions.push(currentFunction);
        currentFunction = null;
      }
    }
  }
  
  // Special lines to report
  if (lineNum === 107) {
    console.log(`Line 107 (Component start): brace count = ${braceCount}`);
  }
  if (lineNum === 451) {
    console.log(`Line 451: brace count = ${braceCount}`);
    console.log(`  Content: ${line.trim()}`);
  }
  if (lineNum === 681) {
    console.log(`Line 681: brace count = ${braceCount}`);
    console.log(`  Content: ${line.trim()}`);
  }
}

console.log('\nFunctions found:');
functions.forEach(f => {
  console.log(`  ${f.name}: lines ${f.startLine}-${f.endLine}`);
});

// Find where sortedOrders is defined
let sortedOrdersLine = -1;
for (let i = 0; i < lines.length; i++) {
  if (lines[i].includes('const sortedOrders =')) {
    sortedOrdersLine = i + 1;
    break;
  }
}

console.log(`\nsortedOrders defined at line: ${sortedOrdersLine}`);

// Check the actual component boundaries more carefully
let componentBraceCount = 0;
let inComponent = false;
for (let i = 106; i < lines.length; i++) {
  const line = lines[i];
  
  if (i === 106) {
    inComponent = true;
  }
  
  if (inComponent) {
    // Count braces but handle arrow function syntax
    let j = 0;
    while (j < line.length) {
      if (line[j] === '=' && j + 1 < line.length && line[j + 1] === '>') {
        j += 2; // Skip =>
        continue;
      }
      
      if (line[j] === '{') componentBraceCount++;
      if (line[j] === '}') {
        componentBraceCount--;
        if (componentBraceCount === 0) {
          console.log(`\nComponent appears to end at line ${i + 1}`);
          console.log(`But this might be incorrect due to arrow function syntax`);
          break;
        }
      }
      j++;
    }
  }
  
  if (componentBraceCount === 0 && inComponent) break;
}