# Kit Architecture Note

## Current Mismatch

The frontend and backend have different approaches to kits:

### Backend Architecture
- Kits are **independent entities** (separate `Kit` table)
- Kits contain `KitItem` entries that reference `ProductVariation` records
- Kits are NOT children of products
- API endpoint: `POST /api/v1/kits`

### Frontend Architecture
- UI shows kits as part of a product detail page
- Expects kits to be associated with a specific product
- Tri<PERSON> to call `/api/v1/products/:id/kits` (which doesn't exist)

## Current Fix

The service now:
1. Creates kits at the correct endpoint `/api/v1/kits`
2. Transforms the data to match backend expectations:
   - `variationId` → `productVariationId`
   - `isActive` → `active`

## Future Improvements

To properly align the architectures, consider:

1. **Option A**: Modify backend to support product-specific kits
   - Add `productId` to Kit model
   - Add kit routes under products controller

2. **Option B**: Modify frontend to treat kits independently
   - Create a separate kits management page
   - Show kits that contain variations from a product

3. **Option C**: Create a "Product Kits" view
   - Filter kits by those containing variations from a specific product
   - This maintains the current backend structure while providing a product-centric view