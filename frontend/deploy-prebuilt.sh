#!/bin/bash

echo "🚀 Building and deploying ZenCash Frontend to Vercel..."
echo ""

# Set memory limit
export NODE_OPTIONS="--max-old-space-size=8192"
export GENERATE_SOURCEMAP=false

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf build

# Build locally
echo "📦 Building project locally with 8GB memory..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"
echo ""

# Deploy pre-built files to Vercel
echo "🌐 Deploying pre-built files to Vercel..."
vercel --prod --yes --prebuilt

echo ""
echo "✨ Deployment complete!"