const fs = require('fs');
const content = fs.readFileSync('src/components/OrdersTable.tsx', 'utf-8');
const lines = content.split('\n');

// Find the actual component boundaries more carefully
let inComponent = false;
let braceCount = 0;
let componentStart = -1;
let componentEnd = -1;

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  // Component start
  if (line.includes('const OrdersTable: React.FC<OrdersTableProps> = ({')) {
    componentStart = i;
    inComponent = true;
    console.log('Component starts at line:', i + 1);
  }
  
  if (inComponent) {
    // Count braces more carefully, considering arrow function syntax
    for (let j = 0; j < line.length; j++) {
      const char = line[j];
      const nextChar = j < line.length - 1 ? line[j + 1] : '';
      const prevChar = j > 0 ? line[j - 1] : '';
      
      // Skip arrow functions
      if (char === '=' && nextChar === '>') {
        j++; // skip the >
        continue;
      }
      
      // Count braces
      if (char === '{') braceCount++;
      if (char === '}') {
        braceCount--;
        
        // Check if component ended
        if (braceCount === 0 && i > componentStart + 5) {
          // Look for semicolon after the brace
          let hasSemicolon = false;
          for (let k = j + 1; k < line.length; k++) {
            if (line[k] === ';') {
              hasSemicolon = true;
              break;
            } else if (line[k] !== ' ') {
              break;
            }
          }
          
          if (hasSemicolon || (j === line.length - 1 && i + 1 < lines.length && lines[i + 1].trim().startsWith('export'))) {
            componentEnd = i;
            inComponent = false;
            console.log('Component ends at line:', i + 1);
            console.log('Ending line:', line.trim());
            break;
          }
        }
      }
    }
  }
  
  if (componentEnd !== -1) break;
}

// Now check where sortedOrders is defined and used
console.log('\nChecking sortedOrders scope:');
let sortedOrdersDefinedAt = -1;
let sortedOrdersUsedAt = -1;

for (let i = 0; i < lines.length; i++) {
  if (lines[i].includes('const sortedOrders =')) {
    sortedOrdersDefinedAt = i + 1;
  }
  if (i === 680 && lines[i].includes('sortedOrders')) {
    sortedOrdersUsedAt = i + 1;
  }
}

console.log('sortedOrders defined at line:', sortedOrdersDefinedAt);
console.log('sortedOrders used at line:', sortedOrdersUsedAt);
console.log('Component range:', componentStart + 1, 'to', componentEnd + 1);

if (sortedOrdersUsedAt > componentEnd) {
  console.log('\nERROR: sortedOrders is used outside the component!');
  console.log('This is why we get "return outside of function" error.');
}