<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h1>Testing Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <pre id="result"></pre>
    
    <script>
        async function testLogin() {
            const resultEl = document.getElementById('result');
            resultEl.textContent = 'Testing...';
            
            try {
                // First, let's check what API URL is being used
                const apiUrl = 'https://zencash-production.up.railway.app/api/v1';
                resultEl.textContent += '\nUsing API URL: ' + apiUrl;
                
                const response = await fetch(apiUrl + '/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-tenant-id': '28a833c0-c2a1-4498-85ca-b028f982ffb2'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Password123!'
                    })
                });
                
                resultEl.textContent += '\nResponse status: ' + response.status;
                resultEl.textContent += '\nResponse headers: ' + JSON.stringify([...response.headers]);
                
                const text = await response.text();
                resultEl.textContent += '\nResponse body: ' + text;
                
                if (response.ok) {
                    const data = JSON.parse(text);
                    resultEl.textContent += '\n\nSUCCESS! Login worked!';
                    resultEl.textContent += '\nAccess token: ' + (data.data?.access_token || data.access_token || 'not found');
                }
                
            } catch (error) {
                resultEl.textContent += '\n\nERROR: ' + error.message;
                resultEl.textContent += '\n\nThis might be a CORS issue. Check browser console for details.';
            }
        }
    </script>
</body>
</html>