#!/bin/bash

echo "=== Checking Frontend Deployment ==="
echo ""

# Check current build
echo "1. Checking local build for API URL..."
if [ -f "build/static/js/main.*.js" ]; then
    grep -o "http://localhost:3000\|https://zencash-production.up.railway.app" build/static/js/main.*.js | head -5
else
    echo "No build found locally"
fi

echo ""
echo "2. Current environment variables:"
echo "REACT_APP_API_URL=${REACT_APP_API_URL}"

echo ""
echo "3. Production env file:"
cat .env.production

echo ""
echo "=== To force Railway rebuild ==="
echo "Option 1: Trigger via empty commit"
echo "  git commit --allow-empty -m 'force rebuild' && git push"
echo ""
echo "Option 2: Clear build cache in Railway dashboard"
echo "  Go to Railway dashboard > Service > Settings > Clear build cache"