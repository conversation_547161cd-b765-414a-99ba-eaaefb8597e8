// Script to login a test user
const axios = require('axios');

async function login() {
  try {
    const response = await axios.post('http://localhost:3000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      headers: {
        'x-tenant-id': 'cmc4a2wg50000uvkzj76t0mpt'
      }
    });

    console.log('Login successful!');
    console.log('Access token:', response.data.data?.access_token || response.data.access_token);
    console.log('\nTo use in browser console:');
    console.log(`localStorage.setItem('unified_auth_tokens', '${JSON.stringify({
      access_token: response.data.data?.access_token || response.data.access_token,
      refresh_token: 'refresh-' + Date.now(),
      token_type: 'bearer'
    })}');`);
    
    if (response.data.data?.user || response.data.user) {
      const user = response.data.data?.user || response.data.user;
      console.log(`\nlocalStorage.setItem('unified_user_info', '${JSON.stringify({
        id: user.id,
        email: user.email,
        fullName: user.fullName || user.name || '',
        role: user.role.toLowerCase(),
        tenantId: user.tenantId || response.data.data?.tenantId || response.data.tenantId
      })}');`);
    }
    
    console.log(`\nlocalStorage.setItem('unified_token_expiry', '${Date.now() + (7 * 24 * 60 * 60 * 1000)}');`);
    
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
  }
}

login();