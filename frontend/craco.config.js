const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Remove ForkTsCheckerWebpackPlugin to prevent memory issues
      webpackConfig.plugins = webpackConfig.plugins.filter(
        (plugin) => {
          const pluginName = plugin.constructor.name;
          // Log to help debug
          if (pluginName === 'ForkTsCheckerWebpackPlugin') {
            console.log('Removing ForkTsCheckerWebpackPlugin to prevent memory issues');
            return false;
          }
          return true;
        }
      );

      // Optimize for production builds
      if (process.env.NODE_ENV === 'production') {
        // Disable source maps completely
        webpackConfig.devtool = false;
        
        // Configure Terser for better memory usage
        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          minimize: true,
          minimizer: [
            new TerserPlugin({
              parallel: 2, // Reduce parallel processing to save memory
              terserOptions: {
                compress: {
                  drop_console: true, // Remove console logs
                  drop_debugger: true,
                  pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'],
                },
                mangle: true,
                output: {
                  comments: false,
                },
              },
              extractComments: false,
            }),
          ],
          splitChunks: {
            chunks: 'all',
            maxSize: 244000, // Split chunks larger than 244KB
            cacheGroups: {
              default: false,
              vendors: false,
              // Split vendor chunks by package
              react: {
                test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
                name: 'react',
                priority: 30,
                chunks: 'all',
              },
              mui: {
                test: /[\\/]node_modules[\\/]@mui[\\/]/,
                name: 'mui',
                priority: 25,
                chunks: 'all',
              },
              antd: {
                test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
                name: 'antd',
                priority: 25,
                chunks: 'all',
              },
              vendor: {
                test: /[\\/]node_modules[\\/]/,
                name: 'vendor',
                priority: 20,
                chunks: 'all',
              },
              common: {
                name: 'common',
                minChunks: 2,
                priority: 10,
                reuseExistingChunk: true,
                enforce: true,
              }
            }
          }
        };

        // Additional performance optimizations
        webpackConfig.performance = {
          hints: false, // Disable performance warnings
        };

        // Reduce memory usage during build
        webpackConfig.stats = 'minimal';
      }

      return webpackConfig;
    },
  },
  // Disable TypeScript type checking
  typescript: {
    enableTypeChecking: false
  }
};