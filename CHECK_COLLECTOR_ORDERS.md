# Diagnostic Steps for <PERSON><PERSON> Not Seeing Orders

## Issue
Cobrador users see the correct order count in the sidebar but don't see the actual orders in the main list.

## Root Cause
The backend filters orders for COBRADOR role by `collectorId = userId`. This means collectors only see orders assigned to them.

## Debugging Steps

1. **Check if orders have collectorId assigned**
   - In Railway logs, look for: `[OrdersService.findAll] COBRADOR role - filtering by collectorId: <userId>`
   - Check if the userId matches any order's collectorId

2. **Verify the Cobrador's User ID**
   - When the Cobrador logs in, check the console or logs for their userId
   - Note this ID for comparison

3. **Check Order Assignment**
   - Look at the orders in the database
   - Check if any orders have `collectorId` matching the Cobrador's userId

## Common Issues

1. **Orders not assigned to any collector**
   - Orders created without a collectorId
   - Need to assign orders to collectors

2. **Mismatch between user roles**
   - User might have role 'collector' in frontend but 'COBRADOR' in backend
   - Check role consistency

3. **User ID mismatch**
   - Cobrador's userId might not match any order's collectorId

## Solution

To fix this, you need to:

1. **Assign orders to collectors when creating them**
   - Pass `collectorId` when creating orders
   - Or use the auto-assignment feature

2. **Update existing orders**
   - Use the admin edit form to assign collectors to orders
   - Select the appropriate "Operador" from the dropdown

3. **Verify assignment**
   - After assigning, check if the Cobrador can see the orders