<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook Test - ZenCash</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .endpoint {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .endpoint h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
        }
        .success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .url {
            display: inline-block;
            background-color: #e0e0e0;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Webhook Test - ZenCash</h1>
        
        <div class="endpoint">
            <h3>1. Test Health Check</h3>
            <p>URL: <span class="url">https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/health</span></p>
            <button onclick="testHealth()">Test GET Health</button>
            <div id="health-response"></div>
        </div>

        <div class="endpoint">
            <h3>2. Test Info Endpoint</h3>
            <p>URL: <span class="url">https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/info</span></p>
            <button onclick="testInfo()">Test GET Info</button>
            <div id="info-response"></div>
        </div>

        <div class="endpoint">
            <h3>3. Test Webhook Endpoint</h3>
            <p>URL: <span class="url">https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test</span></p>
            <button onclick="testWebhook()">Test POST Webhook</button>
            <div id="test-response"></div>
        </div>

        <div class="endpoint">
            <h3>4. Test Production Webhook</h3>
            <p>URL: <span class="url">https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/shipments</span></p>
            <button onclick="testProduction()">Test POST Production</button>
            <div id="production-response"></div>
        </div>
    </div>

    <script>
        const baseUrl = 'https://zencash-production-1ccd.up.railway.app';

        async function makeRequest(url, method = 'GET', body = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            try {
                console.log(`Making ${method} request to:`, url);
                const response = await fetch(url, options);
                const data = await response.text();
                
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    jsonData = data;
                }

                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    data: jsonData,
                    headers: Object.fromEntries(response.headers.entries())
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    type: 'Network Error'
                };
            }
        }

        function displayResponse(elementId, response) {
            const element = document.getElementById(elementId);
            const className = response.success ? 'success' : 'error';
            
            let content = `Status: ${response.status || 'N/A'} ${response.statusText || ''}\n`;
            if (response.error) {
                content += `Error: ${response.error}\n`;
            }
            if (response.data) {
                content += `\nResponse:\n${typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2)}`;
            }
            
            element.innerHTML = `<div class="response ${className}">${content}</div>`;
        }

        async function testHealth() {
            const response = await makeRequest(`${baseUrl}/api/v1/webhooks/health`);
            displayResponse('health-response', response);
        }

        async function testInfo() {
            const response = await makeRequest(`${baseUrl}/api/v1/webhooks/info`);
            displayResponse('info-response', response);
        }

        async function testWebhook() {
            const testData = {
                test: 'Hello from browser',
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            };
            const response = await makeRequest(`${baseUrl}/api/v1/webhooks/test`, 'POST', testData);
            displayResponse('test-response', response);
        }

        async function testProduction() {
            const testData = {
                codigoRastreio: 'TEST123456789BR',
                situacao: 'Em trânsito',
                cliente: 'Teste Browser',
                telefone: '11999999999',
                valorTotal: 100.00
            };
            const response = await makeRequest(`${baseUrl}/api/v1/webhooks/shipments`, 'POST', testData);
            displayResponse('production-response', response);
        }
    </script>
</body>
</html>