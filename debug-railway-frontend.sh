#!/bin/bash

echo "🔍 Railway Frontend Debugging Script"
echo "===================================="
echo ""

# Link to project
echo "1. Linking to Railway project..."
railway link

# Get logs
echo ""
echo "2. Fetching deployment logs..."
railway logs --service frontend | tail -100 > frontend-logs.txt

# Check deployment status
echo ""
echo "3. Checking deployment status..."
railway status

# Show recent logs
echo ""
echo "4. Recent logs from frontend service:"
echo "-------------------------------------"
tail -20 frontend-logs.txt

echo ""
echo "5. Looking for common issues..."
echo "-------------------------------------"
grep -i "error\|fail\|crash\|exit" frontend-logs.txt | tail -10

echo ""
echo "📋 Full logs saved to: frontend-logs.txt"
echo ""
echo "Common fixes:"
echo "- If PORT is undefined: Add PORT env variable in Railway"
echo "- If 'serve' not found: Check if npm install completed"
echo "- If build failed: Check memory usage"