# Product and Inventory Migration Guide

## Overview

All product, offer, kit, and inventory management has been moved from localStorage to the backend PostgreSQL database. This ensures data consistency, multi-user support, and proper inventory tracking.

## What Was Implemented

### Backend (NestJS)

1. **Database Schema**
   - `Product` model: Core product information
   - `Offer` model: Product offers with pricing and quantities
   - `InventoryItem` model: Track stock levels for GEL and CAPSULES
   - `InventoryTransaction` model: Track all inventory movements

2. **Products Module**
   - Full CRUD for products
   - Full CRUD for offers
   - Support for kits (offers with both gel and capsules)
   - Soft delete for offers in use

3. **Inventory Module**
   - Real-time stock tracking
   - Transaction history
   - Low stock alerts
   - Inventory statistics
   - Automatic stock adjustment on order creation

### Frontend Updates

1. **ProductService**
   - Now uses backend API instead of localStorage
   - Maintains same interface for compatibility

2. **InventoryService**
   - Now uses backend API instead of localStorage
   - Real-time inventory tracking

## API Endpoints

### Products
- `GET /products` - List all products
- `GET /products/:id` - Get single product
- `POST /products` - Create product (Admin/Supervisor)
- `PATCH /products/:id` - Update product (Admin/Supervisor)
- `DELETE /products/:id` - Delete product (Admin)

### Offers
- `GET /products/offers/active` - Get all active offers
- `GET /products/:productId/offers` - Get offers for a product
- `POST /products/:productId/offers` - Create offer (Admin/Supervisor)
- `PATCH /products/offers/:id` - Update offer (Admin/Supervisor)
- `DELETE /products/offers/:id` - Delete/deactivate offer (Admin)

### Inventory
- `GET /inventory` - List inventory items
- `GET /inventory/statistics` - Get inventory stats
- `GET /inventory/low-stock` - Get low stock items
- `GET /inventory/:productType` - Get item details (GEL or CAPSULES)
- `PATCH /inventory/:productType` - Update settings (Admin/Supervisor)
- `POST /inventory/transactions` - Create transaction (Admin/Supervisor)

## Database Migration Steps

1. **Stop the backend**
```bash
# Press Ctrl+C in the terminal running the backend
```

2. **Generate and run migrations**
```bash
cd backend
npx prisma generate
npx prisma db push
```

3. **Restart the backend**
```bash
npm run start:dev
```

4. **Verify seed data**
The seed script creates:
- 1 Product: "Potência Azul"
- 7 Offers: Including "Kit Completo 30 dias"
- Initial inventory: 100 GEL, 100 CAPSULES

## Features

### Product Management
- ✅ Create/update/delete products
- ✅ Multiple offers per product
- ✅ Kit support (combined gel + capsules)
- ✅ Display order management
- ✅ Active/inactive status
- ✅ Usage tracking (prevents deletion of offers in use)

### Inventory Management
- ✅ Real-time stock levels
- ✅ Transaction history with user tracking
- ✅ Automatic stock deduction on order creation
- ✅ Purchase, sale, adjustment, and return transactions
- ✅ Low stock alerts
- ✅ Cost tracking and valuation
- ✅ Statistics dashboard

### Multi-Tenancy
- ✅ All products/inventory isolated by tenant
- ✅ Tenant ID automatically added to all queries
- ✅ Role-based access control

## Integration with Orders

When an order is created with an offer:
1. The offer's gel/capsules quantities are automatically deducted from inventory
2. Transaction records are created for audit trail
3. If insufficient stock, the order creation fails

## Security

- Products: All users can view, Admin/Supervisor can modify
- Inventory: All users can view, Admin/Supervisor can create transactions
- Tenant isolation: Each tenant has separate products and inventory

## Testing

1. **Test product creation**
```bash
# <NAME_EMAIL>
# Navigate to Products page
# Create a new product
```

2. **Test inventory**
```bash
# Navigate to Inventory page
# Check current stock levels
# Create a purchase transaction
```

3. **Test order integration**
```bash
# Create a new order with an offer
# Check inventory is automatically reduced
```

## Troubleshooting

### Products not showing
- Check console for API errors
- Verify you're logged in with correct tenant
- Check backend logs for errors

### Inventory not updating
- Ensure you have Admin/Supervisor role
- Check for insufficient stock errors
- Verify transaction was created successfully

### Migration errors
- Drop and recreate database if needed
- Check PostgreSQL is running
- Verify DATABASE_URL in .env