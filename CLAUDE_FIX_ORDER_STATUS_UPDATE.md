# Order Status Update Fix

## Problem
When trying to change the status of an order (pedido), users were getting the error message: "Erro ao atualizar status. Por favor, tente novamente."

## Root Cause
1. The frontend was using an old OrderStatus enum (PENDING, IN_PROGRESS, etc.) that didn't match the backend's Prisma enum values (PagamentoPendente, Separacao, etc.)
2. The status mapping in `convertToBackendOrderUpdate` was incorrect
3. The backend expects status updates via a specific endpoint: `PATCH /orders/:id/status`

## Solution Implemented

### 1. Created New Status Update Method (OrderService.ts)
```typescript
async updateOrderStatus(orderId: string, status: string): Promise<Order> {
  // Map Portuguese status names to Prisma enum values
  const statusMap: Record<string, string> = {
    'Pagamento Pendente': 'PagamentoPendente',
    'Completo': 'Completo',
    'Parcial': 'Parcial',
    'Negociação': 'Negociacao',
    'Cancelado': '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    'Recupera<PERSON>': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    'Separação': 'Separacao',
    'Tr<PERSON><PERSON>to': 'Transito',
    'Retirar Correios': 'RetirarCorreios',
    'Entrega Falha': 'EntregaFalha',
    'Confirmar Entrega': 'ConfirmarEntrega',
    'Devolvido Correios': 'DevolvidoCorreios',
  };
  
  const mappedStatus = statusMap[status] || status;
  
  // Use the specific status update endpoint
  const response = await api.patch(`/orders/${orderId}/status`, {
    status: mappedStatus
  });
}
```

### 2. Updated Status Mapping in convertToBackendOrderUpdate
Fixed the status mapping to use the correct Prisma enum values instead of the old enum.

### 3. Updated OrdersTable Component
Modified to use the new `updateOrderStatus` method when only status is being updated.

## Testing
1. Try changing an order status in the orders table
2. Check console logs for the status mapping
3. Verify the status is updated correctly in the backend
4. Confirm the UI reflects the new status