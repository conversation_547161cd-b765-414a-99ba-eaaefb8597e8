version: '3.8'

services:
  backend:
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=True
      - ENVIRONMENT=development
    command: sh -c "python -m app.db.init_db && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

  frontend:
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_MOCK_API=true
      - NODE_ENV=development

  db:
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

volumes:
  postgres_data:
