#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===============================================${NC}"
echo -e "${BLUE}  Product & Inventory Migration Script${NC}"
echo -e "${BLUE}===============================================${NC}"
echo ""

# Function to check if a command succeeded
check_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1 failed${NC}"
        exit 1
    fi
}

# Function to wait for user confirmation
confirm() {
    read -p "$(echo -e ${YELLOW}"$1 (y/n): "${NC})" -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}Migration cancelled.${NC}"
        exit 1
    fi
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ Error: docker-compose.yml not found.${NC}"
    echo -e "${YELLOW}Please run this script from the sistema-cobranca root directory.${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Pre-migration checklist:${NC}"
echo "   - PostgreSQL should be running (docker-compose up -d)"
echo "   - Backend .env file should be configured"
echo "   - You should have backed up any important data"
echo ""

confirm "Have you completed the pre-migration checklist?"

# Step 1: Check if PostgreSQL is running
echo -e "\n${BLUE}Step 1: Checking PostgreSQL...${NC}"
if docker-compose ps | grep -q "postgres.*Up"; then
    echo -e "${GREEN}✅ PostgreSQL is running${NC}"
else
    echo -e "${YELLOW}⚠️  PostgreSQL is not running. Starting it now...${NC}"
    docker-compose up -d postgres
    sleep 5
    check_status "PostgreSQL started"
fi

# Step 2: Check if backend dependencies are installed
echo -e "\n${BLUE}Step 2: Checking backend dependencies...${NC}"
cd backend
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing backend dependencies...${NC}"
    npm install
    check_status "Dependencies installed"
else
    echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Step 3: Generate Prisma client
echo -e "\n${BLUE}Step 3: Generating Prisma client...${NC}"
npx prisma generate
check_status "Prisma client generated"

# Step 4: Check current database state
echo -e "\n${BLUE}Step 4: Checking database state...${NC}"
echo -e "${YELLOW}Current database tables:${NC}"
npx prisma db pull --print 2>/dev/null | grep "model" || echo "No existing tables found"

# Step 5: Push schema changes
echo -e "\n${BLUE}Step 5: Applying database schema changes...${NC}"
echo -e "${YELLOW}This will update your database schema to include:${NC}"
echo "   - Product table"
echo "   - Offer table"
echo "   - InventoryItem table"
echo "   - InventoryTransaction table"
echo ""

confirm "Do you want to apply these schema changes?"

npx prisma db push --accept-data-loss
check_status "Database schema updated"

# Step 6: Run seed data
echo -e "\n${BLUE}Step 6: Seeding initial data...${NC}"
echo -e "${YELLOW}This will create:${NC}"
echo "   - 1 Product: Potência Azul"
echo "   - 7 Offers: Including kits"
echo "   - Initial inventory: 100 GEL, 100 CAPSULES"
echo "   - Sample transactions"
echo ""

confirm "Do you want to seed the database?"

npm run db:seed
check_status "Database seeded"

# Step 7: Test the API endpoints
echo -e "\n${BLUE}Step 7: Testing API endpoints...${NC}"

# Get JWT token for testing
echo -e "${YELLOW}Getting authentication token...${NC}"
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:4000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: acme-corp" \
  -d '{"email":"<EMAIL>","password":"admin123"}' 2>/dev/null)

if echo "$TOKEN_RESPONSE" | grep -q "access_token"; then
    TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Authentication successful${NC}"
else
    echo -e "${RED}❌ Authentication failed. Is the backend running?${NC}"
    echo -e "${YELLOW}Start the backend with: npm run start:dev${NC}"
    exit 1
fi

# Test products endpoint
echo -e "\n${YELLOW}Testing /products endpoint...${NC}"
PRODUCTS_RESPONSE=$(curl -s -X GET http://localhost:4000/api/v1/products \
  -H "Authorization: Bearer $TOKEN" \
  -H "x-tenant-id: acme-corp" 2>/dev/null)

if echo "$PRODUCTS_RESPONSE" | grep -q "Potência Azul"; then
    echo -e "${GREEN}✅ Products API is working${NC}"
    PRODUCT_COUNT=$(echo "$PRODUCTS_RESPONSE" | grep -o '"id"' | wc -l)
    echo -e "   Found $PRODUCT_COUNT product(s)"
else
    echo -e "${RED}❌ Products API test failed${NC}"
fi

# Test inventory endpoint
echo -e "\n${YELLOW}Testing /inventory endpoint...${NC}"
INVENTORY_RESPONSE=$(curl -s -X GET http://localhost:4000/api/v1/inventory \
  -H "Authorization: Bearer $TOKEN" \
  -H "x-tenant-id: acme-corp" 2>/dev/null)

if echo "$INVENTORY_RESPONSE" | grep -q "currentStock"; then
    echo -e "${GREEN}✅ Inventory API is working${NC}"
else
    echo -e "${RED}❌ Inventory API test failed${NC}"
fi

# Step 8: Update frontend environment
echo -e "\n${BLUE}Step 8: Checking frontend configuration...${NC}"
cd ../frontend

if [ ! -f ".env" ]; then
    echo -e "${YELLOW}Creating frontend .env file...${NC}"
    cat > .env << EOF
REACT_APP_API_URL=http://localhost:4000/api/v1
REACT_APP_TENANT_ID=acme-corp
REACT_APP_MOCK_API=false
EOF
    check_status "Frontend .env created"
else
    echo -e "${GREEN}✅ Frontend .env already exists${NC}"
fi

# Final summary
echo -e "\n${GREEN}===============================================${NC}"
echo -e "${GREEN}  Migration Complete! 🎉${NC}"
echo -e "${GREEN}===============================================${NC}"
echo ""
echo -e "${BLUE}Summary:${NC}"
echo -e "✅ Database schema updated"
echo -e "✅ Products and inventory tables created"
echo -e "✅ Initial data seeded"
echo -e "✅ API endpoints tested"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "1. Start the backend: ${YELLOW}cd backend && npm run start:dev${NC}"
echo -e "2. Start the frontend: ${YELLOW}cd frontend && npm start${NC}"
echo -e "3. Login as: ${YELLOW}<EMAIL> / admin123${NC}"
echo -e "4. Navigate to Products or Inventory pages"
echo ""
echo -e "${BLUE}Available Features:${NC}"
echo -e "• Product management with multiple offers"
echo -e "• Kit support (products with gel + capsules)"
echo -e "• Real-time inventory tracking"
echo -e "• Automatic stock deduction on orders"
echo -e "• Transaction history and audit trail"
echo -e "• Low stock alerts"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"