#!/bin/bash

# Test webhook endpoints
BASE_URL="https://zencash-production-1ccd.up.railway.app"

echo "Testing Webhook Endpoints..."
echo "============================"

# Test the info endpoint
echo -e "\n1. Testing GET /api/v1/webhooks/info"
curl -X GET "$BASE_URL/api/v1/webhooks/info" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\n"

# Test the test endpoint
echo -e "\n\n2. Testing POST /api/v1/webhooks/test"
curl -X POST "$BASE_URL/api/v1/webhooks/test" \
  -H "Content-Type: application/json" \
  -d '{
    "test": "Hello from webhook test",
    "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'"
  }' \
  -w "\nHTTP Status: %{http_code}\n"

# Test the production endpoint without signature (should work in dev)
echo -e "\n\n3. Testing POST /api/v1/webhooks/shipments (without signature)"
curl -X POST "$BASE_URL/api/v1/webhooks/shipments" \
  -H "Content-Type: application/json" \
  -d '{
    "codigoRastreio": "TEST123456789BR",
    "situacao": "Em trânsito",
    "cliente": "Teste Webhook",
    "telefone": "11999999999",
    "valorTotal": 100.00
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo -e "\n\nWebhook URLs for external systems:"
echo "=================================="
echo "Test URL: $BASE_URL/api/v1/webhooks/test"
echo "Production URL: $BASE_URL/api/v1/webhooks/shipments"
echo "Info URL: $BASE_URL/api/v1/webhooks/info"