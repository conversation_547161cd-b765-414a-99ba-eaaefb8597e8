# Unified User Fields Documentation

## Overview
This document describes the unified naming convention for user-related fields in the Order model, ensuring consistency between frontend and backend.

## User Role Fields

### 1. Seller (Vendedor)
- **ID Field**: `sellerId` (string)
- **Name Field**: `vendedor` (string) - display name
- **Backend**: Uses `sellerId` in Prisma schema
- **Frontend**: Uses `sellerId` in Order type (previously was `vendedorId`)

### 2. Collector/Operator (Cobrador/Operador)
- **ID Field**: `collectorId` (string)
- **Name Field**: `operador` (string) - display name
- **Backend**: Uses `collectorId` in Prisma schema
- **Frontend**: Uses `collectorId` in Order type (previously had both `collectorId` and `operadorId`)

### 3. Admin/Supervisor Actions
These fields track actions by admin/supervisor users:

- **`reviewedBy`** (string) - ID of admin/supervisor who reviewed for anti-fraud
- **`deletedBy`** (string) - ID of admin who soft-deleted the order
- **`changedById`** (string) - ID of user who changed status (in OrderStatusHistory)

## Key Principles

1. **Use Backend Field Names**: Frontend should always use the same field names as the backend Prisma schema
2. **Single ID per Role**: Each role has only one ID field (no duplicates like operadorId/collectorId)
3. **Consistent Naming**: 
   - IDs use camelCase with "Id" suffix: `sellerId`, `collectorId`
   - Name fields are simple: `vendedor`, `operador`

## Migration Summary

### What Changed:
1. **Removed `operadorId`** - Now only use `collectorId`
2. **Changed `vendedorId` to `sellerId`** - To match backend schema
3. **Removed vendedor fields from order creation** - Backend determines seller from authenticated user

### Backend Schema (Prisma):
```prisma
model Order {
  sellerId     String    // The seller who created the order
  collectorId  String?   // The collector/operator assigned to the order
  reviewedBy   String?   // Admin/supervisor who reviewed
  deletedBy    String?   // Admin who deleted
  
  // Relations
  seller       User      @relation("SellerOrders", fields: [sellerId], references: [id])
  collector    User?     @relation("CollectorOrders", fields: [collectorId], references: [id])
}
```

### Frontend Type:
```typescript
interface Order {
  // IDs
  sellerId?: string;      // Seller's user ID
  collectorId?: string;   // Collector's user ID
  
  // Display names
  vendedor: string;       // Seller's name
  operador: string;       // Collector's name
}
```

## Benefits

1. **Consistency**: Frontend and backend use identical field names
2. **Clarity**: No confusion about which field to use
3. **Maintainability**: Easier to understand and maintain
4. **Type Safety**: TypeScript can properly validate field usage