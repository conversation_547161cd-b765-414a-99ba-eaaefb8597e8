#!/bin/bash

# Quick migration script - runs everything automatically
set -e

echo "🚀 Starting Product & Inventory Migration..."

# Ensure we're in the right directory
cd "$(dirname "$0")"

# Start PostgreSQL if not running
echo "📦 Starting PostgreSQL..."
docker-compose up -d postgres
sleep 5

# Backend setup
echo "🔧 Setting up backend..."
cd backend
npm install --silent
npx prisma generate
npx prisma db push --accept-data-loss
npm run db:seed

# Create frontend .env if it doesn't exist
cd ../frontend
if [ ! -f ".env" ]; then
    echo "📝 Creating frontend .env..."
    cat > .env << EOF
REACT_APP_API_URL=http://localhost:4000/api/v1
REACT_APP_TENANT_ID=acme-corp
REACT_APP_MOCK_API=false
EOF
fi

echo "✅ Migration complete!"
echo ""
echo "To start the application:"
echo "  Backend:  cd backend && npm run start:dev"
echo "  Frontend: cd frontend && npm start"
echo ""
echo "Login: <EMAIL> / admin123"