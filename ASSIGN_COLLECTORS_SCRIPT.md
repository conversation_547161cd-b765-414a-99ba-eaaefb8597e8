# Script to Assign Collectors to Orders

## Quick Fix for Cobrador Not Seeing Orders

### Problem
Orders don't have collectorId assigned, so Cobradors can't see them.

### Solution
1. <PERSON>gin as Admin
2. Open browser console (F12)
3. Run this script to see orders without collectors:

```javascript
// Check orders without collectors
const orders = JSON.parse(localStorage.getItem('orders') || '[]');
const unassignedOrders = orders.filter(o => !o.collectorId && !o.operadorId);
console.log(`Found ${unassignedOrders.length} orders without collectors`);
console.table(unassignedOrders.map(o => ({
  id: o.id,
  idVenda: o.idVenda,
  cliente: o.cliente,
  vendedor: o.vendedor,
  operador: o.operador,
  collectorId: o.collectorId
})));
```

### To Fix in the UI

1. As Admin, open each order in the edit form
2. Select an "Operador" from the dropdown
3. Save the changes
4. The selected Cobrador will now see the order

### Alternative: Batch Assignment

For many orders, you can:
1. Use the admin panel to edit orders
2. Select multiple orders
3. Assign them to specific collectors

### Verification

After assigning collectors:
1. <PERSON><PERSON> as the Cobrador
2. Check if orders now appear in the main list
3. The sidebar count should match the visible orders