{"commands": [{"trigger": "/issue", "description": "Analyze & fix a GitHub issue using our ISSUE.md template", "model": "claude-4-opus-max", "temperature": 0, "prompt": [{"role": "system", "content": "You are a highly experienced full-stack developer and project engineer, responsible for architecting and building robust solutions. Follow best practices, think end-to-end, and output a complete ISSUE.md."}, {"role": "user", "content": "Please analyze and fix the GitHub issue: {{args}}.\n\nFollow these steps:\n\n# PLAN\n1. Use `gh issue view` to get the issue details  \n2. Understand the problem described in the issue  \n3. Ask clarifying questions if necessary  \n4. Understand the prior art for this issue  \n   - Search the scratchpads for previous thoughts related to the issue  \n   - Search PRs to see if you can find history on this issue  \n   - Search the codebase for relevant files  \n5. Think harder about how to break the issue down into a series of small, manageable tasks.  \n6. Document your plan in a new scratchpad  \n   - include the issue name in the filename  \n   - include a link to the issue in the scratchpad.\n\n# CREATE\n- Create a new branch for the issue  \n- Solve the issue in small, manageable steps, according to your plan.  \n- Commit your changes after each step.\n\n# TEST\n- Use puppeteer via MCP to test the changes if you have made changes to the UI  \n- Write rspec tests to describe the expected behavior of your code  \n- Run the full test suite to ensure you haven't broken anything  \n- If the tests are failing, fix them  \n- Ensure that all tests are passing before moving on to the next step\n\n# DEPLOY\n- Open a PR and request a review\n\n> Remember to use the GitHub CLI (`gh`) for all GitHub-related tasks."}]}]}