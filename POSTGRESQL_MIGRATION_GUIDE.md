# PostgreSQL Migration Guide

This guide will help you migrate from SQLite to PostgreSQL for production use.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Node.js 18+ installed
- Access to a PostgreSQL database (local or cloud)

## Step 1: Start PostgreSQL with Docker

```bash
# Start PostgreSQL and Redis
docker-compose up -d

# Verify containers are running
docker-compose ps
```

## Step 2: Configure Environment Variables

Create a `.env` file in the backend directory:

```bash
cd backend
cp .env.example .env
```

Edit the `.env` file with your database credentials:
```env
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/sistema_cobranca?schema=public"
JWT_SECRET="your-very-secure-random-string-here"
JWT_EXPIRATION="7d"
FRONTEND_URL="http://localhost:3000"
NODE_ENV="development"
PORT=4000
```

## Step 3: Generate Prisma Client

```bash
# Install dependencies if not already installed
npm install

# Generate Prisma Client
npx prisma generate
```

## Step 4: Create Database Schema

```bash
# Create the database schema
npx prisma db push

# Run migrations (for production)
npx prisma migrate dev --name init
```

## Step 5: Seed the Database

```bash
# Run the seed script
npm run db:seed
```

This will create:
- Default tenant (ACME Corporation)
- 4 users with different roles
- Sample orders with various statuses
- Tracking history

## Step 6: Update Backend Services

The backend services need to be updated to properly handle:
1. Multi-tenancy
2. Transactions
3. Connection pooling

Key changes made:
- Updated Prisma schema with proper relations
- Added soft delete support
- Added proper indexes for performance
- Fixed model naming (PascalCase)

## Step 7: Update Frontend Configuration

Update the frontend to use the new backend:

```typescript
// frontend/src/services/api.ts
const tenantId = 'acme-corp'; // This should come from environment or auth
```

## Step 8: Test the Migration

1. Start the backend:
```bash
cd backend
npm run start:dev
```

2. Start the frontend:
```bash
cd frontend
npm start
```

3. Login with:
- Email: <EMAIL>
- Password: admin123

## Production Considerations

### 1. Database URL Security
Never commit the `.env` file. Use environment variables in production:
```bash
DATABASE_URL="********************************/db?sslmode=require"
```

### 2. Connection Pooling
PostgreSQL has built-in connection pooling. For production, consider:
- PgBouncer for connection pooling
- Prisma's connection limit settings

### 3. Backup Strategy
- Set up automated backups
- Test restore procedures
- Use point-in-time recovery

### 4. Monitoring
- Monitor connection count
- Track slow queries
- Set up alerts for errors

### 5. Security
- Use SSL/TLS connections
- Rotate credentials regularly
- Use least privilege principle
- Enable row-level security

## Troubleshooting

### Error: Database does not exist
```bash
# Create database manually
docker exec -it sistema-cobranca-postgres-1 psql -U postgres -c "CREATE DATABASE sistema_cobranca;"
```

### Error: Permission denied
```bash
# Grant permissions
docker exec -it sistema-cobranca-postgres-1 psql -U postgres -d sistema_cobranca -c "GRANT ALL PRIVILEGES ON DATABASE sistema_cobranca TO postgres;"
```

### Error: Connection refused
- Check if PostgreSQL is running: `docker-compose ps`
- Check connection string in `.env`
- Verify firewall/network settings

## Next Steps

After successful migration:
1. Update JWT secret to a secure value
2. Implement proper tenant isolation
3. Add rate limiting
4. Set up monitoring and logging
5. Configure backup strategy