# Webhook Debug Guide

## Why "Invalid or inaccessible webhook URL" Error?

This error typically occurs when the webhook provider:
1. Cannot reach the URL (network/firewall issue)
2. Gets an unexpected response format
3. Times out waiting for response
4. Receives wrong HTTP status code
5. Cannot validate the endpoint

## All Available Webhook URLs

### Main Endpoints
1. **Production Webhook**
   ```
   POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/shipments
   ```

2. **Test Webhook**
   ```
   POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test
   ```

### Validation Endpoints
3. **Simple Validation** (returns plain text "ok")
   ```
   ANY https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/validate
   ```

4. **Ping Endpoint** (returns JSON {status: "ok"})
   ```
   POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/ping
   ```

5. **Echo Endpoint** (mirrors back what you send)
   ```
   ANY https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/echo
   ```

6. **Root Webhook** (shows all endpoints)
   ```
   ANY https://zencash-production-1ccd.up.railway.app/api/v1/webhooks
   ```

## Testing Methods

### Method 1: Try Different Endpoints
If the main endpoint doesn't work, try these in order:
1. `/api/v1/webhooks/validate` - Simplest response
2. `/api/v1/webhooks/ping` - Basic JSON response
3. `/api/v1/webhooks/echo` - Accepts any method
4. `/api/v1/webhooks` - Root endpoint

### Method 2: Test with cURL
```bash
# Test validate endpoint
curl https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/validate

# Test ping endpoint
curl -X POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/ping

# Test echo endpoint
curl -X POST https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/echo \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

### Method 3: Check with HEAD Request
Some providers validate with HEAD first:
```bash
curl -I https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test
```

## Common Issues and Solutions

### Issue: Platform requires specific response format
**Solution:** Use `/api/v1/webhooks/validate` which returns plain "ok"

### Issue: Platform does OPTIONS preflight
**Solution:** All endpoints now support OPTIONS method

### Issue: Platform validates with HEAD request
**Solution:** All endpoints now support HEAD method

### Issue: Platform expects empty response
**Solution:** HEAD requests return empty body with headers

### Issue: Platform has short timeout
**Solution:** All endpoints respond immediately

## Platform-Specific URLs

Some platforms work better with different URL formats:

1. **Without /api/v1 prefix:**
   ```
   https://zencash-production-1ccd.up.railway.app/webhooks/test
   ```

2. **With trailing slash:**
   ```
   https://zencash-production-1ccd.up.railway.app/api/v1/webhooks/test/
   ```

3. **Direct backend URL (if available):**
   ```
   https://zencash-backend-production.up.railway.app/api/v1/webhooks/test
   ```

## Response Examples

### /validate endpoint
```
ok
```

### /ping endpoint
```json
{
  "status": "ok"
}
```

### /echo endpoint
```json
{
  "success": true,
  "message": "Echo endpoint working",
  "method": "POST",
  "timestamp": "2025-01-23T00:00:00.000Z",
  "received": { ... your data ... }
}
```

## Still Not Working?

1. **Check if n8n webhook format works** - Since n8n works, try using the same format
2. **Contact platform support** - Ask what validation they perform
3. **Check platform documentation** - Look for webhook requirements
4. **Use request bin** - Test what the platform actually sends
5. **Check Railway logs** - See if requests are reaching your server