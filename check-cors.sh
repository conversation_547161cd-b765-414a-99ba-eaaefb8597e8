#!/bin/bash

echo "Checking CORS configuration..."
echo "Testing from frontend origin..."

curl -X OPTIONS "https://zencash-production.up.railway.app/api/v1/auth/login" \
  -H "Origin: https://zencash-production-1ccd.up.railway.app" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: content-type,x-tenant-id" \
  -v 2>&1 | grep -E "(< HTTP|< access-control-allow-origin)"

echo ""
echo "If you see 'access-control-allow-origin: https://zencash-production-1ccd.up.railway.app', CORS is configured correctly!"